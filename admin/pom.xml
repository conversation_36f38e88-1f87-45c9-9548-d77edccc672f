<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.qc.agent</groupId>
		<artifactId>qc-ai-agent</artifactId>
		<version>1.0.0</version>
	</parent>
	<artifactId>admin</artifactId>
	<version>1.0.0</version>
	<name>ai-agent</name>
	<description>ai-agent</description>
	<packaging>jar</packaging>
	<properties>

	</properties>
	<dependencies>
		<dependency>
			<groupId>com.qc.agent</groupId>
			<artifactId>ai-agent-common</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>com.qc.agent</groupId>
			<artifactId>ai-agent-lla</artifactId>
			<version>1.0.0</version>
		</dependency>

		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-api</artifactId>
			<version>2.17.0</version>
		</dependency>
		<dependency>
			<groupId>com.qc.agent</groupId>
			<artifactId>ai-agent-vectordb</artifactId>
			<version>1.0.0</version>
		</dependency>
<!--		<dependency>-->
<!--			<groupId>com.qc.agent</groupId>-->
<!--			<artifactId>ai-agent-elastic-job</artifactId>-->
<!--			<version>1.0.0</version>-->
<!--		</dependency>-->

		<dependency>
			<groupId>io.projectreactor</groupId>
			<artifactId>reactor-core</artifactId>
		</dependency>

		<!-- https://mvnrepository.com/artifact/org.asynchttpclient/async-http-client -->
		<dependency>
			<groupId>org.asynchttpclient</groupId>
			<artifactId>async-http-client</artifactId>
			<version>2.12.3</version>
		</dependency>

		<dependency>
			<groupId>com.tencentcloudapi</groupId>
			<artifactId>tencentcloud-sdk-java-hunyuan</artifactId>
			<version>3.1.956</version>
		</dependency>

		<dependency>
			<groupId>com.qc.agent</groupId>
			<artifactId>ai-agent-storage</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi</artifactId>
			<version>5.2.5</version> <!-- 可以使用最新版本 -->
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
			<version>5.2.5</version>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>easyexcel</artifactId>
			<version>3.3.2</version>
		</dependency>
		<dependency>
			<groupId>commons-beanutils</groupId>
			<artifactId>commons-beanutils</artifactId>
			<version>1.9.4</version> <!-- 使用最新版本 -->
		</dependency>

		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-text</artifactId>
			<version>1.10.0</version>
		</dependency>

		<dependency>
			<groupId>org.junit.jupiter</groupId>
			<artifactId>junit-jupiter</artifactId>
			<version>5.10.0</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-core</artifactId>
			<version>5.10.0</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-junit-jupiter</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>


	</dependencies>

	<build>
		<finalName>ai-agent</finalName>

		<resources>
			<resource>
				<directory>src/main/resources</directory>
				<filtering>true</filtering>
				<excludes>
					<!--
					<exclude>*.properties</exclude>
					 -->
				</excludes>
			</resource>
		</resources>

		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<mainClass>com.qc.agent.AdminApplication</mainClass>
				</configuration>
				<executions>
					<execution>
						<goals>
							<goal>repackage</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>

</project>
