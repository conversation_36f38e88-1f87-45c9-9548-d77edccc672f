dromara:
  x-file-storage: #文件存储配置
    default-platform: aliyun-oss-1 #默认使用的存储平台
    thumbnail-suffix: ".min.jpg" #缩略图后缀，例如【.min.jpg】【.png】
    aliyun-oss:
      - platform: aliyun-oss-1 # 存储平台标识
        enable-storage: true  # 启用存储
        access-key: LTAI4Fkk66e1yEYkeG8dykyp
        secret-key: ******************************
        end-point: http://oss-cn-hangzhou.aliyuncs.com
        bucket-name: waiqin365-test
        domain: https://image-test.waiqin365.com/ # 访问域名，注意“/”结尾，例如：https://abc.oss-cn-shanghai.aliyuncs.com/
        base-path: ai-agent/ # 基础路径
    local-plus:
      - platform: local-plus-1 # 存储平台标识
        enable-storage: true  #启用存储
        enable-access: true #启用访问（线上请使用 Nginx 配置，效率更高）
        domain: http://127.0.0.1:8700/file/ # 访问域名，访问域名，例如：“http://127.0.0.1:8030/file/”，注意后面要和 path-patterns 保持一致，“/”结尾，本地存储建议使用相对路径，方便后期更换域名
        base-path: ai-agent/ # 基础路径
        path-patterns: /file/** # 访问路径
        storage-path: /APP_DEPLOY/UploadFiles/ # 存储路径
file:
  rootPath: /APP_DEPLOY
  dns:
    url: https://image-test.waiqin365.com




