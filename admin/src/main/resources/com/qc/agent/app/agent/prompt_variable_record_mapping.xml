<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qc.agent.app.agent.mapper.PromptVariableRecordMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.qc.agent.app.agent.model.entity.PromptVariableRecord">
        <id column="id" property="id"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="config_id" property="configId"/>
        <result column="dimension_code" property="dimensionCode"/>
        <result column="prompt_type" property="promptType"/>
        <result column="prompt_fragment_id" property="promptFragmentId"/>
        <result column="variable_key" property="variableKey"/>
        <result column="variable_order" property="variableOrder"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, status, create_time, modify_time, config_id, dimension_code, prompt_type,
        prompt_fragment_id, variable_key, variable_order
    </sql>

    <!-- 根据配置ID查询变量记录列表 -->
    <select id="selectByConfigId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM qc_ai_prompt_variable_record
        WHERE config_id = #{configId}
        AND status = '1'
        ORDER BY prompt_fragment_id, variable_order
    </select>

    <!-- 根据配置ID、维度代码、提示词类型查询变量记录列表 -->
    <select id="selectByConfigIdAndDimensionCodeAndPromptType" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM qc_ai_prompt_variable_record
        WHERE config_id = #{configId}
        AND dimension_code = #{dimensionCode}
        AND prompt_type = #{promptType}
        AND status = '1'
        ORDER BY prompt_fragment_id, variable_order
    </select>

    <!-- 根据提示词片段ID查询变量记录列表 -->
    <select id="selectByPromptFragmentId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM qc_ai_prompt_variable_record
        WHERE prompt_fragment_id = #{promptFragmentId}
        AND status = '1'
        ORDER BY variable_order
    </select>

    <!-- 插入变量记录 -->
    <insert id="insert" parameterType="com.qc.agent.app.agent.model.entity.PromptVariableRecord">
        INSERT INTO qc_ai_prompt_variable_record (
            status, create_time, config_id, dimension_code, prompt_type,
            prompt_fragment_id, variable_key, variable_order
        )
        VALUES (
            #{status}, #{createTime}, #{configId}, #{dimensionCode}, #{promptType},
            #{promptFragmentId}, #{variableKey}, #{variableOrder}
        )
    </insert>

    <!-- 批量插入变量记录 -->
    <insert id="batchInsert">
        INSERT INTO qc_ai_prompt_variable_record (
            status, create_time, config_id, dimension_code, prompt_type,
            prompt_fragment_id, variable_key, variable_order
        )
        VALUES
        <foreach collection="promptVariableRecords" item="item" separator=",">
            (
                #{item.status}, #{item.createTime}, #{item.configId}, #{item.dimensionCode}, #{item.promptType},
                #{item.promptFragmentId}, #{item.variableKey}, #{item.variableOrder}
            )
        </foreach>
    </insert>

    <!-- 根据ID更新变量记录 -->
    <update id="updateById" parameterType="com.qc.agent.app.agent.model.entity.PromptVariableRecord">
        UPDATE qc_ai_prompt_variable_record
        SET status = #{status},
            modify_time = #{modifyTime},
            config_id = #{configId},
            dimension_code = #{dimensionCode},
            prompt_type = #{promptType},
            prompt_fragment_id = #{promptFragmentId},
            variable_key = #{variableKey},
            variable_order = #{variableOrder}
        WHERE id = #{id}
    </update>

    <!-- 根据配置ID、维度代码、提示词类型删除变量记录 -->
    <delete id="deleteByConfigId">
        UPDATE qc_ai_prompt_variable_record
        SET status = '0', modify_time = CURRENT_TIMESTAMP
        WHERE config_id = #{configId} and status = '1'
    </delete>

    <!-- 根据提示词片段ID删除变量记录 -->
    <delete id="deleteByPromptFragmentId">
        UPDATE qc_ai_prompt_variable_record
        SET status = '0', modify_time = CURRENT_TIMESTAMP
        WHERE prompt_fragment_id = #{promptFragmentId}
    </delete>

    <!-- 根据配置ID列表批量删除变量记录 -->
    <delete id="deleteByConfigIds">
        UPDATE qc_ai_prompt_variable_record
        SET status = '0', modify_time = CURRENT_TIMESTAMP
        WHERE config_id IN
        <foreach collection="configIds" item="configId" open="(" separator="," close=")">
            #{configId}
        </foreach>
    </delete>

</mapper>
