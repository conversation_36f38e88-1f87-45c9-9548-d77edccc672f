<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.qc.agent.app.agent.mapper.QcAiAgentAuthorityDistributeDetailMapper">

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO qc_ai_agent_authority_distribute_detail (
        id, status, creator_id, create_time, modifier_id, modify_time, agent_id, dept_id,dept_name,
        user_id,user_name
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id}, #{item.status}, #{item.creatorId}, #{item.createTime},
            #{item.modifierId}, #{item.modifyTime}, #{item.agentId}, #{item.deptId},#{item.deptName},
            #{item.userId},#{item.userName}
            )
        </foreach>
    </insert>

    <delete id="deleteByAgentId" parameterType="java.lang.Long">
        delete
        from qc_ai_agent_authority_distribute_detail
        where agent_id = #{id}
    </delete>
    <select id="isUserAuthorizedForAgent" resultType="java.lang.Long">
        select *
        FROM qc_ai_agent qca
        WHERE qca.status = '1' and qca.id = #{agentId} and
        (exists (SELECT 1 FROM qc_ai_agent_authority_distribute_detail qcaad
        where qcaad.user_id = #{userId} and qcaad.agent_id = qca.id) or
        exists ( SELECT 1 FROM qc_ai_agent_authority_distribute_detail qcaad
        where qcaad.agent_id = qca.id
        <if test="deptIdList != null and deptIdList.size() > 0">
            and (qcaad.dept_id in
            <foreach collection="deptIdList" item="deptId" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
            or qcaad.dept_id = -1)
        </if>
        <if test="deptIdList == null or deptIdList.size() == 0">
            and qcaad.dept_id = -1
        </if>
        )
        )
    </select>

</mapper>
