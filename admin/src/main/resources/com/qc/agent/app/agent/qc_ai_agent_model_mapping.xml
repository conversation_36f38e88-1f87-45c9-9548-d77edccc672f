<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.qc.agent.app.agent.mapper.QcAiAgentModelMapper">

    <resultMap id="BaseResultMap" type="com.qc.agent.app.agent.model.po.QcAiAgentModel">
        <id column="id" property="id"/>
        <result column="status" property="status"/>
        <result column="creator_id" property="creatorId"/>
        <result column="create_time" property="createTime"/>
        <result column="modifyier_id" property="modifyierId"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="vendor" property="vendor"/>
        <result column="vendor_name" property="vendorName"/>
        <result column="model" property="model"/>
        <result column="model_name" property="modelName"/>
        <result column="key" property="key"/>
        <result column="secret" property="secret"/>
        <result column="icon" property="icon"/>
        <result column="enable" property="enable"/>
        <result column="api_key_config_url" property="apiKeyConfigUrl"/>
        <result column="api_url" property="apiUrl"/>
        <result column="api_port" property="apiPort"/>
        <result column="mode_endpoint" property="modelEndPoint"/>
        <result column="thinking" property="thinking"/>
        <result column="privacy_deployment" property="privacyDeployment"/>
        <result column="aip_application_id" property="aipApplicationId"/>
        <result column="aip_user_id" property="aipUserId"/>
    </resultMap>

    <resultMap id="BaseResultVOMap" type="com.qc.agent.app.agent.model.vo.QcAiAgentModelVO">
        <id column="id" property="id"/>
        <result column="status" property="status"/>
        <result column="creator_id" property="creatorId"/>
        <result column="create_time" property="createTime"/>
        <result column="modifyier_id" property="modifyierId"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="vendor" property="vendor"/>
        <result column="vendor_name" property="vendorName"/>
        <result column="model" property="model"/>
        <result column="model_name" property="modelName"/>
        <result column="key" property="key"/>
        <result column="secret" property="secret"/>
        <result column="icon" property="icon"/>
        <result column="enable" property="enable"/>
        <result column="api_key_config_url" property="apiKeyConfigUrl"/>
        <result column="api_url" property="apiUrl"/>
        <result column="api_port" property="apiPort"/>
        <result column="privacy_deployment" property="privacyDeployment"/>
        <result column="aip_application_id" property="aipApplicationId"/>
        <result column="aip_user_id" property="aipUserId"/>
        <result column="mode_endpoint" property="endpoint"/>
    </resultMap>

    <insert id="insert" parameterType="com.qc.agent.app.agent.model.po.QcAiAgentModel">
        INSERT INTO qc_ai_agent_model (id, status, creator_id, create_time, modifyier_id, modify_time, vendor, vendor_name,
                                              model, model_name, key, secret, enable, api_key_config_url, icon, api_url, api_port, privacy_deployment)
        VALUES (#{id}, #{status}, #{creatorId}, #{createTime}, #{modifyierId}, #{modifyTime}, #{vendor}, #{vendorName}, #{model}, #{modelName},
                #{key}, #{secret}, #{enable}, #{apiKeyConfigUrl}, #{icon}, #{apiUrl}, #{apiPort}, #{privacyDeployment})
    </insert>
    <insert id="insertOrUpdate">
        INSERT INTO qc_ai_agent_model (id, status, creator_id, create_time, modifyier_id, modify_time, vendor, vendor_name,
                                              model, model_name, key, secret, enable, api_key_config_url, icon, api_url, api_port, privacy_deployment)
        VALUES (#{id}, #{status}, #{creatorId}, #{createTime}, #{modifyierId}, #{modifyTime}, #{vendor}, #{vendorName}, #{model}, #{modelName},
                #{key}, #{secret}, #{enable}, #{apiKeyConfigUrl}, #{icon}, #{apiUrl}, #{apiPort}, #{privacyDeployment})
        ON CONFLICT (id) DO UPDATE SET
            status = #{status},
            modifyier_id = #{creatorId},
            modify_time = #{createTime},
            model = #{model},
            model_name = #{modelName},
            key = #{key},
            secret = #{secret},
            enable = #{enable},
            api_key_config_url = #{apiKeyConfigUrl},
            icon = #{icon},
            api_url = #{apiUrl},
            api_port = #{apiPort},
            privacy_deployment = #{privacyDeployment}
    </insert>

    <delete id="deleteById">
        UPDATE qc_ai_agent_model
        SET status = '0'
        WHERE id = #{id}
    </delete>

    <update id="update" parameterType="com.qc.agent.app.agent.model.po.QcAiAgentModel">
        UPDATE qc_ai_agent_model
        <trim prefix="SET" suffixOverrides=",">
            modify_time = current_timestamp,
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="creatorId != null and creatorId != ''">creator_id = #{creatorId},</if>
            <if test="modifyierId != null and modifyierId != ''">modifyier_id = #{modifyierId},</if>
            <if test="vendor != null and vendor != ''">vendor = #{vendor},</if>
            <if test="model != null and model != ''">model = #{model},</if>
            <if test="modelName != null and modelName != ''">model_name = #{modelName},</if>
            <if test="key != null and key != ''">key = #{key},</if>
            <if test="secret != null and secret != ''">secret = #{secret},</if>
            <if test="enable != null and enable != ''">enable = #{enable},</if>
            <if test="apiKeyConfigUrl != null and apiKeyConfigUrl != ''">api_key_config_url = #{apiKeyConfigUrl},</if>
        </trim>
        WHERE id = #{id}
    </update>

    <update id="remove">
        update qc_ai_agent_model set enable = '0', key = null, secret = null where vendor = #{vendor}
    </update>

    <update id="updateModelCredentials">
        update qc_ai_agent_model set key = #{apiKey}, 
        secret = #{apiSecret},
        aip_application_id = #{aipApplicationId},
        aip_user_id = #{aipUserId},
        mode_endpoint = #{endpoint},
        enable = '1' 
        where vendor = #{vendor}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT *
        FROM qc_ai_agent_model
        WHERE id = #{id}
    </select>

    <select id="selectAll" resultMap="BaseResultVOMap">
        with model as (select * from qc_ai_agent_model where status = '1' and privacy_deployment = '0' and model is not null order by create_time)
        select
            string_agg(model_name, ',') as model_name,
            vendor,
            vendor_name,
            api_key_config_url,
            key,
            secret,
            enable,
            icon,
            aip_application_id,
            aip_user_id,
            mode_endpoint
        from model
        group by vendor,vendor_name, api_key_config_url, key, secret, enable, icon, aip_application_id, aip_user_id, mode_endpoint
    </select>

    <select id="selectEnabledLLM" resultMap="BaseResultMap">
        select id,
               vendor,
               vendor_name,
               model,
               model_name,
               icon,
               enable
        from qc_ai_agent_model
        where status = '1' and model is not null and privacy_deployment = '0'
    </select>

    <select id="selectByVendor" resultMap="BaseResultMap">
        with model as (select * from qc_ai_agent_model where status = '1' and model is not null order by vendor,model desc)
        select
            model,
            vendor,
            vendor_name,
            api_key_config_url,
            key,
            secret,
            enable,
            icon,
            aip_application_id,
            aip_user_id,
            mode_endpoint
        from model
        where vendor = #{vendor}
        limit 1
    </select>
    <select id="selectPrivateModels" resultMap="BaseResultVOMap">
        select * from qc_ai_agent_model where status = '1' and privacy_deployment = '1'
    </select>
    <select id="selectModelsByVendor" resultType="com.qc.agent.app.agent.model.po.QcAiAgentModel">
        select * from qc_ai_agent_model where status = '1' and vendor = #{vendor}
    </select>

    <select id="selectByAgentId" resultMap="BaseResultMap">
        select
            qaam.*
        from qc_ai_agent qaa
        join qc_ai_agent_model qaam on qaa.model_id = qaam.id
        where qaa.id = #{agentId}
    </select>

</mapper>
