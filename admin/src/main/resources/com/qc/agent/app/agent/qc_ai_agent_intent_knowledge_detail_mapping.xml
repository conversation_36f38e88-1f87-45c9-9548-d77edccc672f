<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.qc.agent.app.workflow.inner.mapper.QcAiAgentIntentKnowledgeDetailMapper">
    <resultMap id="BaseResultMap" type="com.qc.agent.app.workflow.inner.pojo.QcAiAgentIntentKnowledgeDetail">
    <id property="id" column="id"/>
    <result property="status" column="status"/>
    <result property="creatorId" column="creator_id"/>
    <result property="creatorName" column="creator_name"/>
    <result property="createTime" column="create_time"/>
    <result property="modifyierId" column="modifyier_id"/>
    <result property="modifyierName" column="modifyier_name"/>
    <result property="modifyTime" column="modify_time"/>
    <result property="agentId" column="agent_id"/>
    <result property="intentId" column="intent_id"/>
    <result property="collectionId" column="collection_id"/>
</resultMap>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT * FROM qc_ai_agent_Intent_knowledge_detail WHERE id = #{id}
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        SELECT * FROM qc_ai_agent_Intent_knowledge_detail
    </select>

    <insert id="insert" parameterType="com.qc.agent.app.workflow.inner.pojo.QcAiAgentIntentKnowledgeDetail">
        INSERT INTO qc_ai_agent_Intent_knowledge_detail
        (id, status, creator_id, creator_name, create_time, modifyier_id, modifyier_name, modify_time,
         agent_id, intent_id, collection_id)
        VALUES
            (#{id}, #{status}, #{creatorId}, #{creatorName}, #{createTime}, #{modifyierId}, #{modifyierName}, #{modifyTime},
             #{agentId}, #{intentId}, #{collectionId})
    </insert>


    <update id="update" parameterType="com.qc.agent.app.workflow.inner.pojo.QcAiAgentIntentKnowledgeDetail">
        UPDATE qc_ai_agent_Intent_knowledge_detail
        SET status = #{status},
            creator_id = #{creatorId},
            creator_name = #{creatorName},
            create_time = #{createTime},
            modifyier_id = #{modifyierId},
            modifyier_name = #{modifyierName},
            modify_time = #{modifyTime},
            agent_id = #{agentId},
            intent_id = #{intentId},
            collection_id = #{collectionId}
        WHERE id = #{id}
    </update>

    <delete id="deleteById">
        DELETE FROM qc_ai_agent_Intent_knowledge_detail WHERE id = #{id}
    </delete>

    <delete id="deleteByAgentId">
        DELETE FROM qc_ai_agent_Intent_knowledge_detail WHERE agent_id = #{agentId} and status = #{status}
    </delete>

    <insert id="batchInsert" >
        INSERT INTO qc_ai_agent_intent_knowledge_detail (
        id,
        status,
        creator_id,
        creator_name,
        create_time,
        modifyier_id,
        modifyier_name,
        modify_time,
        agent_id,
        intent_id,
        collection_id
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id},
            #{item.status},
            #{item.creatorId},
            #{item.creatorName},
            #{item.createTime},
            #{item.modifyierId},
            #{item.modifyierName},
            #{item.modifyTime},
            #{item.agentId},
            #{item.intentId},
            #{item.collectionId}
            )
        </foreach>
    </insert>

</mapper>
