<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.qc.agent.app.question.mapper.QcKnowledgeHistoryQuestionMapper" >
  <resultMap id="BaseResultMap" type="com.qc.agent.app.question.pojo.QcKnowledgeHistoryQuestion" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="question_id" property="questionId" jdbcType="BIGINT" />
    <result column="history_question_id" property="historyQuestionId" jdbcType="BIGINT" />
    <result column="question_title" property="questionTitle" jdbcType="VARCHAR" />
    <result column="question_content" property="questionContent" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="create_user_id" property="createUserId" jdbcType="BIGINT" />
    <result column="question_status" property="questionStatus" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, question_id, history_question_id, question_title, question_content, create_time,
    create_user_id, question_status
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from qc_knowledge_history_question
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from qc_knowledge_history_question
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.qc.agent.app.question.pojo.QcKnowledgeHistoryQuestion" >
    insert into qc_knowledge_history_question (id, question_id, history_question_id,
      question_title, question_content, create_time,
      create_user_id, question_status)
    values (#{id,jdbcType=BIGINT}, #{questionId,jdbcType=BIGINT}, #{historyQuestionId,jdbcType=BIGINT},
      #{questionTitle,jdbcType=VARCHAR}, #{questionContent,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
      #{createUserId,jdbcType=BIGINT}, #{questionStatus,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.qc.agent.app.question.pojo.QcKnowledgeHistoryQuestion" >
    insert into qc_knowledge_history_question
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="questionId != null" >
        question_id,
      </if>
      <if test="historyQuestionId != null" >
        history_question_id,
      </if>
      <if test="questionTitle != null" >
        question_title,
      </if>
      <if test="questionContent != null" >
        question_content,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="createUserId != null" >
        create_user_id,
      </if>
      <if test="questionStatus != null" >
        question_status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="questionId != null" >
        #{questionId,jdbcType=BIGINT},
      </if>
      <if test="historyQuestionId != null" >
        #{historyQuestionId,jdbcType=BIGINT},
      </if>
      <if test="questionTitle != null" >
        #{questionTitle,jdbcType=VARCHAR},
      </if>
      <if test="questionContent != null" >
        #{questionContent,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null" >
        #{createUserId,jdbcType=BIGINT},
      </if>
      <if test="questionStatus != null" >
        #{questionStatus,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.qc.agent.app.question.pojo.QcKnowledgeHistoryQuestion" >
    update qc_knowledge_history_question
    <set >
      <if test="questionId != null" >
        question_id = #{questionId,jdbcType=BIGINT},
      </if>
      <if test="historyQuestionId != null" >
        history_question_id = #{historyQuestionId,jdbcType=BIGINT},
      </if>
      <if test="questionTitle != null" >
        question_title = #{questionTitle,jdbcType=VARCHAR},
      </if>
      <if test="questionContent != null" >
        question_content = #{questionContent,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null" >
        create_user_id = #{createUserId,jdbcType=BIGINT},
      </if>
      <if test="questionStatus != null" >
        question_status = #{questionStatus,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.qc.agent.app.question.pojo.QcKnowledgeHistoryQuestion" >
    update qc_knowledge_history_question
    set question_id = #{questionId,jdbcType=BIGINT},
      history_question_id = #{historyQuestionId,jdbcType=BIGINT},
      question_title = #{questionTitle,jdbcType=VARCHAR},
      question_content = #{questionContent,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_user_id = #{createUserId,jdbcType=BIGINT},
      question_status = #{questionStatus,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <insert id="batchInsert">
    INSERT INTO qc_knowledge_history_question
    (id,
    question_id,
    history_question_id
    )
    VALUES
    <foreach collection="list" item="item" separator=",">
      (
      #{item.id}
      ,#{item.questionId}
      ,#{item.historyQuestionId}
      )
    </foreach>
  </insert>

  <select id="countContext" resultType="java.lang.Long">
    SELECT COUNT(history_question_id)
    FROM qc_knowledge_history_question
    WHERE question_id = #{questionId, jdbcType=BIGINT}
  </select>

  <select id="queryContextQuestionId" resultType="java.lang.Long">
    SELECT history_question_id
    FROM qc_knowledge_history_question
    WHERE question_id = #{questionId, jdbcType=BIGINT}
  </select>
</mapper>
