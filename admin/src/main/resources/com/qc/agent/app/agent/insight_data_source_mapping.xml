<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qc.agent.app.agent.mapper.InsightDataSourceMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.qc.agent.app.agent.model.entity.InsightDataSource">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="source_name" property="sourceName" jdbcType="VARCHAR"/>
        <result column="source_code" property="sourceCode" jdbcType="VARCHAR"/>
        <result column="api_url" property="apiUrl" jdbcType="VARCHAR"/>
        <result column="http_method" property="httpMethod" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="belong_dimension_code" property="belongDimensionCode" jdbcType="VARCHAR"/>
        <result column="belong_dimension_name" property="belongDimensionName" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, status, source_name, source_code, api_url, http_method, description,
        belong_dimension_code, belong_dimension_name,
        creator_id, creator_name, create_time, modifyier_id, modifyier_name, modify_time
    </sql>

    <!-- 根据ID查询 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM qc_ai_data_source
        WHERE id = #{id}
    </select>

    <!-- 查询所有 -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM qc_ai_data_source
        ORDER BY id ASC
    </select>

    <!-- 根据状态查询 -->
    <select id="selectByStatus" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM qc_ai_data_source
        WHERE status = #{status}
        ORDER BY id ASC
    </select>

    <!-- 插入 -->
    <insert id="insert" parameterType="com.qc.agent.app.agent.model.entity.InsightDataSource" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO qc_ai_data_source (
            status, source_name, source_code, api_url, http_method, description,
            belong_dimension_code, belong_dimension_name,
            creator_id, creator_name, create_time, modifyier_id, modifyier_name, modify_time
        ) VALUES (
            #{status}, #{sourceName}, #{sourceCode}, #{apiUrl}, #{httpMethod}, #{description},
            #{belongDimensionCode}, #{belongDimensionName},
            #{creatorId}, #{creatorName}, #{createTime}, #{modifyierId}, #{modifyierName}, #{modifyTime}
        )
    </insert>

    <!-- 更新 -->
    <update id="updateById" parameterType="com.qc.agent.app.agent.model.entity.InsightDataSource">
        UPDATE qc_ai_data_source
        SET status = #{status},
            source_name = #{sourceName},
            source_code = #{sourceCode},
            api_url = #{apiUrl},
            http_method = #{httpMethod},
            description = #{description},
            belong_dimension_code = #{belongDimensionCode},
            belong_dimension_name = #{belongDimensionName},
            modifyier_id = #{modifyierId},
            modifyier_name = #{modifyierName},
            modify_time = #{modifyTime}
        WHERE id = #{id}
    </update>

    <!-- 根据ID删除 -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM qc_ai_data_source WHERE id = #{id}
    </delete>

    <!-- 根据维度编码查询数据源 -->
    <select id="selectByBelongDimensionCode" resultMap="BaseResultMap">
        SELECT * FROM qc_ai_data_source WHERE belong_dimension_code = #{belongDimensionCode} AND status = '1'
        ORDER BY id ASC
    </select>

    <!-- 根据数据源编码查询数据源 -->
    <select id="selectBySourceCode" resultMap="BaseResultMap">
        SELECT * FROM qc_ai_data_source WHERE source_code = #{sourceCode} AND status = '1'
        ORDER BY id ASC
    </select>

</mapper> 