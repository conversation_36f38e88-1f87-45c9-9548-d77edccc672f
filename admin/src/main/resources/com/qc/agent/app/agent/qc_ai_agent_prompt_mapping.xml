<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.qc.agent.app.agent.mapper.QcAiAgentPromptMapper">

    <insert id="insert" parameterType="com.qc.agent.app.agent.model.po.QcAiAgentPrompt">
        INSERT INTO public.qc_ai_agent_prompt
        (id, status, creator_id, creator_name, create_time, modifyier_id, modifyier_name, modify_time, name, describe,
         content)
        VALUES (#{id}, #{status}, #{creatorId}, #{creatorName}, #{createTime}, #{modifyierId}, #{modifyierName},
                #{modifyTime}, #{name}, #{describe}, #{content})
    </insert>

    <update id="deleteById">
        UPDATE public.qc_ai_agent_prompt
        SET status         = '0',
            modifyier_id   = #{userId},
            modifyier_name = #{userName},
            modify_time    = CURRENT_TIMESTAMP
        WHERE id = #{id}
    </update>

    <update id="update" parameterType="com.qc.agent.app.agent.model.po.QcAiAgentPrompt">
        UPDATE public.qc_ai_agent_prompt
        SET modifyier_id   = #{modifyierId},
            modifyier_name = #{modifyierName},
            modify_time    = CURRENT_TIMESTAMP,
            name           = #{name},
            describe       = #{describe},
            content        = #{content}
        WHERE id = #{id}
    </update>

    <select id="selectById" parameterType="long" resultType="com.qc.agent.app.agent.model.po.QcAiAgentPrompt">
        SELECT *
        FROM public.qc_ai_agent_prompt
        WHERE id = #{id}
    </select>

    <select id="selectAll" resultType="com.qc.agent.app.agent.model.po.QcAiAgentPrompt">
        SELECT
            id,
            status,
            creator_id AS "creatorId",
            creator_name AS "creatorName",
            create_time AS "createTime",
            modifyier_id AS "modifyierId",
            modifyier_name AS "modifyierName",
            modify_time AS "modifyTime",
            name,
            describe,
            content,
            recommend_flag AS "recommendFlag",
            logo
        FROM public.qc_ai_agent_prompt
        WHERE status = '1'
        ORDER BY create_time DESC
    </select>

    <select id="queryPrompts" resultType="com.qc.agent.app.agent.model.po.QcAiAgentPrompt">
        SELECT
            id,
            status,
            creator_id AS "creatorId",
            creator_name AS "creatorName",
            create_time AS "createTime",
            modifyier_id AS "modifyierId",
            modifyier_name AS "modifyierName",
            modify_time AS "modifyTime",
            name,
            describe,
            content,
            recommend_flag AS "recommendFlag",
            logo
        FROM public.qc_ai_agent_prompt
        WHERE status = '1'
        <if test="recommend == '1'.toString()">
            AND recommend_flag = '1'
        </if>
        <if test="mine == '1'.toString()">
            AND creator_id = #{userId}
        </if>
        ORDER BY create_time DESC
    </select>
    <select id="searchPrompts" resultType="com.qc.agent.app.agent.model.po.QcAiAgentPrompt">
        select
            id,
            status,
            creator_id AS "creatorId",
            creator_name AS "creatorName",
            create_time AS "createTime",
            modifyier_id AS "modifyierId",
            modifyier_name AS "modifyierName",
            modify_time AS "modifyTime",
            name,
            describe,
            content,
            recommend_flag AS "recommendFlag",
            logo
        from public.qc_ai_agent_prompt
        where status = '1'
        <if test="filter != null and filter != ''">
            and (name like '%'||#{filter}||'%' or describe like '%'||#{filter}||'%' or content like '%'||#{filter}||'%')
        </if>
        <choose>
            <when test="recommendFlag == '1'.toString()">
                and recommend_flag = '1'
            </when>
            <otherwise>
                and creator_id = #{userId}
            </otherwise>
        </choose>
        order by create_time desc
    </select>

    <select id="countByName" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM qc_ai_agent_prompt
        WHERE status = '1'
          AND name = #{promptName}
          <if test="promptId != null">
              AND id != #{promptId}
          </if>
    </select>

</mapper>
