<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.qc.agent.app.workflow.inner.mapper.QcAiAgentConversationQuoteMapper">

    <insert id="batchInsert">
        INSERT INTO qc_ai_agent_conversation_quote (
        id, status, creator_id, create_time, modifier_id, modify_time,
        doc_name, knowledge_name, score, content,conversation_id
        ) VALUES
        <foreach collection="list" item="record" separator=",">
            (
            #{record.id},
            #{record.status},
            #{record.creatorId},
            #{record.createTime},
            #{record.modifierId},
            #{record.modifyTime},
            #{record.docName},
            #{record.knowledgeName},
            #{record.score},
            #{record.content},
             #{record.conversationId}
            )
        </foreach>
    </insert>


    <resultMap id="qcAiAgentConversationQuoteResultMap" type="com.qc.agent.app.agent.model.po.QcAiAgentConversationQuote">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="creator_id" property="creatorId" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modifier_id" property="modifierId" jdbcType="BIGINT"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
        <result column="doc_name" property="docName" jdbcType="VARCHAR"/>
        <result column="knowledge_name" property="knowledgeName" jdbcType="VARCHAR"/>
        <result column="score" property="score" jdbcType="DECIMAL"/>
        <result column="content" property="content" jdbcType="VARCHAR"/>
        <result column="conversation_id" property="conversationId" jdbcType="BIGINT"/>
    </resultMap>

    <select id="selectListByConverstaionId" resultMap="qcAiAgentConversationQuoteResultMap">
        select id, status, creator_id, create_time, modifier_id, modify_time,
               doc_name, knowledge_name, score, content,conversation_id
        from qc_ai_agent_conversation_quote
        where conversation_id = #{conversationId}
    </select>

</mapper>
