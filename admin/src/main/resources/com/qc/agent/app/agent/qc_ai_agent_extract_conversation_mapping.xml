<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.qc.agent.app.job.extract_conversation.mapper.QcAiAgentExtractConversationMapper">

    <resultMap id="ConversationBaseResultMap" type="com.qc.agent.app.job.extract_conversation.pojo.QcAiAgentConversationDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="status" property="status" jdbcType="CHAR"/>
        <result column="creator_id" property="creatorId" jdbcType="BIGINT"/>
        <result column="creator_name" property="creatorName" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="answer_time" property="answerTime" jdbcType="TIMESTAMP"/>
        <result column="session_id" property="sessionId" jdbcType="BIGINT"/>
        <result column="model_id" property="modelId" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="agent_id" property="agentId" jdbcType="BIGINT"/>
        <result column="agent_name" property="agentName" jdbcType="VARCHAR"/>
        <result column="question" property="question" jdbcType="VARCHAR"/>
        <result column="question_token" property="questionToken" jdbcType="NUMERIC"/>
        <result column="answer" property="answer" jdbcType="VARCHAR"/>
        <result column="answer_token" property="answerToken" jdbcType="NUMERIC"/>
        <result column="conversation_time" property="conversationTime" jdbcType="NUMERIC"/>
        <result column="conversation_status" property="conversationStatus" jdbcType="VARCHAR"/>
        <result column="source" property="source" jdbcType="VARCHAR"/>
        <result column="agent_type" property="agentType" jdbcType="VARCHAR"/>
        <result column="tenant_id" property="tenantId" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="selectList" resultMap="ConversationBaseResultMap">
        select qaac.*, qaa.internal_flag as agent_type,
               #{tenantId} as tenant_id,qaa.name as agent_name
        from qc_ai_agent_conversation qaac
        left join qc_ai_agent qaa on qaa.id = qaac.agent_id
        where to_char(qaac.create_time,'yyyy-MM-dd') = #{currentDate} and qaac.status = '1'
    </select>

    <select id="selectMasterTenantIdList" resultType="java.lang.Long">
        select id from sys_db_datasource limit #{rows} offset #{offset}
    </select>

</mapper>
