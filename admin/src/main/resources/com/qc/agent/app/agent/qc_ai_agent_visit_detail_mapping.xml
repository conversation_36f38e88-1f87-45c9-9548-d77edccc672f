<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.qc.agent.app.agent.mapper.QcAiAgentVisitDetailMapper">
    <resultMap id="BaseResultMap" type="com.qc.agent.app.agent.model.po.QcAiAgentVisitDetail">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="status" property="status" jdbcType="CHAR"/>
        <result column="creator_id" property="creatorId" jdbcType="BIGINT"/>
        <result column="creator_name" property="creatorName" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modifyier_id" property="modifyierId" jdbcType="BIGINT"/>
        <result column="modifyier_name" property="modifyierName" jdbcType="VARCHAR"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
        <result column="agent_id" property="agentId" jdbcType="BIGINT"/>
        <result column="retrieve_radius" property="retrieveRadius" jdbcType="DECIMAL"/>
        <result column="search_scope_days" property="searchScopeDays" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, status, creator_id, creator_name, create_time,
        modifyier_id, modifyier_name, modify_time, agent_id,
        retrieve_radius, search_scope_days
    </sql>


    <update id="update" parameterType="com.qc.agent.app.agent.model.po.QcAiAgentVisitDetail">
        UPDATE public.qc_ai_agent_visit_detail
        <set>
            <if test="retrieveRadius != null">
                retrieve_radius = #{retrieveRadius,jdbcType=DECIMAL},
            </if>
            <if test="searchScopeDays != null">
                search_scope_days = #{searchScopeDays,jdbcType=DECIMAL},
            </if>
        </set>
        WHERE user_id = #{userId,jdbcType=BIGINT} and agent_id = #{agentId,jdbcType=BIGINT}
    </update>

    <select id="selectByAgentId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM public.qc_ai_agent_visit_detail
        WHERE agent_id = #{agentId,jdbcType=BIGINT} and status = '1' and user_id = #{userId,jdbcType=BIGINT}
    </select>

    <insert id="insert" parameterType="com.qc.agent.app.agent.model.po.QcAiAgentVisitDetail">
        INSERT INTO qc_ai_agent_visit_detail (
            id,
            status,
            creator_id,
            creator_name,
            create_time,
            modifyier_id,
            modifyier_name,
            modify_time,
            agent_id,
            user_id,
            retrieve_radius,
            search_scope_days
        ) VALUES (
                     #{id,jdbcType=BIGINT},
                     #{status,jdbcType=CHAR},
                     #{creatorId,jdbcType=BIGINT},
                     #{creatorName,jdbcType=VARCHAR},
                     #{createTime,jdbcType=TIMESTAMP},
                     #{modifyierId,jdbcType=BIGINT},
                     #{modifyierName,jdbcType=VARCHAR},
                     #{modifyTime,jdbcType=TIMESTAMP},
                     #{agentId,jdbcType=BIGINT},
                     #{userId,jdbcType=BIGINT},
                     #{retrieveRadius,jdbcType=DECIMAL},
                     #{searchScopeDays,jdbcType=DECIMAL}
                 )
    </insert>
</mapper>
