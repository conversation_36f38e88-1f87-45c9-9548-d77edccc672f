<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.qc.agent.app.workflow.inner.mapper.QcAiAgentIntentRecognitionMapper">

    <resultMap id="BaseResultMap" type="com.qc.agent.app.workflow.inner.pojo.QcAiAgentIntentRecognition">
        <id property="id" column="id"/>
        <result property="status" column="status"/>
        <result property="creatorId" column="creator_id"/>
        <result property="creatorName" column="creator_name"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyierId" column="modifyier_id"/>
        <result property="modifyierName" column="modifyier_name"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="agentId" column="agent_id"/>
        <result property="intentName" column="intent_name"/>
        <result property="intentCode" column="intent_code"/>
        <result property="maxRecallCount" column="max_recall_count"/>
        <result property="minMatchThreshold" column="min_match_threshold"/>
        <result property="qaMinMatchThreshold" column="qa_min_match_threshold"/>
        <result property="searchScope" column="search_scope"/>
    </resultMap>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT * FROM qc_ai_agent_intent_recognition WHERE id = #{id} and status = '1'
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        SELECT * FROM qc_ai_agent_intent_recognition where status = '1'
    </select>


    <insert id="insert" parameterType="com.qc.agent.app.workflow.inner.pojo.QcAiAgentIntentRecognition">
        INSERT INTO qc_ai_agent_intent_recognition
        (id, status, creator_id, creator_name, create_time, modifyier_id, modifyier_name, modify_time,
         agent_id, intent_name, intent_code, max_recall_count, min_match_threshold,
         qa_min_match_threshold, search_scope)
        VALUES
            (#{id}, #{status}, #{creatorId}, #{creatorName}, #{createTime}, #{modifyierId}, #{modifyierName}, #{modifyTime},
             #{agentId}, #{intentName}, #{intentCode}, #{maxRecallCount}, #{minMatchThreshold},
             #{qaMinMatchThreshold}, #{searchScope})
    </insert>


    <update id="update" parameterType="com.qc.agent.app.workflow.inner.pojo.QcAiAgentIntentRecognition">
        UPDATE qc_ai_agent_intent_recognition
        SET status = #{status},
            creator_id = #{creatorId},
            creator_name = #{creatorName},
            create_time = #{createTime},
            modifyier_id = #{modifyierId},
            modifyier_name = #{modifyierName},
            modify_time = #{modifyTime},
            agent_id = #{agentId},
            intent_name = #{intentName},
            intent_code = #{intentCode},
            max_recall_count = #{maxRecallCount},
            min_match_threshold = #{minMatchThreshold},
            qa_min_match_threshold = #{qaMinMatchThreshold},
            search_scope = #{searchScope}
        WHERE id = #{id}
    </update>

    <delete id="deleteById">
        DELETE FROM qc_ai_agent_intent_recognition WHERE id = #{id}
    </delete>
    <delete id="deleteByAgentId">
        DELETE FROM qc_ai_agent_intent_recognition WHERE agent_id = #{agentId} and status = #{status}
    </delete>


    <resultMap id="QcAiKnowledgeFileMap" type="com.qc.agent.app.workflow.inner.pojo.QcAiKnowledgeFile">
        <id property="categoryId" column="category_id"/>
        <id property="collectionId" column="collection_id"/>
        <result property="docName" column="origin_name"/>
    </resultMap>

    <resultMap id="QcAiKnowledgeRelatedMap" type="com.qc.agent.app.workflow.inner.pojo.QcAiKnowledgeRelated">
        <id property="collectionId" column="collection_id"/>
        <id property="dataImpType" column="data_imp_type"/>
        <result property="name" column="knowledge_name"/>
        <collection property="relateDocList" ofType="com.qc.agent.app.workflow.inner.pojo.QcAiKnowledgeFile" resultMap="QcAiKnowledgeFileMap"/>
    </resultMap>

    <resultMap id="QcAiFileRelatedMap" type="com.qc.agent.app.workflow.inner.pojo.QcUploadFile">
        <id property="id" column="file_id"/>
        <result property="name" column="file_name"/>
        <result property="size" column="size"/>
        <result property="url" column="url"/>
        <result property="platform" column="platform"/>
        <result property="basePath" column="base_path"/>
        <result property="path" column="path"/>
        <result property="ext" column="ext"/>
    </resultMap>

    <resultMap id="QcAiKnowledgeRelatedResultMap" type="com.qc.agent.app.workflow.inner.pojo.QcAiAgentIntentRecognition">
        <id property="id" column="id"/>
        <result property="agentId" column="agent_id"/>
        <result property="intentName" column="intent_name"/>
        <result property="intentCode" column="intent_code"/>
        <result property="maxRecallCount" column="max_recall_count"/>
        <result property="qaMinMatchThreshold" column="qa_min_match_threshold"/>
        <result property="minMatchThreshold" column="min_match_threshold"/>
        <result property="searchScope" column="search_scope"/>
        <collection property="relateKnowledgeList" ofType="com.qc.agent.app.workflow.inner.pojo.QcAiKnowledgeRelated" resultMap="QcAiKnowledgeRelatedMap"/>
        <collection property="relateFileList" ofType="com.qc.agent.app.workflow.inner.pojo.QcAiKnowledgeRelated" resultMap="QcAiFileRelatedMap"/>
    </resultMap>


    <select id="selectRelatedKnowledgeAndCategoryList" resultMap="QcAiKnowledgeRelatedResultMap">
       select
           qaair.id,
           qaair.agent_id,
           qaair.intent_name,
           qaair.intent_code,
           qaair.max_recall_count,
           qaair.min_match_threshold,
           qaair.qa_min_match_threshold,
           qaair.search_scope,
           qaair.type,

           qaikd.collection_id,
           qki.knowledge_name,
           qkf.data_imp_type,

           qaikd.id as relation_id,
           qaikcm.category_id,
           qkf.origin_name,

           quf.id as file_id,
           quf.name as file_name,
           quf.size,
           quf.url,
           quf.platform,
           quf.base_path,
           quf.path,
           quf.ext
       from qc_ai_agent_intent_recognition qaair
       left join qc_ai_agent_intent_knowledge_detail qaikd on qaair.id = qaikd.intent_id and qaikd.agent_id = qaair.agent_id
       left join qc_ai_agent_intent_knowledge_category_mapping qaikcm
           on qaikd.collection_id = qaikcm.collection_id and qaikcm.relation_id = qaikd.id
       left join qc_knowledge_info qki ON qaikd.collection_id = qki.id  and qki.status = '1' AND qki.publish_flag = '1'
       left join qc_knowledge_file qkf ON qaikd.collection_id = qkf.collection_id
            and qkf.category_id = qaikcm.category_id AND qkf.file_status = '1'
       left join qc_ai_agent_intent_file_detail qaifd
       on qaifd.intent_id = qaair.id and qaifd.agent_id = qaair.agent_id and qaair.type = '2'
       left join qc_upload_file quf on qaifd.file_id = quf.id and quf.status = #{status}
       where qaair.intent_code = #{intentCode} and qaair.agent_id = #{agentId}
        and qaair.status = #{status}
        AND (
        qki.id IS NULL
        OR (
        EXISTS (
        SELECT 1 FROM qc_knowledge_info_range r WHERE r.knowledge_id = qki.id AND r.range_type = '1'
        <choose>
            <when test="deptIdList != null and deptIdList.size() > 0">
                AND r.range_id IN
                <foreach collection="deptIdList" item="deptId" open="(" close=")" separator=",">
                    #{deptId}
                </foreach>
            </when>
            <otherwise>
                AND 1=2
            </otherwise>
        </choose>
        )
        OR
        EXISTS (
        SELECT 1 FROM qc_knowledge_info_range r WHERE r.knowledge_id = qki.id AND r.range_type = '2'
        <choose>
            <when test="userId != null">
                AND r.range_id = #{userId}
            </when>
            <otherwise>
                AND 1=2
            </otherwise>
        </choose>
        )
        ))
    </select>


    <insert id="batchInsert">
        INSERT INTO qc_ai_agent_intent_recognition (
        id,
        status,
        creator_id,
        creator_name,
        create_time,
        modifyier_id,
        modifyier_name,
        modify_time,
        agent_id,
        intent_name,
        intent_code,
        max_recall_count,
        min_match_threshold,
        qa_min_match_threshold,
        search_scope,
        type
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id},
            #{item.status},
            #{item.creatorId},
            #{item.creatorName},
            #{item.createTime},
            #{item.modifyierId},
            #{item.modifyierName},
            #{item.modifyTime},
            #{item.agentId},
            #{item.intentName},
            #{item.intentCode},
            #{item.maxRecallCount},
            #{item.minMatchThreshold},
            #{item.qaMinMatchThreshold},
            #{item.searchScope},
            #{item.type}
            )
        </foreach>
    </insert>

</mapper>
