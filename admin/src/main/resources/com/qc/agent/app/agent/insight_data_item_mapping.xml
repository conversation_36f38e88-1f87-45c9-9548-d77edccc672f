<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qc.agent.app.agent.mapper.InsightDataItemMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.qc.agent.app.agent.model.entity.InsightDataItem">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="item_code" property="itemCode" jdbcType="VARCHAR"/>
        <result column="item_name" property="itemName" jdbcType="VARCHAR"/>
        <result column="query_business_code" property="queryBusinessCode" jdbcType="VARCHAR"/>
        <result column="data_type_code" property="dataTypeCode" jdbcType="VARCHAR"/>
        <result column="placeholder_name" property="placeholderName" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="sort_order" property="sortOrder" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, status, item_code, item_name, query_business_code, data_type_code,
        placeholder_name, description, sort_order
    </sql>

    <!-- 根据ID查询 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM qc_ai_data_source_ref_data_item
        WHERE id = #{id}
    </select>

    <!-- 根据ID列表批量查询 -->
    <select id="selectBatchIds" parameterType="java.util.List" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM qc_ai_data_source_ref_data_item
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        ORDER BY sort_order ASC
    </select>

    <!-- 根据查询业务代码查询 -->
    <select id="selectByQueryBusinessCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM qc_ai_data_source_ref_data_item
        WHERE query_business_code = #{queryBusinessCode}
        ORDER BY sort_order ASC
    </select>

    <!-- 查询所有 -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM qc_ai_data_source_ref_data_item
        ORDER BY sort_order ASC
    </select>

    <!-- 插入 -->
    <insert id="insert" parameterType="com.qc.agent.app.agent.model.entity.InsightDataItem" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO qc_ai_data_source_ref_data_item (
            status, item_code, item_name, query_business_code, data_type_code,
            placeholder_name, description, sort_order
        ) VALUES (
            #{status}, #{itemCode}, #{itemName}, #{queryBusinessCode}, #{dataTypeCode},
            #{placeholderName}, #{description}, #{sortOrder}
        )
    </insert>

    <!-- 更新 -->
    <update id="updateById" parameterType="com.qc.agent.app.agent.model.entity.InsightDataItem">
        UPDATE qc_ai_data_source_ref_data_item
        SET status = #{status},
            item_code = #{itemCode},
            item_name = #{itemName},
            query_business_code = #{queryBusinessCode},
            data_type_code = #{dataTypeCode},
            placeholder_name = #{placeholderName},
            description = #{description},
            sort_order = #{sortOrder}
        WHERE id = #{id}
    </update>

    <!-- 根据ID删除 -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM qc_ai_data_source_ref_data_item WHERE id = #{id}
    </delete>

</mapper> 