<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qc.agent.app.agent.mapper.PromptFragmentMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.qc.agent.app.agent.model.entity.PromptFragment">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="status" property="status" jdbcType="CHAR"/>
        <result column="main_prompt_id" property="mainPromptId" jdbcType="BIGINT"/>
        <result column="fragment_key" property="fragmentKey" jdbcType="VARCHAR"/>
        <result column="fragment_name" property="fragmentName" jdbcType="VARCHAR"/>
        <result column="sort_order" property="sortOrder" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, status, main_prompt_id, fragment_key, fragment_name, sort_order
    </sql>

    <!-- 根据ID查询 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM qc_ai_prompt_fragment
        WHERE id = #{id,jdbcType=BIGINT} AND status = '1'
    </select>

    <!-- 根据主提示词ID查询并按排序号排序 -->
    <select id="selectByMainPromptIdOrderBySort" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM qc_ai_prompt_fragment
        WHERE main_prompt_id = #{mainPromptId,jdbcType=BIGINT} AND status = '1'
        ORDER BY sort_order ASC
    </select>

    <!-- 查询所有有效记录 -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM qc_ai_prompt_fragment
        WHERE status = '1'
        ORDER BY main_prompt_id ASC, sort_order ASC
    </select>
    <select id="selectByKey" resultType="com.qc.agent.app.agent.model.entity.PromptFragment">
        SELECT
        <include refid="Base_Column_List"/>
        FROM qc_ai_prompt_fragment
        WHERE fragment_key = #{key,jdbcType=VARCHAR} and main_prompt_id = #{mainId,jdbcType=BIGINT} AND status = '1'
    </select>

    <!-- 插入记录 -->
    <insert id="insert" parameterType="com.qc.agent.app.agent.model.entity.PromptFragment" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO qc_ai_prompt_fragment (
            status, main_prompt_id, fragment_key, fragment_name, sort_order
        ) VALUES (
            #{status,jdbcType=CHAR}, #{mainPromptId,jdbcType=BIGINT}, 
            #{fragmentKey,jdbcType=VARCHAR}, #{fragmentValue,jdbcType=VARCHAR},
            #{sortOrder,jdbcType=INTEGER}
        )
    </insert>

    <!-- 批量插入 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO qc_ai_prompt_fragment (
            status, main_prompt_id, fragment_key, fragment_name, sort_order
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.status,jdbcType=CHAR}, #{item.mainPromptId,jdbcType=BIGINT}, 
                #{item.fragmentKey,jdbcType=VARCHAR}, #{item.fragmentValue,jdbcType=VARCHAR},
                #{item.sortOrder,jdbcType=INTEGER}
            )
        </foreach>
    </insert>

    <!-- 根据ID更新 -->
    <update id="updateById" parameterType="com.qc.agent.app.agent.model.entity.PromptFragment">
        UPDATE qc_ai_prompt_fragment
        SET status = #{status,jdbcType=CHAR},
            main_prompt_id = #{mainPromptId,jdbcType=BIGINT},
            fragment_key = #{fragmentKey,jdbcType=VARCHAR},
            fragment_name = #{fragmentValue,jdbcType=VARCHAR},
            sort_order = #{sortOrder,jdbcType=INTEGER}
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 根据ID删除（逻辑删除） -->
    <update id="deleteById" parameterType="java.lang.Long">
        UPDATE qc_ai_prompt_fragment
        SET status = '0'
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 根据主提示词ID删除（逻辑删除） -->
    <update id="deleteByMainPromptId" parameterType="java.lang.Long">
        UPDATE qc_ai_prompt_fragment
        SET status = '0'
        WHERE main_prompt_id = #{mainPromptId,jdbcType=BIGINT}
    </update>

</mapper> 