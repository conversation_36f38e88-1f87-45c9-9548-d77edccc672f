<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.qc.agent.app.outside_interface.mapper.QcAiAgentExtractDataMapper">

    <select id="selectTenantIds" resultType="java.lang.Long">
        select id from sys_db_datasource
        order by id
        limit #{rows} offset #{offset}
    </select>

    <resultMap id="QcAiAgentConversationResultMap" type="com.qc.agent.app.outside_interface.pojo.QcAiAgentConversationDO">
        <!-- 主键ID -->
        <id property="id" column="id" />

        <!-- 状态，默认值为'1' -->
        <result property="status" column="status" />

        <!-- 创建人ID -->
        <result property="creatorId" column="creator_id" />

        <!-- 创建人姓名 -->
        <result property="creatorName" column="creator_name" />

        <!-- 创建时间 -->
        <result property="createTime" column="create_time" />

        <!-- 回答时间 -->
        <result property="answerTime" column="answer_time" />

        <!-- 对话ID -->
        <result property="sessionId" column="session_id" />

        <!-- 大模型ID -->
        <result property="modelId" column="model_id" />

        <!-- 用户ID -->
        <result property="userId" column="user_id" />

        <!-- 智能体ID -->
        <result property="agentId" column="agent_id" />

        <!-- 智能体名称 -->
        <result property="agentName" column="agent_name" />
        <result property="agentType" column="agent_type" />

        <!-- 问题 -->
        <result property="question" column="question" />

        <!-- 问题Token数 -->
        <result property="questionToken" column="question_token" />

        <!-- 回答 -->
        <result property="answer" column="answer" />

        <!-- 回答Token数 -->
        <result property="answerToken" column="answer_token" />

        <!-- 会话时长，单位：秒 -->
        <result property="conversationTime" column="conversation_time" />

        <!-- 会话状态：-1：对话失败，0：对话中，1：对话成功 -->
        <result property="conversationStatus" column="conversation_status" />

        <!-- 来源：1：web端，2：客户端 -->
        <result property="source" column="source" />
    </resultMap>

    <select id="selectConversationRecordList" resultMap="QcAiAgentConversationResultMap">
        select
            qaac.id,
            qaac.status,
            qaac.creator_id,
            qaac.creator_name,
            qaac.create_time,
            qaac.answer_time,
            qaac.session_id,
            qaac.model_id,
            qaac.user_id,
            qaac.agent_id,
            qaa.name as agent_name,
            qaa.internal_flag as agent_type,
            qaac.question,
            qaac.question_token,
            qaac.answer,
            qaac.answer_token,
            qaac.conversation_time,
            qaac.conversation_status,
            qaac.source
        from qc_ai_agent_conversation qaac
        left join qc_ai_agent qaa on qaa.id = qaac.agent_id
        where qaac.create_time >= #{startDate}::TIMESTAMP
          and qaac.create_time <![CDATA[<]]> #{endDate}::TIMESTAMP + INTERVAL '1 day'
          and qaac.answer is not null
          and qaac.status = '1'
        order by id
        limit #{rows} offset #{offset}
    </select>
</mapper>
