<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.qc.agent.app.workflow.inner.mapper.QcAiAgentIntentFileDetailMapper">

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO qc_ai_agent_intent_file_detail (
        id,
        status,
        creator_id,
        creator_name,
        create_time,
        modifyier_id,
        modifyier_name,
        modify_time,
        agent_id,
        intent_id,
        file_id
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id},
            #{item.status},
            #{item.creatorId},
            #{item.creatorName},
            #{item.createTime},
            #{item.modifyierId},
            #{item.modifyierName},
            #{item.modifyTime},
            #{item.agentId},
            #{item.intentId},
            #{item.fileId}
            )
        </foreach>
    </insert>

    <delete id="deleteByAgentId">
        delete from qc_ai_agent_intent_file_detail where agent_id = #{agentId} and status = #{status}
    </delete>

</mapper>
