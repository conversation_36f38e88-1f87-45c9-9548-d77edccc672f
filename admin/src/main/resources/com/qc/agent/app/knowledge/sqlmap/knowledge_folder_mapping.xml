<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.qc.agent.app.knowledge.mapper.KnowledgeFolderMapper">

    <resultMap type="com.qc.agent.app.knowledge.model.QcKnowledgeFolder" id="QcKnowledgeFolderResult">
        <result property="id" column="id"/>
        <result property="folderName" column="folder_name"/>
        <result property="folderComments" column="folder_comments"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="folderLogoUrl" column="folder_logo_url"/>
        <result property="createUserId" column="create_user_id"/>
        <result property="createUserName" column="create_user_name"/>
        <result property="modifyUserId" column="modify_user_id"/>
        <result property="modifyUserName" column="modify_user_name"/>
        <result property="parentId" column="parent_id"/>
        <result property="sequence" column="sequence"/>
    </resultMap>

    <sql id="selectQcKnowledgeFolderVo">
        select t.id, t.folder_name, t.folder_comments, t.create_time, t.modify_time, t.folder_logo_url, t.create_user_id, t.create_user_name, t.modify_user_id, t.modify_user_name, t.parent_id, t.sequence::varchar
        from qc_knowledge_folder t
    </sql>

    <select id="selectQcKnowledgeFolderById" parameterType="Long"
            resultMap="QcKnowledgeFolderResult">
        <include refid="selectQcKnowledgeFolderVo"/>
        where id = #{id}
    </select>

    <select id="selectDeepListByParentId" parameterType="Long"
            resultMap="QcKnowledgeFolderResult">
        WITH RECURSIVE org_tree AS (
            <include refid="selectQcKnowledgeFolderVo"/>

            WHERE t.id = #{id}
            UNION
            <include refid="selectQcKnowledgeFolderVo"/>
                     JOIN org_tree ot ON t.parent_id = ot.id
        )
        SELECT * FROM org_tree
    </select>

    <select id="selectListByParentId" parameterType="Long"
            resultMap="QcKnowledgeFolderResult">
        <include refid="selectQcKnowledgeFolderVo"/>
        WHERE t.parent_id = #{id}
    </select>



    <insert id="insertQcKnowledgeFolder" parameterType="com.qc.agent.app.knowledge.model.QcKnowledgeFolderParams">
        insert into qc_knowledge_folder
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,
            </if>
            <if test="folderName != null">folder_name,
            </if>
            <if test="folderComments != null">folder_comments,
            </if>
            <if test="createTime != null">create_time,
            </if>
            <if test="modifyTime != null">modify_time,
            </if>
            <if test="folderLogoUrl != null">folder_logo_url,
            </if>
            <if test="createUserId != null">create_user_id,
            </if>
            <if test="createUserName != null">create_user_name,
            </if>
            <if test="modifyUserId != null">modify_user_id,
            </if>
            <if test="modifyUserName != null">modify_user_name,
            </if>
            <if test="parentId != null">parent_id,
            </if>
            <if test="sequence != null">sequence,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},
            </if>
            <if test="folderName != null">#{folderName},
            </if>
            <if test="folderComments != null">#{folderComments},
            </if>
            <if test="createTime != null">#{createTime},
            </if>
            <if test="modifyTime != null">#{modifyTime},
            </if>
            <if test="folderLogoUrl != null">#{folderLogoUrl},
            </if>
            <if test="createUserId != null">#{createUserId},
            </if>
            <if test="createUserName != null">#{createUserName},
            </if>
            <if test="modifyUserId != null">#{modifyUserId},
            </if>
            <if test="modifyUserName != null">#{modifyUserName},
            </if>
            <if test="parentId != null">#{parentId},
            </if>
            <if test="sequence != null">#{sequence}::numeric,
            </if>
        </trim>
    </insert>

    <update id="updateQcKnowledgeFolder" parameterType="com.qc.agent.app.knowledge.model.QcKnowledgeFolderParams">
        update qc_knowledge_folder
        <trim prefix="SET" suffixOverrides=",">
            <if test="folderName != null">folder_name =
                #{folderName},
            </if>
            <if test="folderComments != null">folder_comments =
                #{folderComments},
            </if>
            <if test="createTime != null">create_time =
                #{createTime},
            </if>
            <if test="modifyTime != null">modify_time =
                #{modifyTime},
            </if>
            <if test="folderLogoUrl != null">folder_logo_url =
                #{folderLogoUrl},
            </if>
            <if test="createUserId != null">create_user_id =
                #{createUserId},
            </if>
            <if test="createUserName != null">create_user_name =
                #{createUserName},
            </if>
            <if test="modifyUserId != null">modify_user_id =
                #{modifyUserId},
            </if>
            <if test="modifyUserName != null">modify_user_name =
                #{modifyUserName},
            </if>
            <if test="parentId != null">parent_id =
                #{parentId},
            </if>
            <if test="sequence != null">sequence =
                #{sequence}::numeric,
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteQcKnowledgeFolderById" parameterType="Long">
        delete from qc_knowledge_folder where id = #{id}
    </delete>

    <delete id="deleteQcKnowledgeFolderByIds" parameterType="String">
        delete from qc_knowledge_folder where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}::BIGINT
        </foreach>
    </delete>
</mapper>
