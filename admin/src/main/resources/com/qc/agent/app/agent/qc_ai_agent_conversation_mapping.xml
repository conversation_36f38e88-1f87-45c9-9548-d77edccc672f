<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.qc.agent.app.agent.mapper.QcAiAgentConversationMapper">
    <resultMap id="BaseResultMap" type="com.qc.agent.app.agent.model.po.QcAiAgentConversation">
        <id column="id" property="id"/>
        <result column="status" property="status"/>
        <result column="creator_id" property="creatorId"/>
        <result column="creator_name" property="creatorName"/>
        <result column="create_time" property="createTime"/>
        <result column="session_id" property="sessionId"/>
        <result column="model_id" property="modelId"/>
        <result column="user_id" property="userId"/>
        <result column="agent_id" property="agentId"/>
        <result column="question" property="question"/>
        <result column="question_token" property="questionToken"/>
        <result column="answer" property="answer"/>
        <result column="tokens" property="tokens"/>
        <result column="answer_time" property="answerTime"/>
        <result column="answer_token" property="answerToken"/>
        <result column="conversation_time" property="conversationTime"/>
        <result column="conversation_status" property="conversationStatus"/>
    </resultMap>

    <resultMap id="QcAiAgentConversationMap" type="com.qc.agent.app.agent.model.po.QcAiAgentConversation">
        <id property="id" column="id"/>
        <result property="status" column="status"/>
        <result property="creatorId" column="creator_id"/>
        <result property="creatorName" column="creator_name"/>
        <result property="createTime" column="create_time"/>
        <result property="answerTime" column="answer_time"/>
        <result property="sessionId" column="session_id"/>
        <result property="modelId" column="model_id"/>
        <result property="userId" column="user_id"/>
        <result property="agentId" column="agent_id"/>
        <result property="question" column="question"/>
        <result property="questionToken" column="question_token"/>
        <result property="answer" column="answer"/>
        <result property="answerToken" column="answer_token"/>
        <result property="conversationTime" column="conversation_time"/>
        <result property="conversationStatus" column="conversation_status"/>
    </resultMap>

    <insert id="insert" parameterType="com.qc.agent.app.agent.model.po.QcAiAgentConversation">
        INSERT INTO qc_ai_agent_conversation (id, status, creator_id, creator_name, create_time, session_id, model_id, user_id,
                                              agent_id, conversation_status,
                                              question, question_token, answer,answer_time,
                                              answer_token, conversation_time, source,chat_session_id)
        VALUES (#{id}, #{status}, #{creatorId}, #{creatorName}, #{createTime}, #{sessionId}, #{modelId}, #{userId}, #{agentId},
                #{conversationStatus}, #{question},
                #{questionToken}, #{answer},#{answerTime}, #{answerToken}, #{conversationTime}, #{source},#{chatSessionId})
    </insert>

    <update id="update" parameterType="com.qc.agent.app.agent.model.po.QcAiAgentConversation">
        UPDATE qc_ai_agent_conversation
        SET status         = #{status},
            creator_id     = #{creatorId},
            model_id       = #{modelId},
            user_id        = #{userId},
            agent_id       = #{agentId},
            question       = #{question},
            question_token = #{questionToken},
            answer         = #{answer},
            answer_token   = #{answerToken}
        WHERE id = #{id}
    </update>

    <update id="updateQuestionAndAnswer" parameterType="com.qc.agent.app.agent.model.po.QcAiAgentConversation">
        <if test="(question != null and question != '') or (answer != null and answer != '')">
            UPDATE qc_ai_agent_conversation
            <set>
                <if test="question != null and question != ''">
                    question = #{question},
                </if>
                <if test="answer != null and answer != ''">
                    answer = #{answer},
                </if>
            </set>
            WHERE id = #{id}
        </if>
    </update>

    <select id="queryAgentConversation" resultType="com.qc.agent.app.agent.model.vo.ConversationVO">
        SELECT create_time AS "createTime",
               answer_time AS "answerTime",
               question,
               answer,
               chat_session_id AS "chatSessionId",
               evaluation_type AS "evaluationType",
               feedback,
               view_count AS "viewCount",
               reference_content AS "referenceContent"
        FROM qc_ai_agent_conversation
        WHERE status = #{status}
          AND agent_id = #{agentId}
          AND creator_id = #{userId}
          AND COALESCE(answer, '') != ''
          AND COALESCE(question, '') != ''
          <if test="status != null and status == '1'.toString()">
             AND session_id = (
                 SELECT session_id
                 FROM qc_ai_agent_conversation
                 WHERE status = #{status}
                 AND agent_id = #{agentId}
                 AND creator_id = #{userId}
                 AND COALESCE(answer, '') != ''
                 AND COALESCE(question, '') != ''
                 ORDER BY create_time DESC
                 LIMIT 1
             )
          </if>
        ORDER BY create_time DESC
        <if test="limit != null and offset != null">
            LIMIT ${limit} OFFSET ${offset}
        </if>
    </select>


    <select id="queryAgentHistoryConversation" resultType="com.qc.agent.app.agent.model.vo.ConversationVO">
        SELECT create_time AS "createTime",
        answer_time AS "answerTime",
        question,
        answer,
        chat_session_id AS "chatSessionId",
        evaluation_type AS "evaluationType",
        feedback,
        view_count AS "viewCount",
        reference_content AS "referenceContent",
        id,
        id as "conversationId"
        FROM qc_ai_agent_conversation
        WHERE status = '1'
        AND agent_id = #{agentId}
        AND creator_id = #{userId}
        AND COALESCE(answer, '') != ''
        AND COALESCE(question, '') != ''
        ORDER BY create_time DESC
        <if test="limit != null and offset != null">
            LIMIT ${limit} OFFSET ${offset}
        </if>
    </select>

    <sql id="conversation_where_sql">
        where qaa.id = #{agentId}
        <if test="askTimeStart != null and askTimeEnd != null">
            and qaac.create_time::date &gt;= #{askTimeStart}::date and qaac.create_time::date &lt;= #{askTimeEnd}::date
        </if>
        <if test="evaluationType != null and evaluationType != ''">
            and qaac.evaluation_type in
            <foreach item="type" collection="evaluationType.split(',')" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>
        <if test="tokenTypeList != null and tokenTypeList.size() > 0">
            and qaac.status in
            <foreach item="item" collection="tokenTypeList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="intentIds != null and intentIds.size() > 0">
            and qaair.id in
            <foreach item="id" collection="intentIds" open="(" separator="," close=")">
                #{id}::bigint
            </foreach>
        </if>
        <if test="conversationStatus != null and conversationStatus != ''">
            and qaac.conversation_status = #{conversationStatus}
        </if>
        <if test="filter != null and filter != ''">
            and (qaac.question like concat('%', #{filter}, '%') or
            qaac.answer like concat('%', #{filter}, '%') or
            qaac.creator_name like concat('%', #{filter}, '%'))
        </if>
    </sql>

    <select id="queryHistoryConversations" resultType="com.qc.agent.app.agent.model.vo.ConversationVO">
    with qaa as (
        with quote_count_sql as (
        select qaacq.conversation_id ,
               count(*) as count
            from qc_ai_agent_conversation_quote  qaacq
            left join qc_ai_agent_conversation qaac on qaacq.conversation_id = qaac.id
            left join qc_ai_agent qaa on qaac.agent_id = qaa.id
        left join qc_ai_agent_intent_recognition qaair on qaac.intent_id = qaair.id and qaair.status = '1' and qaair.agent_id = qaa.id
        <include refid="conversation_where_sql"/>
        group by qaacq.conversation_id
        )
        select
        qaac.id,
        qaac.status,
        qaac.create_time as "createTime",
        qaac.creator_id as "creatorId",
        qaac.creator_name as "creatorName",
        qaac.question,
        qaac.answer_time as "answerTime",
        qaac.evaluation_type as "evaluationType",
        qaac.feedback,
        CASE
        WHEN qaa.show_chat_log_content_type = '1' THEN qaac.answer
        WHEN qaa.show_chat_log_content_type = '0' or qaa.show_chat_log_content_type is null THEN
        CASE
        WHEN LENGTH(qaac.answer) &lt;= 4 THEN '********'
        ELSE SUBSTRING(qaac.answer, 1, 4) || '********'
        END
        END AS "answer",
        qaac.question_token as "questionToken",
        qaac.answer_token as "answerToken",
        coalesce(qaac.question_token + qaac.answer_token, 0) as tokens,
        qaac.conversation_time as "conversationTime",
        qaac.conversation_status as "conversationStatus",
        qaair.intent_name as "intentName",
        qcs.count as "quoteCount"
        from qc_ai_agent qaa
        left join qc_ai_agent_conversation qaac on qaa.id = qaac.agent_id
        left join quote_count_sql qcs on qcs.conversation_id = qaac.id
        left join qc_ai_agent_intent_recognition qaair on qaac.intent_id = qaair.id and qaair.status = '1' and qaair.agent_id = qaa.id
        <include refid="conversation_where_sql"/>
        <include refid="filter_where_sql"/>
        )
        select * from qaa
        <if test="column != null and sort != null and column != '' and sort != ''">
            <if test="column == 'tokens'">
                order by tokens ${sort}
            </if>
            <if test="column != 'tokens'">
                order by "${column}" ${sort}
            </if>
        </if>
        <if test="page != null and rows != null">
            LIMIT #{rows} offset (#{page} - 1) * #{rows}
        </if>
    </select>

    <sql id="filter_where_sql">
        <if test="isOutOfScope == '1'.toString()">
            and (qcs.count = 0 or qcs.count is null)
        </if>
        <if test="isOutOfScope == '0'.toString()">
            and qcs.count > 0
        </if>
    </sql>

    <update id="updateStreamChatDone">
        UPDATE qc_ai_agent_conversation
        SET question_token      = #{promptTokens},
            <if test="content != null and content != ''">
                answer              = #{content},
            </if>
            answer_token        = #{completionTokens},
            conversation_time   = #{conversationTime},
            conversation_status = #{conversationStatus},
            answer_time         = #{conversationStart}
        WHERE id = #{conversationId}
    </update>

    <update id="updateReference">
        update qc_ai_agent_conversation
        set view_count = #{size},
            reference_content = #{content}
        where id = #{id}::BIGINT
    </update>

    <update id="updateEvaluation">
        UPDATE qc_ai_agent_conversation
        <set>
            <if test='evaluationType == "0"'>
                <choose>
                    <when test='feedback != null and feedback != "" and preFeedback != null and preFeedback != ""'>
                        feedback = concat(#{preFeedback}, '，', #{feedback}),
                    </when>
                    <when test='feedback != null and feedback != ""'>
                        feedback = #{feedback},
                    </when>
                    <when test='preFeedback != null and preFeedback != ""'>
                        feedback = #{preFeedback},
                    </when>
                    <otherwise>
                        feedback = null,
                    </otherwise>
                </choose>
            </if>
            <if test='evaluationType == "1" or evaluationType == "2"'>
                feedback = null,
            </if>
            evaluation_type = #{evaluationType}
        </set>
        WHERE (chat_session_id = #{chatSessionId} or id = #{chatSessionId}::BIGINT)
    </update>

    <update id="updateAnswerById">
        UPDATE qc_ai_agent_conversation
        set answer = #{answer} ,
            answer_time = now()
        where id = #{conversationId}
    </update>

    <update id="updateIntentId">
        update qc_ai_agent_conversation
        set intent_id = #{intentId}::bigint
        where id = #{conversationId}
    </update>

    <update id="updatePost">
        UPDATE qc_ai_agent_conversation
        SET question_token      = #{promptTokens},
            question     = #{question},
            answer              = #{content},
            answer_token        = #{completionTokens},
            conversation_time   = #{conversationTime},
            conversation_status = #{conversationStatus},
            answer_time         = #{conversationStart}
            WHERE id = #{conversationId}
    </update>
    <update id="updateQuestion">
        UPDATE qc_ai_agent_conversation
        SET question = #{question}
        WHERE id = #{id}::BIGINT
    </update>


    <select id="selectById" resultMap="QcAiAgentConversationMap">
        SELECT *
        FROM qc_ai_agent_conversation
        WHERE id = #{id}
    </select>

    <select id="queryHistoryConversationsCount" resultType="java.lang.Integer">
        with qaa as (
          with quote_count_sql as (
          select qaacq.conversation_id ,
          count(*) as count
          from qc_ai_agent_conversation_quote  qaacq
          left join qc_ai_agent_conversation qaac on qaacq.conversation_id = qaac.id
          left join qc_ai_agent qaa on qaac.agent_id = qaa.id
          left join qc_ai_agent_intent_recognition qaair on qaac.intent_id = qaair.id and qaair.status = '1' and qaair.agent_id = qaa.id
          <include refid="conversation_where_sql"/>
          group by qaacq.conversation_id
          )
        select
        qaac.id,
        qaac.status,
        qaac.create_time as "createTime",
        qaac.creator_id as "creatorId",
        qaac.creator_name as "creatorName",
        qaac.question,
        qaac.answer_time as "answerTime",
        qaac.answer,
        qaac.question_token as "questionToken",
        qaac.answer_token as "answerToken",
        coalesce(qaac.question_token + qaac.answer_token, 0) as tokens,
        qaac.conversation_time as "conversationTime",
        qaac.conversation_status as "conversationStatus"
        from qc_ai_agent qaa
        left join qc_ai_agent_conversation qaac on qaa.id = qaac.agent_id
        left join quote_count_sql qcs on qcs.conversation_id = qaac.id
        left join qc_ai_agent_intent_recognition qaair on qaac.intent_id = qaair.id and qaair.status = '1' and qaair.agent_id = qaa.id
        <include refid="conversation_where_sql"/>
        <include refid="filter_where_sql"/>
        )
        select count(1) from qaa
    </select>


    <select id="getCommonFeedback" resultType="com.qc.agent.app.agent.model.dto.CommonFeedbackDTO">
        SELECT id, feedback_type as feedbackType, feedback_content as feedbackContent
        FROM qc_ai_conversation_common_feedback
        WHERE feedback_type = #{feedbackType}
    </select>

</mapper>
