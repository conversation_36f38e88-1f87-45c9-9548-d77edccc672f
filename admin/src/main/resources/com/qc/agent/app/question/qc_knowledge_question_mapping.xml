<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.qc.agent.app.question.mapper.QcKnowledgeQuestionMapper" >
  <resultMap id="BaseResultMap" type="com.qc.agent.app.question.pojo.QcKnowledgeQuestion" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="folder_id" property="folderId" jdbcType="BIGINT" />
    <result column="question_title" property="questionTitle" jdbcType="VARCHAR" />
    <result column="question_content" property="questionContent" jdbcType="VARCHAR" />
    <result column="answer_content" property="answerContent" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
    <result column="create_user_id" property="createUserId" jdbcType="BIGINT" />
    <result column="create_user_name" property="createUserName" jdbcType="VARCHAR" />
    <result column="modify_user_id" property="modifyUserId" jdbcType="BIGINT" />
    <result column="modify_user_name" property="modifyUserName" jdbcType="VARCHAR" />
    <result column="question_status" property="questionStatus" jdbcType="VARCHAR" />
    <result column="view_count" property="viewCount" jdbcType="NUMERIC" />
    <result column="database_id" property="databaseId" jdbcType="BIGINT" />
    <result column="collection_id" property="collectionId" jdbcType="BIGINT" />
    <result column="session_id" property="sessionId" jdbcType="BIGINT" />
    <result column="reference_content" property="referenceContent" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, folder_id, question_title, question_content, answer_content, create_time, modify_time,
    create_user_id, create_user_name, modify_user_id, modify_user_name, question_status,
    view_count, database_id, collection_id, session_id, reference_content
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from qc_knowledge_question
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectByDatabaseId" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from qc_knowledge_history_question
    where database_id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from qc_knowledge_question
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.qc.agent.app.question.pojo.QcKnowledgeQuestion" >
    insert into qc_knowledge_question (id, folder_id, question_title,
      question_content, answer_content, create_time,
      modify_time, create_user_id, create_user_name,
      modify_user_id, modify_user_name, question_status,
      view_count, database_id, collection_id,
      session_id, reference_content, msg_id)
    values (#{id,jdbcType=BIGINT}, #{folderId,jdbcType=BIGINT}, #{questionTitle,jdbcType=VARCHAR},
      #{questionContent,jdbcType=VARCHAR}, #{answerContent,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
      #{modifyTime,jdbcType=TIMESTAMP}, #{createUserId,jdbcType=BIGINT}, #{createUserName,jdbcType=VARCHAR},
      #{modifyUserId,jdbcType=BIGINT}, #{modifyUserName,jdbcType=VARCHAR}, #{questionStatus,jdbcType=VARCHAR},
      #{viewCount,jdbcType=NUMERIC}, #{databaseId,jdbcType=BIGINT}, #{collectionId,jdbcType=BIGINT},
      #{sessionId,jdbcType=BIGINT}, #{referenceContent,jdbcType=VARCHAR}, #{msgId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.qc.agent.app.question.pojo.QcKnowledgeQuestion" >
    insert into qc_knowledge_question
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="folderId != null" >
        folder_id,
      </if>
      <if test="questionTitle != null" >
        question_title,
      </if>
      <if test="questionContent != null" >
        question_content,
      </if>
      <if test="answerContent != null" >
        answer_content,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="modifyTime != null" >
        modify_time,
      </if>
      <if test="createUserId != null" >
        create_user_id,
      </if>
      <if test="createUserName != null" >
        create_user_name,
      </if>
      <if test="modifyUserId != null" >
        modify_user_id,
      </if>
      <if test="modifyUserName != null" >
        modify_user_name,
      </if>
      <if test="questionStatus != null" >
        question_status,
      </if>
      <if test="viewCount != null" >
        view_count,
      </if>
      <if test="databaseId != null" >
        database_id,
      </if>
      <if test="collectionId != null" >
        collection_id,
      </if>
      <if test="sessionId != null" >
        session_id,
      </if>
      <if test="referenceContent != null" >
        reference_content,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="folderId != null" >
        #{folderId,jdbcType=BIGINT},
      </if>
      <if test="questionTitle != null" >
        #{questionTitle,jdbcType=VARCHAR},
      </if>
      <if test="questionContent != null" >
        #{questionContent,jdbcType=VARCHAR},
      </if>
      <if test="answerContent != null" >
        #{answerContent,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null" >
        #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null" >
        #{createUserId,jdbcType=BIGINT},
      </if>
      <if test="createUserName != null" >
        #{createUserName,jdbcType=VARCHAR},
      </if>
      <if test="modifyUserId != null" >
        #{modifyUserId,jdbcType=BIGINT},
      </if>
      <if test="modifyUserName != null" >
        #{modifyUserName,jdbcType=VARCHAR},
      </if>
      <if test="questionStatus != null" >
        #{questionStatus,jdbcType=VARCHAR},
      </if>
      <if test="viewCount != null" >
        #{viewCount,jdbcType=NUMERIC},
      </if>
      <if test="databaseId != null" >
        #{databaseId,jdbcType=BIGINT},
      </if>
      <if test="collectionId != null" >
        #{collectionId,jdbcType=BIGINT},
      </if>
      <if test="sessionId != null" >
        #{sessionId,jdbcType=BIGINT},
      </if>
      <if test="referenceContent != null" >
        #{referenceContent,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.qc.agent.app.question.pojo.QcKnowledgeQuestion" >
    update qc_knowledge_question
    <set >
      <if test="folderId != null" >
        folder_id = #{folderId,jdbcType=BIGINT},
      </if>
      <if test="questionTitle != null" >
        question_title = #{questionTitle,jdbcType=VARCHAR},
      </if>
      <if test="questionContent != null" >
        question_content = #{questionContent,jdbcType=VARCHAR},
      </if>
      <if test="answerContent != null" >
        answer_content = #{answerContent,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null" >
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null" >
        create_user_id = #{createUserId,jdbcType=BIGINT},
      </if>
      <if test="createUserName != null" >
        create_user_name = #{createUserName,jdbcType=VARCHAR},
      </if>
      <if test="modifyUserId != null" >
        modify_user_id = #{modifyUserId,jdbcType=BIGINT},
      </if>
      <if test="modifyUserName != null" >
        modify_user_name = #{modifyUserName,jdbcType=VARCHAR},
      </if>
      <if test="questionStatus != null" >
        question_status = #{questionStatus,jdbcType=VARCHAR},
      </if>
      <if test="viewCount != null" >
        view_count = #{viewCount,jdbcType=NUMERIC},
      </if>
      <if test="databaseId != null" >
        database_id = #{databaseId,jdbcType=BIGINT},
      </if>
      <if test="collectionId != null" >
        collection_id = #{collectionId,jdbcType=BIGINT},
      </if>
      <if test="sessionId != null" >
        session_id = #{sessionId,jdbcType=BIGINT},
      </if>
      <if test="referenceContent != null" >
        reference_content = #{referenceContent,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.qc.agent.app.question.pojo.QcKnowledgeQuestion" >
    update qc_knowledge_question
    set folder_id = #{folderId,jdbcType=BIGINT},
      question_title = #{questionTitle,jdbcType=VARCHAR},
      question_content = #{questionContent,jdbcType=VARCHAR},
      answer_content = #{answerContent,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      create_user_id = #{createUserId,jdbcType=BIGINT},
      create_user_name = #{createUserName,jdbcType=VARCHAR},
      modify_user_id = #{modifyUserId,jdbcType=BIGINT},
      modify_user_name = #{modifyUserName,jdbcType=VARCHAR},
      question_status = #{questionStatus,jdbcType=VARCHAR},
      view_count = #{viewCount,jdbcType=NUMERIC},
      database_id = #{databaseId,jdbcType=BIGINT},
      collection_id = #{collectionId,jdbcType=BIGINT},
      session_id = #{sessionId,jdbcType=BIGINT},
      reference_content = #{referenceContent,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateAnswer">
    update qc_knowledge_question
    set answer_content = #{answer, jdbcType=VARCHAR},
        answer_status = '1'
    where id = #{questionId, jdbcType=BIGINT}
  </update>

  <update id="updateReference">
    update qc_knowledge_question
    set view_count = #{referenceTotal, jdbcType=NUMERIC},
        reference_content = #{referenceContent, jdbcType=VARCHAR}
    where id = #{questionId, jdbcType=BIGINT}
  </update>

  <select id="queryAnswerHistory" resultType="com.qc.agent.app.question.pojo.QcKnowledgeQuestion">
    with context_info as (select qkhq.question_id,
                                 count(history_question_id) AS context_count
                          from qc_knowledge_history_question qkhq
                                 join qc_knowledge_question qkq on qkhq.question_id = qkq.id
                          where qkq.session_id = #{sessionId, jdbcType=BIGINT}
                          group by qkhq.question_id)
    select qkq.id,
           coalesce(qkq.view_count, 0)       AS "viewCount",
           coalesce(ci.context_count, 0)     AS "contextCount",
           qkq.create_time                   AS "createTime",
           case when qkq.question_status IN ('1', '2') then qkq.question_content else null end AS "questionContent",
           case when qkq.answer_status = '1' then qkq.answer_content else null end AS "answerContent",
           qkq.feedback_type AS "feedbackType",
           qkq.feedback_message AS "feedbackMessage",
           qkq.question_status AS "questionStatus"
    from qc_knowledge_question qkq
           left join context_info ci on qkq.id = ci.question_id
    where qkq.session_id = #{sessionId, jdbcType=BIGINT}
      and qkq.question_content is not null
      and qkq.answer_content is not null
      and (qkq.question_status IN ('1', '2') or qkq.answer_status = '1')
    order by qkq.create_time
  </select>

  <select id="selectAnswerHistory" resultType="com.qc.agent.app.question.pojo.QcKnowledgeQuestion">
    select qkq.id,
           qkq.view_count AS "viewCount",
           qkq.create_time AS "createTime",
           qkq.question_content AS "questionContent",
           qkq.answer_content AS "answerContent"
    from qc_knowledge_question qkq
    where qkq.session_id = #{sessionId, jdbcType=BIGINT}
      and qkq.question_content is not null
      and qkq.answer_content is not null
      and qkq.question_status = '1'
      and qkq.answer_status = '1'
      <if test="id != null">
        and qkq.id != #{id}
      </if>
    order by qkq.create_time
  </select>

  <select id="queryAnswerContext" resultType="com.qc.agent.app.question.pojo.QcKnowledgeQuestion">
    select qkq.id,
           qkq.question_content AS "questionContent",
           qkq.create_time AS "createTime",
           qkq.answer_content AS "answerContent"
    from qc_knowledge_question qkq
    where qkq.id IN (
        SELECT history_question_id
        FROM qc_knowledge_history_question
        WHERE question_id = #{id, jdbcType=BIGINT}
      )
      and qkq.question_status = '1'
      and qkq.question_content is not null
      and qkq.answer_content is not null
    order by qkq.create_time
  </select>

  <update id="deleteQuestion">
    update qc_knowledge_question
    set question_status = '0'
    <if test="id != null">
      where id = #{id, jdbcType=BIGINT}
    </if>
    <if test="msgId != null and msgId != ''">
      where msg_id = #{msgId}
    </if>

  </update>

  <update id="deleteAnswer">
    update qc_knowledge_question
    set answer_status = '0'
    <if test="id != null">
      where id = #{id, jdbcType=BIGINT}
    </if>
    <if test="msgId != null and msgId != ''">
      where msg_id = #{msgId}
    </if>
  </update>

  <update id="updateFeedbackInfo">
    update qc_knowledge_question
    <choose>
      <when test="feedbackType == '-1'.toString()">
        set feedback_type = null,
        feedback_message = null
      </when>
      <otherwise>
        set feedback_type = #{feedbackType, jdbcType=VARCHAR},
        feedback_message = #{feedbackMessage, jdbcType=VARCHAR}
      </otherwise>
    </choose>
    where id = #{id, jdbcType=BIGINT}
  </update>

  <update id="stopQuestion">
    update qc_knowledge_question
    set question_status = '2',
        answer_status = '1',
        answer_content = #{stopMessage, jdbcType=VARCHAR}
    where msg_id = #{msgId, jdbcType=VARCHAR}
  </update>

  <update id="updateAnswerByMsgId">
    update qc_knowledge_question
    set answer_content = #{answer, jdbcType=VARCHAR}
    where msg_id = #{msgId, jdbcType=VARCHAR}
  </update>

  <select id="selectByMsgId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from qc_knowledge_question
    where msg_id = #{msgId,jdbcType=VARCHAR}
    limit 1
  </select>
</mapper>
