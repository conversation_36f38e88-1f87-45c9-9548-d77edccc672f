<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.qc.agent.app.knowledge.mapper.KnowledgeQuestionAnswerSettingMapper">

    <resultMap type="com.qc.agent.app.knowledge.model.KnowledgeQuestionAnswerSettingInfo"
               id="knowledgeQuestionAnswerSettingInfo">
        <result property="id" column="id"/>
        <result property="threshold" column="threshold"/>
        <result property="eachSearchCount" column="each_search_count"/>
        <result property="engineType" column="engine_type"/>
        <result property="engineTypeName" column="engine_type_name"/>
        <result property="promptWord" column="prompt_word"/>
        <result property="defaultAnswer" column="default_answer"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="createUserId" column="create_user_id"/>
        <result property="createUserName" column="create_user_name"/>
        <result property="modifyUserId" column="modify_user_id"/>
        <result property="modifyUserName" column="modify_user_name"/>
        <result property="functionLogo" column="function_logo"/>
        <result property="answerSearchRange" column="answer_search_range"/>
        <result property="prompt" column="prompt"/>
        <result property="errorAnswer" column="error_answer"/>
        <result property="showReference" column="show_reference"/>
        <result property="qaThreshold" column="qa_threshold"/>
    </resultMap>

    <sql id="knowledgeQuestionAnswerSettingInfo">
        select t.*,
               case
                   when t.engine_type = 1 then '腾讯混元模型'
                   when t.engine_type = 2 then '百度文心一言'
                   when t.engine_type = 3 then '阿里通义千问'
                   when t.engine_type = 4 then '智谱AI'
                   when t.engine_type = 5 then '扣子'
                   when t.engine_type = 6 then 'OpenAi'
                   when t.engine_type = 7 then 'Kimi'
                   when t.engine_type = 8 then 'Deepseek'
                   end as engine_type_name
        from qc_knowledge_question_answer_setting t
    </sql>


    <insert id="insert" parameterType="com.qc.agent.app.knowledge.model.QcKnowledgeFileParams">
        insert into qc_knowledge_question_answer_setting
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,
            </if>
            <if test="threshold != null">threshold,
            </if>
            <if test="qaThreshold != null">qa_threshold,
            </if>
            <if test="eachSearchCount != null">each_search_count,
            </if>
            <if test="engineType != null">engine_type,
            </if>
            <if test="promptWord != null">prompt_word,
            </if>
            <if test="defaultAnswer != null">default_answer,
            </if>
            <if test="functionLogo != null">function_logo,
            </if>
            <if test="createUserId != null">create_user_id,
            </if>
            <if test="createUserName != null">create_user_name,
            </if>
            <if test="modifyUserId != null">modify_user_id,
            </if>
            <if test="modifyUserName != null">modify_user_name,
            </if>
            <if test="createTime != null">create_time,
            </if>
            <if test="modifyTime != null">modify_time,
            </if>
            <if test="answerSearchRange != null">answer_search_range,
            </if>
            <if test="prompt != null">prompt,
            </if>
            <if test="prompt != null">prompt,
            </if>
            <if test="errorAnswer != null">error_answer,
            </if>
            <if test="showReference != null">show_reference,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},
            </if>
            <if test="threshold != null">#{threshold}::numeric,
            </if>
            <if test="qaThreshold != null">#{qaThreshold}::numeric,
            </if>
            <if test="eachSearchCount != null">#{eachSearchCount},
            </if>
            <if test="engineType != null">#{engineType},
            </if>
            <if test="promptWord != null">#{promptWord},
            </if>
            <if test="defaultAnswer != null">#{defaultAnswer},
            </if>
            <if test="functionLogo != null">#{functionLogo},
            </if>
            <if test="createUserId != null">#{createUserId},
            </if>
            <if test="createUserName != null">#{createUserName},
            </if>
            <if test="modifyUserId != null">#{modifyUserId},
            </if>
            <if test="modifyUserName != null">#{modifyUserName},
            </if>
            <if test="createTime != null">#{createTime},
            </if>
            <if test="modifyTime != null">#{modifyTime},
            </if>
            <if test="answerSearchRange != null">#{answerSearchRange},
            </if>
            <if test="prompt != null">#{prompt},
            </if>
            <if test="errorAnswer != null">#{errorAnswer},
            </if>
            <if test="showReference != null">#{showReference},
            </if>
        </trim>
    </insert>

    <delete id="deleteById" parameterType="Long">
        delete
        from qc_knowledge_question_answer_setting
        where id = #{id}
    </delete>

    <select id="queryList" resultMap="knowledgeQuestionAnswerSettingInfo">
        <include refid="knowledgeQuestionAnswerSettingInfo"/>
    </select>
    <update id="update" parameterType="com.qc.agent.app.knowledge.model.KnowledgeQuestionAnswerSettingParams">
        update qc_knowledge_question_answer_setting
        <trim prefix="SET" suffixOverrides=",">
            <if test="threshold != null">threshold =
                #{threshold}::numeric,
            </if>
            <if test="qaThreshold != null">qa_threshold =
                #{qaThreshold}::numeric,
            </if>
            <if test="eachSearchCount != null">each_search_count =
                #{eachSearchCount},
            </if>
            <if test="engineType != null">engine_type =
                #{engineType},
            </if>
            <if test="promptWord != null">prompt_word =
                #{promptWord},
            </if>
            <if test="defaultAnswer != null">default_answer =
                #{defaultAnswer},
            </if>
            <if test="functionLogo != null">function_logo =
                #{functionLogo},
            </if>
            <if test="createTime != null">create_time =
                #{createTime},
            </if>
            <if test="modifyTime != null">modify_time =
                #{modifyTime},
            </if>

            <if test="createUserId != null">create_user_id =
                #{createUserId},
            </if>
            <if test="createUserName != null">create_user_name =
                #{createUserName},
            </if>
            <if test="modifyUserId != null">modify_user_id =
                #{modifyUserId},
            </if>
            <if test="modifyUserName != null">modify_user_name =
                #{modifyUserName},
            </if>
            <if test="answerSearchRange != null">answer_search_range =
                #{answerSearchRange},
            </if>
            <if test="prompt != null">prompt =
                #{prompt},
            </if>
            <if test="showReference != null">show_reference =
                #{showReference},
            </if>
            <if test="errorAnswer != null">error_answer =
                #{errorAnswer},
            </if>
        </trim>
        where id = #{id}
    </update>

</mapper>
