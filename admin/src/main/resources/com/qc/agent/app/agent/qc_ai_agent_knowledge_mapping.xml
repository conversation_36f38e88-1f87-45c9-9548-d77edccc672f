<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.qc.agent.app.agent.mapper.QcAiAgentKnowledgeMapper">

    <resultMap id="QcAiAgentKnowledgeResultMap" type="com.qc.agent.app.agent.model.po.QcAiAgentKnowledge">
        <id column="id" property="id"/>
        <result column="status" property="status"/>
        <result column="creator_id" property="creatorId"/>
        <result column="creator_name" property="creatorName"/>
        <result column="create_time" property="createTime"/>
        <result column="agentId" property="agentId"/>
        <result column="knowledge_id" property="knowledgeId"/>
    </resultMap>

    <insert id="insert" parameterType="com.qc.agent.app.agent.model.po.QcAiAgentKnowledge">
        INSERT INTO qc_ai_agent_knowledge (id, status, creator_id, creator_name, create_time, agent_id, knowledge_id)
        VALUES (#{id}, #{status}, #{creatorId}, #{creatorName}, #{createTime}, #{agentId}, #{knowledgeId})
    </insert>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO qc_ai_agent_knowledge (id, status, creator_id, creator_name, create_time, agent_id, knowledge_id)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.status}, #{item.creatorId}, #{item.creatorName}, #{item.createTime}, #{item.agentId},
            #{item.knowledgeId})
        </foreach>
    </insert>

    <update id="update" parameterType="com.qc.agent.app.agent.model.po.QcAiAgentKnowledge">
        UPDATE qc_ai_agent_knowledge
        SET status=#{status},
            creator_id=#{creatorId},
            creator_name=#{creatorName},
            create_time=#{createTime},
            agent_id=#{agentId},
            knowledge_id=#{knowledgeId}
        WHERE id = #{id}
    </update>

    <delete id="deleteByAgentId">
        DELETE
        FROM qc_ai_agent_knowledge
        WHERE agent_id = #{agentId}
    </delete>



    <select id="selectAssociatedKnowledgeList" resultMap="QcAiKnowledgeRelatedMap">
        select
            qaak.knowledge_id,
            qki.knowledge_name,
            qkf.data_imp_type,

            qkf.origin_name,
            qkf.category_id
        from qc_ai_agent_knowledge qaak
        join qc_knowledge_info qki on qki.id = qaak.knowledge_id
        join qc_knowledge_file qkf on qki.id = qkf.collection_id
        WHERE qaak.agent_id = #{agentId}
        and qkf.file_status = '1'
        AND (
        EXISTS (
        SELECT 1 FROM qc_knowledge_info_range r WHERE r.knowledge_id = qki.id AND r.range_type = '1'
        <choose>
            <when test="deptIdList != null and deptIdList.size() > 0">
                AND r.range_id IN
                <foreach collection="deptIdList" item="deptId" open="(" close=")" separator=",">
                    #{deptId}
                </foreach>
            </when>
            <otherwise>
                AND 1=2
            </otherwise>
        </choose>
        )
        OR
        EXISTS (
        SELECT 1 FROM qc_knowledge_info_range r WHERE r.knowledge_id = qki.id AND r.range_type = '2'
        <choose>
            <when test="userId != null">
                AND r.range_id = #{userId}
            </when>
            <otherwise>
                AND 1=2
            </otherwise>
        </choose>
        )
        )
    </select>

    <select id="selectKnowledgeByAgentId" resultType="java.lang.Long">
        SELECT id
        FROM qc_ai_agent_knowledge
        WHERE agent_id = #{id}
    </select>

    <resultMap id="AuthorityResultMap" type="com.qc.agent.app.agent.model.dto.QcAiAgentKnowledgeAuthorityDTO">
        <id column="knowledge_id" property="knowledgeId"/>
        <result column="knowledge_name" property="knowledgeName"/>
        <collection property="deptList" ofType="com.qc.agent.app.agent.model.dto.QcAiAgentDeptDTO">
            <id column="dept_id" property="id"/>
            <result column="dept_name" property="name"/>
        </collection>
        <collection property="userList" ofType="com.qc.agent.app.agent.model.dto.QcAiAgentUserDTO">
            <id column="user_id" property="id"/>
            <result column="user_name" property="name"/>
        </collection>
    </resultMap>

    <select id="selectAssociatedKnowledgeAuthorityList" resultMap="AuthorityResultMap">
        select
            qaak.knowledge_id,
            qki.knowledge_name,
            qkir_dept.range_id as dept_id,
            qkir_dept.range_name as dept_name,
            qkir_user.range_id as user_id,
            qkir_user.range_name as user_name
        from qc_ai_agent_knowledge qaak
        left join qc_knowledge_info qki on qki.id = qaak.knowledge_id
        left join qc_knowledge_info_range qkir_dept on qkir_dept.knowledge_id = qaak.knowledge_id and qkir_dept.range_type = '1'
        left join qc_knowledge_info_range qkir_user on qkir_user.knowledge_id = qaak.knowledge_id and qkir_user.range_type = '2'
        where qaak.agent_id = #{agentId}
    </select>


    <resultMap id="QcAiAgentConversationQuoteMap" type="com.qc.agent.app.agent.model.po.QcAiAgentConversationQuote">
        <id property="id" column="id" />
        <result property="status" column="status" />
        <result property="creatorId" column="creator_id" />
        <result property="createTime" column="create_time" />
        <result property="modifierId" column="modifier_id" />
        <result property="modifyTime" column="modify_time" />
        <result property="docName" column="doc_name" />
        <result property="knowledgeName" column="knowledge_name" />
        <result property="score" column="score" />
        <result property="content" column="content" />
        <result property="conversationId" column="conversation_id" />
    </resultMap>

    <select id="selectQuoteList" resultMap="QcAiAgentConversationQuoteMap">
           select
               id,
               status,
               creator_id,
               create_time,
               modifier_id,
               modify_time,
               doc_name,
               knowledge_name,
               score,
               content,
               conversation_id
           from  qc_ai_agent_conversation_quote
           where conversation_id = #{conversationId}
    </select>


    <resultMap id="QcAiKnowledgeFileMap" type="com.qc.agent.app.workflow.inner.pojo.QcAiKnowledgeFile">
        <id property="collectionId" column="knowledge_id"/>
        <id property="categoryId" column="category_id"/>
        <id property="docName" column="origin_name"/>
    </resultMap>

    <resultMap id="QcAiKnowledgeRelatedMap" type="com.qc.agent.app.workflow.inner.pojo.QcAiKnowledgeRelated">
        <id property="collectionId" column="knowledge_id"/>
        <id property="dataImpType" column="data_imp_type"/>
        <result property="name" column="knowledge_name"/>
        <collection property="relateDocList" ofType="com.qc.agent.app.workflow.inner.pojo.QcAiKnowledgeFile" resultMap="QcAiKnowledgeFileMap"/>
    </resultMap>

    <select id="selectKnowledgeList" resultMap="QcAiKnowledgeRelatedMap">
        select
        qki.id as knowledge_id,
        qki.knowledge_name,
        qkf.data_imp_type,

        qkf.origin_name,
        qkf.category_id
        from
        qc_knowledge_info qki
        join qc_knowledge_file qkf on qki.id = qkf.collection_id
        WHERE qkf.file_status = '1' and qki.id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND (
        EXISTS (
        SELECT 1 FROM qc_knowledge_info_range r WHERE r.knowledge_id = qki.id AND r.range_type = '1'
        <choose>
            <when test="deptIdList != null and deptIdList.size() > 0">
                AND r.range_id IN
                <foreach collection="deptIdList" item="deptId" open="(" close=")" separator=",">
                    #{deptId}
                </foreach>
            </when>
            <otherwise>
                AND 1=2
            </otherwise>
        </choose>
        )
        OR
        EXISTS (
        SELECT 1 FROM qc_knowledge_info_range r WHERE r.knowledge_id = qki.id AND r.range_type = '2'
        <choose>
            <when test="userId != null">
                AND r.range_id = #{userId}
            </when>
            <otherwise>
                AND 1=2
            </otherwise>
        </choose>
        )
        )
    </select>


</mapper>
