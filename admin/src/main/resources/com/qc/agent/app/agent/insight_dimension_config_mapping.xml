<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qc.agent.app.agent.mapper.InsightDimensionConfigMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.qc.agent.app.agent.model.entity.InsightDimensionConfig">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="agent_id" property="agentId" jdbcType="BIGINT"/>
        <result column="dimension_name" property="dimensionName" jdbcType="VARCHAR"/>
        <result column="dimension_code" property="dimensionCode" jdbcType="VARCHAR"/>
        <result column="sort_order" property="sortOrder" jdbcType="INTEGER"/>
        <result column="creator_id" property="creatorId" jdbcType="BIGINT"/>
        <result column="creator_name" property="creatorName" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modifyier_id" property="modifyierId" jdbcType="BIGINT"/>
        <result column="modifyier_name" property="modifyierName" jdbcType="VARCHAR"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
        <result column="insight_mode" property="insightMode" jdbcType="CHAR"/>
        <result column="insight_mode_type" property="insightModeType" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, status, agent_id, dimension_name, dimension_code, sort_order, insight_mode, insight_mode_type,
        creator_id, creator_name, create_time, modifyier_id, modifyier_name, modify_time
    </sql>

    <!-- 根据ID查询 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM qc_ai_dimension_config
        WHERE id = #{id}
    </select>

    <!-- 根据代理ID查询 -->
    <select id="selectByAgentId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM qc_ai_dimension_config
        WHERE agent_id = #{agentId}
        ORDER BY sort_order ASC
    </select>

    <!-- 根据代理ID和洞察模式查询 -->
    <select id="selectByAgentIdAndMode" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM qc_ai_dimension_config
        WHERE agent_id = #{agentId} AND insight_mode = #{insightMode}
        ORDER BY sort_order ASC
    </select>

    <!-- 插入 -->
    <insert id="insert" parameterType="com.qc.agent.app.agent.model.entity.InsightDimensionConfig" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO qc_ai_dimension_config (
            status, agent_id, dimension_name, dimension_code, sort_order, insight_mode, insight_mode_type,
            creator_id, creator_name, create_time, modifyier_id, modifyier_name, modify_time
        ) VALUES (
            #{status}, #{agentId}, #{dimensionName}, #{dimensionCode}, #{sortOrder}, #{insightMode}, #{insightModeType},
            #{creatorId}, #{creatorName}, #{createTime}, #{modifyierId}, #{modifyierName}, #{modifyTime}
        )
    </insert>

    <!-- 更新 -->
    <update id="updateById" parameterType="com.qc.agent.app.agent.model.entity.InsightDimensionConfig">
        UPDATE qc_ai_dimension_config
        <set>
            <if test="agentId != null">agent_id = #{agentId},</if>
            <if test="dimensionName != null">dimension_name = #{dimensionName},</if>
            <if test="dimensionCode != null">dimension_code = #{dimensionCode},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="insightMode != null">insight_mode = #{insightMode},</if>
            <if test="insightModeType != null">insight_mode_type = #{insightModeType},</if>
            <if test="modifyierId != null">modifyier_id = #{modifyierId},</if>
            <if test="modifyierName != null">modifyier_name = #{modifyierName},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据ID删除 -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM qc_ai_dimension_config WHERE id = #{id}
    </delete>

    <!-- 批量删除 -->
    <delete id="deleteBatchIds" parameterType="java.util.List">
        DELETE FROM qc_ai_dimension_config WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper> 