<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qc.agent.app.agent.mapper.DimensionPromptValueMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.qc.agent.app.agent.model.entity.DimensionPromptValue">
        <id column="id" property="id"/>
        <result column="status" property="status"/>
        <result column="config_id" property="configId"/>
        <result column="prompt_fragment_id" property="promptFragmentId"/>
        <result column="fragment_value" property="fragmentValue"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, status, config_id, prompt_fragment_id, fragment_value
    </sql>
    <delete id="deleteByConfigIds">
        DELETE FROM qc_ai_dimension_prompt_value
        WHERE config_id IN
        <foreach item="item" collection="ids" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
    </delete>

    <!-- 根据配置ID查询提示词片段值列表 -->
    <select id="selectByConfigId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM qc_ai_dimension_prompt_value
        WHERE config_id = #{configId}
        AND status = '1'
        ORDER BY id
    </select>

    <!-- 根据配置ID和提示词片段ID查询提示词片段值 -->
    <select id="selectByConfigIdAndPromptFragmentId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM qc_ai_dimension_prompt_value
        WHERE config_id = #{configId}
        AND prompt_fragment_id = #{promptFragmentId}
        AND status = '1'
        LIMIT 1
    </select>

    <!-- 插入提示词片段值 -->
    <insert id="insert" parameterType="com.qc.agent.app.agent.model.entity.DimensionPromptValue">
        INSERT INTO qc_ai_dimension_prompt_value (status, config_id, prompt_fragment_id, fragment_value)
        VALUES (#{status}, #{configId}, #{promptFragmentId}, #{fragmentValue})
    </insert>

    <!-- 批量插入提示词片段值 -->
    <insert id="batchInsert">
        INSERT INTO qc_ai_dimension_prompt_value (status, config_id, prompt_fragment_id, fragment_value)
        VALUES
        <foreach collection="dimensionPromptValues" item="item" separator=",">
            (#{item.status}, #{item.configId}, #{item.promptFragmentId}, #{item.fragmentValue})
        </foreach>
    </insert>

    <!-- 根据ID更新提示词片段值 -->
    <update id="updateById" parameterType="com.qc.agent.app.agent.model.entity.DimensionPromptValue">
        UPDATE qc_ai_dimension_prompt_value
        SET status = #{status},
        config_id = #{configId},
        prompt_fragment_id = #{promptFragmentId},
        fragment_value = #{fragmentValue}
        WHERE id = #{id}
    </update>

    <!-- 根据配置ID删除提示词片段值 -->
    <update id="deleteByConfigId">
        UPDATE qc_ai_dimension_prompt_value
        SET status = '0'
        WHERE config_id = #{configId}
    </update>

    <!-- 根据配置ID和提示词片段ID删除提示词片段值 -->
    <update id="deleteByConfigIdAndPromptFragmentId">
        UPDATE qc_ai_dimension_prompt_value
        SET status = '0'
        WHERE config_id = #{configId}
        AND prompt_fragment_id = #{promptFragmentId}
    </update>

</mapper>