<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.qc.agent.app.agent.mapper.QcAiAgentMapper">

    <resultMap id="qcAiAgentResultMap" type="com.qc.agent.app.agent.model.po.QcAiAgent">
        <id property="id" column="id"/>
        <result property="status" column="status"/>
        <result property="creatorId" column="creator_id"/>
        <result property="creatorName" column="creator_name"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyierId" column="modifyier_id"/>
        <result property="modifyierName" column="modifyier_name"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="modelId" column="model_id"/>
        <result property="sequ" column="sequ"/>
        <result property="name" column="name"/>
        <result property="logo" column="logo"/>
        <result property="description" column="description"/>
        <result property="prompt" column="prompt"/>
        <result property="leadingQuestion" column="leading_question"/>
        <result property="categoryId" column="category_id"/>
        <result property="contextSearchAmount" column="context_search_amount"/>
        <result property="introduction" column="introduction"/>
        <result property="internetSearch" column="internet_search"/>
        <result property="nullResultAnswer" column="null_result_answer"/>
        <result property="errorResultAnswer" column="error_result_answer"/>
        <result property="dataFetchUrl" column="data_fetch_url"/>
        <result property="internalFlag" column="internal_flag"/>
        <result property="publishFlag" column="publish_flag"/>
        <result property="publishTime" column="publish_time"/>
        <result property="modelTemperature" column="model_temperature"/>
        <result property="modelTopP" column="model_top_p"/>
        <result property="modelMaxTokens" column="model_max_tokens"/>
        <result property="maxRecallCount" column="max_recall_count"/>
        <result property="minMatchThreshold" column="min_match_threshold"/>
        <result property="qaMinMatchThreshold" column="qa_min_match_threshold"/>
        <result property="searchScope" column="search_scope"/>
        <result property="splitSqlPrompt" column="split_sql_prompt"/>
        <result property="bizPrompt" column="biz_prompt"/>
        <result property="showChatLogContentType" column="show_chat_log_content_type"/>
        <result property="intentIsEnabled" column="intent_is_enabled"/>
    </resultMap>

    <select id="selectById" parameterType="long" resultMap="qcAiAgentResultMap">
        SELECT *
        FROM public.qc_ai_agent
        WHERE id = #{id}
    </select>

    <insert id="insert" parameterType="com.qc.agent.app.agent.model.po.QcAiAgent">
        INSERT INTO qc_ai_agent (id, status, creator_id, creator_name, create_time, modifyier_id, modifyier_name,
                                 modify_time, model_id, sequ, name, logo, description, prompt, leading_question,
                                 category_id, context_search_amount, introduction, internet_search, null_result_answer,
                                 error_result_answer, data_fetch_url, internal_flag, publish_flag, publish_time,
                                 model_temperature, model_top_p, model_max_tokens,max_recall_count,min_match_threshold,
                                 search_scope,qa_min_match_threshold,split_sql_prompt, show_chat_log_content_type,
                                 intent_is_enabled)
        VALUES (#{id}, #{status}, #{creatorId}, #{creatorName}, #{createTime}, #{modifyierId}, #{modifyierName},
                #{modifyTime}, #{modelId}, #{sequ}, #{name}, #{logo}, #{description}, #{prompt}, #{leadingQuestion},
                #{categoryId}, #{contextSearchAmount}, #{introduction}, #{internetSearch}, #{nullResultAnswer},
                #{errorResultAnswer}, #{dataFetchUrl}, #{internalFlag}, #{publishFlag}, #{publishTime},
                #{modelTemperature}, #{modelTopP}, #{modelMaxTokens},
                #{maxRecallCount},#{minMatchThreshold},#{searchScope},
                #{qaMinMatchThreshold},#{splitSqlPrompt}, #{showChatLogContentType},#{intentIsEnabled}::BIGINT)
    </insert>

    <update id="update" parameterType="com.qc.agent.app.agent.model.po.QcAiAgent">
        UPDATE qc_ai_agent
        <set>
            <if test="modifyierId != null">
                modifyier_id = #{modifyierId},
            </if>
            <if test="modifyierName != null">
                modifyier_name = #{modifyierName},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime},
            </if>
            <if test="modelId != null">
                model_id = #{modelId},
            </if>
            <if test="prompt != null">
                prompt = #{prompt},
            </if>
            <if test="leadingQuestion != null">
                leading_question = #{leadingQuestion},
            </if>
            <if test="contextSearchAmount != null">
                context_search_amount = #{contextSearchAmount},
            </if>
            <if test="introduction != null">
                introduction = #{introduction},
            </if>
            <if test="internetSearch != null">
                internet_search = #{internetSearch},
            </if>
            <if test="nullResultAnswer != null">
                null_result_answer = #{nullResultAnswer},
            </if>
            <if test="errorResultAnswer != null">
                error_result_answer = #{errorResultAnswer},
            </if>
            <if test="modelTemperature != null">
                model_temperature = #{modelTemperature},
            </if>
            <if test="modelTopP != null">
                model_top_p = #{modelTopP},
            </if>
            <if test="modelMaxTokens != null">
                model_max_tokens = #{modelMaxTokens},
            </if>
            <if test="maxRecallCount != null">
                max_recall_count = #{maxRecallCount},
            </if>
            <if test="minMatchThreshold != null">
                min_match_threshold = #{minMatchThreshold},
            </if>
            <if test="qaMinMatchThreshold != null">
                qa_min_match_threshold = #{qaMinMatchThreshold},
            </if>
            <if test="searchScope != null">
                search_scope = #{searchScope},
            </if>
            <if test="splitSqlPrompt != null">
                split_sql_prompt = #{splitSqlPrompt},
            </if>
            <if test="bizPrompt != null">
                biz_prompt = #{bizPrompt},
            </if>
            <if test="showChatLogContentType != null">
                show_chat_log_content_type = #{showChatLogContentType},
            </if>
            <if test="intentIsEnabled != null">
                intent_is_enabled = #{intentIsEnabled}::BIGINT,
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <update id="updateBaseInfo" parameterType="com.qc.agent.app.agent.model.po.QcAiAgent">
        UPDATE qc_ai_agent
        SET modifyier_id=#{modifyierId},
            modifyier_name=#{modifyierName},
            modify_time=#{modifyTime},
            name=#{name},
            logo=#{logo},
            description=#{description}
        WHERE id = #{id}
    </update>

    <select id="queryCustomAgents" resultType="com.qc.agent.app.agent.model.vo.QcAiAgentVO">
        SELECT id AS "agentId",
               creator_id AS "creatorId",
               creator_name AS "creatorName",
               create_time AS "createTime",
               modifyier_id AS "modifyierId",
               modifyier_name AS "modifyierName",
               modify_time AS "modifyTime",
               name AS "agentName",
               internal_flag AS "internalFlag",
               logo AS "agentLogo",
               creator_name AS "creatorName",
               description,
               introduction,
               leading_question AS "leadingQuestion",
               publish_flag AS "publishFlag",
               publish_time AS "publishTime",
               publish_user_id AS "publishUserId",
               publish_user_name AS "publishUserName",
               enable
        FROM qc_ai_agent
        WHERE status = '1'
        <if test="viewAll != true">
            AND creator_id = #{userId}
        </if>
        AND internal_flag = '0'
        <if test="agentName != null and agentName != ''">
            AND agent_name LIKE CONCAT('%',#{agentName},'%')
        </if>
        <if test="publishFlag != null and publishFlag != ''">
            AND publish_flag = #{publishFlag}
        </if>
        ORDER BY CASE WHEN creator_id = #{userId} THEN 0 ELSE 1 END,
                 publish_flag DESC, publish_time DESC,
                 sequ NULLS LAST,
                 COALESCE(modify_time, create_time) DESC
    </select>

    <select id="queryInternalAgents" resultType="com.qc.agent.app.agent.model.vo.QcAiAgentVO">
        SELECT qaa.id AS "agentId",
               qaa.creator_id AS "creatorId",
               qaa.creator_name AS "creatorName",
               qaa.create_time AS "createTime",
               qaa.modifyier_id AS "modifyierId",
               qaa.modifyier_name AS "modifyierName",
               qaa.modify_time AS "modifyTime",
               qaa.name AS "agentName",
               qaa.internal_flag AS "internalFlag",
               qaa.logo AS "agentLogo",
               qaa.creator_name AS "creatorName",
               qaa.description,
               qaa.introduction,
               qaa.leading_question AS "leadingQuestion",
               qaa.publish_flag AS "publishFlag",
               qaa.publish_time AS "publishTime",
               qaa.publish_user_id AS "publishUserId",
               qaa.publish_user_name AS "publishUserName",
               qaa.enable,
               qaa.data_range AS "dataRange",
               qaa.h5_url AS "h5Url"
        FROM qc_ai_agent qaa
        WHERE qaa.status = '1'
        AND qaa.internal_flag = '1'
          <if test="enable != null and enable != ''">
              AND COALESCE(qaa.enable, '1') = '1'
          </if>
          <if test="agentId != null and agentId != ''">
              AND qaa.id = #{agentId}
          </if>
        and
        (exists (SELECT 1 FROM qc_ai_agent_authority_distribute_detail qcaad
        where   qcaad.user_id = #{userId} and qcaad.agent_id = qaa.id)  or
        exists ( SELECT 1 FROM qc_ai_agent_authority_distribute_detail qcaad
        where qcaad.agent_id = qaa.id and ( qcaad.dept_id = -1
        <if test = "deptIdList != null and deptIdList.size() > 0">
            or qcaad.dept_id in
            <foreach collection="deptIdList" item="deptId" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>)
        )
        )
        ORDER BY qaa.enable DESC, qaa.id
    </select>

    <select id="queryRecentAgents" resultType="com.qc.agent.app.agent.model.vo.RecentUseQcAiAgentVO">
        WITH latest_conversation AS (
            SELECT t.*
            FROM (
                SELECT
                    agent_id,
                    create_time,
                    question,
                    ROW_NUMBER() OVER (PARTITION BY agent_id ORDER BY create_time DESC) AS rn
                FROM qc_ai_agent_conversation
                WHERE creator_id = #{userId} and status = '1'
            )t
            WHERE t.rn = 1
        )
        SELECT qaa.id AS "agentId",
               qaa.creator_id AS "creatorId",
               qaa.creator_name AS "creatorName",
               qaa.create_time AS "createTime",
               qaa.modifyier_id AS "modifyierId",
               qaa.modifyier_name AS "modifyierName",
               qaa.modify_time AS "modifyTime",
               qaa.name AS "agentName",
               qaa.internal_flag AS "internalFlag",
               qaa.logo AS "agentLogo",
               qaa.creator_name AS "creatorName",
               qaa.description,
               qaa.introduction,
               qaa.leading_question AS "leadingQuestion",
               qaa.publish_flag AS "publishFlag",
               qaa.publish_time AS "publishTime",
               qaa.publish_user_id AS "publishUserId",
               qaa.publish_user_name AS "publishUserName",
               qaa.enable,
               t.create_time AS "useDate",
               t.question AS "recentQuestion",
               qaa.data_range AS "dataRange"
        FROM qc_ai_agent qaa
        JOIN latest_conversation t ON qaa.id = t.agent_id
        WHERE qaa.status = '1'
        AND COALESCE(qaa.enable, '1') = '1'
        AND COALESCE(qaa.publish_flag, '1') = '1'
        and
        (exists (SELECT 1 FROM qc_ai_agent_authority_distribute_detail qcaad
        where   qcaad.user_id = #{userId} and qcaad.agent_id = qaa.id)  or
        exists ( SELECT 1 FROM qc_ai_agent_authority_distribute_detail qcaad
        where qcaad.agent_id = qaa.id
        <if test = "deptIdList != null and deptIdList.size() > 0">
            and qcaad.dept_id in
            <foreach collection="deptIdList" item="deptId" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        )
        or qaa.internal_flag = '1'
        )
        ORDER BY t.create_time DESC LIMIT 6
    </select>

    <select id="queryAllAgents" resultType="com.qc.agent.app.agent.model.vo.QcAiAgentVO">
        WITH latest_use_agent AS (
        SELECT
        agent_id,
        max(create_time) AS latest_create_time
        FROM qc_ai_agent_conversation
        WHERE creator_id = #{userId} and status = '1'
        GROUP BY agent_id
        )
        SELECT qc_ai_agent.id AS "agentId",
               qc_ai_agent.creator_id AS "creatorId",
               qc_ai_agent.creator_name AS "creatorName",
               qc_ai_agent.create_time AS "createTime",
               qc_ai_agent.modifyier_id AS "modifyierId",
               qc_ai_agent.modifyier_name AS "modifyierName",
               qc_ai_agent.modify_time AS "modifyTime",
               qc_ai_agent.name AS "agentName",
               qc_ai_agent.internal_flag AS "internalFlag",
               qc_ai_agent.logo AS "agentLogo",
               qc_ai_agent.creator_name AS "creatorName",
               qc_ai_agent.description,
               qc_ai_agent.introduction,
               qc_ai_agent.leading_question AS "leadingQuestion",
               qc_ai_agent.publish_flag AS "publishFlag",
               qc_ai_agent.publish_time AS "publishTime",
               qc_ai_agent.publish_user_id AS "publishUserId",
               qc_ai_agent.publish_user_name AS "publishUserName",
               qc_ai_agent.enable,
               qc_ai_agent.data_range AS "dataRange",
               qc_ai_agent.h5_url AS "h5Url"
        FROM qc_ai_agent
        LEFT JOIN latest_use_agent lua ON qc_ai_agent.id = lua.agent_id
        WHERE qc_ai_agent.status = '1'
          AND COALESCE(qc_ai_agent.enable, '1') = '1'
          AND COALESCE(qc_ai_agent.publish_flag, '1') = '1'
        <if test="name != null and name != ''">
            AND qc_ai_agent.name LIKE CONCAT('%',#{name},'%')
        </if>
        and
        (exists (SELECT 1 FROM qc_ai_agent_authority_distribute_detail qcaad
        where   qcaad.user_id = #{userId} and qcaad.agent_id = qc_ai_agent.id)  or
        exists (SELECT 1 FROM qc_ai_agent_authority_distribute_detail qcaad
        where qcaad.agent_id = qc_ai_agent.id and (qcaad.dept_id in
        <foreach collection="deptIdList" item="deptId" open="(" close=")" separator=",">
            #{deptId}
        </foreach> or qcaad.dept_id = -1))
        )
        ORDER BY lua.latest_create_time desc NULLS LAST, qc_ai_agent.name, qc_ai_agent.internal_flag DESC, qc_ai_agent.sequ, qc_ai_agent.publish_time DESC
    </select>

    <select id="seekAgents" resultType="com.qc.agent.app.agent.model.vo.QcAiAgentVO">
        WITH latest_use_agent AS (
            SELECT
                agent_id,
                max(create_time) AS latest_create_time
            FROM qc_ai_agent_conversation
            WHERE creator_id = #{userId} and status = '1'
            GROUP BY agent_id
        )
        SELECT qca.id AS "agentId",
               qca.creator_id AS "creatorId",
               qca.creator_name AS "creatorName",
               qca.create_time AS "createTime",
               qca.modifyier_id AS "modifyierId",
               qca.modifyier_name AS "modifyierName",
               qca.modify_time AS "modifyTime",
               qca.name AS "agentName",
               qca.internal_flag AS "internalFlag",
               qca.logo AS "agentLogo",
               qca.creator_name AS "creatorName",
               qca.description,
               qca.introduction,
               qca.leading_question AS "leadingQuestion",
               qca.publish_flag AS "publishFlag",
               qca.publish_time AS "publishTime",
               qca.publish_user_id AS "publishUserId",
               qca.publish_user_name AS "publishUserName",
               qca.enable
        FROM qc_ai_agent qca
        LEFT JOIN latest_use_agent lua ON qca.id = lua.agent_id
        WHERE qca.status = '1' and
              (exists (SELECT 1 FROM qc_ai_agent_authority_distribute_detail qcaad
                                where   qcaad.user_id = #{userId} and qcaad.agent_id = qca.id)  or
               exists ( SELECT 1 FROM qc_ai_agent_authority_distribute_detail qcaad
                       where qcaad.agent_id = qca.id
                         <if test = "deptIdList != null and deptIdList.size() > 0">
                             and qcaad.dept_id in
                            <foreach collection="deptIdList" item="deptId" open="(" close=")" separator=",">
                               #{deptId}
                            </foreach>
                         </if>
                         )
                  )
        AND
        qca.internal_flag = '0'
        <if test="name != null and name != ''">
            AND qca.agent_name LIKE CONCAT('%',#{name},'%')
        </if>
        AND qca.publish_flag = '1'
        ORDER BY lua.latest_create_time DESC NULLS LAST, qca.publish_time DESC
    </select>

    <update id="publish">
        UPDATE qc_ai_agent
        SET publish_flag = '1',
            publish_time = CURRENT_TIMESTAMP,
            publish_user_id = #{userId},
            publish_user_name = #{userName},
            modifyier_id = #{userId},
            modifyier_name = #{userName},
            show_chat_log_content_type = #{showChatLogContentType}
        WHERE id = #{id}
    </update>

    <update id="deactivate">
        UPDATE qc_ai_agent
        SET publish_flag = '0',
            publish_time = null,
            modifyier_id = #{userId},
            modifyier_name = #{userName}
        WHERE id = #{id}
    </update>

    <!-- 定义 resultMap -->
    <resultMap id="QcAiAgentDetailVOMap" type="com.qc.agent.app.agent.model.vo.QcAiAgentDetailVO">
        <id property="id" column="id" />
        <result property="internalFlag" column="internal_flag" />
        <result property="modelId" column="model_id" />
        <result property="modelName" column="model" jdbcType="VARCHAR" javaType="java.lang.String"/>
        <result property="name" column="name" />
        <result property="logo" column="logo" />
        <result property="description" column="description" />
        <result property="prompt" column="prompt" />
        <result property="leadingQuestion" column="leading_question" />
        <result property="contextSearchAmount" column="context_search_amount" />
        <result property="introduction" column="introduction" />
        <result property="internetSearch" column="internet_search" />
        <result property="nullResultAnswer" column="null_result_answer" />
        <result property="errorResultAnswer" column="error_result_answer" />
        <result property="modelTemperature" column="model_temperature" />
        <result property="modelTopP" column="model_top_p" />
        <result property="modelMaxTokens" column="model_max_tokens" />
        <result property="maxRecallCount" column="max_recall_count" />
        <result property="minMatchThreshold" column="min_match_threshold" />
        <result property="qaMinMatchThreshold" column="qa_min_match_threshold" />
        <result property="searchScope" column="search_scope" />
        <result property="splitSqlPrompt" column="split_sql_prompt" />
        <result property="bizPrompt" column="biz_prompt" />
        <result property="publishFlag" column="publish_flag" />
        <result property="intentIsEnabled" column="intent_is_enabled" />
    </resultMap>

    <select id="queryAgentDetail" resultMap="QcAiAgentDetailVOMap">
        SELECT qag.id,
               qag.internal_flag ,
               qag.model_id ,
               qaam.model,
               qag.name,
               qag.logo,
               qag.description,
               qag.prompt,
               qag.leading_question     ,
               qag.context_search_amount ,
               qag.introduction,
               qag.internet_search       ,
               qag.null_result_answer    ,
               qag.error_result_answer   ,
               qag.model_temperature     ,
               qag.model_top_p           ,
               qag.model_max_tokens      ,
               qag.max_recall_count      ,
               qag.min_match_threshold   ,
               qag.qa_min_match_threshold ,
               qag.search_scope          ,
               qag.split_sql_prompt,
               qag.biz_prompt,
               qag.publish_flag,
               qag.intent_is_enabled
        FROM qc_ai_agent qag
                 LEFT JOIN qc_ai_agent_model qaam ON qag.model_id = qaam.id
        WHERE qag.id = #{id}
    </select>

    <resultMap id="authorityDistributeDetailResultMap" type="com.qc.agent.app.agent.model.po.QcAiAgentAuthorityDistributeDetail">
        <id property="id" column="id"/>
        <result property="status" column="status"/>
        <result property="creatorId" column="creator_id"/>
        <result property="createTime" column="create_time"/>
        <result property="modifierId" column="modifier_id"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="agentId" column="agent_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="deptName" column="dept_name"/>
        <result property="userId" column="user_id"/>
        <result property="userName" column="user_name"/>
    </resultMap>

    <select id="selectAuthorityDistributeDetailList" resultType="com.qc.agent.app.agent.model.po.QcAiAgentAuthorityDistributeDetail">
        select
            qcaad.id as "id",
            qcaad.status as "status",
            qcaad.creator_id as "creatorId",
            qcaad.create_time as "createTime",
            qcaad.modifier_id as "modifierId",
            qcaad.modify_time as "modifyTime",
            qcaad.agent_id as "agentId",
            qcaad.dept_id as "deptId",
            qcaad.dept_name as "deptName",
            qcaad.user_id as "userId",
            qcaad.user_name as "userName",
            case when qcaad.user_id is not null then '1' else '2' end as "type"
        from qc_ai_agent_authority_distribute_detail qcaad
        where qcaad.agent_id = #{id} and qcaad.id != -1
    </select>

    <update id="enableAgent">
        UPDATE qc_ai_agent
        SET enable = '1',
            modifyier_id = #{userId},
            modifyier_name = #{userName}
        WHERE id = #{id}
    </update>

    <update id="disableAgent">
        UPDATE qc_ai_agent
        SET enable = '0',
            modifyier_id = #{userId},
            modifyier_name = #{userName}
        WHERE id = #{id}
    </update>

    <update id="deleteAgent">
        UPDATE qc_ai_agent
        SET status = '0',
            modifyier_id = #{userId},
            modifyier_name = #{userName}
        WHERE id = #{id}
    </update>
    <update id="updateExtConfigValue">
        UPDATE qc_ai_agent_ext_config
        SET config_value = #{configValue},
            modify_time = #{now}
        WHERE agent_id = #{agentId}
          AND config_key = #{configKey}
    </update>

    <resultMap id="QcAiKnowledgeFileMap" type="com.qc.agent.app.workflow.inner.pojo.QcAiKnowledgeFile">
        <id property="categoryId" column="category_id"/>
        <id property="collectionId" column="detail_collection_id"/>
        <result property="categoryName" column="category_name"/>
    </resultMap>

    <resultMap id="QcAiKnowledgeRelatedMap" type="com.qc.agent.app.workflow.inner.pojo.QcAiKnowledgeRelated">
        <id property="collectionId" column="collection_id"/>
        <result property="publishFlag" column="publish_flag"/>
        <result property="name" column="knowledge_name"/>
        <result property="knowledgeStatus" column="knowledge_status"/>
        <collection property="relateDocList" ofType="com.qc.agent.app.workflow.inner.pojo.QcAiKnowledgeFile" resultMap="QcAiKnowledgeFileMap"/>
    </resultMap>

    <resultMap id="QcAiFileRelatedMap" type="com.qc.agent.app.workflow.inner.pojo.QcUploadFile">
        <id property="id" column="file_id"/>
        <result property="name" column="file_name"/>
        <result property="size" column="size"/>
        <result property="url" column="url"/>
        <result property="platform" column="platform"/>
        <result property="basePath" column="base_path"/>
        <result property="path" column="path"/>
        <result property="ext" column="ext"/>
    </resultMap>

    <resultMap id="QcAiKnowledgeRelatedResultMap" type="com.qc.agent.app.workflow.inner.pojo.QcAiAgentIntentRecognition">
        <id property="id" column="id"/>
        <result property="agentId" column="agent_id"/>
        <result property="intentName" column="intent_name"/>
        <result property="intentCode" column="intent_code"/>
        <result property="maxRecallCount" column="max_recall_count"/>
        <result property="qaMinMatchThreshold" column="qa_min_match_threshold"/>
        <result property="minMatchThreshold" column="min_match_threshold"/>
        <result property="searchScope" column="search_scope"/>
        <result property="type" column="type"/>
        <collection property="relateKnowledgeList" ofType="com.qc.agent.app.workflow.inner.pojo.QcAiKnowledgeRelated" resultMap="QcAiKnowledgeRelatedMap"/>
        <collection property="relateFileList" ofType="com.qc.agent.app.workflow.inner.pojo.QcAiKnowledgeRelated" resultMap="QcAiFileRelatedMap"/>
    </resultMap>

    <select id="selectIntentList" resultMap="QcAiKnowledgeRelatedResultMap">
        select
            distinct
            qaair.id,
            qaair.create_time,
            qaair.intent_name,
            qaair.intent_code,
            qaair.max_recall_count,
            qaair.min_match_threshold,
            qaair.qa_min_match_threshold,
            qaair.search_scope,
            qaair.type,

            qaikd.collection_id,
            qki.publish_flag,
            qki.status as knowledge_status,
            qki.knowledge_name,

            qaikcm.collection_id as detail_collection_id,
            qaikd.id as relation_id,
            qaikcm.category_id,
            qkfc.category_name,

            quf.id as file_id,
            quf.name as file_name,
            quf.size,
            quf.url,
            quf.platform,
            quf.base_path,
            quf.path,
            quf.ext,

            CASE WHEN qaair.intent_code IS NOT NULL AND trim(qaair.intent_code) ~ '^[0-9]+$'
                THEN 0 ELSE 1 END AS intent_code_order_flag,
            CASE
                WHEN qaair.intent_code IS NOT NULL AND trim(qaair.intent_code) ~ '^[0-9]+$' THEN
                    trim(qaair.intent_code)::INT
                ELSE NULL
                END AS intent_code_order_value
        from qc_ai_agent_intent_recognition qaair
        left join qc_ai_agent_intent_knowledge_detail qaikd on qaair.id = qaikd.intent_id
                    and qaikd.agent_id = qaair.agent_id and qaikd.status = '1' and qaair.type = '1'
        left join qc_ai_agent_intent_knowledge_category_mapping qaikcm
        on qaikd.collection_id = qaikcm.collection_id and qaikcm.relation_id = qaikd.id
        left join qc_knowledge_info qki ON qaikd.collection_id = qki.id
        left join qc_knowledge_file qkf ON qaikd.collection_id = qkf.collection_id
        and qkf.category_id = qaikcm.category_id
        left join qc_knowledge_file_category qkfc on qkf.category_id = qkfc.id and qkfc.status = '1'
        and qkfc.collection_id = qki.id
        left join qc_ai_agent_intent_file_detail qaifd
            on qaifd.intent_id = qaair.id and qaifd.agent_id = qaair.agent_id and qaair.type = '2'
        left join qc_upload_file quf on qaifd.file_id = quf.id and quf.status = '1'
        where qaair.agent_id = #{agentId}
        and qaair.status = '1'
        order by
            intent_code_order_flag,
            intent_code_order_value,
            qaair.intent_code,
            qaair.create_time desc,
            qaikd.collection_id,
            qaikcm.category_id,
            quf.name
    </select>
    <select id="selectExtConfigByAgentId" resultType="com.qc.agent.app.agent.model.dto.QcAiAgentExtConfig">
        SELECT config_key as "configKey",
               config_value as "configValue",
               description
        FROM qc_ai_agent_ext_config
        WHERE agent_id = #{id} and status = '1'
    </select>
    <select id="selectSalesIntoRecommendInterface" resultType="java.lang.String">
        select config_value as "configValue"
        FROM qc_ai_agent_ext_config
        WHERE agent_id = 6 and status = '1' and config_key = 'salesIntoRecommendInterface'
    </select>

    <select id="queryInternalAgentById" resultType="com.qc.agent.app.agent.model.vo.QcAiAgentVO">
        SELECT id AS "agentId",
        creator_id AS "creatorId",
        creator_name AS "creatorName",
        create_time AS "createTime",
        modifyier_id AS "modifyierId",
        modifyier_name AS "modifyierName",
        modify_time AS "modifyTime",
        name AS "agentName",
        internal_flag AS "internalFlag",
        logo AS "agentLogo",
        description,
        introduction,
        leading_question AS "leadingQuestion",
        publish_flag AS "publishFlag",
        publish_time AS "publishTime",
        publish_user_id AS "publishUserId",
        publish_user_name AS "publishUserName",
        enable,
        data_range AS "dataRange",
        h5_url AS "h5Url"
        FROM qc_ai_agent
        WHERE status = '1'
        AND internal_flag = '1'
        AND id = #{id}
        ORDER BY enable DESC, id
    </select>

</mapper>
