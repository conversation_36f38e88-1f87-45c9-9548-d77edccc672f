<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.qc.agent.app.agent.mapper.QcAiAgentAdminMapper">
    <resultMap  id="QcAiAgentAdminChart" type="com.qc.agent.app.agent.model.po.QcAiAgentAdminChart">
        <result column="date" property="date" />
        <result column="count" property="count" />
        <result column="average_count" property="averageCount" />
        <result column="kb_answer_count" property="kbAnswerCount" />
        <result column="total_count" property="totalCount" />
        <result column="kb_answer_success_rate" property="kbAnswerSuccessRate" />
        <result column="dissatisfied_count" property="dissatisfiedCount" />
    </resultMap>

    <select id="selectDialogueChart" resultMap="QcAiAgentAdminChart">
        select
            to_char(create_time, 'yyyy-mm-dd') as date,
            count(1) as count
        from qc_ai_agent_conversation
        where status = '1' and create_time >= #{startDate}::TIMESTAMP
          and create_time <![CDATA[<]]> #{endDate}::TIMESTAMP + INTERVAL '1 day'
           and agent_id = #{agentId}
        group by to_char(create_time, 'yyyy-mm-dd')
    </select>

    <select id="selectActiveUserChart" resultMap="QcAiAgentAdminChart">
        select
            to_char(create_time, 'yyyy-mm-dd') as date,
            count(distinct user_id) as count
        from qc_ai_agent_conversation
        where status = '1' and create_time >= #{startDate}::TIMESTAMP
          and create_time <![CDATA[<]]> #{endDate}::TIMESTAMP + INTERVAL '1 day'
          and agent_id = #{agentId}
        group by to_char(create_time, 'yyyy-mm-dd')
    </select>

    <select id="selectTokenChart" resultMap="QcAiAgentAdminChart">
        select
            to_char(create_time, 'yyyy-mm-dd') as date,
            sum(COALESCE(question_token,0)+ COALESCE(answer_token,0)) as count
        from qc_ai_agent_conversation
        where status = '1' and create_time >= #{startDate}::TIMESTAMP
          and create_time <![CDATA[<]]> #{endDate}::TIMESTAMP + INTERVAL '1 day'
          and agent_id = #{agentId}
        group by to_char(create_time, 'yyyy-mm-dd')
        order by to_char(create_time, 'yyyy-mm-dd')
    </select>

    <select id="selectAverageDialogueChart" resultMap="QcAiAgentAdminChart">
        select
            to_char(create_time, 'yyyy-mm-dd') as date,
            round(count(id)*1.0/count(distinct user_id),2) as average_count
        from qc_ai_agent_conversation
        where status = '1' and create_time >= #{startDate}::TIMESTAMP
          and create_time <![CDATA[<]]> #{endDate}::TIMESTAMP + INTERVAL '1 day'
          and agent_id = #{agentId}
        group by to_char(create_time, 'yyyy-mm-dd')
    </select>


    <resultMap id="QcAiAgentConversationResultMap" type="com.qc.agent.app.outside_interface.pojo.QcAiAgentConversationDO">
        <!-- 主键映射 -->
        <id property="id" column="id" />
        <!-- 普通字段映射 -->
        <result property="status" column="status" />
        <result property="creatorId" column="creator_id" />
        <result property="creatorName" column="creator_name" />
        <result property="createTime" column="create_time" />
        <result property="answerTime" column="answer_time" />
        <result property="sessionId" column="session_id" />
        <result property="modelId" column="model_id" />
        <result property="userId" column="user_id" />
        <result property="agentId" column="agent_id" />
        <result property="agentName" column="agent_name" />
        <result property="question" column="question" />
        <result property="questionToken" column="question_token" />
        <result property="answer" column="answer" />
        <result property="answerToken" column="answer_token" />
        <result property="conversationTime" column="conversation_time" />
        <result property="conversationStatus" column="conversation_status" />
    </resultMap>

    <select id="selectConversationRecordList" resultMap="QcAiAgentConversationResultMap">
        select
            qaac.id,
            qaac.status,
            qaac.creator_id,
            qaac.creator_name,
            qaac.create_time,
            qaac.answer_time,
            qaac.session_id,
            qaac.model_id,
            qaac.user_id,
            qaac.agent_id,
            qaa.name as agent_name,
            qaac.question,
            qaac.question_token,
            qaac.answer,
            qaac.answer_token,
            qaac.conversation_time,
            qaa.name as agent_name,
            qaa.internal_flag
        from qc_ai_agent_conversation qaac
        left join qc_ai_agent qaa on qaa.id = qaac.agent_id
        where qaac.status = '1' and qaac.create_time >= CURRENT_DATE
          and qaac.create_time <![CDATA[<]]> CURRENT_DATE + INTERVAL '1 day'
    </select>

    <select id="selectAllTenantIds" resultType="java.lang.Long">
        SELECT id FROM sys_db_datasource
    </select>

    <select id="selectDistinctUserCount" resultType="java.lang.Long">
        select
            count(distinct user_id) as count
        from qc_ai_agent_conversation
        where status = '1' and create_time >= #{startDate}::TIMESTAMP
          and create_time <![CDATA[<]]> #{endDate}::TIMESTAMP + INTERVAL '1 day'
          and agent_id = #{agentId}
    </select>


    <select id="selectKnowledgeChart" resultMap="QcAiAgentAdminChart">
        WITH quote_count_sql AS (
              SELECT
              qaacq.conversation_id,
              COUNT (*) AS COUNT
              FROM
              qc_ai_agent_conversation_quote qaacq
              LEFT JOIN qc_ai_agent_conversation qaac ON qaacq.conversation_id = qaac.ID
              where qaac.create_time >= #{startDate}::TIMESTAMP
                and qaac.create_time <![CDATA[<]]> #{endDate}::TIMESTAMP + INTERVAL '1 day'
                and qaac.agent_id = #{agentId}
              GROUP BY
              qaacq.conversation_id
        )
        select
            to_char(qaac.create_time, 'yyyy-mm-dd') as date,
            round(sum(case when qcqs.count > 0 then 1 else 0 end)*1.0*100/count(*),2) as kb_answer_success_rate,
            count(*) as total_count,
            sum(case when qcqs.count > 0 then 1 else 0 end) as kb_answer_count,
            sum(case when qaac.evaluation_type = '0' then 1 else 0 end) as dissatisfied_count
        from qc_ai_agent_conversation qaac
        left join quote_count_sql qcqs on qaac.id = qcqs.conversation_id
        where qaac.status = '1' and qaac.create_time >= #{startDate}::TIMESTAMP
          and qaac.create_time <![CDATA[<]]> #{endDate}::TIMESTAMP + INTERVAL '1 day'
          and qaac.agent_id = #{agentId}
        group by to_char(qaac.create_time, 'yyyy-mm-dd')
    </select>

</mapper>
