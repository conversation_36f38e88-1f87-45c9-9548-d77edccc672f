<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.qc.agent.app.knowledge.mapper.QcKnowledgeExcelDataMapper" >
  <resultMap id="BaseResultMap" type="com.qc.agent.app.knowledge.model.QcKnowledgeExcelData" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="status" property="status" jdbcType="CHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
    <result column="create_user_id" property="createUserId" jdbcType="BIGINT" />
    <result column="create_user_name" property="createUserName" jdbcType="VARCHAR" />
    <result column="modify_user_id" property="modifyUserId" jdbcType="BIGINT" />
    <result column="modify_user_name" property="modifyUserName" jdbcType="VARCHAR" />
    <result column="sheet_id" property="sheetId" jdbcType="BIGINT" />
    <result column="question" property="question" jdbcType="VARCHAR" />
    <result column="answer" property="answer" jdbcType="VARCHAR" />
    <result column="sequ" property="sequ" jdbcType="NUMERIC" />
    <result column="file_id" property="fileId" jdbcType="BIGINT" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, status, create_time, modify_time, create_user_id, create_user_name, modify_user_id,
    modify_user_name, sheet_id, question, answer, sequ, file_id
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from qc_knowledge_excel_data
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from qc_knowledge_excel_data
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByFileId">
    delete from qc_knowledge_excel_data
    where file_id = #{fileId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.qc.agent.app.knowledge.model.QcKnowledgeExcelData" >
    insert into qc_knowledge_excel_data (id, status, create_time,
      modify_time, create_user_id, create_user_name,
      modify_user_id, modify_user_name, sheet_id,
      question, answer, file_id, sequ
      )
    values (#{id,jdbcType=BIGINT}, #{status,jdbcType=CHAR}, #{createTime,jdbcType=TIMESTAMP},
      #{modifyTime,jdbcType=TIMESTAMP}, #{createUserId,jdbcType=BIGINT}, #{createUserName,jdbcType=VARCHAR},
      #{modifyUserId,jdbcType=BIGINT}, #{modifyUserName,jdbcType=VARCHAR}, #{sheetId,jdbcType=BIGINT},
      #{question,jdbcType=VARCHAR}, #{answer,jdbcType=VARCHAR}, #{fileId, jdbcType=BIGINT}, #{sequ,jdbcType=NUMERIC}
      )
  </insert>

  <insert id="insertWithMaxSequ" parameterType="com.qc.agent.app.knowledge.model.QcKnowledgeExcelData" >
    insert into qc_knowledge_excel_data (id, status, create_time,
                                         modify_time, create_user_id, create_user_name,
                                         modify_user_id, modify_user_name, sheet_id,
                                         question, answer, file_id, sequ
    )
    values (#{id,jdbcType=BIGINT}, #{status,jdbcType=CHAR}, #{createTime,jdbcType=TIMESTAMP},
            #{modifyTime,jdbcType=TIMESTAMP}, #{createUserId,jdbcType=BIGINT}, #{createUserName,jdbcType=VARCHAR},
            #{modifyUserId,jdbcType=BIGINT}, #{modifyUserName,jdbcType=VARCHAR}, #{sheetId,jdbcType=BIGINT},
            #{question,jdbcType=VARCHAR}, #{answer,jdbcType=VARCHAR}, #{fileId, jdbcType=BIGINT},
            (select max(sequ) + 1 from qc_knowledge_excel_data where file_id = #{fileId, jdbcType=BIGINT})
           )
  </insert>

  <insert id="insertSelective" parameterType="com.qc.agent.app.knowledge.model.QcKnowledgeExcelData" >
    insert into qc_knowledge_excel_data
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="modifyTime != null" >
        modify_time,
      </if>
      <if test="createUserId != null" >
        create_user_id,
      </if>
      <if test="createUserName != null" >
        create_user_name,
      </if>
      <if test="modifyUserId != null" >
        modify_user_id,
      </if>
      <if test="modifyUserName != null" >
        modify_user_name,
      </if>
      <if test="sheetId != null" >
        sheet_id,
      </if>
      <if test="question != null" >
        question,
      </if>
      <if test="answer != null" >
        answer,
      </if>
      <if test="sequ != null" >
        sequ,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="status != null" >
        #{status,jdbcType=CHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null" >
        #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null" >
        #{createUserId,jdbcType=BIGINT},
      </if>
      <if test="createUserName != null" >
        #{createUserName,jdbcType=VARCHAR},
      </if>
      <if test="modifyUserId != null" >
        #{modifyUserId,jdbcType=BIGINT},
      </if>
      <if test="modifyUserName != null" >
        #{modifyUserName,jdbcType=VARCHAR},
      </if>
      <if test="sheetId != null" >
        #{sheetId,jdbcType=BIGINT},
      </if>
      <if test="question != null" >
        #{question,jdbcType=VARCHAR},
      </if>
      <if test="answer != null" >
        #{answer,jdbcType=VARCHAR},
      </if>
      <if test="sequ != null" >
        #{sequ,jdbcType=NUMERIC},
      </if>
    </trim>
  </insert>

  <insert id="batchInsert">
    INSERT INTO qc_knowledge_excel_data(
         id,
         status,
         create_time,
         create_user_id,
         create_user_name,
         sheet_id,
         question,
         answer,
         sequ,
         file_id
    )
    VALUES
    <foreach collection="records" item="item" separator=",">
      (
      #{item.id}
      ,#{item.status}
      ,#{item.createTime}
      ,#{item.createUserId}
      ,#{item.createUserName}
      ,#{item.sheetId}
      ,#{item.question}
      ,#{item.answer}
      ,#{item.sequ}
      ,#{item.fileId}
      )
    </foreach>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.qc.agent.app.knowledge.model.QcKnowledgeExcelData" >
    update qc_knowledge_excel_data
    <set >
      <if test="status != null" >
        status = #{status,jdbcType=CHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null" >
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null" >
        create_user_id = #{createUserId,jdbcType=BIGINT},
      </if>
      <if test="createUserName != null" >
        create_user_name = #{createUserName,jdbcType=VARCHAR},
      </if>
      <if test="modifyUserId != null" >
        modify_user_id = #{modifyUserId,jdbcType=BIGINT},
      </if>
      <if test="modifyUserName != null" >
        modify_user_name = #{modifyUserName,jdbcType=VARCHAR},
      </if>
      <if test="sheetId != null" >
        sheet_id = #{sheetId,jdbcType=BIGINT},
      </if>
      <if test="question != null" >
        question = #{question,jdbcType=VARCHAR},
      </if>
      <if test="answer != null" >
        answer = #{answer,jdbcType=VARCHAR},
      </if>
      <if test="sequ != null" >
        sequ = #{sequ,jdbcType=NUMERIC},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.qc.agent.app.knowledge.model.QcKnowledgeExcelData" >
    update qc_knowledge_excel_data
    set status = #{status,jdbcType=CHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      create_user_id = #{createUserId,jdbcType=BIGINT},
      create_user_name = #{createUserName,jdbcType=VARCHAR},
      modify_user_id = #{modifyUserId,jdbcType=BIGINT},
      modify_user_name = #{modifyUserName,jdbcType=VARCHAR},
      sheet_id = #{sheetId,jdbcType=BIGINT},
      question = #{question,jdbcType=VARCHAR},
      answer = #{answer,jdbcType=VARCHAR},
      sequ = #{sequ,jdbcType=NUMERIC}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="countQAByFileId" resultType="java.lang.Integer">
    SELECT
        COUNT(1)
    FROM qc_knowledge_excel_data
    WHERE status = '1'
      AND file_id = #{fileId, jdbcType=BIGINT}
    <if test="content != null and content != ''">
      AND (question LIKE concat('%', #{content}, '%') OR answer LIKE concat('%', #{content}, '%'))
    </if>
  </select>

  <select id="listQAByFileId" resultType="com.qc.agent.app.knowledge.model.QcKnowledgeExcelData">
    SELECT
        id,
        question,
        answer,
        file_id AS "fileId",
        row_number() over (order by sequ) as sequ
    FROM qc_knowledge_excel_data
    WHERE status = '1'
      AND file_id = #{fileId, jdbcType=BIGINT}
    <if test="content != null and content != ''">
      AND (question LIKE concat('%', #{content}, '%') OR answer LIKE concat('%', #{content}, '%'))
    </if>
    ORDER BY sequ DESC
    <if test="limit != null and offset != null">
      LIMIT ${limit} OFFSET ${offset}
    </if>

  </select>

  <update id="deleteById">
    UPDATE qc_knowledge_excel_data
    SET status = '0'
    WHERE id = #{id, jdbcType=BIGINT}
  </update>

  <select id="findByFileId" resultType="com.qc.agent.app.knowledge.model.QcKnowledgeExcelData">
    SELECT
      id,
      question,
      answer,
      file_id AS "fileId",
      sequ
    FROM qc_knowledge_excel_data
    WHERE status = '1'
      AND file_id = #{fileId, jdbcType=BIGINT}
    ORDER BY sequ
  </select>
</mapper>
