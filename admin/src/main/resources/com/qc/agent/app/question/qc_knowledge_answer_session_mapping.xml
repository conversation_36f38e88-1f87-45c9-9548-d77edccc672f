<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.qc.agent.app.question.mapper.QcKnowledgeAnswerSessionMapper" >
  <resultMap id="BaseResultMap" type="com.qc.agent.app.question.pojo.QcKnowledgeAnswerSession" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="status" property="status" jdbcType="CHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
    <result column="create_user_id" property="createUserId" jdbcType="BIGINT" />
    <result column="create_user_name" property="createUserName" jdbcType="VARCHAR" />
    <result column="modify_user_id" property="modifyUserId" jdbcType="BIGINT" />
    <result column="modify_user_name" property="modifyUserName" jdbcType="VARCHAR" />
    <result column="session_name" property="sessionName" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, status, create_time, modify_time, create_user_id, create_user_name, modify_user_id,
    modify_user_name, session_name
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from qc_knowledge_answer_session
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from qc_knowledge_answer_session
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.qc.agent.app.question.pojo.QcKnowledgeAnswerSession" >
    insert into qc_knowledge_answer_session (id, status, create_time,
      modify_time, create_user_id, create_user_name,
      modify_user_id, modify_user_name, session_name
      )
    values (#{id,jdbcType=BIGINT}, #{status,jdbcType=CHAR}, #{createTime,jdbcType=TIMESTAMP},
      #{modifyTime,jdbcType=TIMESTAMP}, #{createUserId,jdbcType=BIGINT}, #{createUserName,jdbcType=VARCHAR},
      #{modifyUserId,jdbcType=BIGINT}, #{modifyUserName,jdbcType=VARCHAR}, #{sessionName,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.qc.agent.app.question.pojo.QcKnowledgeAnswerSession" >
    insert into qc_knowledge_answer_session
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="modifyTime != null" >
        modify_time,
      </if>
      <if test="createUserId != null" >
        create_user_id,
      </if>
      <if test="createUserName != null" >
        create_user_name,
      </if>
      <if test="modifyUserId != null" >
        modify_user_id,
      </if>
      <if test="modifyUserName != null" >
        modify_user_name,
      </if>
      <if test="sessionName != null" >
        session_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="status != null" >
        #{status,jdbcType=CHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null" >
        #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null" >
        #{createUserId,jdbcType=BIGINT},
      </if>
      <if test="createUserName != null" >
        #{createUserName,jdbcType=VARCHAR},
      </if>
      <if test="modifyUserId != null" >
        #{modifyUserId,jdbcType=BIGINT},
      </if>
      <if test="modifyUserName != null" >
        #{modifyUserName,jdbcType=VARCHAR},
      </if>
      <if test="sessionName != null" >
        #{sessionName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.qc.agent.app.question.pojo.QcKnowledgeAnswerSession" >
    update qc_knowledge_answer_session
    <set >
      <if test="status != null" >
        status = #{status,jdbcType=CHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null" >
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null" >
        create_user_id = #{createUserId,jdbcType=BIGINT},
      </if>
      <if test="createUserName != null" >
        create_user_name = #{createUserName,jdbcType=VARCHAR},
      </if>
      <if test="modifyUserId != null" >
        modify_user_id = #{modifyUserId,jdbcType=BIGINT},
      </if>
      <if test="modifyUserName != null" >
        modify_user_name = #{modifyUserName,jdbcType=VARCHAR},
      </if>
      <if test="sessionName != null" >
        session_name = #{sessionName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.qc.agent.app.question.pojo.QcKnowledgeAnswerSession" >
    update qc_knowledge_answer_session
    set status = #{status,jdbcType=CHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      create_user_id = #{createUserId,jdbcType=BIGINT},
      create_user_name = #{createUserName,jdbcType=VARCHAR},
      modify_user_id = #{modifyUserId,jdbcType=BIGINT},
      modify_user_name = #{modifyUserName,jdbcType=VARCHAR},
      session_name = #{sessionName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="delete">
    update qc_knowledge_answer_session
    set status = '0'
    where id = #{id, jdbcType=BIGINT}
  </update>

  <select id="listAnswerSession" resultType="com.qc.agent.app.question.pojo.AnswerSessionView">
    select
            qkas.create_time AS "createTime",
            qkas.session_name AS "sessionName",
            qkas.id AS "sessionId",
            t.question_content AS "recentlyQuestion"
        from qc_knowledge_answer_session qkas
    left join (
            SELECT session_id,
                   question_content,
                   row_number() over (partition by session_id order by create_time desc) as row_num
            FROM qc_knowledge_question
            ) t on qkas.id = t.session_id and t.row_num = 1
    where qkas.status = '1'
      <if test="adminUser != null and adminUser == '0'.toString()">
        AND qkas.create_user_id = #{createUserId, jdbcType=BIGINT}
      </if>
    order by qkas.create_time desc
  </select>
</mapper>
