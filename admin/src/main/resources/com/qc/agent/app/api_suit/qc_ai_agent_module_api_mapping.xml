<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.qc.agent.app.api_suit.mapper.QcAiAgentModuleApiMapper">
    <resultMap id="ModuleApiVOMap" type="com.qc.agent.app.api_suit.model.vo.ModuleApiVO">
        <id property="id" column="id"/>
        <result property="moduleId" column="module_id"/>
        <result property="name" column="name"/>
        <result property="describe" column="describe"/>
        <result property="sequ" column="sequ"/>
        <result property="url" column="url"/>
        <result property="documentUrl" column="document_url"/>
        <result property="requestMethod" column="request_method"/>
        <result property="headers" column="headers"/>
        <result property="body" column="body"/>
        <result property="queryParam" column="query_param"/>
        <result property="response" column="response"/>
        <result property="createTime" column="create_time"/>
        <result property="creatorName" column="creator_name"/>
        <result property="creatorId" column="creator_id"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="modifyierId" column="modifyier_id"/>
        <result property="modifyierName" column="modifyier_name" />
    </resultMap>

    <select id="selectById" resultMap="ModuleApiVOMap">
        SELECT *
        FROM qc_ai_agent_module_api
        WHERE id = #{id}
    </select>

    <select id="selectAll" resultMap="ModuleApiVOMap">
        SELECT *
        FROM qc_ai_agent_module_api
        ORDER BY sequ
    </select>

    <select id="selectByModuleId" resultMap="ModuleApiVOMap">
        SELECT *
        FROM qc_ai_agent_module_api
        WHERE module_id = #{moduleId}
          AND status = '1'
        ORDER BY sequ
    </select>

    <insert id="insert">
        INSERT INTO qc_ai_agent_module_api(
                                           id,
                                           status,
                                           creator_id,
                                           creator_name,
                                           module_id,
                                           name,
                                           describe,
                                           sequ,
                                           url,
                                           document_url,
                                           create_time,
                                           request_method,
                                           headers,
                                           body,
                                           query_param,
                                           response)
        VALUES (#{id},
                #{status},
                #{creatorId},
                #{creatorName},
                #{moduleId},
                #{name},
                #{describe},
                #{sequ},
                #{url},
                #{documentUrl},
                #{createTime},
                #{requestMethod},
                #{headers},
                #{body},
                #{queryParam},
                #{response})
    </insert>

    <update id="update">
        UPDATE qc_ai_agent_module_api
        SET name           = #{name},
            describe       = #{describe},
            sequ           = #{sequ},
            url            = #{url},
            document_url   = #{documentUrl},
            request_method = #{requestMethod},
            headers        = #{headers},
            body           = #{body},
            query_param    = #{queryParam},
            response       = #{response},
            modify_time    = #{modifyTime},
            modifyier_id   = #{modifyierId},
            modifyier_name = #{modifyierName}
        WHERE id = #{id}
    </update>

    <delete id="delete">
        UPDATE qc_ai_agent_module_api
        SET status = '0'
        WHERE id = #{id}
    </delete>
</mapper>
