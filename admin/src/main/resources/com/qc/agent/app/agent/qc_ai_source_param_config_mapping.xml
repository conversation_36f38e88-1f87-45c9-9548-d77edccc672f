<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qc.agent.app.agent.mapper.QcAiSourceParamConfigMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.qc.agent.app.agent.model.entity.QcAiSourceParamConfig">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="source_id" property="sourceId" jdbcType="BIGINT"/>
        <result column="param_code" property="paramCode" jdbcType="VARCHAR"/>
        <result column="group_code" property="groupCode" jdbcType="VARCHAR"/>
        <result column="group_name" property="groupName" jdbcType="VARCHAR"/>
        <result column="group_enum_values" property="groupEnumValues" jdbcType="VARCHAR"/>
        <result column="display_order" property="displayOrder" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, source_id, param_code, group_code, group_name, group_enum_values, display_order
    </sql>

    <!-- 根据ID查询参数配置 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM qc_ai_source_param_config
        WHERE id = #{id}
    </select>

    <!-- 插入参数配置 -->
    <insert id="insert" parameterType="com.qc.agent.app.agent.model.entity.QcAiSourceParamConfig" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO qc_ai_source_param_config (
            source_id, param_code, group_code, group_name, group_enum_values, display_order
        ) VALUES (
            #{sourceId}, #{paramCode}, #{groupCode}, #{groupName}, #{groupEnumValues}, #{displayOrder}
        )
    </insert>

    <!-- 根据ID更新参数配置 -->
    <update id="updateById" parameterType="com.qc.agent.app.agent.model.entity.QcAiSourceParamConfig">
        UPDATE qc_ai_source_param_config
        SET source_id = #{sourceId},
            param_code = #{paramCode},
            group_code = #{groupCode},
            group_name = #{groupName},
            group_enum_values = #{groupEnumValues},
            display_order = #{displayOrder}
        WHERE id = #{id}
    </update>

    <!-- 根据ID删除参数配置 -->
    <delete id="deleteById">
        DELETE FROM qc_ai_source_param_config WHERE id = #{id}
    </delete>

    <!-- 根据数据源ID查询参数配置 -->
    <select id="selectBySourceId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM qc_ai_source_param_config
        WHERE source_id = #{sourceId}
        ORDER BY display_order ASC, id ASC
    </select>

    <!-- 根据数据源ID和分组编码查询参数配置 -->
    <select id="selectBySourceIdAndGroupCode" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM qc_ai_source_param_config
        WHERE source_id = #{sourceId}
        AND group_code = #{groupCode}
        ORDER BY display_order ASC, id ASC
    </select>

    <!-- 根据数据源ID查询所有分组信息（去重） -->
    <select id="selectGroupsBySourceId" resultMap="BaseResultMap">
        SELECT DISTINCT group_code, group_name, group_enum_values, display_order
        FROM qc_ai_source_param_config
        WHERE source_id = #{sourceId}
        ORDER BY display_order ASC
    </select>

</mapper>
