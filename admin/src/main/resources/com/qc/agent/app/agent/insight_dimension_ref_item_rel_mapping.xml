<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qc.agent.app.agent.mapper.InsightDimensionRefItemRelMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.qc.agent.app.agent.model.entity.InsightDimensionRefItemRel">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="dimension_config_id" property="dimensionConfigId" jdbcType="BIGINT"/>
        <result column="ref_data_item_id" property="refDataItemId" jdbcType="BIGINT"/>
        <result column="sort_order" property="sortOrder" jdbcType="INTEGER"/>
        <result column="query_value" property="queryValue" jdbcType="VARCHAR"/>
        <result column="creator_id" property="creatorId" jdbcType="BIGINT"/>
        <result column="creator_name" property="creatorName" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modifyier_id" property="modifyierId" jdbcType="BIGINT"/>
        <result column="modifyier_name" property="modifyierName" jdbcType="VARCHAR"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
        <result column="query_value" property="queryValue" jdbcType="VARCHAR"/>
        <result column="group_code" property="groupCode" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, status, dimension_config_id, ref_data_item_id, sort_order, query_value, group_code,
        creator_id, creator_name, create_time, modifyier_id, modifyier_name, modify_time
    </sql>

    <!-- 根据ID查询 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM qc_ai_dimension_ref_data_item_rel
        WHERE id = #{id}
    </select>

    <!-- 根据维度配置ID查询 -->
    <select id="selectByDimensionConfigId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM qc_ai_dimension_ref_data_item_rel
        WHERE dimension_config_id = #{dimensionConfigId}
        ORDER BY sort_order ASC
    </select>

    <!-- 根据维度配置ID列表查询 -->
    <select id="selectByDimensionConfigIds" parameterType="java.util.List" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM qc_ai_dimension_ref_data_item_rel
        WHERE dimension_config_id IN
        <foreach collection="dimensionConfigIds" item="dimensionConfigId" open="(" separator="," close=")">
            #{dimensionConfigId}
        </foreach>
        ORDER BY sort_order ASC
    </select>

    <!-- 插入 -->
    <insert id="insert" parameterType="com.qc.agent.app.agent.model.entity.InsightDimensionRefItemRel" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO qc_ai_dimension_ref_data_item_rel (
            status, dimension_config_id, ref_data_item_id, sort_order, query_value, group_code,
            creator_id, creator_name, create_time, modifyier_id, modifyier_name, modify_time
        ) VALUES (
            #{status}, #{dimensionConfigId}, #{refDataItemId}, #{sortOrder}, #{queryValue}, #{groupCode},
            #{creatorId}, #{creatorName}, #{createTime}, #{modifyierId}, #{modifyierName}, #{modifyTime}
        )
    </insert>

    <!-- 更新 -->
    <update id="updateById" parameterType="com.qc.agent.app.agent.model.entity.InsightDimensionRefItemRel">
        UPDATE qc_ai_dimension_ref_data_item_rel
        SET status = #{status},
            dimension_config_id = #{dimensionConfigId},
            ref_data_item_id = #{refDataItemId},
            sort_order = #{sortOrder},
            query_value = #{queryValue},
            group_code = #{groupCode},
            modifyier_id = #{modifyierId},
            modifyier_name = #{modifyierName},
            modify_time = #{modifyTime}
        WHERE id = #{id}
    </update>

    <!-- 根据ID删除 -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM qc_ai_dimension_ref_data_item_rel WHERE id = #{id}
    </delete>

    <!-- 根据维度配置ID删除 -->
    <delete id="deleteByDimensionConfigId" parameterType="java.lang.Long">
        DELETE FROM qc_ai_dimension_ref_data_item_rel WHERE dimension_config_id = #{dimensionConfigId}
    </delete>

    <!-- 根据维度配置ID列表批量删除 -->
    <delete id="deleteByDimensionConfigIds" parameterType="java.util.List">
        DELETE FROM qc_ai_dimension_ref_data_item_rel WHERE dimension_config_id IN
        <foreach collection="dimensionConfigIds" item="dimensionConfigId" open="(" separator="," close=")">
            #{dimensionConfigId}
        </foreach>
    </delete>

</mapper> 