<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.qc.agent.app.agent.mapper.StatisticsQcAiAgentAdminMapper">

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO qc_ai_agent_conversation (
        id, status, creator_id, creator_name, create_time, answer_time,
        session_id, model_id, user_id, agent_id, agent_name,
        question, question_token, answer, answer_token,
        conversation_time, conversation_status
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id},
            #{item.status},
            #{item.creatorId},
            #{item.creatorName},
            #{item.createTime},
            #{item.answerTime},
            #{item.sessionId},
            #{item.modelId},
            #{item.userId},
            #{item.agentId},
            #{item.agentName},
            #{item.question},
            #{item.questionToken},
            #{item.answer},
            #{item.answerToken},
            #{item.conversationTime},
            #{item.conversationStatus}
            )
        </foreach>
    </insert>

    <delete id="deleteByCurrentDate">
        delete from qc_ai_agent_conversation
        where create_time >= CURRENT_DATE and create_time <![CDATA[<]]> CURRENT_DATE + INTERVAL '1 day'
    </delete>

</mapper>
