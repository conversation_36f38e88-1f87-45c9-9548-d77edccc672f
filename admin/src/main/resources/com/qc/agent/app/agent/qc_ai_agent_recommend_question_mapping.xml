<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.mapper.QcAiAgentRecommendQuestionsMapper">

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO qc_ai_agent_recommend_questions
        (id, status, creator_id, creator_name, create_time, conversation_id, content,seq)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.id}, #{item.status}, #{item.creatorId},
             #{item.creatorName}, #{item.createTime}, #{item.conversationId}, #{item.content}, #{item.seq})
        </foreach>
    </insert>

    <select id="selectQuestions" resultType="java.lang.String" parameterType="java.lang.Long">
       select  content from  qc_ai_agent_recommend_questions where conversation_id = #{conversationId} order by seq
    </select>

</mapper>
