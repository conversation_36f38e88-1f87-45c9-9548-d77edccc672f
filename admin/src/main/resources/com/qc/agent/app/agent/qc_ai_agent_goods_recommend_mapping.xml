<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qc.agent.app.agent.mapper.AgentGoodsRecommendMapper">

    <resultMap id="BaseResultMap"
               type="com.qc.agent.app.agent.model.po.AgentGoodsRecommend">
        <id column="id" property="id"/>
        <result column="store_id" property="storeId"/>
        <result column="source" property="source"/>
        <result column="timestamp" property="timestamp"/>
        <result column="unsold_goods" property="unsoldGoods"/>
        <result column="recommend_goods" property="recommendGoods"/>
        <result column="recommend_questions" property="recommendQuestions"/>
        <result column="create_date" property="createDate"/>
    </resultMap>

    <insert id="upsert" parameterType="com.qc.agent.app.agent.model.po.AgentGoodsRecommend">
        INSERT INTO qc_ai_agent_goods_recommend(id, store_id, source, timestamp,
                                             unsold_goods, recommend_goods, recommend_questions, create_date)
        VALUES (#{id}, #{storeId}, #{source}, #{timestamp}, #{unsoldGoods}, #{recommendGoods}, #{recommendQuestions},
                CURRENT_DATE) ON CONFLICT (store_id)
        DO
        UPDATE SET
            source = EXCLUDED.source,
            timestamp = EXCLUDED.timestamp,
            unsold_goods = EXCLUDED.unsold_goods,
            recommend_goods = EXCLUDED.recommend_goods,
            recommend_questions = EXCLUDED.recommend_questions
    </insert>

    <insert id="batchInsertOrUpdateUnsold">
        INSERT INTO qc_ai_agent_goods_unsold (
        id,
        create_time,
        name,
        short_name,
        tag_values,
        brand
        ) VALUES
        <foreach collection="goodsList" item="item" separator=",">
            (
            #{item.id},
            CURRENT_TIMESTAMP,
            #{item.name},
            #{item.shortName},
            #{item.tagValues},
            #{item.brand}
            )
        </foreach>
        ON CONFLICT (id) DO
        UPDATE SET
            name = excluded.name,
            short_name = excluded.short_name,
            tag_values = excluded.tag_values,
            brand = excluded.brand,
            modify_time = CURRENT_TIMESTAMP
        WHERE
            qc_ai_agent_goods_unsold.name != excluded.name OR
            qc_ai_agent_goods_unsold.short_name != excluded.short_name OR
            qc_ai_agent_goods_unsold.tag_values != excluded.tag_values OR
            qc_ai_agent_goods_unsold.brand != excluded.brand
    </insert>


    <insert id="batchInsertStoreUnsoldRel">
        insert into qc_ai_store_goods_unsold (store_id, goods_id, create_time)
        values
        <foreach collection="goodsIds" item="item" separator=",">
            (#{storeId}, #{item}, CURRENT_TIMESTAMP)
        </foreach>
    </insert>

    <delete id="deleteUnsoldByStoreId">
        DELETE
        FROM qc_ai_agent_goods_unsold
        WHERE store_id = #{storeId}
    </delete>

    <delete id="batchDeleteStoreUnsoldRel">
        DELETE
        FROM qc_ai_store_goods_unsold
        WHERE store_id = #{storeId} AND
        goods_id IN
        <foreach item="item" collection="goodsIds" separator=",">
            #{item}::varchar
        </foreach>
    </delete>

    <select id="findByStoreIdAndDate" resultMap="BaseResultMap">
        SELECT source, timestamp, recommend_goods, recommend_questions
        FROM qc_ai_agent_goods_recommend
        WHERE store_id = #{storeId}
    </select>
    <select id="selectUnsoldIdsByStore" resultType="java.lang.Long">
        SELECT goods_id
        FROM qc_ai_store_goods_unsold
        WHERE store_id = #{storeId}
    </select>
</mapper>