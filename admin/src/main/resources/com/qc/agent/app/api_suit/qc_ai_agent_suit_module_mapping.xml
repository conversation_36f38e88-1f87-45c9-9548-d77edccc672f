<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.qc.agent.app.api_suit.mapper.QcAiAgentSuitModuleMapper">
    <resultMap id="SuitModuleVOMap" type="com.qc.agent.app.api_suit.model.vo.SuitModuleVO">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="describe" column="describe"/>
        <result property="sequ" column="sequ"/>
        <result property="createTime" column="create_time"/>
        <result property="creatorName" column="creator_name"/>
        <result property="creatorId" column="create_time"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="modifyierId" column="modifyier_id"/>
        <result property="modifyierName" column="modifyier_name" />
    </resultMap>

    <resultMap id="SuitModuleVOResultMap" type="com.qc.agent.app.api_suit.model.vo.SuitModuleVO">
        <result property="id" column="id" javaType="java.lang.Long" jdbcType="BIGINT"/>
        <result property="suitId" column="suit_id" javaType="java.lang.Long" jdbcType="BIGINT"/>
        <result property="name" column="name" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="describe" column="describe" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="sequ" column="sequ" javaType="java.math.BigDecimal" jdbcType="DECIMAL"/>
        <result property="createTime" column="create_time" javaType="java.time.LocalDateTime" jdbcType="TIMESTAMP"/>
        <result property="creatorName" column="creator_name" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="creatorId" column="creator_id" javaType="java.lang.Long" jdbcType="BIGINT"/>
        <result property="modifyTime" column="modify_time" javaType="java.time.LocalDateTime" jdbcType="TIMESTAMP"/>
        <result property="modifyierId" column="modifyier_id" javaType="java.lang.Long" jdbcType="BIGINT"/>
        <result property="modifyierName" column="modifyier_name" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <collection property="apiList" ofType="com.qc.agent.app.api_suit.model.vo.SuitModuleApiVO"
                    resultMap="SuitModuleApiVOResultMap"/>
    </resultMap>

    <resultMap id="SuitModuleApiVOResultMap" type="com.qc.agent.app.api_suit.model.vo.SuitModuleApiVO">
        <result property="apiId" column="api_id" javaType="java.lang.Long" jdbcType="BIGINT"/>
        <result property="apiName" column="api_name" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="apiDescribe" column="api_describe" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="apiSequ" column="api_sequ" javaType="java.math.BigDecimal" jdbcType="DECIMAL"/>
        <result property="apiUrl" column="api_url" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="apiDocumentUrl" column="api_document_url" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="requestMethod" column="request_method" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="apiCreateTime" column="api_create_time" javaType="java.time.LocalDateTime"
                jdbcType="TIMESTAMP"/>
        <result property="apiCreatorName" column="api_creator_name" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="apiCreatorId" column="api_creator_id" javaType="java.lang.Long" jdbcType="BIGINT"/>
        <result property="apiModifyTime" column="api_modify_time" javaType="java.time.LocalDateTime"
                jdbcType="TIMESTAMP"/>
        <result property="apiModifyierId" column="api_modifyier_id" javaType="java.lang.Long" jdbcType="BIGINT"/>
        <result property="apiModifyierName" column="api_modifyier_name" javaType="java.lang.String" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="selectById" resultMap="SuitModuleVOMap">
        SELECT *
        FROM qc_ai_agent_suit_module qaasm
        WHERE qaasm.id = #{id}
    </select>

    <select id="selectAll" resultMap="SuitModuleVOMap">
        SELECT *
        FROM qc_ai_agent_suit_module
        ORDER BY sequ
    </select>

    <select id="selectBySuitId" resultMap="SuitModuleVOResultMap">
        SELECT qaasm.id,
               qaasm.suit_id,
               qaasm.name,
               qaasm.describe,
               qaasm.sequ,
               qaasm.create_time,
               qaasm.creator_id,
               qaasm.creator_name,
               qaasm.modify_time,
               qaasm.modifyier_id,
               qaasm.modifyier_name,
               qaama.id             AS api_id,
               qaama.name           AS api_name,
               qaama.describe       AS api_describe,
               qaama.sequ,
               qaama.url            AS api_url,
               qaama.document_url   AS api_document_url,
               qaama.create_time    AS api_create_time,
               qaama.creator_id     AS api_creator_id,
               qaama.creator_name   AS api_creator_name,
               qaama.modify_time    AS api_modify_time,
               qaama.modifyier_id   AS api_modifyier_id,
               qaama.modifyier_name AS api_modifyier_name,
               qaama.request_method
        FROM qc_ai_agent_suit_module qaasm
                 LEFT JOIN qc_ai_agent_module_api qaama ON qaasm.id = qaama.module_id AND qaama.status = '1'
        WHERE qaasm.suit_id = #{suitId}
          AND qaasm.status = '1'
        ORDER BY qaasm.sequ
    </select>

    <insert id="insert">
        INSERT INTO qc_ai_agent_suit_module(id, status, creator_id, creator_name, suit_id, name, describe, sequ,
                                            create_time)
        VALUES (#{id}, #{status}, #{creatorId}, #{creatorName}, #{suitId}, #{name}, #{describe}, #{sequ}, #{createTime})
    </insert>

    <update id="update">
        UPDATE qc_ai_agent_suit_module
        SET name           = #{name},
            describe       = #{describe},
            sequ           = #{sequ},
            modifyier_id   = #{modifyierId},
            modifyier_name = #{modifyierName},
            modify_time    = #{modifyTime}
        WHERE id = #{id}
    </update>

    <delete id="delete">
        UPDATE qc_ai_agent_suit_module
        SET status = '0'
        WHERE id = #{id}
    </delete>
</mapper>
