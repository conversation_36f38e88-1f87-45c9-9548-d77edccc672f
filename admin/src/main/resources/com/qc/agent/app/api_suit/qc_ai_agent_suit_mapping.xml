<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.qc.agent.app.api_suit.mapper.QcAiAgentSuitMapper">
    <resultMap id="SuitVOMap" type="com.qc.agent.app.api_suit.model.vo.SuitVO">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="describe" column="describe"/>
        <result property="sequ" column="sequ"/>
        <result property="createTime" column="create_time"/>
        <result property="creatorName" column="creator_name"/>
        <result property="creatorId" column="creator_id"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="modifyierId" column="modifyier_id"/>
        <result property="modifyierName" column="modifyier_name" />
    </resultMap>

    <select id="selectById" resultMap="SuitVOMap">
        SELECT *
        FROM qc_ai_agent_suit
        WHERE id = #{id}
    </select>

    <select id="selectAll" resultType="com.qc.agent.app.api_suit.model.vo.SuitVO">
        SELECT qaas.id,
               qaas.name,
               qaas.describe,
               qaas.creator_id          AS "creatorId",
               qaas.creator_name        AS "creatorName",
               qaas.create_time         AS "createTime",
               qaas.modifyier_id        AS "modifyierId",
               qaas.modifyier_name      AS "modifyierName",
               qaas.modify_time         AS "modifyTime",
               COUNT(DISTINCT qaasm.id) AS "totalModule",
               COUNT(qaama.id)          AS "totalApi"
        FROM qc_ai_agent_suit qaas
                 LEFT JOIN qc_ai_agent_suit_module qaasm ON qaas.id = qaasm.suit_id
                 LEFT JOIN qc_ai_agent_module_api qaama ON qaasm.id = qaama.module_id
        WHERE qaas.status = '1'
        GROUP BY qaas.id, qaas.name, qaas.describe, qaas.creator_id, qaas.creator_name, qaas.create_time
               , qaas.modifyier_id, qaas.modifyier_name, qaas.modify_time
        ORDER BY qaas.sequ
    </select>

    <insert id="insert">
        INSERT INTO qc_ai_agent_suit(id, status, creator_id, creator_name, name, describe, sequ, create_time)
        VALUES (#{id}, #{status}, #{creatorId}, #{creatorName}, #{name}, #{describe}, #{sequ}, #{createTime})
    </insert>

    <update id="update">
        UPDATE qc_ai_agent_suit
        SET name           = #{name},
            describe       = #{describe},
            sequ           = #{sequ},
            modifyier_id   = #{modifyierId},
            modifyier_name = #{modifyierName},
            modify_time    = #{modifyTime}
        WHERE id = #{id}
    </update>

    <update id="delete">
        UPDATE qc_ai_agent_suit
        SET status = '0'
        WHERE id = #{id}
    </update>
</mapper>
