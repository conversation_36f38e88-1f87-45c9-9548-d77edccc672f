<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qc.agent.app.agent.mapper.InsightConfigStandardRelMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.qc.agent.app.agent.model.entity.InsightConfigStandardRel">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="config_id" property="configId" jdbcType="BIGINT"/>
        <result column="config_type" property="configType" jdbcType="VARCHAR"/>
        <result column="standard_id" property="standardId" jdbcType="BIGINT"/>
        <result column="creator_id" property="creatorId" jdbcType="BIGINT"/>
        <result column="creator_name" property="creatorName" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modifyier_id" property="modifyierId" jdbcType="BIGINT"/>
        <result column="modifyier_name" property="modifyierName" jdbcType="VARCHAR"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, status, config_id, config_type, standard_id,
        creator_id, creator_name, create_time, modifyier_id, modifyier_name, modify_time
    </sql>

    <!-- 根据ID查询 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM qc_ai_config_standard_rel
        WHERE id = #{id}
    </select>

    <!-- 根据配置ID和配置类型查询 -->
    <select id="selectByConfigIdAndType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM qc_ai_config_standard_rel
        WHERE config_id = #{configId} AND config_type = #{configType}
    </select>

    <!-- 根据配置ID列表和配置类型查询 -->
    <select id="selectByConfigIdsAndType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM qc_ai_config_standard_rel
        WHERE config_type = #{configType} AND config_id IN
        <foreach collection="configIds" item="configId" open="(" separator="," close=")">
            #{configId}
        </foreach>
    </select>

    <!-- 插入 -->
    <insert id="insert" parameterType="com.qc.agent.app.agent.model.entity.InsightConfigStandardRel" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO qc_ai_config_standard_rel (
            status, config_id, config_type, standard_id,
            creator_id, creator_name, create_time, modifyier_id, modifyier_name, modify_time
        ) VALUES (
            #{status}, #{configId}, #{configType}, #{standardId},
            #{creatorId}, #{creatorName}, #{createTime}, #{modifyierId}, #{modifyierName}, #{modifyTime}
        )
    </insert>

    <!-- 更新 -->
    <update id="updateById" parameterType="com.qc.agent.app.agent.model.entity.InsightConfigStandardRel">
        UPDATE qc_ai_config_standard_rel
        SET status = #{status},
            config_id = #{configId},
            config_type = #{configType},
            standard_id = #{standardId},
            modifyier_id = #{modifyierId},
            modifyier_name = #{modifyierName},
            modify_time = #{modifyTime}
        WHERE id = #{id}
    </update>

    <!-- 根据ID删除 -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM qc_ai_config_standard_rel WHERE id = #{id}
    </delete>

    <!-- 根据配置ID和配置类型删除 -->
    <delete id="deleteByConfigIdAndType">
        DELETE FROM qc_ai_config_standard_rel 
        WHERE config_id = #{configId} AND config_type = #{configType}
    </delete>

    <!-- 根据配置ID列表和配置类型批量删除 -->
    <delete id="deleteByConfigIdsAndType">
        DELETE FROM qc_ai_config_standard_rel 
        WHERE config_type = #{configType} AND config_id IN
        <foreach collection="configIds" item="configId" open="(" separator="," close=")">
            #{configId}
        </foreach>
    </delete>

    <!-- 根据配置ID和标准ID查询关系 -->
    <select id="selectByConfigIdAndStandardId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM qc_ai_config_standard_rel
        WHERE config_id = #{configId} AND standard_id = #{standardId}
    </select>

    <!-- 根据配置ID和标准ID删除关系 -->
    <delete id="deleteByConfigIdAndStandardId">
        DELETE FROM qc_ai_config_standard_rel 
        WHERE config_id = #{configId} AND standard_id = #{standardId}
    </delete>

    <!-- 根据配置ID和标准ID列表批量删除关系 -->
    <delete id="deleteByConfigIdAndStandardIds">
        DELETE FROM qc_ai_config_standard_rel 
        WHERE config_id = #{configId} AND standard_id IN
        <foreach collection="standardIds" item="standardId" open="(" separator="," close=")">
            #{standardId}
        </foreach>
    </delete>

</mapper> 