<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.qc.agent.app.workflow.inner.mapper.QcUploadFileMapper">

    <resultMap id="BaseResultMap" type="com.qc.agent.app.workflow.inner.pojo.QcUploadFile">
        <id column="id" property="id" />
        <result column="status" property="status" />
        <result column="create_user_id" property="createUserId" />
        <result column="create_user_name" property="createUserName" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
        <result column="modify_user_id" property="modifyUserId" />
        <result column="modify_user_name" property="modifyUserName" />
        <result column="name" property="name" />
        <result column="size" property="size" />
        <result column="platform" property="platform" />
        <result column="url" property="url" />
        <result column="base_path" property="basePath" />
        <result column="path" property="path" />
        <result column="ext" property="ext" />
    </resultMap>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT * FROM qc_upload_file WHERE id = #{id}
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        SELECT * FROM qc_upload_file
    </select>

    <insert id="insert" parameterType="com.qc.agent.app.workflow.inner.pojo.QcUploadFile">
        INSERT INTO qc_upload_file (
            id, status, create_user_id, create_user_name, create_time,
            modify_time, modify_user_id, modify_user_name, name, size,
            platform, url, base_path, path, ext
        ) VALUES (
                     #{id}, #{status}, #{createUserId}, #{createUserName}, #{createTime},
                     #{modifyTime}, #{modifyUserId}, #{modifyUserName}, #{name}, #{size},
                     #{platform}, #{url}, #{basePath}, #{path}, #{ext}
                 )
    </insert>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO qc_upload_file (
        id, status, create_user_id, create_user_name, create_time,
        modify_time, modify_user_id, modify_user_name, name, size,
        platform, url, base_path, path, ext
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id}, #{item.status}, #{item.createUserId}, #{item.createUserName}, #{item.createTime},
            #{item.modifyTime}, #{item.modifyUserId}, #{item.modifyUserName}, #{item.name}, #{item.size},
            #{item.platform}, #{item.url}, #{item.basePath}, #{item.path}, #{item.ext}
            )
        </foreach>
    </insert>


    <update id="update" parameterType="com.qc.agent.app.workflow.inner.pojo.QcUploadFile">
        UPDATE qc_upload_file
        SET
            status = #{status},
            create_user_id = #{createUserId},
            create_user_name = #{createUserName},
            create_time = #{createTime},
            modify_time = #{modifyTime},
            modify_user_id = #{modifyUserId},
            modify_user_name = #{modifyUserName},
            name = #{name},
            size = #{size},
            platform = #{platform},
            url = #{url},
            base_path = #{basePath},
            path = #{path},
            ext = #{ext}
        WHERE id = #{id}
    </update>

    <delete id="deleteById">
        DELETE FROM qc_upload_file WHERE id = #{id}
    </delete>

    <delete id="deleteByAgentId">
        DELETE FROM qc_upload_file WHERE id  IN (
            SELECT file_id FROM qc_ai_agent_intent_file_detail WHERE agent_id = #{agentId} AND status = #{status}
        )
    </delete>


</mapper>
