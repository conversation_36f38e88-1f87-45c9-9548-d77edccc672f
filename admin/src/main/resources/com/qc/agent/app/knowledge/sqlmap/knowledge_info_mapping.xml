<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.qc.agent.app.knowledge.mapper.KnowledgeInfoMapper">
    <resultMap type="com.qc.agent.app.knowledge.model.QcKnowledgeInfo" id="QcKnowledgeInfoResult">
        <result property="id" column="id"/>
        <result property="status" column="status"/>
        <result property="creatorId" column="creator_id"/>
        <result property="createTime" column="create_time"/>
        <result property="creatorName" column="creator_name"/>
        <result property="modifyierName" column="modifyier_name"/>
        <result property="modifyierId" column="modifyier_id"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="indexModel" column="index_model"/>
        <result property="maxTokens" column="max_tokens"/>
        <result property="knowledgeFace" column="knowledge_face"/>
        <result property="knowledgeName" column="knowledge_name"/>
        <result property="knowledgeDes" column="knowledge_des"/>
        <result property="publishFlag" column="publish_flag"/>
        <result property="publishTime" column="publish_time"/>
        <result property="publishUserId" column="publish_user_id"/>
        <result property="publishUserName" column="publish_user_name"/>
    </resultMap>

    <resultMap id="QcKnowledgeInfoResultExt" type="com.qc.agent.app.knowledge.model.QcKnowledgeInfoExt"
               extends="QcKnowledgeInfoResult">
        <result property="qaFileTotal" column="qa_file_total"/>
        <result property="rawFileTotal" column="raw_file_total"/>
        <result property="pictureFileTotal" column="picture_file_total"/>

    </resultMap>

    <sql id="selectQcKnowledgeInfoVo">
        select id,
               status,
               creator_id,
               creator_name,
               create_time,
               modifyier_id,
               modifyier_name,
               modify_time,
               index_model,
               max_tokens,
               knowledge_face,
               knowledge_name,
               knowledge_des,
               publish_flag,
               publish_time,
               publish_user_id,
               publish_user_name
        from qc_knowledge_info
    </sql>

    <select id="selectQcKnowledgeInfoList" parameterType="com.qc.agent.app.knowledge.model.QcKnowledgeInfoParams"
            resultMap="QcKnowledgeInfoResult">
        <include refid="selectQcKnowledgeInfoVo"/>
        <where>
            <if test="status != null  and status != ''">
                and status = #{status}
            </if>
            <if test="creatorId != null ">
                and creator_id = #{creatorId}
            </if>
            <if test="modifyierId != null ">
                and modifyier_id = #{modifyierId}
            </if>
            <if test="modifyTime != null ">
                and modify_time = #{modifyTime}
            </if>
            <if test="indexModel != null  and indexModel != ''">
                and index_model = #{indexModel}
            </if>
            <if test="maxTokens != null ">
                and max_tokens = #{maxTokens}
            </if>
            <if test="knowledgeFace != null  and knowledgeFace != ''">
                and knowledge_face = #{knowledgeFace}
            </if>
            <if test="knowledgeName != null  and knowledgeName != ''">
                and knowledge_name like concat('%', #{knowledgeName}, '%')
            </if>
            <if test="knowledgeDes != null  and knowledgeDes != ''">
                and knowledge_des = #{knowledgeDes}
            </if>
        </where>
    </select>

    <select id="selectByAgentId" resultMap="QcKnowledgeInfoResult">
        <include refid="selectQcKnowledgeInfoVo"/>
        WHERE id IN (
        SELECT knowledge_id
        FROM qc_ai_agent_knowledge
        WHERE agent_id = #{agentId}
        )
    </select>

    <select id="selectQcKnowledgeInfoExtList" resultMap="QcKnowledgeInfoResultExt">
        select
        t.id,
        t.status,
        t.creator_id,
        t.creator_name,
        t.create_time,
        t.modifyier_id,
        t.modifyier_name,
        t.modify_time,
        t.index_model,
        t.max_tokens,
        t.knowledge_face,
        t.knowledge_name,
        t.knowledge_des,
        sum(case when f.data_imp_type = 'QA' then 1 else 0 end) as qa_file_total,
        sum(case when f.data_imp_type = 'RAW' then 1 else 0 end) as raw_file_total
        from qc_knowledge_info t
        left join qc_knowledge_file f ON t.id = f.collection_id and f.file_status != '0'
        WHERE t.status = '1'
        <choose>
            <when test="searchFlag = '1'.toString()">
                AND t.creator_id = #{currentUserId}
            </when>
            <when test="searchFlag = '2'.toString()">
                AND t.creator_id = #{currentUserId}
            </when>
            <when test="searchFlag = '1'.toString()">
                AND t.creator_id = #{currentUserId}
            </when>
            <otherwise>
                AND 1=2
            </otherwise>
        </choose>
        group by
        t.id,
        t.status,
        t.creator_id,
        t.create_time,
        t.modifyier_id,
        t.modify_time,
        t.index_model,
        t.max_tokens,
        t.knowledge_face,
        t.knowledge_name,
        t.knowledge_des
        ORDER BY t.create_time DESC
    </select>

    <select id="selectMyQcKnowledgeInfoList" resultMap="QcKnowledgeInfoResultExt">
        select t.id,
               t.status,
               t.creator_id,
               t.creator_name,
               t.create_time,
               t.modifyier_id,
               t.modifyier_name,
               t.modify_time,
               t.publish_flag,
               t.publish_time,
               t.publish_user_id,
               t.publish_user_name,
               t.index_model,
               t.max_tokens,
               t.knowledge_face,
               t.knowledge_name,
               t.knowledge_des,
               sum(case when f.data_imp_type = 'QA' then 1 else 0 end)  as qa_file_total,
               sum(case when f.data_imp_type = 'RAW' then 1 else 0 end) as raw_file_total,
               sum(case when f.data_imp_type = 'PICTURE' then 1 else 0 end) as picture_file_total
        from qc_knowledge_info t
                 left join qc_knowledge_file f ON t.id = f.collection_id and f.file_status != '0'
        WHERE t.status = '1'
          AND t.creator_id = #{currentUserId}
        group by
            t.id,
            t.status,
            t.creator_id,
            t.creator_name,
            t.create_time,
            t.modifyier_id,
            t.modifyier_name,
            t.modify_time,
            t.publish_flag,
            t.publish_time,
            t.publish_user_id,
            t.publish_user_name,
            t.index_model,
            t.max_tokens,
            t.knowledge_face,
            t.knowledge_name,
            t.knowledge_des
        ORDER BY COALESCE (t.modify_time, t.create_time) DESC
    </select>

    <select id="selectPublicQcKnowledgeInfoList" resultMap="QcKnowledgeInfoResultExt">
        select
        t.id,
        t.status,
        t.creator_id,
        t.creator_name,
        t.create_time,
        t.modifyier_id,
        t.modifyier_name,
        t.modify_time,
        t.publish_flag,
        t.publish_time,
        t.publish_user_id,
        t.publish_user_name,
        t.index_model,
        t.max_tokens,
        t.knowledge_face,
        t.knowledge_name,
        t.knowledge_des,
        sum(case when f.data_imp_type = 'QA' then 1 else 0 end) as qa_file_total,
        sum(case when f.data_imp_type = 'RAW' then 1 else 0 end) as raw_file_total,
          sum(case when f.data_imp_type = 'PICTURE' then 1 else 0 end) as picture_file_total
        from qc_knowledge_info t
        left join qc_knowledge_file f ON t.id = f.collection_id and f.file_status != '0'
        WHERE t.status = '1'
        AND t.publish_flag = '1'
        AND t.creator_id != #{currentUserId}
        AND (
        EXISTS (
        SELECT 1 FROM qc_knowledge_info_range r WHERE r.knowledge_id = t.id AND r.range_type = '1'
        <choose>
            <when test="parentDeptIds != null and parentDeptIds.size() > 0">
                AND r.range_id IN
                <foreach collection="parentDeptIds" item="parentDeptId" open="(" close=")" separator=",">
                    #{parentDeptId}
                </foreach>
            </when>
            <otherwise>
                AND 1=2
            </otherwise>
        </choose>
        )
        OR
        EXISTS (
        SELECT 1 FROM qc_knowledge_info_range r WHERE r.knowledge_id = t.id AND r.range_type = '2'
        <choose>
            <when test="currentUserId != null">
                AND r.range_id = #{currentUserId}
            </when>
            <otherwise>
                AND 1=2
            </otherwise>
        </choose>
        )
        )
        group by
        t.id,
        t.status,
        t.creator_id,
        t.creator_name,
        t.create_time,
        t.modifyier_id,
        t.modifyier_name,
        t.modify_time,
        t.publish_flag,
        t.publish_time,
        t.publish_user_id,
        t.publish_user_name,
        t.index_model,
        t.max_tokens,
        t.knowledge_face,
        t.knowledge_name,
        t.knowledge_des
        ORDER BY t.publish_time DESC
    </select>

    <resultMap id="QcKnowledgeInfoResultMap" type="com.qc.agent.app.knowledge.model.QcKnowledgeInfoExt"
               extends="QcKnowledgeInfoResult">
        <result property="qaFileTotal" column="qa_file_total"/>
        <result property="rawFileTotal" column="raw_file_total"/>
        <collection property="categoryList" ofType="com.qc.agent.app.knowledge.model.QcKnowledgeFileCategory">
            <id property="id" column="category_id"/>
            <result property="categoryName" column="category_name"/>
        </collection>

    </resultMap>

    <select id="selectAgentQcKnowledgeInfoList" resultMap="QcKnowledgeInfoResultMap">
       with base_sql as (select
        t.id,
        t.status,
        t.creator_id,
        t.creator_name,
        t.create_time,
        t.modifyier_id,
        t.modifyier_name,
        t.modify_time,
        t.publish_flag,
        t.publish_time,
        t.publish_user_id,
        t.publish_user_name,
        t.index_model,
        t.max_tokens,
        t.knowledge_face,
        t.knowledge_name,
        t.knowledge_des,
        sum(case when f.data_imp_type = 'QA' then 1 else 0 end) as qa_file_total,
        sum(case when f.data_imp_type = 'RAW' then 1 else 0 end) as raw_file_total,
        sum(case when f.data_imp_type = 'PICTURE' then 1 else 0 end) as picture_file_total
        from qc_knowledge_info t
        left join qc_knowledge_file f ON t.id = f.collection_id and f.file_status != '0'
        WHERE t.status = '1'
        AND t.publish_flag = '1'
        AND (
        EXISTS (
        SELECT 1 FROM qc_knowledge_info_range r WHERE r.knowledge_id = t.id AND r.range_type = '1'
        <choose>
            <when test="parentDeptIds != null and parentDeptIds.size() > 0">
                AND r.range_id IN
                <foreach collection="parentDeptIds" item="parentDeptId" open="(" close=")" separator=",">
                    #{parentDeptId}
                </foreach>
            </when>
            <otherwise>
                AND 1=2
            </otherwise>
        </choose>
        )
        OR
        EXISTS (
        SELECT 1 FROM qc_knowledge_info_range r WHERE r.knowledge_id = t.id AND r.range_type = '2'
        <choose>
            <when test="currentUserId != null">
                AND r.range_id = #{currentUserId}
            </when>
            <otherwise>
                AND 1=2
            </otherwise>
        </choose>
        )
        )
        group by
        t.id,
        t.status,
        t.creator_id,
        t.creator_name,
        t.create_time,
        t.modifyier_id,
        t.modifyier_name,
        t.modify_time,
        t.publish_flag,
        t.publish_time,
        t.publish_user_id,
        t.publish_user_name,
        t.index_model,
        t.max_tokens,
        t.knowledge_face,
        t.knowledge_name,
        t.knowledge_des
        ) select
           base_sql.*,

           qkfc.id::VARCHAR as category_id,
           qkfc.category_name
          from base_sql
          left join qc_knowledge_file_category qkfc on qkfc.collection_id = base_sql.id
        ORDER BY base_sql.publish_time DESC
    </select>

    <select id="selectManageQcKnowledgeInfoList" resultMap="QcKnowledgeInfoResultExt">
        select t.id,
               t.status,
               t.creator_id,
               t.creator_name,
               t.create_time,
               t.modifyier_id,
               t.modifyier_name,
               t.modify_time,
               t.publish_flag,
               t.publish_time,
               t.publish_user_id,
               t.publish_user_name,
               t.index_model,
               t.max_tokens,
               t.knowledge_face,
               t.knowledge_name,
               t.knowledge_des,
               sum(case when f.data_imp_type = 'QA' then 1 else 0 end)  as qa_file_total,
               sum(case when f.data_imp_type = 'RAW' then 1 else 0 end) as raw_file_total,
                         sum(case when f.data_imp_type = 'PICTURE' then 1 else 0 end) as picture_file_total
        from qc_knowledge_info t
                 left join qc_knowledge_file f ON t.id = f.collection_id and f.file_status != '0'
        WHERE t.status = '1'
        group by
            t.id,
            t.status,
            t.creator_id,
            t.creator_name,
            t.create_time,
            t.modifyier_id,
            t.modifyier_name,
            t.modify_time,
            t.publish_flag,
            t.publish_time,
            t.publish_user_id,
            t.publish_user_name,
            t.index_model,
            t.max_tokens,
            t.knowledge_face,
            t.knowledge_name,
            t.knowledge_des
        ORDER BY COALESCE (t.modify_time, t.create_time) DESC
    </select>

    <select id="selectQcKnowledgeInfoById" parameterType="Long"
            resultMap="QcKnowledgeInfoResult">
        <include refid="selectQcKnowledgeInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertQcKnowledgeInfo" parameterType="com.qc.agent.app.knowledge.model.QcKnowledgeInfoParams">
        insert into qc_knowledge_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,
            </if>
            <if test="status != null">status,
            </if>
            <if test="creatorId != null">creator_id,
            </if>
            <if test="creatorName != null">creator_name,
            </if>
            <if test="createTime != null">create_time,
            </if>
            <if test="modifyierId != null">modifyier_id,
            </if>
            <if test="modifyierName != null">modifyier_name,
            </if>
            <if test="modifyTime != null">modify_time,
            </if>
            <if test="indexModel != null">index_model,
            </if>
            <if test="maxTokens != null">max_tokens,
            </if>
            <if test="knowledgeFace != null">knowledge_face,
            </if>
            <if test="knowledgeName != null">knowledge_name,
            </if>
            <if test="knowledgeDes != null">knowledge_des,
            </if>
            <if test="publishFlag != null">publish_flag,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},
            </if>
            <if test="status != null">#{status},
            </if>
            <if test="creatorId != null">#{creatorId},
            </if>
            <if test="creatorName != null">#{creatorName},
            </if>
            <if test="createTime != null">#{createTime},
            </if>
            <if test="modifyierId != null">#{modifyierId},
            </if>
            <if test="modifyierName != null">#{modifyierName},
            </if>
            <if test="modifyTime != null">#{modifyTime},
            </if>
            <if test="indexModel != null">#{indexModel},
            </if>
            <if test="maxTokens != null">#{maxTokens},
            </if>
            <if test="knowledgeFace != null">#{knowledgeFace},
            </if>
            <if test="knowledgeName != null">#{knowledgeName},
            </if>
            <if test="knowledgeDes != null">#{knowledgeDes},
            </if>
            <if test="publishFlag != null">#{publishFlag},
            </if>
        </trim>
    </insert>
    <insert id="insertQcKnowledgeCategory">
        insert into qc_knowledge_file_category (id,
                                                status,
                                                create_time,
                                                category_name,
                                                collection_id)
        values (#{id},
                '1',
                CURRENT_TIMESTAMP,
                #{name},
                #{collectionId}::bigint)
    </insert>

    <update id="updateQcKnowledgeInfo" parameterType="com.qc.agent.app.knowledge.model.QcKnowledgeInfoParams">
        update qc_knowledge_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="status != null">status =
                #{status},
            </if>
            <if test="creatorId != null">creator_id =
                #{creatorId},
            </if>
            <if test="creatorName != null">creator_name =
                #{creatorName},
            </if>
            <if test="createTime != null">create_time =
                #{createTime},
            </if>
            <if test="modifyierId != null">modifyier_id =
                #{modifyierId},
            </if>
            <if test="modifyierName != null">modifyier_name =
                #{modifyierName},
            </if>
            <if test="modifyTime != null">modify_time =
                #{modifyTime},
            </if>
            <if test="indexModel != null">index_model =
                #{indexModel},
            </if>
            <if test="maxTokens != null">max_tokens =
                #{maxTokens},
            </if>
            <if test="knowledgeFace != null">knowledge_face =
                #{knowledgeFace},
            </if>
            <if test="knowledgeName != null">knowledge_name =
                #{knowledgeName},
            </if>
            <if test="knowledgeDes != null">knowledge_des =
                #{knowledgeDes},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteQcKnowledgeInfoById" parameterType="Long">
        delete
        from qc_knowledge_info
        where id = #{id}
    </delete>

    <delete id="deleteQcKnowledgeInfoByIds" parameterType="String">
        delete from qc_knowledge_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteQcKnowledgeCategoryById">
        delete
        from qc_knowledge_file_category
        where id = #{id}::bigint and collection_id = #{collectionId}::bigint
    </delete>


    <select id="queryAllCollectionIds" resultType="java.lang.Long">
        SELECT id
        FROM qc_knowledge_info
        WHERE status = '1'
    </select>

    <select id="summaryQcKnowledgeQuestion" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM qc_knowledge_question
        WHERE question_content is not null
          AND answer_content is not null
    </select>

    <select id="summaryQcKnowledgeFile" resultType="java.lang.Long">
        SELECT
        COUNT(*)
        FROM qc_knowledge_file
        WHERE file_status != '0'
        <if test="dataImpType != null and dataImpType != ''">
            AND data_imp_type = #{dataImpType}
        </if>
    </select>

    <update id="enableKnowledgeInfo">
        update qc_knowledge_info
        set enable         = '1',
            modify_time    = CURRENT_TIMESTAMP,
            modifyier_id   = #{userId},
            modifyier_name = #{userName}
        WHERE id = #{id}
    </update>

    <update id="disableKnowledgeInfo">
        update qc_knowledge_info
        set enable         = '0',
            modify_time    = CURRENT_TIMESTAMP,
            modifyier_id   = #{userId},
            modifyier_name = #{userName}
        WHERE id = #{id}
    </update>

    <update id="publishKnowledgeInfo">
        update qc_knowledge_info
        set publish_flag      = '1',
            modify_time       = CURRENT_TIMESTAMP,
            modifyier_id      = #{userId},
            modifyier_name    = #{userName},
            publish_time      = CURRENT_TIMESTAMP,
            publish_user_id   = #{userId},
            publish_user_name = #{userName}
        WHERE id = #{id}
    </update>

    <update id="deactivateKnowledgeInfo">
        update qc_knowledge_info
        set publish_flag      = '0',
            modify_time       = CURRENT_TIMESTAMP,
            modifyier_id      = #{userId},
            modifyier_name    = #{userName},
            publish_time      = NULL,
            publish_user_id   = NULL,
            publish_user_name = NULL
        WHERE id = #{id}
    </update>
    <update id="updateQcKnowledgeCategory">
        update qc_knowledge_file_category
        set category_name = #{categoryName},
            modify_time   = CURRENT_TIMESTAMP
        where id = #{id}::bigint
          and collection_id = #{collectionId}::bigint
    </update>
    <update id="updateQcKnowledgeFileById">
        update qc_knowledge_file
        set category_id = null
        where id = #{id}::bigint
            and collection_id = #{collectionId}::bigint
    </update>


    <select id="selectFilterAuthorityKnowledgeList" resultType="java.lang.Long">
        select
        t.id
        from qc_knowledge_info t
        WHERE t.status = '1'
        AND t.publish_flag = '1'
        and t.id in
        <foreach collection="knowledgeIdList" item="knowledgeId" open="(" close=")" separator=",">
            #{knowledgeId}
        </foreach>
        AND (
        EXISTS (
        SELECT 1 FROM qc_knowledge_info_range r WHERE r.knowledge_id = t.id AND r.range_type = '1'
        <choose>
            <when test="deptIdList != null and deptIdList.size() > 0">
                AND r.range_id IN
                <foreach collection="deptIdList" item="deptId" open="(" close=")" separator=",">
                    #{deptId}
                </foreach>
            </when>
            <otherwise>
                AND 1=2
            </otherwise>
        </choose>
        )
        OR
        EXISTS (
        SELECT 1 FROM qc_knowledge_info_range r WHERE r.knowledge_id = t.id AND r.range_type = '2'
        <choose>
            <when test="userId != null">
                AND r.range_id = #{userId}
            </when>
            <otherwise>
                AND 1=2
            </otherwise>
        </choose>
        )
        )
    </select>

    <select id="queryCategorySetting" resultType="com.qc.agent.app.knowledge.model.QcKnowledgeFileCategory">
        select id,
               category_name as "categoryName",
               description,
               create_time   as "createTime",
               modify_time   as "modifyTime"
        from qc_knowledge_file_category
        WHERE status = '1'
          and collection_id = #{collectionId}::bigint
        order by "createTime"
    </select>

    <select id="selectFileCategoryByName" resultType="com.qc.agent.app.knowledge.model.QcKnowledgeFileCategory">
        select id,
               category_name as "categoryName",
               description,
               create_time   as "createTime",
               modify_time   as "modifyTime"
        from qc_knowledge_file_category
        WHERE status = '1'
          and category_name = #{categoryName}
            and collection_id = #{collectionId}::bigint
    </select>
    <select id="checkCategorySetting" resultType="java.lang.Integer">
        select count(1)
        from qc_knowledge_file_category
        WHERE status = '1'
          and category_name = #{name}
            and collection_id = #{collectionId}::bigint
    </select>
    <select id="selectQcKnowledgeFileByCategoryIds"
            resultType="com.qc.agent.app.knowledge.model.QcKnowledgeFile">
        select qkf.id,
               qkf.file_name as "fileName",
               qkf.file_type as "fileType",
               qkf.file_size as "fileSize",
               qkf.file_status as "fileStatus",
               qkf.data_imp_type as "dataImpType",
               qkf.create_time as "createTime",
               qkf.modify_time as "modifyTime",
               qkf.category_id as "categoryId",
               qkfc.category_name as "categoryName"
        from qc_knowledge_file qkf
        left join qc_knowledge_file_category qkfc on qkf.category_id = qkfc.id
        where qkf.collection_id = #{collectionId}::bigint
        and qkf.file_status != '0'
        <if test="ids != null and ids.size() > 0">
            and qkf.category_id in
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}::bigint
            </foreach>
        </if>
    </select>
    <select id="selectAgentsByKnowledgeId" resultType="com.qc.agent.app.agent.model.po.QcAiAgent">
        select * from
        qc_ai_agent
        where status = '1' and
            id in (
            select agent_id from qc_ai_agent_knowledge
            where status = '1' and
             knowledge_id = #{knowledgeId}
        )
    </select>

</mapper>
