<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.qc.agent.app.knowledge.mapper.QcKnowledgeInfoRangeMapper">

    <resultMap id="QcKnowledgeInfoRangeMap" type="com.qc.agent.app.knowledge.model.QcKnowledgeInfoRange">
        <id column="id" property="id"/>
        <result column="status" property="status"/>
        <result column="creator_id" property="creatorId"/>
        <result column="creator_name" property="creatorName"/>
        <result column="create_time" property="createTime"/>
        <result column="modifyier_id" property="modifyierId"/>
        <result column="modifyier_name" property="modifyierName"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="range_id" property="rangeId"/>
        <result column="range_name" property="rangeName"/>
        <result column="range_type" property="rangeType"/>
        <result column="knowledge_id" property="knowledgeId"/>
    </resultMap>

    <select id="getById" resultMap="QcKnowledgeInfoRangeMap">
        SELECT *
        FROM public.qc_knowledge_info_range
        WHERE id = #{id}
    </select>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO public.qc_knowledge_info_range (id, status, creator_id, creator_name, create_time,
                                                    modifyier_id, modifyier_name, modify_time, range_id, range_name,
                                                    range_type, knowledge_id)
        VALUES (#{id}, #{status}, #{creatorId}, #{creatorName}, #{createTime}, #{modifyierId},
                #{modifyierName}, #{modifyTime}, #{rangeId}, #{rangeName}, #{rangeType}, #{knowledgeId})
    </insert>

    <insert id="insertBatch">
        INSERT INTO qc_knowledge_info_range (id, status, creator_id, creator_name, create_time, range_id, range_name,range_type, knowledge_id)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.status}, #{item.creatorId}, #{item.creatorName}, #{item.createTime}, #{item.rangeId}, #{item.rangeName}, #{item.rangeType}, #{item.knowledgeId})
        </foreach>
    </insert>

    <update id="update">
        UPDATE public.qc_knowledge_info_range
        SET status         = #{status},
            creator_id     = #{creatorId},
            creator_name   = #{creatorName},
            create_time    = #{createTime},
            modifyier_id   = #{modifyierId},
            modifyier_name = #{modifyierName},
            modify_time    = #{modifyTime},
            range_id       = #{rangeId},
            range_name     = #{rangeName},
            range_type     = #{rangeType}
        WHERE id = #{id}
    </update>

    <delete id="delete">
        DELETE
        FROM public.qc_knowledge_info_range
        WHERE id = #{id}
    </delete>

    <delete id="deleteByKnowledgeId">
        DELETE
        FROM public.qc_knowledge_info_range
        WHERE knowledge_id = #{knowledgeId}
    </delete>

    <select id="getByKnowledgeId" resultType="com.qc.agent.app.knowledge.model.KnowledgeRange">
        SELECT
            range_id AS id,
            range_name AS name
            FROM qc_knowledge_info_range
        WHERE knowledge_id = #{knowledgeId}
          AND range_type = #{rangeType}
    </select>


</mapper>
