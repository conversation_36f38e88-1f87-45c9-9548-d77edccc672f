<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.qc.agent.app.knowledge.mapper.KnowledgeFileMapper">

    <resultMap type="com.qc.agent.app.knowledge.model.QcKnowledgeFile" id="QcKnowledgeFileResult">
        <result property="id" column="id"/>
        <result property="fileName" column="id"/>
        <result property="fileUrl" column="File_name"/>
        <result property="folderId" column="File_comments"/>
    </resultMap>

    <sql id="selectQcKnowledgeFileVo">
        select *
        from qc_knowledge_file t
    </sql>




    <insert id="insertQcKnowledgeFile" parameterType="com.qc.agent.app.knowledge.model.QcKnowledgeFileParams">
        insert into qc_knowledge_file
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,
            </if>
            <if test="folderId != null">folder_id,
            </if>
            <if test="fileName != null">file_name,origin_name,
            </if>
            <if test="fileComments != null">file_comments,
            </if>
            <if test="fileLogo != null">file_logo,
            </if>
            <if test="fileUrl != null">file_url,
            </if>
            <if test="fileSize != null">file_size,
            </if>
            <if test="fileStatus != null">file_status,
            </if>
            <if test="fileType != null">file_type,
            </if>
            <if test="createUserId != null">create_user_id,
            </if>
            <if test="createUserName != null">create_user_name,
            </if>
            <if test="modifyUserId != null">modify_user_id,
            </if>
            <if test="modifyUserName != null">modify_user_name,
            </if>
            <if test="viewCount != null">view_count,
            </if>
            <if test="downCount != null">down_count,
            </if>
            <if test="databaseId != null">database_id,
            </if>
            <if test="collectionId != null">collection_id,
            </if>
            <if test="createTime != null">create_time,
            </if>
            <if test="modifyTime != null">modify_time,
            </if>
            <if test="dataImpType != null">data_imp_type,
            </if>
            <if test="categoryId != null">category_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},
            </if>
            <if test="folderId != null">#{folderId},
            </if>
            <if test="fileName != null">#{fileName},#{fileName},
            </if>
            <if test="fileComments != null">#{fileComments},
            </if>
            <if test="fileLogo != null">#{fileLogo},
            </if>
            <if test="fileUrl != null">#{fileUrl},
            </if>
            <if test="fileSize != null">#{fileSize}::NUMERIC,
            </if>
            <if test="fileStatus != null">#{fileStatus},
            </if>
            <if test="fileType != null">#{fileType},
            </if>
            <if test="createUserId != null">#{createUserId},
            </if>
            <if test="createUserName != null">#{createUserName},
            </if>
            <if test="modifyUserId != null">#{modifyUserId},
            </if>
            <if test="modifyUserName != null">#{modifyUserName},
            </if>
            <if test="viewCount != null">#{viewCount}::NUMERIC,
            </if>
            <if test="downCount != null">#{downCount}::NUMERIC,
            </if>
            <if test="databaseId != null">#{databaseId},
            </if>
            <if test="collectionId != null">#{collectionId},
            </if>
            <if test="createTime != null">#{createTime},
            </if>
            <if test="modifyTime != null">#{modifyTime},
            </if>
            <if test="dataImpType != null">#{dataImpType},
            </if>
            <if test="categoryId != null">#{categoryId}::BIGINT,
            </if>
        </trim>
    </insert>

    <delete id="deleteQcKnowledgeFileById" parameterType="Long">
        delete from qc_knowledge_file where id = #{id}
    </delete>

    <delete id="deleteQcKnowledgeFileByFolderId" parameterType="Long">
        delete from qc_knowledge_file where folder_id = #{folderId}
    </delete>

    <delete id="deleteQcKnowledgeFileByIds" parameterType="String">
        delete from qc_knowledge_file where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <sql id="folderRecursive">
        WITH RECURSIVE org_tree AS (
            SELECT
                id
            FROM
                qc_knowledge_folder
            WHERE id = #{folderId, jdbcType=BIGINT}

            UNION ALL

            SELECT
                t.id
            FROM
                qc_knowledge_folder t
            JOIN org_tree ot ON t.parent_id = ot.id
        )
    </sql>

    <select id="countFile" resultType="java.lang.Integer">
        SELECT
            COUNT(*)
        FROM qc_knowledge_file
        WHERE file_status != '0'
        <if test="collectionId != null and collectionId != ''">
            AND collection_Id = #{collectionId}
        </if>
        <if test="databaseId != null and databaseId != ''">
            AND database_Id = #{databaseId}
        </if>
        <if test="fileType != null and fileType != ''">
            AND data_imp_type = #{fileType}
        </if>
        <if test="fileStatuses != null and fileStatuses.size() > 0">
            AND file_status in
            <foreach collection="fileStatuses" item="fileStatus" open="(" close=")" separator=",">
                #{fileStatus}
            </foreach>
        </if>
        <if test="categoryId != null and categoryId.size() > 0">
            AND category_id in
            <foreach collection="categoryIds" item="categoryId" open="(" close=")" separator=",">
                #{categoryId}::bigint
            </foreach>
        </if>
        <choose>
            <when test="fileNames != null and fileNames.size() > 0">
                AND (
                    file_name IN
                    <foreach collection="fileNames" item="fileName" open="(" close=")" separator=",">
                        #{fileName}
                    </foreach>
                    OR file_name LIKE concat('%', #{fileName}, '%')
                )
            </when>
            <otherwise>
                <if test="fileName != null and fileName != ''">
                    AND file_name LIKE concat('%', #{fileName}, '%')
                </if>
            </otherwise>
        </choose>
    </select>

    <select id="queryFileList" resultType="com.qc.agent.app.knowledge.model.QcKnowledgeFile">
        SELECT
            qkf.id,
            qkf.file_name        AS "fileName",
            qkf.origin_name     AS "originName",
            qkf.file_url         AS "fileUrl",
            qkf.view_count       AS "viewCount",
            qkf.create_user_name AS "createUserName",
            qkf.create_time      AS "createTime",
            CASE
            WHEN qkf.file_size >= 1024 * 1024 * 1024 THEN ROUND(qkf.file_size / (1024.0 * 1024 * 1024), 2) || 'GB'
            WHEN qkf.file_size >= 1024 * 1024 THEN ROUND(qkf.file_size / (1024.0 * 1024), 2) || 'MB'
            WHEN qkf.file_size >= 1024 THEN ROUND(qkf.file_size / 1024.0, 2) || 'KB'
            ELSE qkf.file_size || 'B' end as "fileSize",
            qkf.collection_id        AS "collectionId",
            qkf.database_Id        AS "databaseId",
            qkf.file_status      AS "fileStatus",
            qkf.data_imp_type    AS "dataImpType",
            qkfc.category_name AS "categoryName",
            qkfc.id as "categoryId"
        FROM qc_knowledge_file qkf
        left join qc_knowledge_file_category qkfc on qkf.category_id = qkfc.id and qkfc.status = '1'
        WHERE qkf.file_status != '0'
        <choose>
            <when test="fileNames != null and fileNames.size() > 0">
                AND (
                qkf.file_name IN
                <foreach collection="fileNames" item="fileName" open="(" close=")" separator=",">
                    #{fileName}
                </foreach>
                OR qkf.file_name LIKE concat('%', #{fileName}, '%')
                )
            </when>
            <otherwise>
                <if test="fileName != null and fileName != ''">
                    AND qkf.file_name LIKE concat('%', #{fileName}, '%')
                </if>
            </otherwise>
        </choose>
        <if test="fileStatuses != null and fileStatuses.size() > 0">
            AND qkf.file_status in
            <foreach collection="fileStatuses" item="fileStatus" open="(" close=")" separator=",">
                #{fileStatus}
            </foreach>
        </if>
        <if test="categoryIds != null and categoryIds.size() > 0">
            AND qkf.category_id in
            <foreach collection="categoryIds" item="categoryId" open="(" close=")" separator=",">
                #{categoryId}::bigint
            </foreach>
        </if>
        <if test="collectionId != null and collectionId != ''">
            AND qkf.collection_id = #{collectionId}
        </if>
        <if test="databaseId != null and databaseId != ''">
            AND qkf.database_Id = #{databaseId}
        </if>
        <if test="fileType != null and fileType != ''">
            AND qkf.data_imp_type = #{fileType}
        </if>
        ORDER BY qkf.create_time DESC
        <if test="limit != null and offset != null">
            LIMIT ${limit} OFFSET ${offset}
        </if>
    </select>


    <select id="countByFileNameAndCollectionId" resultType="java.lang.Integer">
        SELECT
            COUNT(*)
        FROM qc_knowledge_file
        WHERE file_status != '0'
        AND file_name = #{fileName}
        AND collection_id = #{collectionId}
    </select>

    <select id="queryById" resultType="com.qc.agent.app.knowledge.model.QcKnowledgeFile">
        SELECT
            id,
            file_name        AS "fileName",
            origin_name     AS "originName",
            file_url         AS "fileUrl",
            view_count       AS "viewCount",
            create_user_name AS "createUserName",
            create_time      AS "createTime",
            file_size        AS "fileSize",
            category_id       AS "categoryId",
            collection_Id        AS "collectionId",
            database_Id        AS "databaseId",
            file_status      AS "fileStatus",
            data_imp_type    AS "dataImpType"
        FROM qc_knowledge_file
        WHERE id = #{id, jdbcType=BIGINT}
    </select>

    <update id="updateFileStatus">
        UPDATE qc_knowledge_file
        SET file_status = #{fileStatus, jdbcType=VARCHAR}
        <if test="fileName != null and fileName != ''">
            , file_name = #{fileName, jdbcType=VARCHAR}
        </if>
        <if test="categoryId != null and categoryId != ''">
            , category_id = #{categoryId}::BIGINT
        </if>
        <if test="'QA'.equals(dataImpType) and fileName != null and fileName != ''">
            , origin_name = #{fileName, jdbcType=VARCHAR}
        </if>
        WHERE id = #{id, jdbcType=BIGINT}
    </update>

    <select id="queryFileNameByDatabaseId" resultType="com.qc.agent.app.knowledge.model.QcKnowledgeFile">
        SELECT id,
               file_name        AS "fileName",
               origin_name     AS "originName",
               file_url         AS "fileUrl",
               view_count       AS "viewCount",
               create_user_name AS "createUserName",
               create_time      AS "createTime",
               file_size        AS "fileSize",
               collection_Id        AS "collectionId",
               database_Id        AS "databaseId",
               file_status      AS "fileStatus",
               data_imp_type    AS "dataImpType"
        FROM qc_knowledge_file
        WHERE collection_id = #{id, jdbcType=BIGINT} and file_status = '1'
    </select>

    <select id="queryFileNameByOSSUrl" resultType="java.lang.String">
        SELECT file_name
        FROM qc_knowledge_file
        WHERE file_url = #{ossUrl, jdbcType=VARCHAR}
          AND file_status = '1'
        ORDER BY create_time DESC
        LIMIT 1
    </select>

    <update id="deleteByFileName">
        UPDATE qc_knowledge_file
        SET file_status = '0'
        WHERE file_name = #{fileName, jdbcType=VARCHAR}
        and collection_id = #{collectionId, jdbcType=BIGINT}
    </update>

    <update id="updateViewCount">
        UPDATE qc_knowledge_file
        set view_count = #{count}
        where id = #{id}
    </update>

    <update id="updateFile">
        UPDATE qc_knowledge_file
        <set>
            <if test="viewCount != null">
                view_count = #{viewCount}::numeric,
            </if>
            <if test="fileSize != null">
                file_size = #{fileSize}::numeric,
            </if>
            <if test="fileStatus != null">
                file_status = #{fileStatus},
            </if>
        </set>
        WHERE id = #{id, jdbcType=BIGINT}
    </update>

    <select id="findAllFiles" resultType="com.qc.agent.app.knowledge.model.QcKnowledgeFile">
        SELECT
            id,
            folder_id AS "folderId",
            file_name AS "fileName",
            origin_name     AS "originName",
            file_url  AS "fileUrl",
            file_size AS "fileSize",
            file_status AS "fileStatus",
            file_type AS "fileType",
            view_count AS "viewCount",
            collection_id AS "collectionId",
            data_imp_type AS "dataImpType"
        FROM qc_knowledge_file
        WHERE file_status = '1'
    </select>
    <select id="queryByNameAndCollectionId" resultType="com.qc.agent.app.knowledge.model.QcKnowledgeFile">
        SELECT
            id,
            folder_id AS "folderId",
            file_name AS "fileName",
            origin_name     AS "originName",
            file_url  AS "fileUrl",
            file_size AS "fileSize",
            file_status AS "fileStatus",
            file_type AS "fileType",
            view_count AS "viewCount",
            collection_id AS "collectionId",
            data_imp_type AS "dataImpType"
        FROM qc_knowledge_file
        WHERE file_status = '1'
          AND file_name = #{fileName}
          AND collection_id = #{collectionId}
    </select>

    <!-- batchInsert --> 
    <insert id="batchInsert" parameterType="java.util.List">
    INSERT INTO qc_knowledge_file (
        id, folder_id, file_name, origin_name, file_comments, file_logo,
        file_url, file_size, file_status, file_type,
        create_user_id, create_user_name, modify_user_id, modify_user_name,
        view_count, down_count, database_id, collection_id,
        create_time, modify_time, data_imp_type, category_id
    )
    VALUES
    <foreach collection="list" item="item" separator=",">
        (
            #{item.id},
            #{item.folderId},
            #{item.fileName},
            #{item.fileName},  <!-- origin_name 与 file_name 同 -->
            #{item.fileComments},
            #{item.fileLogo},
            #{item.fileUrl},
            #{item.fileSize}::NUMERIC,
            #{item.fileStatus},
            #{item.fileType},
            #{item.createUserId},
            #{item.createUserName},
            #{item.modifyUserId},
            #{item.modifyUserName},
            #{item.viewCount}::NUMERIC,
            #{item.downCount}::NUMERIC,
            #{item.databaseId},
            #{item.collectionId},
            #{item.createTime},
            #{item.modifyTime},
            #{item.dataImpType},
            #{item.categoryId}::BIGINT
        )
    </foreach>
</insert>

    </mapper>
