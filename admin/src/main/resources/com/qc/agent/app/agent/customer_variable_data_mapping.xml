<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qc.agent.app.agent.mapper.CustomerVariableDataMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.qc.agent.app.agent.model.entity.CustomerVariableData">
        <id column="id" property="id"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="customer_id" property="customerId"/>
        <result column="prompt_variable_record_id" property="promptVariableRecordId"/>
        <result column="conversation_id" property="conversationId"/>
        <result column="variable_value" property="variableValue"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, status, create_time, modify_time, customer_id, prompt_variable_record_id, conversation_id, variable_value
    </sql>

    <!-- 根据客户ID查询变量数据列表 -->
    <select id="selectByCustomerId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM qc_ai_customer_variable_data
        WHERE customer_id = #{customerId}
        AND status = '1'
        ORDER BY create_time DESC
    </select>

    <!-- 根据对话ID查询变量数据列表 -->
    <select id="selectByConversationId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM qc_ai_customer_variable_data
        WHERE conversation_id = #{conversationId}
        AND status = '1'
        ORDER BY prompt_variable_record_id, id
    </select>

    <!-- 根据客户ID和对话ID查询变量数据列表 -->
    <select id="selectByCustomerIdAndConversationId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM qc_ai_customer_variable_data
        WHERE customer_id = #{customerId}
        AND conversation_id = #{conversationId}
        AND status = '1'
        ORDER BY prompt_variable_record_id, id
    </select>

    <!-- 根据提示词变量记录ID查询变量数据列表 -->
    <select id="selectByPromptVariableRecordId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM qc_ai_customer_variable_data
        WHERE prompt_variable_record_id = #{promptVariableRecordId}
        AND status = '1'
        ORDER BY create_time DESC
    </select>

    <!-- 插入变量数据 -->
    <insert id="insert" parameterType="com.qc.agent.app.agent.model.entity.CustomerVariableData">
        INSERT INTO qc_ai_customer_variable_data (
            status, create_time, customer_id, prompt_variable_record_id, conversation_id, variable_value
        )
        VALUES (
            #{status}, #{createTime}, #{customerId}, #{promptVariableRecordId}, #{conversationId}, #{variableValue}
        )
    </insert>

    <!-- 批量插入变量数据 -->
    <insert id="batchInsert">
        INSERT INTO qc_ai_customer_variable_data (
            status, create_time, customer_id, prompt_variable_record_id, conversation_id, variable_value
        )
        VALUES
        <foreach collection="customerVariableDataList" item="item" separator=",">
            (
                #{item.status}, #{item.createTime}, #{item.customerId}, #{item.promptVariableRecordId}, 
                #{item.conversationId}, #{item.variableValue}
            )
        </foreach>
    </insert>

    <!-- 根据ID更新变量数据 -->
    <update id="updateById" parameterType="com.qc.agent.app.agent.model.entity.CustomerVariableData">
        UPDATE qc_ai_customer_variable_data
        SET status = #{status},
            modify_time = #{modifyTime},
            customer_id = #{customerId},
            prompt_variable_record_id = #{promptVariableRecordId},
            conversation_id = #{conversationId},
            variable_value = #{variableValue}
        WHERE id = #{id}
    </update>

    <!-- 根据客户ID删除变量数据 -->
    <delete id="deleteByCustomerId">
        UPDATE qc_ai_customer_variable_data
        SET status = '0', modify_time = CURRENT_TIMESTAMP
        WHERE customer_id = #{customerId}
    </delete>

    <!-- 根据对话ID删除变量数据 -->
    <delete id="deleteByConversationId">
        UPDATE qc_ai_customer_variable_data
        SET status = '0', modify_time = CURRENT_TIMESTAMP
        WHERE conversation_id = #{conversationId}
    </delete>

    <!-- 根据客户ID和对话ID删除变量数据 -->
    <delete id="deleteByCustomerIdAndConversationId">
        UPDATE qc_ai_customer_variable_data
        SET status = '0', modify_time = CURRENT_TIMESTAMP
        WHERE customer_id = #{customerId}
        AND conversation_id = #{conversationId}
    </delete>

</mapper>
