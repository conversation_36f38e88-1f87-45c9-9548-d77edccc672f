<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qc.agent.app.agent.mapper.MainPromptMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.qc.agent.app.agent.model.entity.MainPrompt">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="status" property="status" jdbcType="CHAR"/>
        <result column="prompt_name" property="promptName" jdbcType="VARCHAR"/>
        <result column="prompt_type" property="promptType" jdbcType="VARCHAR"/>
        <result column="dimension_code" property="dimensionCode" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, status, prompt_name, prompt_type, dimension_code
    </sql>

    <!-- 根据ID查询 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM qc_ai_main_prompt
        WHERE id = #{id,jdbcType=BIGINT} AND status = '1'
    </select>

    <!-- 查询所有有效记录 -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM qc_ai_main_prompt
        WHERE status = '1'
        ORDER BY id DESC
    </select>

    <!-- 插入记录 -->
    <insert id="insert" parameterType="com.qc.agent.app.agent.model.entity.MainPrompt" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO qc_ai_main_prompt (
            status, prompt_name, prompt_type, dimension_code
        ) VALUES (
            #{status,jdbcType=CHAR}, #{promptName,jdbcType=VARCHAR}, 
            #{promptType,jdbcType=VARCHAR}, #{dimensionCode,jdbcType=VARCHAR}
        )
    </insert>

    <select id="selectByDimensionCodeAndType" resultMap="BaseResultMap">
        SELECT * FROM qc_ai_main_prompt WHERE dimension_code = #{dimensionCode} AND prompt_type = #{promptType} AND status = '1'
    </select>

    <!-- 根据ID更新 -->
    <update id="updateById" parameterType="com.qc.agent.app.agent.model.entity.MainPrompt">
        UPDATE qc_ai_main_prompt
        SET status = #{status,jdbcType=CHAR},
            prompt_name = #{promptName,jdbcType=VARCHAR},
            prompt_type = #{promptType,jdbcType=VARCHAR},
            dimension_code = #{dimensionCode,jdbcType=VARCHAR}
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 根据ID删除（逻辑删除） -->
    <update id="deleteById" parameterType="java.lang.Long">
        UPDATE qc_ai_main_prompt
        SET status = '0'
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 根据配置ID删除（逻辑删除） -->
    <update id="deleteByDimensionCode" parameterType="java.lang.Long">
        UPDATE qc_ai_main_prompt
        SET status = '0'
        WHERE dimension_code = #{dimensionCode,jdbcType=VARCHAR}
    </update>

</mapper> 