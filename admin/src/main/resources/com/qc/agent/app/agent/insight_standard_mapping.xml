<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qc.agent.app.agent.mapper.InsightStandardMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.qc.agent.app.agent.model.entity.InsightStandard">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="standard_name" property="standardName" jdbcType="VARCHAR"/>
        <result column="standard_definition" property="standardDefinition" jdbcType="VARCHAR"/>
        <result column="standard_type_code" property="standardTypeCode" jdbcType="VARCHAR"/>
        <result column="standard_type" property="standardType" jdbcType="VARCHAR"/>
        <result column="sort_order" property="sortOrder" jdbcType="INTEGER"/>
        <result column="creator_id" property="creatorId" jdbcType="BIGINT"/>
        <result column="creator_name" property="creatorName" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modifyier_id" property="modifyierId" jdbcType="BIGINT"/>
        <result column="modifyier_name" property="modifyierName" jdbcType="VARCHAR"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, status, standard_name, standard_definition, standard_type_code, standard_type, sort_order,
        creator_id, creator_name, create_time, modifyier_id, modifyier_name, modify_time
    </sql>

    <!-- 根据ID查询 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM qc_ai_measurement_standard
        WHERE id = #{id}
    </select>

    <!-- 根据ID列表批量查询 -->
    <select id="selectBatchIds" parameterType="java.util.List" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM qc_ai_measurement_standard
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        ORDER BY sort_order ASC
    </select>

    <!-- 查询所有 -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM qc_ai_measurement_standard
        ORDER BY sort_order ASC
    </select>

    <!-- 根据状态查询 -->
    <select id="selectByStatus" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM qc_ai_measurement_standard
        WHERE status = #{status}
        ORDER BY sort_order ASC
    </select>

    <!-- 插入 -->
    <insert id="insert" parameterType="com.qc.agent.app.agent.model.entity.InsightStandard" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO qc_ai_measurement_standard (
            status, standard_name, standard_definition, standard_type_code, standard_type, sort_order,
            creator_id, creator_name, create_time, modifyier_id, modifyier_name, modify_time
        ) VALUES (
            #{status}, #{standardName}, #{standardDefinition}, #{standardTypeCode}, #{standardType}, #{sortOrder},
            #{creatorId}, #{creatorName}, #{createTime}, #{modifyierId}, #{modifyierName}, #{modifyTime}
        )
    </insert>

    <!-- 更新 -->
    <update id="updateById" parameterType="com.qc.agent.app.agent.model.entity.InsightStandard">
        UPDATE qc_ai_measurement_standard
        SET status = #{status},
            standard_name = #{standardName},
            standard_definition = #{standardDefinition},
            standard_type_code = #{standardTypeCode},
            standard_type = #{standardType},
            sort_order = #{sortOrder},
            modifyier_id = #{modifyierId},
            modifyier_name = #{modifyierName},
            modify_time = #{modifyTime}
        WHERE id = #{id}
    </update>

    <!-- 更新标准信息（不更新类型相关字段） -->
    <update id="updateStandardInfo" parameterType="com.qc.agent.app.agent.model.entity.InsightStandard">
        UPDATE qc_ai_measurement_standard
        <set>
            <if test="standardName != null">standard_name = #{standardName},</if>
            <if test="standardDefinition != null">standard_definition = #{standardDefinition},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="modifyierId != null">modifyier_id = #{modifyierId},</if>
            <if test="modifyierName != null">modifyier_name = #{modifyierName},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据ID删除 -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM qc_ai_measurement_standard WHERE id = #{id}
    </delete>

    <!-- 根据ID列表批量删除 -->
    <delete id="deleteBatchIds" parameterType="java.util.List">
        DELETE FROM qc_ai_measurement_standard WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper> 