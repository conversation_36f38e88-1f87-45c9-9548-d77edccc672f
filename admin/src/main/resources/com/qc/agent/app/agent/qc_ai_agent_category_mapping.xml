<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.qc.agent.app.agent.mapper.QcAiAgentCategoryMapper">

    <insert id="insert" parameterType="com.qc.agent.app.agent.model.po.QcAiAgentCategory">
        INSERT INTO qc_ai_agent_category (id, status, creator_id, create_time, modifyier_id, modify_time, name)
        VALUES (#{id}, #{status}, #{creatorId}, #{createTime}, #{modifyierId}, #{modifyTime}, #{name})
    </insert>

    <delete id="deleteById">
        UPDATE qc_ai_agent_category
        SET status = '0'
        WHERE id = #{id}
    </delete>

    <update id="update" parameterType="com.qc.agent.app.agent.model.po.QcAiAgentCategory">
        UPDATE qc_ai_agent_category
        SET status       = #{status},
            creator_id   = #{creatorId},
            modifyier_id = #{modifyierId},
            modify_time  = #{modifyTime},
            name         = #{name}
        WHERE id = #{id}
    </update>

    <select id="selectById" resultType="com.qc.agent.app.agent.model.po.QcAiAgentCategory">
        SELECT *
        FROM qc_ai_agent_category
        WHERE id = #{id}
    </select>

    <select id="selectAll" resultType="com.qc.agent.app.agent.model.po.QcAiAgentCategory">
        SELECT *
        FROM qc_ai_agent_category
        WHERE status = '1'
    </select>

</mapper>
