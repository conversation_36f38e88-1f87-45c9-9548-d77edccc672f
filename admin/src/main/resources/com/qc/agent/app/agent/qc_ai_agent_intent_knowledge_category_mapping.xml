<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.qc.agent.app.workflow.inner.mapper.QcAiAgentIntentKnowledgeCategoryMappingMapper">

    <resultMap id="BaseResultMap" type="com.qc.agent.app.workflow.inner.pojo.QcAiAgentIntentKnowledgeCategoryMapping">
        <id property="id" column="id"/>
        <result property="collectionId" column="collection_id"/>
        <result property="categoryId" column="category_id"/>
    </resultMap>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT * FROM qc_ai_agent_Intent_knowledge_category_mapping WHERE id = #{id}
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        SELECT * FROM qc_ai_agent_Intent_knowledge_category_mapping
    </select>

    <insert id="insert" parameterType="com.qc.agent.app.workflow.inner.pojo.QcAiAgentIntentKnowledgeCategoryMapping">
        INSERT INTO qc_ai_agent_Intent_knowledge_category_mapping
            (collection_id, category_id)
        VALUES
            (#{collectionId}, #{categoryId})
    </insert>

    <update id="update" parameterType="com.qc.agent.app.workflow.inner.pojo.QcAiAgentIntentKnowledgeCategoryMapping">
        UPDATE qc_ai_agent_Intent_knowledge_category_mapping
        SET collection_id = #{collectionId},
            category_id = #{categoryId}
        WHERE id = #{id}
    </update>

    <delete id="deleteById">
        DELETE FROM qc_ai_agent_Intent_knowledge_category_mapping WHERE id = #{id}
    </delete>

    <delete id="deleteByAgentId">
        DELETE FROM qc_ai_agent_Intent_knowledge_category_mapping WHERE agent_id = #{agentId} and status = #{status}
    </delete>

    <insert id="batchInsert" >
        INSERT INTO qc_ai_agent_intent_knowledge_category_mapping (
        id,
        status,
        agent_id,
        relation_id,
        collection_id,
        category_id
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id},
            #{item.status},
            #{item.agentId},
            #{item.relationId},
            #{item.collectionId},
            #{item.categoryId}
            )
        </foreach>
    </insert>

</mapper>
