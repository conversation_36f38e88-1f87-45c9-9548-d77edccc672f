<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.qc.agent.platform.datasource.mapper.DatasourceMapper" >
  <resultMap id="queryDbDataSourceConfigResultMap" type="com.qc.agent.platform.datasource.model.DatasourceConfig" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="user_name" property="userName" jdbcType="VARCHAR" />
    <result column="password" property="password" jdbcType="VARCHAR" />
    <result column="max_conn_count" property="maxConnCount" jdbcType="NUMERIC" />
    <result column="min_conn_count" property="minConnCount" jdbcType="NUMERIC" />
    <result column="sleep_keep_time" property="sleepKeepTime" jdbcType="NUMERIC" />
    <result column="max_conn_lifetime" property="maxConnLifetime" jdbcType="NUMERIC" />
    <association property="server" javaType="com.qc.agent.platform.datasource.model.ServerConfig" >
      <id column="server_id" property="id" />
      <result column="ip_address" property="ipAddress" jdbcType="VARCHAR" />
      <result column="port" property="port" jdbcType="VARCHAR" />
      <result column="server_user_name" property="userName" jdbcType="VARCHAR" />
      <result column="server_password" property="password" jdbcType="VARCHAR" />
    </association>
    <collection property="slaves" ofType="com.qc.agent.platform.datasource.model.SlaveDatasourceConfig" >
      <id column="primary_datasource" property="id" />
      <result column="slave_user_name" property="userName" jdbcType="VARCHAR" />
      <result column="slave_password" property="password" jdbcType="VARCHAR" />
      <result column="slave_max_conn_count" property="maxConnCount" jdbcType="NUMERIC" />
      <result column="slave_min_conn_count" property="minConnCount" jdbcType="NUMERIC" />
      <result column="slave_sleep_keep_time" property="sleepKeepTime" jdbcType="NUMERIC" />
      <result column="slave_max_conn_lifetime" property="maxConnLifetime" jdbcType="NUMERIC" />
      <association columnPrefix="slave_" property="server" javaType="com.qc.agent.platform.datasource.model.ServerConfig" >
        <id column="server_id" property="id" />
        <result column="ip_address" property="ipAddress" jdbcType="VARCHAR" />
        <result column="port" property="port" jdbcType="INTEGER" />
      </association>
    </collection>
  </resultMap>

  <select id="queryDbDataSourceConfig" resultMap="queryDbDataSourceConfigResultMap" parameterType="java.lang.Long" >
    SELECT
      dd.id,
      ds.ip_address,
      ds.port,
      ds.user_name server_user_name,
      ds.password server_password,
      dd.user_name,
      dd.password,
      dd.max_conn_count,
      dd.min_conn_count,
      dd.sleep_keep_time,
      dd.max_conn_lifetime,
      dd.server_id,

      sds.ip_address AS slave_ip_address,
      sds.port AS slave_port,
      sdd.user_name AS slave_user_name,
      sdd.password AS slave_password,
      sdd.max_conn_count AS slave_max_conn_count,
      sdd.min_conn_count AS slave_min_conn_count,
      sdd.sleep_keep_time AS slave_sleep_keep_time,
      sdd.max_conn_lifetime AS slave_max_conn_lifetime
    FROM
      sys_db_datasource dd
      JOIN sys_db_server ds ON dd.server_id = ds.ID
      LEFT JOIN sys_slave_db_datasource sdd ON dd.ID = sdd.primary_datasource
      LEFT JOIN sys_db_server sds ON sdd.server_id = sds.ID

    where dd.id=#{id,jdbcType=BIGINT} AND dd.status='1'

  </select>

  <select id="queryDataSourceWithSqlClause" resultMap="queryDbDataSourceConfigResultMap" parameterType="java.lang.String" >
    SELECT
      dd.id,
      ds.ip_address,
      ds.port,
      ds.user_name server_user_name,
      ds.password server_password,
      dd.user_name,
      dd.password,
      dd.max_conn_count,
      dd.min_conn_count,
      dd.sleep_keep_time,
      dd.max_conn_lifetime,
      dd.server_id,

      sds.ip_address AS slave_ip_address,
      sds.port AS slave_port,
      sdd.user_name AS slave_user_name,
      sdd.password AS slave_password,
      sdd.max_conn_count AS slave_max_conn_count,
      sdd.min_conn_count AS slave_min_conn_count,
      sdd.sleep_keep_time AS slave_sleep_keep_time,
      sdd.max_conn_lifetime AS slave_max_conn_lifetime
    FROM
      sys_db_datasource dd
        JOIN sys_db_server ds ON dd.server_id = ds.ID
        LEFT JOIN sys_slave_db_datasource sdd ON dd.ID = sdd.primary_datasource
        LEFT JOIN sys_db_server sds ON sdd.server_id = sds.ID
    where dd.status='1'
    <if test="sqlClause != null and sqlClause != ''">
      AND ${sqlClause}
    </if>

  </select>

  <insert id="insertDbDataSourceConfig" parameterType="com.qc.agent.platform.datasource.model.DatasourceConfig" >
      insert into sys_db_datasource (id, user_name, password,
                                     max_conn_count, min_conn_count, sleep_keep_time,
                                     server_id, status, creator,
                                     creation_time, max_conn_lifetime)
      values (#{id,jdbcType=BIGINT}, #{userName,jdbcType=VARCHAR}, #{password,jdbcType=VARCHAR},
              #{maxConnCount,jdbcType=NUMERIC}, #{minConnCount,jdbcType=NUMERIC}, #{sleepKeepTime,jdbcType=NUMERIC},
              #{serverId,jdbcType=BIGINT}, #{status,jdbcType=CHAR}, #{creator,jdbcType=BIGINT},
              #{creationTime,jdbcType=TIMESTAMP}, #{maxConnLifetime,jdbcType=NUMERIC})
  </insert>

</mapper>
