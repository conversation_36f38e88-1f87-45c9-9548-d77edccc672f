<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.qc.agent.platform.register.mapper.TenantScriptExecutorMapper">

    <insert id="insert" parameterType="com.qc.agent.platform.register.model.ScriptExecutorRequest">
        INSERT INTO sys_script_executor_log (id, creator, creation_time, sql_clause,execute_sql,remarks,execute_message)
        VALUES (#{id}::INT8, #{creator}, #{creationTime}, #{sqlClause}, #{executeSql}, #{remarks},#{executeMessage})
    </insert>

    <update id="updateWithExecuteStatus">
        UPDATE sys_script_executor_log
        SET execute_message = #{message}
        WHERE id = #{id}::INT8
    </update>

    <select id="selectById" resultType="com.qc.agent.platform.register.model.ScriptExecutorRequest">
        SELECT
            id::<PERSON><PERSON><PERSON><PERSON>,
            creator,
            creation_time AS "creationTime",
            sql_clause AS "sqlClause",
            execute_sql AS "executeSql",
            remarks,
            execute_message AS "executeMessage"
        FROM sys_script_executor_log
        WHERE id = #{id}::INT8
    </select>

    <select id="selectAll" resultType="com.qc.agent.platform.register.model.ScriptExecutorRequest">
        SELECT
            id::VARCHAR,
            creator,
            creation_time AS "creationTime",
            sql_clause AS "sqlClause",
            execute_sql AS "executeSql",
            remarks,
            execute_message AS "executeMessage"
        FROM sys_script_executor_log
        WHERE 1=1
        <if test="likeCondition !=null and likeCondition !=''">
           AND (remarks like '%'||#{likeCondition}||'%' OR execute_sql like '%'||#{likeCondition}||'%')
        </if>
        ORDER BY creation_time DESC
        LIMIT 500
    </select>

</mapper>
