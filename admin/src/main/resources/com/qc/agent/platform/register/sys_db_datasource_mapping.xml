<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.qc.agent.platform.register.mapper.SysDbDatasourceMapper" >
  <resultMap id="BaseResultMap" type="com.qc.agent.platform.register.model.SysDbDatasource" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="user_name" property="userName" jdbcType="VARCHAR" />
    <result column="password" property="password" jdbcType="VARCHAR" />
    <result column="max_conn_count" property="maxConnCount" jdbcType="NUMERIC" />
    <result column="min_conn_count" property="minConnCount" jdbcType="NUMERIC" />
    <result column="sleep_keep_time" property="sleepKeepTime" jdbcType="NUMERIC" />
    <result column="server_id" property="serverId" jdbcType="BIGINT" />
    <result column="status" property="status" jdbcType="CHAR" />
    <result column="creator" property="creator" jdbcType="BIGINT" />
    <result column="creation_time" property="creationTime" jdbcType="TIMESTAMP" />
    <result column="max_conn_lifetime" property="maxConnLifetime" jdbcType="NUMERIC" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, user_name, password, max_conn_count, min_conn_count, sleep_keep_time, server_id,
    status, creator, creation_time, max_conn_lifetime
  </sql>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from sys_db_datasource
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.qc.agent.platform.register.model.SysDbDatasource" >
    insert into sys_db_datasource (id, user_name, password,
      max_conn_count, min_conn_count, sleep_keep_time,
      server_id, status, creator,
      creation_time, max_conn_lifetime)
    values (#{id,jdbcType=BIGINT}, #{userName,jdbcType=VARCHAR}, #{password,jdbcType=VARCHAR},
      #{maxConnCount,jdbcType=NUMERIC}, #{minConnCount,jdbcType=NUMERIC}, #{sleepKeepTime,jdbcType=NUMERIC},
      #{serverId,jdbcType=BIGINT}, #{status,jdbcType=CHAR}, #{creator,jdbcType=BIGINT},
      #{creationTime,jdbcType=TIMESTAMP}, #{maxConnLifetime,jdbcType=NUMERIC})
  </insert>
  <insert id="insertSelective" parameterType="com.qc.agent.platform.register.model.SysDbDatasource" >
    insert into sys_db_datasource
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="userName != null" >
        user_name,
      </if>
      <if test="password != null" >
        password,
      </if>
      <if test="maxConnCount != null" >
        max_conn_count,
      </if>
      <if test="minConnCount != null" >
        min_conn_count,
      </if>
      <if test="sleepKeepTime != null" >
        sleep_keep_time,
      </if>
      <if test="serverId != null" >
        server_id,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="creator != null" >
        creator,
      </if>
      <if test="creationTime != null" >
        creation_time,
      </if>
      <if test="maxConnLifetime != null" >
        max_conn_lifetime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="userName != null" >
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="password != null" >
        #{password,jdbcType=VARCHAR},
      </if>
      <if test="maxConnCount != null" >
        #{maxConnCount,jdbcType=NUMERIC},
      </if>
      <if test="minConnCount != null" >
        #{minConnCount,jdbcType=NUMERIC},
      </if>
      <if test="sleepKeepTime != null" >
        #{sleepKeepTime,jdbcType=NUMERIC},
      </if>
      <if test="serverId != null" >
        #{serverId,jdbcType=BIGINT},
      </if>
      <if test="status != null" >
        #{status,jdbcType=CHAR},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="creationTime != null" >
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="maxConnLifetime != null" >
        #{maxConnLifetime,jdbcType=NUMERIC},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.qc.agent.platform.register.model.SysDbDatasource" >
    update sys_db_datasource
    <set >
      <if test="userName != null" >
        user_name = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="password != null" >
        password = #{password,jdbcType=VARCHAR},
      </if>
      <if test="maxConnCount != null" >
        max_conn_count = #{maxConnCount,jdbcType=NUMERIC},
      </if>
      <if test="minConnCount != null" >
        min_conn_count = #{minConnCount,jdbcType=NUMERIC},
      </if>
      <if test="sleepKeepTime != null" >
        sleep_keep_time = #{sleepKeepTime,jdbcType=NUMERIC},
      </if>
      <if test="serverId != null" >
        server_id = #{serverId,jdbcType=BIGINT},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=CHAR},
      </if>
      <if test="creator != null" >
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="creationTime != null" >
        creation_time = #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="maxConnLifetime != null" >
        max_conn_lifetime = #{maxConnLifetime,jdbcType=NUMERIC},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.qc.agent.platform.register.model.SysDbDatasource" >
    update sys_db_datasource
    set user_name = #{userName,jdbcType=VARCHAR},
      password = #{password,jdbcType=VARCHAR},
      max_conn_count = #{maxConnCount,jdbcType=NUMERIC},
      min_conn_count = #{minConnCount,jdbcType=NUMERIC},
      sleep_keep_time = #{sleepKeepTime,jdbcType=NUMERIC},
      server_id = #{serverId,jdbcType=BIGINT},
      status = #{status,jdbcType=CHAR},
      creator = #{creator,jdbcType=BIGINT},
      creation_time = #{creationTime,jdbcType=TIMESTAMP},
      max_conn_lifetime = #{maxConnLifetime,jdbcType=NUMERIC}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
