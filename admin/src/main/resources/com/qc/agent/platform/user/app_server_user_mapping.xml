<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper
	namespace="com.qc.agent.platform.user.mapper.AppServerUserMapper">

	<select id="selectUserByUserId" resultType="com.qc.agent.common.core.TenantUser" >
		SELECT id userId,name AS userName,user_type AS "userType", ${tenantId} AS tenantId FROM sm_user where id = #{userId}
	</select>

	<select id="selectAdminUserId" resultType="java.lang.Long">
		select id from app_admin_info limit 1;
	</select>

	<select id="getParentDeptIds" resultType="java.lang.Long">
		SELECT pid FROM sys_department_r WHERE id = #{deptId}
	</select>

	<resultMap id="deptListResultMap" type="com.qc.agent.platform.pojo.QcUserInfo">
		<id property="userId" column="id"/>
		<id property="userName" column="user_name"/>
		<collection property="deptInfoList" ofType="com.qc.agent.platform.pojo.QcDeptInfo">
			<id column="dept_id" property="deptId"/>
			<result column="dept_name" property="name"/>
		</collection>
	</resultMap>

	<select id="getAllDeptList" resultMap="deptListResultMap">
		SELECT
		se.id,
		se.name as user_name,
		sdr.pid,
		sd.name
		FROM
		sys_employee se
		left join sys_department_r sdr on se.dept_id = sdr.id
		left join sys_department sd on sd.id = sdr.pid
		where se.id in
		<foreach collection="userIdList" item="userId" open="(" close=")" separator=",">
			#{userId}
		</foreach>
		order by se.id
	</select>

	<select id="queryDeptById" resultType="com.qc.agent.platform.pojo.SysDepartment">
		SELECT
		    id,
		    status,
		    name,
		    full_names AS "fullNames",
		    code,
		    full_codes AS "fullCodes"
		FROM sys_department
		WHERE id = #{deptId}
	</select>

    <select id="selectDeptIdByUserId" resultType="java.lang.Long">
		SELECT
		se.dept_id
		FROM
		sys_employee se
		where se.id =#{userId}
	</select>
    <select id="getProductInfo" resultType="com.qc.agent.platform.pojo.QcProductInfo">
		SELECT code,id,name,brand,short_name as "shortName"
		FROM bas_pd_product where status = '1'
		order by create_time desc limit 2
	</select>

</mapper>





