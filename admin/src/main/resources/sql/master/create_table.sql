CREATE TABLE sys_db_server (
    id int8 NOT NULL,
    user_name varchar(255) COLLATE pg_catalog.default,
    password varchar(255) COLLATE pg_catalog.default,
    max_conn_count numeric(12,0),
    min_conn_count numeric(12,0),
    status char(1) COLLATE pg_catalog.default,
    code varchar(255) COLLATE pg_catalog.default,
    ip_address varchar(255) COLLATE pg_catalog.default,
    port numeric(10,0),
    CONSTRAINT sys_db_server_copy1_pkey PRIMARY KEY (id)
)
;

CREATE TABLE sys_db_datasource (
    id int8 NOT NULL,
    user_name varchar(255) COLLATE pg_catalog.default,
    password varchar(255) COLLATE pg_catalog.default,
    max_conn_count numeric(12,0),
    min_conn_count numeric(12,0),
    sleep_keep_time numeric(12,0),
    server_id int8,
    status char(1) COLLATE pg_catalog.default,
    creator int8,
    creation_time timestamp(6),
    max_conn_lifetime numeric(12,0),
    CONSTRAINT sys_db_datasource1_copy1_pkey PRIMARY KEY (id)
)
;


CREATE TABLE sys_slave_db_datasource (
    id int8 NOT NULL,
    user_name varchar(255) COLLATE pg_catalog.default,
    password varchar(255) COLLATE pg_catalog.default,
    max_conn_count numeric(12,0),
    min_conn_count numeric(12,0),
    sleep_keep_time numeric(12,0),
    server_id int8,
    status char(1) COLLATE pg_catalog.default,
    primary_datasource int8,
    max_conn_lifetime numeric(12,0),
    CONSTRAINT sys_slave_db_datasource1_copy1_pkey PRIMARY KEY (id)
)
;

CREATE TABLE sys_script_executor_log (
    id int8 NOT NULL,
    creator varchar(255),
    creation_time timestamp(6),
    sqlClause text,
    executeSql text,
    remarks text,
    CONSTRAINT sys_script_executor_log_pkey PRIMARY KEY (id)
)
;

