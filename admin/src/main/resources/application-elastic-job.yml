elasticjob:
  reg-center:
    server-lists: 172.31.3.206:2181,172.31.3.206:2182,172.31.3.206:2183
    namespace: elastic-job-demo
  jobs:
    list:
      - clazz: com.qc.agent.app.job.extract_conversation.impl.AiAgentExtractConversationJob
        cron: 0/20 * * * * ?
        shardingTotalCount: 2
        shardingItemParam: 0=text,1=image
        jobParam: name=test
        jobName: AiAgentExtractConversationJob # 本实例中没有特殊含义
#    aiAgentExtractConversationJob:
#      elastic-job-class: com.qc.agent.app.job.extract_conversation.impl.AiAgentExtractConversationJob # 全类名
#      cron: "0 0/1 * * * ?"
#      sharding-total-count: 3
#      overwrite: true
spring:
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      WRITE_DATES_AS_TIMESTAMPS: false
