########################## core-common ##########################
httpcomponents.connectReqTimeout=6000
httpcomponents.socketTimeout=6000
httpcomponents.connectionTimeout=6000
# total connections in pool
httpcomponents.maxTotal=200
#max connections per host
httpcomponents.defaultMaxPerRoute = 100

########################## core-alarm ##########################
#### influx db config ####
alarm.all.close=true
alarm.influxdb.retentionPolicy=autogen
#alarm.influxdb.url=http://internal-elb-influxdb-945682325.cn-north-1.elb.amazonaws.com.cn
#alarm.influxdb.user=root
#alarm.influxdb.password=root

###DEV###
#alarm.influxdb.url=http://************:8086
alarm.influxdb.url=http://127.0.0.1:8086
alarm.influxdb.user=root
alarm.influxdb.password=root

#whether publish serviceAcessEvent
alarm.serviceAcessEvent.open=true
#whether publish exceptionEvent
alarm.exceptionEvent.open=true
#whether publish sessionExceptionEvent
alarm.sessionExceptionEvent.open=true
profile.threshold=2000

#### context of app,each should be different. for ex: appsvr, cisvr
web.context=search

### thread profile interceptor time limit ,if request execute more than this time, will println the stack trace, default is 500 ms(profile.threshold = 500)
#profile.threshold=500


########################## token-redis ##########################
redis.pool.maxTotal=500
redis.pool.maxIdle=500
redis.pool.maxWaitMillis=2000
redis.pool.minIdle=50
redis.pool.testOnBorrow=false
redis.pool.testWhileIdle=true

#### session twemproxy  address, for ex:  127.0.0.1:6379,***********:6379
#redis.notsync.twemproxy.addresses=data-cache.rqnhhk.ng.0001.cnn1.cache.amazonaws.com.cn:6379
#redis.notsync.twemproxy.auth=

###DEV###

#redis.notsync.twemproxy.addresses=***********:7000,***********:7001,***********:7002,***********:7003,***********:7004,***********:7005


#redis.notsync.twemproxy.addresses=************:6379
redis.notsync.twemproxy.addresses=************:6379
redis.notsync.twemproxy.auth=111111



#### redis db index, default is 0
redis.notsync.twemproxy.database=1


########################## data-redis ##########################
#### data cache redis, for ex:  127.0.0.1:6379,***********:6379
#redis.datacache.addresses=data-cache.rqnhhk.ng.0001.cnn1.cache.amazonaws.com.cn:6379
#redis.datacache.auth=

###DEV###
#redis.datacache.addresses=***********:7000,***********:7001,***********:7002,***********:7003,***********:7004,***********:7005

redis.datacache.addresses=************:6379
redis.datacache.auth=111111

#redis.datacache.addresses=127.0.0.1:6379

#### redis db index, default is 0
#redis.datacache.database=0

#redis.slave.datacache.addresses=***********:7000,***********:7001,***********:7002,***********:7003,***********:7004,***********:7005

#redis.slave.datacache.addresses=************:6379

#redis.slave.datacache.auth=111111




##  ***********************oss  config **************************

## 0-disable 1-enable
core.oss.upload.enable=1
##  upload config  0-local  1-aliyun 2-aws
core.oss.upload.channel=1
## 0- not encrypt access_key    1- encrypt access_key
core.oss.upload.encrypt_access_key=0

core.oss.upload.bucket=waiqin365-test
core.oss.upload.access_id=LTAI4Fkk66e1yEYkeG8dykyp
core.oss.upload.access_key=******************************
core.oss.upload.end_point=http://oss-cn-hangzhou.aliyuncs.com

## 0-disable 1-enable
core.oss.offline.enable=1
##  contract config 0-local 1-aliyun 2-aws
core.oss.offline.channel=2
## 0- not encrypt access_key    1- encrypt access_key
core.oss.offline.encrypt_access_key=1
core.oss.offline.bucket=waiqin365-offline
core.oss.offline.access_id=AKIAUTGUGXJG27E6ZF6H
core.oss.offline.access_key=9Z81xGWBcAosVs29fBMjlGAjJPP8g5i7r3K3t82iWCoXEGNtHwVnkjECvut2ks7o
core.oss.offline.end_point=https://s3.cn-north-1.amazonaws.com.cn


