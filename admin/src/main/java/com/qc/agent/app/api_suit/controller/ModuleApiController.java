package com.qc.agent.app.api_suit.controller;

import com.qc.agent.app.api_suit.model.dto.BaseDTO;
import com.qc.agent.app.api_suit.model.dto.ModuleApiDTO;
import com.qc.agent.app.api_suit.service.QcAiAgentModuleApiService;
import com.qc.agent.common.core.Message;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/ai-agent/suit/api")
public class ModuleApiController {

    @Resource
    private QcAiAgentModuleApiService service;

    @GetMapping("/{id}")
    public Message get(@PathVariable Long id) {
        return Message.of().ok().data(service.getById(id));
    }

    @GetMapping("/list")
    public Message list(Long suiId) {
        return Message.of().ok().data(service.list(suiId));
    }

    @PostMapping("/save")
    public Message save(@RequestBody ModuleApiDTO dto) {
        service.save(dto);
        return Message.of().ok();
    }

    @PostMapping("/delete")
    public Message delete(@RequestBody BaseDTO baseDTO) {
        service.delete(baseDTO.getId());
        return Message.of().ok();
    }
}
