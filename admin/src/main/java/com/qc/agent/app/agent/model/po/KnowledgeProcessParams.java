package com.qc.agent.app.agent.model.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-20
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@AllArgsConstructor
@Data
@Builder
public class KnowledgeProcessParams {
    private List<Long> knowledgeIds;
    private String question;
    /**
     * 知识库-召回设置-最大召回数量
     */
    private Integer maxRecallCount;
    /**
     * 知识库-召回设置-最小匹配度
     */
    private Double minMatchThreshold;

    /**
     * 知识库-召回设置-qa最小匹配度
     */
    private Double qaMinMatchThreshold;
    /**
     * 知识库-召回设置-检索范围 1:仅引用知识库 2:引用知识库+模型通用知识库
     */
    private String searchScope;
    private String nullResultAnswer;
    private Long tenantId;
    private Boolean forTest;
}
