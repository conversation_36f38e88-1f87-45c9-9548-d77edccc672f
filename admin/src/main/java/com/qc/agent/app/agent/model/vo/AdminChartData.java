package com.qc.agent.app.agent.model.vo;

import com.qc.agent.app.agent.model.po.QcAiAgentAdminChart;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-07-04
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Data
@AllArgsConstructor
public class AdminChartData {
    private List<QcAiAgentAdminChart> dialogueList;
    private long dialogueTotal;
    private List<QcAiAgentAdminChart> activeUserList;
    private Long activeUserTotal;
    private List<QcAiAgentAdminChart> tokenList;
    private long tokenTotal;
    private List<QcAiAgentAdminChart> averageDialogueList;
    private String averageDialogue;
    private KnowledgeStats knowledgeChart;
}
