package com.qc.agent.app.agent.service;

import com.qc.agent.app.agent.model.entity.CustomerVariableData;

import java.util.List;
import java.util.Map;

/**
 * 客户洞察变量数据服务接口
 */
public interface CustomerVariableDataService {

    /**
     * 根据客户ID查询变量数据列表
     *
     * @param customerId 客户ID
     * @return 变量数据列表
     */
    List<CustomerVariableData> getVariableDataByCustomerId(Long customerId);

    /**
     * 根据对话ID查询变量数据列表
     *
     * @param conversationId 对话ID
     * @return 变量数据列表
     */
    List<CustomerVariableData> getVariableDataByConversationId(Long conversationId);

    /**
     * 根据客户ID和对话ID查询变量数据列表
     *
     * @param customerId 客户ID
     * @param conversationId 对话ID
     * @return 变量数据列表
     */
    List<CustomerVariableData> getVariableDataByCustomerIdAndConversationId(Long customerId, Long conversationId);

    /**
     * 保存客户变量数据
     *
     * @param customerId 客户ID
     * @param conversationId 对话ID
     * @param variableDataMap 变量数据映射 (promptVariableRecordId -> variableValue)
     * @return 是否保存成功
     */
    boolean saveCustomerVariableData(Long customerId, Long conversationId, Map<Long, String> variableDataMap);

    /**
     * 批量保存客户变量数据
     *
     * @param customerVariableDataList 变量数据列表
     * @return 是否保存成功
     */
    boolean batchSaveCustomerVariableData(List<CustomerVariableData> customerVariableDataList);

    /**
     * 更新客户变量数据
     *
     * @param customerVariableData 变量数据
     * @return 是否更新成功
     */
    boolean updateCustomerVariableData(CustomerVariableData customerVariableData);

    /**
     * 根据客户ID和对话ID删除变量数据
     *
     * @param customerId 客户ID
     * @param conversationId 对话ID
     * @return 是否删除成功
     */
    boolean deleteVariableDataByCustomerIdAndConversationId(Long customerId, Long conversationId);

    /**
     * 根据对话ID删除变量数据
     *
     * @param conversationId 对话ID
     * @return 是否删除成功
     */
    boolean deleteVariableDataByConversationId(Long conversationId);

    /**
     * 获取客户变量数据的Map形式
     * 
     * @param customerId 客户ID
     * @param conversationId 对话ID
     * @return 变量数据映射 (promptVariableRecordId -> variableValue)
     */
    Map<Long, String> getVariableDataMap(Long customerId, Long conversationId);
}
