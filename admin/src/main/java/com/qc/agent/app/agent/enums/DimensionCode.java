package com.qc.agent.app.agent.enums;

import java.util.Arrays;
import java.util.List;

/**
 * 维度编码枚举
 * 用于管理维度编码和名称的映射关系
 *
 * <AUTHOR>
 */
public enum DimensionCode {
    ORDER("ORDER", "订单维度"),
    DISPLAY("DISPLAY", "铺货维度"),
    VIVID("VIVID", "生动化维度"),
    PAID_DISPLAY("PAID_DISPLAY", "付费陈列维度"),
    COUPON("COUPON", "兑换券维度"),
    ASSET("ASSET", "资产维度"),
    AUTHENTICITY("AUTHENTICITY", "真实性维度"),
    CUSTOM("CUSTOM", "自定义维度"),
    SUMMARY_COMPREHENSIVE("SUMMARY_COMPREHENSIVE", "综合衡量"),
    SUMMARY_ADVICE("SUMMARY_ADVICE", "总结及建议");

    private final String code;
    private final String name;

    // 维度关键词常量声明
    private static final List<String> ORDER_KEYWORDS = Arrays.asList("ORDER", "订单");
    private static final List<String> DISPLAY_KEYWORDS = Arrays.asList("DISPLAY", "铺货", "陈列");
    private static final List<String> PAID_DISPLAY_KEYWORDS = Arrays.asList("PAID_DISPLAY", "付费陈列", "付费铺货");
    private static final List<String> VIVID_KEYWORDS = Arrays.asList("VIVID", "生动化", "生动");
    private static final List<String> COUPON_KEYWORDS = Arrays.asList("COUPON", "兑换券", "券");
    private static final List<String> ASSET_KEYWORDS = Arrays.asList("ASSET", "资产");
    private static final List<String> AUTHENTICITY_KEYWORDS = Arrays.asList("AUTHENTICITY", "真实性", "真实");
    private static final List<String> CUSTOM_KEYWORDS = Arrays.asList("CUSTOM", "自定义");
    private static final List<String> SUMMARY_COMPREHENSIVE_KEYWORDS = Arrays.asList("SUMMARY_COMPREHENSIVE", "综合");
    private static final List<String> SUMMARY_ADVICE_KEYWORDS = Arrays.asList("SUMMARY_ADVICE", "总结", "建议");

    DimensionCode(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    /**
     * 根据queryBusinessCode推导维度编码
     */
    public static DimensionCode fromQueryBusinessCode(String queryBusinessCode) {
        String upperCode = queryBusinessCode.toUpperCase().trim();

        // 按优先级顺序检查（付费陈列优先级最高）
        if (containsAnyKeyword(upperCode, PAID_DISPLAY_KEYWORDS)) {
            return PAID_DISPLAY;
        }
        if (containsAnyKeyword(upperCode, ORDER_KEYWORDS)) {
            return ORDER;
        }
        if (containsAnyKeyword(upperCode, DISPLAY_KEYWORDS)) {
            return DISPLAY;
        }
        if (containsAnyKeyword(upperCode, VIVID_KEYWORDS)) {
            return VIVID;
        }
        if (containsAnyKeyword(upperCode, COUPON_KEYWORDS)) {
            return COUPON;
        }
        if (containsAnyKeyword(upperCode, ASSET_KEYWORDS)) {
            return ASSET;
        }
        if (containsAnyKeyword(upperCode, AUTHENTICITY_KEYWORDS)) {
            return AUTHENTICITY;
        }
        if (containsAnyKeyword(upperCode, CUSTOM_KEYWORDS)) {
            return CUSTOM;
        }
        if (containsAnyKeyword(upperCode, SUMMARY_COMPREHENSIVE_KEYWORDS)) {
            return SUMMARY_COMPREHENSIVE;
        }
        if (containsAnyKeyword(upperCode, SUMMARY_ADVICE_KEYWORDS)) {
            return SUMMARY_ADVICE;
        }

        return CUSTOM;
    }

    /**
     * 检查字符串是否包含任意关键词
     */
    private static boolean containsAnyKeyword(String text, List<String> keywords) {
        return keywords.stream().anyMatch(keyword -> text.contains(keyword.toUpperCase()));
    }

    /**
     * 根据维度编码获取枚举
     */
    public static DimensionCode fromCode(String code) {
        if (code == null) {
            return CUSTOM;
        }

        for (DimensionCode dimensionCode : values()) {
            if (dimensionCode.getCode().equals(code)) {
                return dimensionCode;
            }
        }

        return CUSTOM;
    }

    /**
     * 获取所有维度编码列表
     */
    public static String[] getAllCodes() {
        DimensionCode[] values = values();
        String[] codes = new String[values.length];
        for (int i = 0; i < values.length; i++) {
            codes[i] = values[i].getCode();
        }
        return codes;
    }

    /**
     * 获取所有维度名称列表
     */
    public static String[] getAllNames() {
        DimensionCode[] values = values();
        String[] names = new String[values.length];
        for (int i = 0; i < values.length; i++) {
            names[i] = values[i].getName();
        }
        return names;
    }
}