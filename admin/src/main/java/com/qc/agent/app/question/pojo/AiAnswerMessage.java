package com.qc.agent.app.question.pojo;

import lombok.Builder;

/**
 * @description：TODO
 * @author： sc
 * @create： 2024/2/5 14:56
 */
@Builder
public class AiAnswerMessage {

    public static final String END_FLAG = "[DONE]";

    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public boolean finished() {
        return END_FLAG.equals(content);
    }
}
