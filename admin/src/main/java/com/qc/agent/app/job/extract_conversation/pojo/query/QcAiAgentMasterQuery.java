package com.qc.agent.app.job.extract_conversation.pojo.query;

import lombok.Data;



/**
 * <AUTHOR>
 * @date 2025-03-25
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Data
public class QcAiAgentMasterQuery {
    private Integer page;
    private Integer rows;
    private Integer offset;

    /**
     * offset
     *
     * @return
     */
    public Integer getOffset() {
        if (page != null && rows != null) {
            return (page - 1) * rows;
        }
        return offset;
    }
}
