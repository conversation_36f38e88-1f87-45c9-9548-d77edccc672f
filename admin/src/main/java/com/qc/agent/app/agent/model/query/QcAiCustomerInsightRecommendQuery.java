package com.qc.agent.app.agent.model.query;

import lombok.Data;

/**
 * 客户洞察推荐查询参数
 * 
 * <AUTHOR>
 * @since 1.0.0
 * @version 1.0.0
 */
@Data
public class QcAiCustomerInsightRecommendQuery {

    /**
     * 代理ID
     */
    private Long agentId;

    /**
     * 客户ID
     */
    private String cmId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 客户类型
     */
    private String customerType;

    /**
     * 查询类型：1-客户分析，2-行为洞察，3-价值评估
     */
    private String queryType;
} 