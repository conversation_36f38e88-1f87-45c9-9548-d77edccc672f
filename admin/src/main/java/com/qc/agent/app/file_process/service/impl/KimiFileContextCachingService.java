package com.qc.agent.app.file_process.service.impl;

import cn.hutool.core.io.FileUtil;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.qc.agent.app.agent.mapper.QcAiAgentModelMapper;
import com.qc.agent.app.agent.model.po.QcAiAgentModel;
import com.qc.agent.app.agent.util.JsonUtils;
import com.qc.agent.app.file_process.pojo.FileContextCachingParam;
import com.qc.agent.app.file_process.pojo.QcAiParseFileResult;
import com.qc.agent.app.file_process.service.FileContextCachingService;
import com.qc.agent.app.workflow.inner.pojo.QcUploadFile;
import com.qc.agent.lla.model.LLAConfig;
import com.qc.agent.lla.model.LLAMessage;
import com.qc.agent.lla.model.LLAProvider;
import com.qc.agent.redis.RedisClient;
import com.qc.agent.utils.QcAiBusinessRedisKeyConstants;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.dromara.x.file.storage.core.FileInfo;
import org.dromara.x.file.storage.core.FileStorageService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * Kimi大模型文件处理与上下文缓存服务。
 * <p>
 * 该服务实现了两级缓存策略：
 * 1.  **内容解析缓存 (OSS/FileStorage)**: 对单个文件，其通过Kimi API解析后的文本内容会被缓存。
 * 缓存键基于文件名哈希，确保相同文件只被解析一次。
 * 2.  **消息列表缓存 (Redis)**: 对整个对话（intent），如果启用了Kimi的`caching` API,
 * 生成的包含`cache`角色的消息列表会被缓存到Redis，以供后续对话复用。
 *
 * <AUTHOR>
 * @date 2025-06-12
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Slf4j
@Service("kimiFileContextCachingService")
public class KimiFileContextCachingService implements FileContextCachingService {

    // --- Constants ---
    private static final String KIMI_API_BASE_URL = "https://api.moonshot.cn/v1";
    private static final String KIMI_API_FILES_ENDPOINT = "/files";
    private static final String KIMI_API_CONTENT_ENDPOINT = "/content";
    private static final String KIMI_API_CACHING_ENDPOINT = "/caching";

    private static final String PARSED_FILE_PATH_PREFIX = "parsed/";
    private static final String PARSED_FILE_SUFFIX = ".json";
    private static final String HASH_ALGORITHM = "SHA-256";
    private static final int HASH_ID_LENGTH = 24;

    private static final String ROLE_SYSTEM = "system";
    private static final String ROLE_CACHE = "cache";
    private static final String KIMI_CACHE_MODEL = "moonshot-v1";
    private static final String KIMI_LATEST_MODEL = "kimi-latest";
    private static final String PURPOSE_FILE_EXTRACT = "file-extract";
    private static final String CONTENT_TYPE_JSON = "application/json";

    private static final int KIMI_CACHE_TTL_SECONDS = 300;
    private static final int KIMI_CACHE_RESET_TTL_SECONDS = 600;

    // --- Immutable Dependencies ---
    private final FileStorageService fileStorageService;
    private final RedisClient redisClient;
    private final QcAiAgentModelMapper qcAiAgentModelMapper;
    private final OkHttpClient okHttpClient;
    private final ObjectMapper objectMapper;
    private final String ossBasePath;
    private final String ossPlatform;

    /**
     * 使用构造函数注入所有依赖，确保不变性和易测性。
     */
    public KimiFileContextCachingService(
            FileStorageService fileStorageService,
            RedisClient redisClient,
            QcAiAgentModelMapper qcAiAgentModelMapper,
            @Value("${dromara.x-file-storage.aliyun-oss[0].base-path}") String ossBasePath,
            @Value("${dromara.x-file-storage.aliyun-oss[0].platform}") String ossPlatform
    ) {
        this.fileStorageService = fileStorageService;
        this.redisClient = redisClient;
        this.qcAiAgentModelMapper = qcAiAgentModelMapper;
        this.ossBasePath = ossBasePath;
        this.ossPlatform = ossPlatform;
        this.objectMapper = new ObjectMapper();
        this.okHttpClient = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .retryOnConnectionFailure(true)
                .build();
    }

    // --- Records for Type-Safe API Responses ---
    @JsonIgnoreProperties(ignoreUnknown = true)
    private record KimiFileResponse(String id, String object, int bytes, long created_at, String filename,
                                    String purpose, String status, String status_details) {
    }
    @JsonIgnoreProperties(ignoreUnknown = true)
    private record KimiFileContentResponse(String content, String file_type, String filename, String title,
                                           String type) {
    }

    @Override
    public QcAiParseFileResult getCacheInfo(FileContextCachingParam param) throws IOException {
        final var logPrefix = param.getLogPrefix();
        final var intentId = param.getIntentId();
        log.info("{} [Kimi] 开始处理文件上下文, intentId: {}", logPrefix, intentId);

        // 优先从Redis中获取整个会话的缓存消息列表
        final var redisKey = QcAiBusinessRedisKeyConstants.MODEL_CACHE_KIMI + "cache-messages:" + intentId;

        if (StringUtils.isNotEmpty(param.getCacheTag())) {
            Optional<List<LLAMessage>> cachedMessages = tryGetFromRedisCache(redisKey, logPrefix, intentId);
            if (cachedMessages.isPresent()) {
                return new QcAiParseFileResult().setCacheInfoList(cachedMessages.get());
            }
        }

        // 如果Redis缓存未命中或未使用，则处理文件并可能创建新的Kimi缓存
        final List<LLAMessage> messages = processAndCacheFiles(param, redisKey);
        return new QcAiParseFileResult().setCacheInfoList(messages);
    }

    /**
     * 尝试从Redis中获取缓存的消息列表。
     */
    private Optional<List<LLAMessage>> tryGetFromRedisCache(String redisKey, String logPrefix, Long intentId) {
        try {
            Object object = redisClient.get(redisKey);
            if (ObjectUtils.isEmpty(object)) {
                return Optional.empty();
            }
            String cachedValue = object.toString();
            if (cachedValue != null) {
                log.info("{} [Kimi] Redis缓存命中, intentId: {}", logPrefix, intentId);
                List<LLAMessage> messages = objectMapper.readValue(cachedValue, new TypeReference<>() {
                });
                return Optional.of(messages);
            }
        } catch (IOException e) {
            log.error("{} [Kimi] 从Redis反序列化缓存消息失败, intentId: {}", logPrefix, intentId, e);
        }
        log.info("{} [Kimi] Redis缓存未命中, intentId: {}", logPrefix, intentId);
        return Optional.empty();
    }

    /**
     * 核心处理逻辑：遍历文件，获取或提取内容，并应用Kimi的缓存标签。
     */
    public List<LLAMessage> processAndCacheFiles(FileContextCachingParam param, String redisKey) throws IOException {
        final var logPrefix = param.getLogPrefix();
        final var config = initLLAConfig();
        final List<LLAMessage> contentMessages = new ArrayList<>();

        for (final var file : param.getFileList()) {
            final var parsedOssPath = generateParsedFilePath(param.getTenantId(), param.getIntentId(), file.getName());
            final var content = getOrExtractFileContent(file, parsedOssPath, config, logPrefix);
            contentMessages.add(LLAMessage.builder().role(ROLE_SYSTEM).content(content).build());
        }

        return applyKimiCacheTagIfNeeded(contentMessages, param, config, redisKey);
    }

    /**
     * 如果条件满足，调用Kimi的/caching接口创建缓存引用，并更新Redis。
     */
    private List<LLAMessage> applyKimiCacheTagIfNeeded(List<LLAMessage> contentMessages, FileContextCachingParam param,
                                                       LLAConfig config, String redisKey) {
        final var logPrefix = param.getLogPrefix();
        final var cacheTag = param.getCacheTag();

        // 仅当提供了cacheTag且模型不是特殊的'kimi-latest'时，才使用Kimi的caching API
        if (StringUtils.isNotEmpty(cacheTag) && !KIMI_LATEST_MODEL.equals(param.getModel())) {
            log.info("{} [Kimi] 准备为tag '{}' 创建Kimi缓存", logPrefix, cacheTag);
            try {
                ObjectNode cacheBody = objectMapper.createObjectNode();
                cacheBody.put("model", KIMI_CACHE_MODEL);
                cacheBody.set("messages", objectMapper.valueToTree(contentMessages));
                cacheBody.put("ttl", KIMI_CACHE_TTL_SECONDS);
                cacheBody.putArray("tags").add(cacheTag);

                Request request = new Request.Builder()
                        .url(KIMI_API_BASE_URL + KIMI_API_CACHING_ENDPOINT)
                        .header("Authorization", "Bearer " + config.getApiKey())
                        .post(RequestBody.create(cacheBody.toString(), MediaType.parse(CONTENT_TYPE_JSON)))
                        .build();

                try (Response response = okHttpClient.newCall(request).execute()) {
                    if (response.isSuccessful()) {
                        log.info("{} [Kimi] 成功为tag '{}' 创建Kimi缓存", logPrefix, cacheTag);
                        // 创建成功后，返回一个指向缓存的特殊消息
                        LLAMessage cacheMessage = LLAMessage.builder()
                                .role(ROLE_CACHE)
                                .content(String.format("tag=%s;reset_ttl=%d", cacheTag, KIMI_CACHE_RESET_TTL_SECONDS))
                                .build();
                        List<LLAMessage> finalMessages = List.of(cacheMessage);

                        // 将这个特殊消息存入Redis
                        redisClient.set(redisKey, objectMapper.writeValueAsString(finalMessages),8*60);
                        return finalMessages;
                    } else {
                        log.error("{} [Kimi] 创建Kimi缓存失败, tag: '{}', 响应: {}", logPrefix, cacheTag, response.body() != null ? response.body().string() : "N/A");
                    }
                }
            } catch (IOException e) {
                log.error("{} [Kimi] 调用Kimi Caching API时发生IO异常, tag: '{}'", logPrefix, cacheTag, e);
            }
        }
        // 如果不创建Kimi缓存，则直接返回原始文件内容消息
        return contentMessages;
    }

    /**
     * 获取文件内容，优先从OSS缓存获取，否则通过Kimi API提取并存入缓存。
     */
    private String getOrExtractFileContent(QcUploadFile qcUploadFile, String parsedPath, LLAConfig config, String logPrefix) throws IOException {
        // 1. 尝试从我们自己的OSS存储中获取已解析的文件内容
        Optional<String> cachedContent = tryGetFromParsedFileCache(parsedPath, logPrefix);
        if (cachedContent.isPresent()) {
            return cachedContent.get();
        }

        // 2. 如果缓存未命中，则通过Kimi API进行提取
        log.info("{} [Kimi] OSS缓存未命中，准备通过API提取文件内容: {}", logPrefix, qcUploadFile.getName());
        return extractContentViaKimiAndCache(qcUploadFile, parsedPath, config, logPrefix);
    }

    /**
     * [重构自 reparseUploadFile]
     * 公开方法，用于强制重新解析文件并更新OSS缓存。
     */
    public void reparseUploadFile(String logPrefix, QcUploadFile qcUploadFile, String parsedPath) throws IOException {
        log.info("{} [Kimi] 强制重新解析文件: {}", logPrefix, qcUploadFile.getName());
        final var config = initLLAConfig();
        extractContentViaKimiAndCache(qcUploadFile, parsedPath, config, logPrefix);
    }

    /**
     * 通过Kimi API提取文件内容，并缓存结果到OSS。
     * [重构自 uploadFile 和 reparseUploadFile]
     */
    private String extractContentViaKimiAndCache(QcUploadFile qcUploadFile, String parsedPath, LLAConfig config, String logPrefix) throws IOException {
        File tempFile = null;
        try {
            // 下载文件到本地临时文件
            final var uri = qcUploadFile.getBasePath() + qcUploadFile.getPath() + qcUploadFile.getName();
            tempFile = downloadToTemp(uri, qcUploadFile.getExt(), logPrefix);

            // 上传文件到Kimi files API
            String fileId = uploadToKimiFilesApi(tempFile, config);
            log.info("{} [Kimi] 文件上传成功, fileId: {}", logPrefix, fileId);

            // 获取文件内容
            String fileContent = getKimiFileContent(fileId, config, logPrefix);
            // 删除文件
            deleteFile(fileId, config);

            // 将解析后的内容上传到我们自己的OSS作为缓存
            uploadParsedContentToStorage(parsedPath, fileContent, logPrefix);

            return fileContent;
        } finally {
            // 清理临时文件
            if (tempFile != null) {
                FileUtil.del(tempFile);
            }
        }
    }

    /**
     * 从我们自己的文件存储（如OSS）中尝试获取已解析的文件内容。
     */
    private Optional<String> tryGetFromParsedFileCache(String parsedPath, String logPrefix) {
        final var fullPath = ossBasePath + parsedPath;
        final var fileInfo = new FileInfo().setPlatform(ossPlatform).setFilename(fullPath);

        if (fileStorageService.exists(fileInfo)) {
            log.info("{} [Kimi] OSS缓存命中: {}", logPrefix, parsedPath);
            try {
                byte[] contentBytes = fileStorageService.download(fileInfo).bytes();
                // Kimi返回的content本身就是一个JSON字符串，需要再次解析
                final var contentResponse = objectMapper.readValue(contentBytes, KimiFileContentResponse.class);
                return Optional.of(objectMapper.writeValueAsString(contentResponse));
            } catch (IOException e) {
                log.error("{} [Kimi] 读取或解析OSS缓存文件失败: {}", logPrefix, parsedPath, e);
            }
        }
        return Optional.empty();
    }

    private String uploadToKimiFilesApi(File file, LLAConfig config) throws IOException {
        RequestBody fileBody = RequestBody.create(file, MediaType.parse("application/octet-stream"));
        RequestBody requestBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("file", file.getName(), fileBody)
                .addFormDataPart("purpose", PURPOSE_FILE_EXTRACT)
                .build();

        Request request = new Request.Builder()
                .url(KIMI_API_BASE_URL + KIMI_API_FILES_ENDPOINT)
                .header("Authorization", "Bearer " + config.getApiKey())
                .post(requestBody)
                .build();

        try (Response response = okHttpClient.newCall(request).execute()) {
            final var responseBody = response.body();
            if (!response.isSuccessful() || responseBody == null) {
                final var errorMsg = responseBody != null ? responseBody.string() : "Empty response body";
                throw new IOException("Kimi文件上传失败: " + errorMsg);
            }
            return objectMapper.readValue(responseBody.string(), KimiFileResponse.class).id();
        }
    }

    public void deleteFile(String fileId, LLAConfig config) throws IOException {
        Request request = new Request.Builder()
                .url("https://api.moonshot.cn/v1/files/" + fileId)
                .addHeader("Authorization", config.getApiKey())
                .delete()
                .build();

        try (Response response = okHttpClient.newCall(request).execute()) {
            final var responseBody = response.body();
            if (!response.isSuccessful() || responseBody == null) {
                final var errorMsg = responseBody != null ? responseBody.string() : "Empty response body";
                throw new IOException("Kimi文件删除失败: " + errorMsg);
            }
        }
    }


    private String getKimiFileContent(String fileId, LLAConfig config, String logPrefix) throws IOException {
        Request request = new Request.Builder()
                .url(KIMI_API_BASE_URL + KIMI_API_FILES_ENDPOINT + "/" + fileId + KIMI_API_CONTENT_ENDPOINT)
                .header("Authorization", "Bearer " + config.getApiKey())
                .get()
                .build();

        try (Response response = okHttpClient.newCall(request).execute()) {
            final var responseBody = response.body();
            if (!response.isSuccessful() || responseBody == null) {
                final var errorMsg = responseBody != null ? responseBody.string() : "Empty response body";
                throw new IOException("获取Kimi文件内容失败: " + errorMsg);
            }
            // Kimi返回的是一个JSON对象，我们直接将其作为字符串返回
            return responseBody.string();
        }
    }

    private void uploadParsedContentToStorage(String parsedPath, String content, String logPrefix) {
        try (InputStream is = JsonUtils.jsonToInputStream(content)) {
            fileStorageService.of(is)
                    .setPlatform(ossPlatform)
                    .setPath(FilenameUtils.getPath(parsedPath))
                    .setSaveFilename(FilenameUtils.getName(parsedPath))
                    .setContentType(CONTENT_TYPE_JSON)
                    .upload();
            log.info("{} [Kimi] 文件解析结果已缓存至OSS: {}", logPrefix, parsedPath);
        } catch (Exception e) {
            log.error("{} [Kimi] 上传解析缓存到OSS失败: {}", logPrefix, parsedPath, e);
            // 不向上抛出，避免主流程失败，但记录严重错误
        }
    }

    private File downloadToTemp(String uri, String ext, String logPrefix) {
        log.debug("{} [Kimi] 开始下载原始文件: {}", logPrefix, uri);
        final var tempFile = FileUtil.createTempFile("." + ext, true);
        fileStorageService.download(new FileInfo().setPlatform(ossPlatform).setFilename(uri)).file(tempFile);
        log.debug("{} [Kimi] 原始文件已下载至临时文件: {}", logPrefix, tempFile.getAbsolutePath());
        return tempFile;
    }

    /**
     * 理想情况下，应从请求参数、会话或更灵活的配置中获取模型信息。
     */
    private LLAConfig initLLAConfig() {
        QcAiAgentModel qcAiAgentModel = qcAiAgentModelMapper.selectById(93L);
        return LLAConfig.builder()
                .apiKey(qcAiAgentModel.getKey())
                .secretKey(qcAiAgentModel.getSecret())
                .stream(false)
                .provider(LLAProvider.getEnumByValue(qcAiAgentModel.getVendor()))
                .model(qcAiAgentModel.getModel())
                .build();
    }

    public String generateParsedFilePath(Long tenantId, Long intentId, String originalFilename) {
        return tenantId + "/" + intentId + "/" + PARSED_FILE_PATH_PREFIX + generateHashId(originalFilename) + PARSED_FILE_SUFFIX;
    }

    private String generateHashId(String input) {
        try {
            final var digest = MessageDigest.getInstance(HASH_ALGORITHM);
            final byte[] hashBytes = digest.digest(input.getBytes(StandardCharsets.UTF_8));
            final var hex = new StringBuilder();
            for (byte b : hashBytes) {
                hex.append(String.format("%02x", b));
            }
            return hex.substring(0, HASH_ID_LENGTH);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("生成文件哈希ID失败，算法不可用: " + HASH_ALGORITHM, e);
        }
    }
}