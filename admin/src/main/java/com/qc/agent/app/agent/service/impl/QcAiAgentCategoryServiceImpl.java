package com.qc.agent.app.agent.service.impl;

import com.qc.agent.app.agent.mapper.QcAiAgentCategoryMapper;
import com.qc.agent.app.agent.model.po.QcAiAgentCategory;
import com.qc.agent.app.agent.service.QcAiAgentCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class QcAiAgentCategoryServiceImpl implements QcAiAgentCategoryService {

    @Autowired
    private QcAiAgentCategoryMapper qcAiAgentCategoryMapper;

    @Override
    public int add(QcAiAgentCategory category) {
        return qcAiAgentCategoryMapper.insert(category);
    }

    @Override
    public int delete(Long id) {
        return qcAiAgentCategoryMapper.deleteById(id);
    }

    @Override
    public int update(QcAiAgentCategory category) {
        return qcAiAgentCategoryMapper.update(category);
    }

    @Override
    public QcAiAgentCategory getById(Long id) {
        return qcAiAgentCategoryMapper.selectById(id);
    }

    @Override
    public List<QcAiAgentCategory> list() {
        return qcAiAgentCategoryMapper.selectAll();
    }
}
