package com.qc.agent.app.agent.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.qc.agent.app.agent.model.entity.InsightDimensionRefItemRel;

/**
 * 维度配置引用数据项关系表 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InsightDimensionRefItemRelMapper {

    /**
     * 根据ID查询关系
     */
    InsightDimensionRefItemRel selectById(@Param("id") Long id);

    /**
     * 根据维度配置ID查询关系列表
     */
    List<InsightDimensionRefItemRel> selectByDimensionConfigId(@Param("dimensionConfigId") Long dimensionConfigId);

    /**
     * 根据维度配置ID列表查询关系列表
     */
    List<InsightDimensionRefItemRel> selectByDimensionConfigIds(
            @Param("dimensionConfigIds") List<Long> dimensionConfigIds);

    /**
     * 插入关系
     */
    int insert(InsightDimensionRefItemRel rel);

    /**
     * 更新关系
     */
    int updateById(InsightDimensionRefItemRel rel);

    /**
     * 根据ID删除关系
     */
    int deleteById(@Param("id") Long id);

    /**
     * 根据维度配置ID删除关系
     */
    int deleteByDimensionConfigId(@Param("dimensionConfigId") Long dimensionConfigId);

    /**
     * 根据维度配置ID列表批量删除关系
     */
    int deleteByDimensionConfigIds(@Param("dimensionConfigIds") List<Long> dimensionConfigIds);
}