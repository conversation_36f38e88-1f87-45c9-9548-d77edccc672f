package com.qc.agent.app.question.impl.baidu;

import com.alibaba.fastjson.JSONObject;
import com.qc.agent.platform.sse.SseMessage;
import com.qc.agent.platform.sse.SseMessageConverter;
import com.qc.agent.platform.sse.SseRequest;
import com.qc.agent.platform.sse.dto.MessageType;
import org.apache.commons.lang3.StringUtils;

/**
 * @description：TODO
 * @author： sc
 * @create： 2024/6/22 13:57
 */
public class BaiduMessageConverter implements SseMessageConverter<String> {

    @Override
    public SseMessage convert(String message, SseRequest request) {
        if(StringUtils.isNotEmpty(message)){
            return SseMessage.builder().message(message).messageType(MessageType.TEXT).build();
        }else{
            JSONObject jsonObject = JSONObject.parseObject(message);
            String result = jsonObject.getString("result");
            return SseMessage.builder().message(result).messageType(MessageType.TEXT).build();
        }
    }
}
