package com.qc.agent.app.agent.mapper;

import com.qc.agent.app.agent.model.dto.QcAiAgentKnowledgeAuthorityDTO;
import com.qc.agent.app.agent.model.po.QcAiAgentConversationQuote;
import com.qc.agent.app.agent.model.po.QcAiAgentKnowledge;
import com.qc.agent.app.agent.model.query.QcAiAgentKnowledgeQuery;
import com.qc.agent.app.workflow.inner.pojo.QcAiAgentknowledgeDO;
import com.qc.agent.app.workflow.inner.pojo.QcAiKnowledgeRelated;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface QcAiAgentKnowledgeMapper {

    void insert(QcAiAgentKnowledge qcAiAgentKnowledge);

    void batchInsert(@Param("list") List<QcAiAgentKnowledge> records);

    void update(QcAiAgentKnowledge qcAiAgentKnowledge);

    void deleteByAgentId(@Param("agentId") Long agentId);

    /**
     * 根据agentId查询关联的知识库id
     *
     * @param agentId
     * @return
     */
    List<QcAiKnowledgeRelated> selectAssociatedKnowledgeList(@Param("agentId") Long agentId,
                                                             @Param("deptIdList") List<Long> deptIdList,
                                                             @Param("userId") Long userId);

    List<Long> selectKnowledgeByAgentId(@Param("id") Long id);

    /**
     * 根据agentId查询关联的知识库授权信息
     *
     * @param query
     * @return
     */
    List<QcAiAgentKnowledgeAuthorityDTO> selectAssociatedKnowledgeAuthorityList(QcAiAgentKnowledgeQuery query);

    /**
     * 查询引用的记录
     *
     * @param query
     * @return
     */
    List<QcAiAgentConversationQuote> selectQuoteList(QcAiAgentKnowledgeQuery query);

    /**
     * 查询知识库
     * @param list
     * @return
     */
    List<QcAiKnowledgeRelated> selectKnowledgeList(@Param("list") List<Long> list,
                                                   @Param("deptIdList") List<Long> deptIdList,
                                                   @Param("userId") Long userId);
}
