package com.qc.agent.app.api_suit.mapper;

import com.qc.agent.app.api_suit.model.po.QcAiAgentModuleApi;
import com.qc.agent.app.api_suit.model.vo.ModuleApiVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface QcAiAgentModuleApiMapper {
    ModuleApiVO selectById(@Param("id") Long id);

    List<ModuleApiVO> selectAll();

    List<ModuleApiVO> selectByModuleId(@Param("moduleId") Long moduleId);

    int insert(QcAiAgentModuleApi record);

    int update(QcAiAgentModuleApi record);

    int delete(@Param("id") Long id);
}
