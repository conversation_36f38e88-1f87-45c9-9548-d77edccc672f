package com.qc.agent.app.outside_interface.config;

import com.qc.agent.app.outside_interface.interceptor.ApiAuthInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 * @date 2025-03-17
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {
    @Autowired
    private ApiAuthInterceptor apiAuthInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(apiAuthInterceptor)
                .addPathPatterns("/ai-agent/api/extract_data/**");
    }
}
