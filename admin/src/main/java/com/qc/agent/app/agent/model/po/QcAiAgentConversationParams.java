package com.qc.agent.app.agent.model.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QcAiAgentConversationParams {

    /**
     * 主键 ID
     */
    private Long id;

    /**
     * 状态，默认值 '1'
     */
    private String status;

    /**
     * 创建人 ID
     */
    private Long creatorId;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 回答时间
     */
    private LocalDateTime answerTime;

    /**
     * 对话 ID
     */
    private Long sessionId;

    /**
     * 大模型 ID
     */
    private Long modelId;

    /**
     * 用户 ID
     */
    private Long userId;

    /**
     * 智能体 ID
     */
    private Long agentId;

    /**
     * 问题
     */
    private String question;

    /**
     * 问题 token 数
     */
    private BigDecimal questionToken;

    /**
     * 回答
     */
    private String answer;

    /**
     * 回答 token 数
     */
    private BigDecimal answerToken;

    /**
     * 会话时长（单位：秒）
     */
    private BigDecimal conversationTime;

    /**
     * 对话状态
     */
    private String conversationStatus;

    /**
     * 问题时间
     */
    private LocalDateTime askTimeStart;

    /**
     * 问题时间
     */
    private LocalDateTime askTimeEnd;

    /**
     * 评价类型 0:不满意 1:满意 2:未反馈
     */
    private String evaluationType;

    /**
     * 意图id
     */
    private List<String> intentIds;

    private List<String> tokenTypeList;

    private String column;

    private String sort;

    private String filter;

    private Integer page;

    private Integer rows;

    /**
     * 是否超出知识库范围 1:是 0：否
     */
    private String isOutOfScope;
}
