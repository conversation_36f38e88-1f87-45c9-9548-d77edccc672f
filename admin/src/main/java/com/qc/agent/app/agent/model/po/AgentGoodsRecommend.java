package com.qc.agent.app.agent.model.po;

import com.alibaba.fastjson.JSON;
import com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.model.domain.Product;
import lombok.Data;


import java.util.Date;
import java.util.List;

@Data
public class AgentGoodsRecommend {
    private Long id;
    private Long storeId;
    private String source;
    private String timestamp;
    private String unsoldGoods;
    private String recommendGoods;
    private String recommendQuestions;
    private Date createDate;

    public List<String> getUnsoldGoodsList() {
        return JSON.parseArray(unsoldGoods, String.class);
    }

    public void setUnsoldGoodsList(List<Product> questions) {
        this.unsoldGoods = JSON.toJSONString(questions);
    }

    public List<Product> getRecommendGoodsList() {
        return JSON.parseArray(recommendGoods, Product.class);
    }

    public void setRecommendGoodsList(List<Product> recommendGoods) {
        this.recommendGoods = JSON.toJSONString(recommendGoods);
    }

    public List<String> getRecommendQuestionsList() {
        return JSON.parseArray(recommendQuestions, String.class);
    }

    public void setRecommendQuestionsList(List<String> recommendQuestions) {
        this.recommendQuestions = JSON.toJSONString(recommendQuestions);
    }
}