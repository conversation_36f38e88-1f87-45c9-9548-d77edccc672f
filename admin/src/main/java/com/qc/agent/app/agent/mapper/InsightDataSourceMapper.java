package com.qc.agent.app.agent.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.qc.agent.app.agent.model.entity.InsightDataSource;

/**
 * 数据源表 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InsightDataSourceMapper {

    /**
     * 根据ID查询数据源
     */
    InsightDataSource selectById(@Param("id") Long id);

    /**
     * 查询所有数据源
     */
    List<InsightDataSource> selectAll();

    /**
     * 根据状态查询数据源列表
     */
    List<InsightDataSource> selectByStatus(@Param("status") String status);

    /**
     * 插入数据源
     */
    int insert(InsightDataSource dataSource);

    /**
     * 更新数据源
     */
    int updateById(InsightDataSource dataSource);

    /**
     * 根据ID删除数据源
     */
    int deleteById(@Param("id") Long id);

    /**
     * 根据维度编码查询数据源
     */
    List<InsightDataSource> selectByBelongDimensionCode(@Param("belongDimensionCode") String belongDimensionCode);

    /**
     * 根据数据源编码查询数据源
     */
    List<InsightDataSource> selectBySourceCode(@Param("sourceCode") String sourceCode);
}