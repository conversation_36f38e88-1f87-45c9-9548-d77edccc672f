package com.qc.agent.app.workflow.inner.impl.customer_insight_assistant.service.processor;

import com.qc.agent.app.workflow.inner.impl.customer_insight_assistant.model.CustomerInsightBusinessIntent;

/**
 * 客户洞察处理器接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 * @version 1.0.0
 */
public interface CustomerInsightProcessor {
    
    /**
     * 生成回答
     * 
     * @param intent 业务意图
     * @param logPrefix 日志前缀
     * @return 生成的回答
     */
    String generateAnswer(CustomerInsightBusinessIntent intent, String logPrefix);
    
    /**
     * 获取支持的意图类型
     * 
     * @return 支持的意图类型
     */
    String getSupportedIntent();
} 