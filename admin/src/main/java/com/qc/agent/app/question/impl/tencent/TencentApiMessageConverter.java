package com.qc.agent.app.question.impl.tencent;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.qc.agent.platform.sse.SseMessage;
import com.qc.agent.platform.sse.SseMessageConverter;
import com.qc.agent.platform.sse.SseRequest;
import com.qc.agent.platform.sse.dto.MessageType;
import com.tencentcloudapi.common.SSEResponseModel;
import org.apache.commons.lang3.StringUtils;

public class TencentApiMessageConverter implements SseMessageConverter<SSEResponseModel.SSE> {
    @Override
    public SseMessage convert(SSEResponseModel.SSE data, SseRequest request) {
        JSONObject jsonObject = JSON.parseObject(data.Data);
        JSONObject choice = jsonObject.getJSONArray("Choices").getJSONObject(0);
        SseMessage message = SseMessage.builder().messageType(MessageType.TEXT).build();
        if (StringUtils.isNotEmpty(choice.getString("FinishReason"))) {
            message.setEnd(true);
        }
        JSONObject delta = choice.getJSONObject("Delta");
        message.setMessage(delta.getString("Content"));
        return message;
    }
}
