package com.qc.agent.app.agent.controller;

import com.qc.agent.app.agent.model.po.QcAiAgentCategory;
import com.qc.agent.app.agent.service.QcAiAgentCategoryService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/ai-agent/agent-category")
public class QcAiAgentCategoryController {

    @Resource
    private QcAiAgentCategoryService qcAiAgentCategoryService;

    @PostMapping("/add")
    public int add(@RequestBody QcAiAgentCategory category) {
        return qcAiAgentCategoryService.add(category);
    }

    @DeleteMapping("/delete/{id}")
    public int delete(@PathVariable Long id) {
        return qcAiAgentCategoryService.delete(id);
    }

    @PutMapping("/update")
    public int update(@RequestBody QcAiAgentCategory category) {
        return qcAiAgentCategoryService.update(category);
    }

    @GetMapping("/list")
    public List<QcAiAgentCategory> list() {
        return qcAiAgentCategoryService.list();
    }

    @GetMapping("/get/{id}")
    public QcAiAgentCategory getById(@PathVariable Long id) {
        return qcAiAgentCategoryService.getById(id);
    }
}
