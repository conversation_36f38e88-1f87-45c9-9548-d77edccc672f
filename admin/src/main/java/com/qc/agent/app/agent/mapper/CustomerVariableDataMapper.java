package com.qc.agent.app.agent.mapper;

import com.qc.agent.app.agent.model.entity.CustomerVariableData;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 客户洞察变量数据Mapper接口
 */
public interface CustomerVariableDataMapper {

    /**
     * 根据客户ID查询变量数据列表
     *
     * @param customerId 客户ID
     * @return 变量数据列表
     */
    List<CustomerVariableData> selectByCustomerId(@Param("customerId") Long customerId);

    /**
     * 根据对话ID查询变量数据列表
     *
     * @param conversationId 对话ID
     * @return 变量数据列表
     */
    List<CustomerVariableData> selectByConversationId(@Param("conversationId") Long conversationId);

    /**
     * 根据客户ID和对话ID查询变量数据列表
     *
     * @param customerId 客户ID
     * @param conversationId 对话ID
     * @return 变量数据列表
     */
    List<CustomerVariableData> selectByCustomerIdAndConversationId(
            @Param("customerId") Long customerId,
            @Param("conversationId") Long conversationId);

    /**
     * 根据提示词变量记录ID查询变量数据列表
     *
     * @param promptVariableRecordId 提示词变量记录ID
     * @return 变量数据列表
     */
    List<CustomerVariableData> selectByPromptVariableRecordId(@Param("promptVariableRecordId") Long promptVariableRecordId);

    /**
     * 插入变量数据
     *
     * @param customerVariableData 变量数据
     * @return 影响行数
     */
    int insert(CustomerVariableData customerVariableData);

    /**
     * 批量插入变量数据
     *
     * @param customerVariableDataList 变量数据列表
     * @return 影响行数
     */
    int batchInsert(@Param("customerVariableDataList") List<CustomerVariableData> customerVariableDataList);

    /**
     * 根据ID更新变量数据
     *
     * @param customerVariableData 变量数据
     * @return 影响行数
     */
    int updateById(CustomerVariableData customerVariableData);

    /**
     * 根据客户ID删除变量数据
     *
     * @param customerId 客户ID
     * @return 影响行数
     */
    int deleteByCustomerId(@Param("customerId") Long customerId);

    /**
     * 根据对话ID删除变量数据
     *
     * @param conversationId 对话ID
     * @return 影响行数
     */
    int deleteByConversationId(@Param("conversationId") Long conversationId);

    /**
     * 根据客户ID和对话ID删除变量数据
     *
     * @param customerId 客户ID
     * @param conversationId 对话ID
     * @return 影响行数
     */
    int deleteByCustomerIdAndConversationId(
            @Param("customerId") Long customerId,
            @Param("conversationId") Long conversationId);
}
