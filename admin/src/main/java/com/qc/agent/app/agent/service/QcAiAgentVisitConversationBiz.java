package com.qc.agent.app.agent.service;

import com.qc.agent.app.agent.model.dto.QcAiAgentChatDTO;
import com.qc.agent.app.agent.model.vo.VisitAgentVO;
import com.qc.agent.common.core.Message;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-04-09
 */
public interface QcAiAgentVisitConversationBiz {

    VisitAgentVO chat(QcAiAgentChatDTO qcAiAgentChatDTO);
    Map<String,Object> customerVisitList(QcAiAgentChatDTO qcAiAgentChatDTO);

    Message addVisitPlan(QcAiAgentChatDTO qcAiAgentChatDTO);
}
