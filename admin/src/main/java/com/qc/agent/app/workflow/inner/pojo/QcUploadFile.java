package com.qc.agent.app.workflow.inner.pojo;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 上传文件信息实体类
 */
@Data
public class QcUploadFile {

    /** 主键ID */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;

    /** 状态 */
    private String status;

    /** 创建人ID */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long createUserId;

    /** 创建人姓名 */
    private String createUserName;

    /** 创建时间 */
    private LocalDateTime createTime;

    /** 修改时间 */
    private LocalDateTime modifyTime;

    /** 修改人ID */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long modifyUserId;

    /** 修改人姓名 */
    private String modifyUserName;

    /** 文件名称 */
    private String name;

    /** 文件大小 */
    private BigDecimal size;

    /** 平台 */
    private String platform;

    /** 文件路径-绝对路径 */
    private String url;

    /** 基础路径 */
    private String basePath;

    /**
     * 意图id
     */
    private Long intentId;

    /** 相对路径 */
    private String path;

    /** 文件后缀 */
    private String ext;
}
