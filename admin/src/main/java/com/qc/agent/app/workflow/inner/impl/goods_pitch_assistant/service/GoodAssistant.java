package com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.service;

import com.qc.agent.app.agent.model.dto.QcAiAgentCommonParam;
import com.qc.agent.app.agent.model.po.QcAiAgent;
import com.qc.agent.app.agent.model.po.QcAiAgentConversation;
import com.qc.agent.app.workflow.inner.AgentType;
import com.qc.agent.app.workflow.inner.impl.AbstractIAgentInvoker;
import com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.model.dto.BusinessIntent;
import com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.model.enums.GoodsIntent;
import com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.processor.IntentProcessor;
import com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.service.tool.RecommendQuestionTool;
import com.qc.agent.app.workflow.inner.impl.tools.LLMTools;
import com.qc.agent.common.core.Message;
import com.qc.agent.lla.model.LLAConfig;
import com.qc.agent.lla.model.LLARequest;
import com.qc.agent.platform.sse.SseClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class GoodAssistant extends AbstractIAgentInvoker {

    private final Map<GoodsIntent, IntentProcessor> processors;

    @Autowired
    private RecommendQuestionTool recommendQuestionTool;

    @Autowired
    public GoodAssistant(List<IntentProcessor> processorList) {
        this.processors = processorList.stream()
                .collect(Collectors.toMap(
                        IntentProcessor::getSupportedIntent,
                        p -> p));
        processors.put(GoodsIntent.SALES_INTO_INPUT, processors.get(GoodsIntent.SALES_INTO));
        processors.put(GoodsIntent.CUSTOMER_SCHEME_INPUT, processors.get(GoodsIntent.CUSTOMER_SCHEME));
    }

    @Override
    public AgentType determineAgent() {
        return AgentType.PRODUCT_ASSISTANT;
    }

    @Override
    public Message invoke(QcAiAgent agent, LLAConfig config, LLARequest request, SseClient client) {
        String logPrefix = LLMTools.getLogPrefix(request, agent);
        return getProcessor(agent, logPrefix)
                .map(processor -> {
                    BusinessIntent intent = null;
                    if (agent.getCommonParam() != null) {
                        intent = agent.getCommonParam().getGoodsIntent();
                    }
                    return processor.process(request, config, client, agent, intent == null ? null : intent.entities());
                })
                .orElseGet(() -> {
                    LLMTools.directReturnMessage(client, request, logPrefix, "参数解析有误");
                    return Message.of().ok();
                });
    }

    @Override
    public Message recommendPrompt(QcAiAgent agent, LLAConfig config, LLARequest request,
            QcAiAgentConversation conversation) {
        String logPrefix = LLMTools.getLogPrefix(request, agent);
        if (agent.getCommonParam() == null) {
            log.warn(
                    "{} No entities were extracted from the user query. Falling back to providing suggested questions.",
                    logPrefix);
            List<String> list = recommendQuestionTool.selectQuestions(conversation, logPrefix);
            return Message.of().data(list).ok();
        }
        return getProcessor(agent, logPrefix)
                .map(processor -> {
                    BusinessIntent intent = agent.getCommonParam().getGoodsIntent();
                    return processor.generateRecommend(request, config, agent, intent.entities(), conversation);
                })
                .orElse(Message.of().error("参数解析有误"));
    }

    /**
     * 提取公共处理器逻辑
     */
    private Optional<IntentProcessor> getProcessor(QcAiAgent agent, String logPrefix) {
        QcAiAgentCommonParam commonParam = agent.getCommonParam();
        if (commonParam == null || commonParam.getGoodsIntent() == null) {
            return Optional.of(processors.get(GoodsIntent.OTHER));
        }

        BusinessIntent intent = commonParam.getGoodsIntent();
        IntentProcessor processor = processors.get(intent.type());
        if (processor == null) {
            log.warn("{} 未找到意图类型 '{}' 的处理器", logPrefix, intent.type());
            return Optional.empty();
        }

        return Optional.of(processor);
    }

}
