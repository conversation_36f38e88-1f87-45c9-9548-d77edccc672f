package com.qc.agent.app.agent.model.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QcAiAgentConversation {

    /**
     * 主键 ID
     */
    private Long id;

    /**
     * 状态，默认值 '1'
     */
    private String status;
    /**
     * 前端传入的唯一id
     */
    private String chatSessionId;

    /**
     * 创建人 ID
     */
    private Long creatorId;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 回答时间
     */
    private LocalDateTime answerTime;

    /**
     * 对话 ID
     */
    private Long sessionId;

    /**
     * 大模型 ID
     */
    private Long modelId;

    /**
     * 用户 ID
     */
    private Long userId;

    /**
     * 智能体 ID
     */
    private Long agentId;

    /**
     * 问题
     */
    private String question;

    /**
     * 问题 token 数
     */
    private BigDecimal questionToken;

    /**
     * 回答
     */
    private String answer;

    /**
     * 回答 token 数
     */
    private BigDecimal answerToken;

    /**
     * 会话时长（单位：秒）
     */
    private BigDecimal conversationTime;

    /**
     * 意图id
     */
    private Long intentId;

    /**
     * 对话状态
     */
    private String conversationStatus;

    private String tokens;

    /**
     * 回答失败
     */
    public static final String CONVERSATION_FAIL = "-1";

    /**
     * 回答中
     */
    public static final String IN_CONVERSATION = "0";

    /**
     * 回答成功
     */
    public static final String CONVERSATION_SUCCESS = "1";

    /**
     *  来源：web悬浮窗：web-levitate ； web端：web ； app端：app
     */
    private String source;

    public static final String WEB_SOURCE = "1";

    public static final String CLIENT_SOURCE = "2";

}
