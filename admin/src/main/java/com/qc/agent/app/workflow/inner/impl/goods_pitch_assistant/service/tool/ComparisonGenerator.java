package com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.service.tool;

import com.qc.agent.app.workflow.ListenerInvoker;
import com.qc.agent.app.workflow.WorkflowLLAClient;
import com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.model.domain.Product;
import com.qc.agent.app.workflow.inner.impl.tools.LLMTools;
import com.qc.agent.lla.model.LLAConfig;
import com.qc.agent.lla.model.LLAProvider;
import com.qc.agent.lla.model.LLARequest;
import com.qc.agent.lla.model.LLAResponse;
import com.qc.agent.lla.sdk.factory.AiClientFactory;
import com.qc.agent.lla.sdk.model.ModelMessage;
import com.qc.agent.lla.sdk.model.ModelParam;
import com.qc.agent.platform.sse.SseClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

import static com.qc.agent.utils.LlmPrompts.*;

/**
 * 竞品对比内容生成器 (Refactored)
 * 负责生成与产品销售和竞品分析相关的内容，包括：
 * 1. 某个产品的竞品列表。
 * 2. 本品与特定竞品的详细对比分析。
 * 3. 围绕产品销售的推荐问题。
 *
 * <AUTHOR>
 * @date 2025-06-23
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Service
@Slf4j
public class ComparisonGenerator {

    /**
     * 生成某个产品的主要竞品列表。
     * (异步流式响应)
     *
     * @param product   我们自己的产品。
     * @param request   The unique request ID for tracking.
     * @param config    The LLM configuration.
     * @param client    The SSE client to stream the response to.
     * @param logPrefix The log prefix for consistent logging.
     */
    public void listCompetitors(Product product, LLARequest request, LLAConfig config, SseClient client, String logPrefix) {
        // 1. 准备该场景特定的用户提示
        String userPrompt = LIST_COMPETITORS_PROMPT_TEMPLATE.formatted(
                product.getBrand(), product.getName()
        );
        request.setSystem(MARKET_RESEARCH_EXPERT_PROMPT);
        request.setContent(userPrompt);
        WorkflowLLAClient.sendAsync(config, request, client);
    }

    /**
     * 生成本品与竞品的详细对比分析，并站在本品角度给出销售建议。
     * (异步流式响应)
     *
     * @param ourProduct     我们自己的产品。
     * @param competitorName 竞品的名称。
     * @param request        The unique request ID for tracking.
     * @param config         The LLM configuration.
     * @param client         The SSE client to stream the response to.
     * @param logPrefix      The log prefix for consistent logging.
     */
    public void generateDetailedComparison(Product ourProduct, String competitorName, LLARequest request, LLAConfig config, SseClient client, String logPrefix) {
        // 1. 准备该场景特定的用户提示
        String ourProductDisplayName = "%s %s".formatted(ourProduct.getBrand(), ourProduct.getName());
        String userPrompt = DETAILED_COMPARISON_PROMPT_TEMPLATE.formatted(
                ourProductDisplayName,
                competitorName
        );
        request.setSystem(SALES_ADVISOR_PROMPT);
        request.setContent(userPrompt);
        WorkflowLLAClient.sendAsync(config, request, client);
    }

    // --- Private Helper Method ---

    /**
     * 封装了与LLM交互的通用逻辑。
     *
     * @param systemPrompt LLM扮演的角色或上下文指令。
     * @param userPrompt   用户的具体问题。
     * @param request      The request context.
     * @param config       The LLM configuration.
     * @param client       The SSE client.
     * @param logPrefix    The log prefix.
     */
    private void executeLlmChat(String systemPrompt, String userPrompt, LLARequest request, LLAConfig config, SseClient client, String logPrefix) {
        var modelParam = ModelParam.build()
                .withApiKey(config.getApiKey())
                .withModel(config.getModel());

        var modelMessage = ModelMessage.builder()
                .systemMessage(systemPrompt)
                .userMessage(userPrompt)
                .build();

        // 两个方法都使用KIMI_SEARCH
        LLAResponse response = AiClientFactory.getClient(LLAProvider.KIMI_SEARCH)
                .chat(modelParam, modelMessage);

        ListenerInvoker.invoke(response, request);
        LLMTools.directReturnMessage(client, request, logPrefix, response.getContent());
    }

    /*
     * 生成卖进门店的销售话术。
     * */
    public void generateSalesIntoPitchWithPromotion(
            String productName,
            String brandName,
            String salesChannel,
            String promotionInfo,
            LLARequest request,
            LLAConfig config,
            SseClient client,
            String logPrefix
    ) {
        String userPrompt = String.format(
                """
                        请根据产品【%s】，品牌【%s】，卖进渠道：【%s】生成一段结构清晰、
                        内容专业、语言自然、没有注释的销售话术，总字数控制在400字以内。。
                        目标是将【%s】下的【%s】卖进【%s】中。
                        输出格式必须如下：
                        【开场白】
                        <自然建立联系>
                                                
                        【产品介绍】
                        <突出产品卖点>
                                                
                        【利润和动销】
                        <突出利益>
                                                
                        【促销支持】
                        <根据商品：【%s】 卖进渠道：【%s】 促销方案：【%s】推送促销>
                                                
                        【行动号召】
                        <促成交易,引导下单>
                                                
                        【应对拒绝】
                        <店主/采购方可能担心的问题>
                                                
                        严格要求：
                           每个模块都必须写
                           内容只能是话术正文，不能有注释、说明、括号提示""",
                productName, brandName, salesChannel,
                brandName, productName, salesChannel,
                productName, salesChannel, promotionInfo
        );
        LLARequest llmRequest = createLlmRequest(SALES_INTO_PROMPT, userPrompt, request, logPrefix);
        log.info("{} 发送销售话术请求(含促销信息)到模型 {}", logPrefix, config.getModel());
        WorkflowLLAClient.sendAsync(config, llmRequest, client);

    }

    /**
     * 生成围绕产品销售的推荐问题。
     * (同步阻塞式响应)
     *
     * @param request   The original LLARequest to inherit the ID from.
     * @param config    The LLM configuration.
     * @param product   The product to generate questions for.
     * @param logPrefix The log prefix for consistent logging.
     * @param count     The number of questions to generate.
     * @return A list of generated questions. Returns an empty list if generation fails or the response is empty.
     */
    public List<String> generateRecommendQuestions(LLARequest request, LLAConfig config, Product product, String logPrefix, int count) {
        String userPrompt = """
                ## 角色
                你是一位经验丰富的金牌销售顾问，任务是站在零售店老板的角度，为他们提供在决定是否引进新商品时，需要向供应商提出的关键问题。

                ## 任务
                为商品“%s”生成 %d 个最关键的销售推荐问题。

                ## 问题核心要点
                问题必须围绕零售店老板最关心的商业决策点，例如：
                - **市场与推广**: 是否提供市场推广支持、开业物料支持。
                - **竞争与定位**: 相比竞品的核心优势、目标客群。

                ## 输出要求
                1. **精准数量**: 严格生成 %d 个问题。
                2. **格式简洁**: 每个问题占一行，不要带任何序号、项目符号或其他多余文字。
                3. **强制包含全称**: 每个问题中都必须【一字不差地】使用上方【核心信息】中提供的%s，严禁任何形式的缩写、简化或更改。例如，必须使用“800G黄豆酱/大众装/自动/1X12”，而不是“G黄豆酱”或“这款黄豆酱”。
                4. **问题直接**: 问题应简明扼要，直击要害。
                5. **主题限制**: 绝对不要提问关于“%s”的历史动销数据（如销售速度、周转率）、顾客复购率、利润空间和最小起订量相关的问题。
                """.formatted(
                product.getName(),
                count,
                count,
                product.getName(),
                product.getName()
        );

        return executeQuestionGeneration(request, config, SALES_ADVISOR_PROMPT, userPrompt, logPrefix);
    }

    /**
     * 生成围绕产品销售的推荐问题。
     * (同步阻塞式响应)
     *
     * @param request   The original LLARequest to inherit the ID from.
     * @param config    The LLM configuration.
     * @param question  user question.
     * @param logPrefix The log prefix for consistent logging.
     * @param count     The number of questions to generate.
     * @return A list of generated questions. Returns an empty list if generation fails or the response is empty.
     */
    public List<String> generateRecommendQuestions(LLARequest request, LLAConfig config, String question, String logPrefix, int count) {
        String userPrompt = """
                生成与%s相关的%d个问题。
                要求：
                1. 基于要卖进门店的老板会关注的问题
                2. 每个问题占一行。
                3. 直接输出问题本身，不要包含序号、项目符号或任何其他额外信息。
                4. 问题简短一点，回答总字数不要超过80
                """.formatted(question, count);

        return executeQuestionGeneration(request, config, SALES_ADVISOR_PROMPT, userPrompt, logPrefix);
    }

    /**
     * 根据通用上下文生成围绕产品销售的推荐问题。
     * (同步阻塞式响应)
     *
     * @param request   The original LLARequest to inherit the ID from. 其 content 将被用作生成问题的上下文。
     * @param config    The LLM configuration.
     * @param logPrefix The log prefix for consistent logging.
     * @param count     The number of questions to generate.
     * @return A list of generated questions. Returns an empty list if generation fails or the response is empty.
     */
    public List<String> generateRecommendQuestions(LLARequest request, LLAConfig config, String logPrefix, int count) {
        // 优化了 Prompt，使其更清晰并包含了 count 参数
        String userPrompt = """
                请根据以下信息生成 %d 个与商品销售相关的推荐问题。
                                
                上下文信息：
                %s
                                
                要求：
                1. 每个问题占一行。
                2. 直接输出问题本身，不要包含序号、项目符号或任何其他额外信息。
                """.formatted(count, request.getContent());

        return executeQuestionGeneration(request, config, GENERAL_SALES_PROMPT, userPrompt, logPrefix);
    }

    /**
     * 执行 LLM 请求并解析结果的核心逻辑。
     *
     * @param request      原始请求
     * @param config       LLM 配置
     * @param systemPrompt 系统级 Prompt
     * @param userPrompt   用户级 Prompt
     * @param logPrefix    日志前缀
     * @return 解析后的问题列表
     */
    private List<String> executeQuestionGeneration(LLARequest request, LLAConfig config, String systemPrompt, String userPrompt, String logPrefix) {
        log.debug("{} Generated user prompt:\n---\n{}\n---", logPrefix, userPrompt);

        LLARequest llmRequest = createLlmRequest(systemPrompt, userPrompt, request, logPrefix);
        llmRequest.setSkipAnswerUpdate(true);

        log.info("{} Sending sync request to LLM for generating recommend questions with model '{}'.", logPrefix, config.getModel());
        request.setSkipAnswerUpdate(true);
        LLAResponse response = WorkflowLLAClient.send(config, llmRequest);
        return parseLlmResponseToQuestions(response.getContent(), logPrefix);
    }

    /**
     * 将LLM返回的原始字符串解析为问题列表。
     *
     * @param rawContent LLM返回的原始文本内容
     * @param logPrefix  日志前缀
     * @return 清理和过滤后的问题列表
     */
    private List<String> parseLlmResponseToQuestions(String rawContent, String logPrefix) {
        if (!StringUtils.hasText(rawContent)) {
            log.warn("{} LLM returned an empty or null response for recommend questions.", logPrefix);
            return Collections.emptyList();
        }

        log.debug("{} Raw LLM response:\n---\n{}\n---", logPrefix, rawContent);

        // 使用 Stream API 和更健壮的清理逻辑来解析响应
        List<String> questions = rawContent.lines() // Java 8+: 按行分割成流，能处理 \n, \r, \r\n
                .map(this::cleanQuestionLine)
                .filter(StringUtils::hasText) // 过滤掉处理后变为空白的行
                .toList(); // Java 16+: 收集到不可变列表，更简洁

        log.info("{} Successfully parsed {} questions from LLM response.", logPrefix, questions.size());
        return questions;
    }

    /**
     * 清理单行问题，移除序号、项目符号和多余的引号。
     *
     * @param line 原始行
     * @return 清理后的行
     */
    private String cleanQuestionLine(String line) {
        // 移除行首的数字、点、-、*、) 等列表标记和空格
        String cleaned = line.trim().replaceAll("^[\\d .)*-]+", "");
        // 移除包裹问题的引号（中英文）
        cleaned = cleaned.replaceAll("^[“”\"]|[“”\"]$", "");
        return cleaned.trim();
    }


    /**
     * 私有辅助方法，用于封装创建LLM请求对象的通用逻辑。
     * 集中管理请求的构建，减少重复代码。
     *
     * @param systemPrompt 系统提示词。
     * @param userPrompt   用户的具体请求。
     * @param requestId    唯一的请求ID。
     * @param logPrefix    日志前缀。
     * @return A configured {@link LLARequest} object.
     */
    private LLARequest createLlmRequest(String systemPrompt, String userPrompt, LLARequest request, String logPrefix) {
        log.info("{} Constructing LLM request with ID '{}'.", logPrefix, request.getId());
        return LLARequest.builder()
                .id(request.getId())
                .clientId(request.getClientId())
                .system(systemPrompt)
                .content(userPrompt)
                .start(LocalDateTime.now())
                .build();
    }
}