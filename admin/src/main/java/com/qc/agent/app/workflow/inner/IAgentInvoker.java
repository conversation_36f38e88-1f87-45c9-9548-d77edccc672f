package com.qc.agent.app.workflow.inner;

import com.qc.agent.app.agent.model.po.QcAiAgent;
import com.qc.agent.app.agent.model.po.QcAiAgentConversation;
import com.qc.agent.common.core.Message;
import com.qc.agent.lla.model.LLAConfig;
import com.qc.agent.lla.model.LLARequest;
import com.qc.agent.platform.sse.SseClient;

/**
 * 代理调用器
 *
 * <AUTHOR>
 * @date 2025/3/19 10:29:45
 *
 */
public interface IAgentInvoker
{
    AgentType determineAgent();

    Message invoke(QcAiAgent qcAiAgent, LLAConfig config, LLARequest request, SseClient client);

    default Message recommendPrompt(QcAiAgent qcAiAgent, LLAConfig config, LLARequest request, QcAiAgentConversation conversation){
        return Message.of().ok();
    }
}
