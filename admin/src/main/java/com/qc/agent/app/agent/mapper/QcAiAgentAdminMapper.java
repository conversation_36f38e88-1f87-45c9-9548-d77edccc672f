package com.qc.agent.app.agent.mapper;

import com.qc.agent.app.agent.model.po.QcAiAgentAdminChart;
import com.qc.agent.app.agent.model.query.QcAiAgentAdminQuery;
import com.qc.agent.app.outside_interface.pojo.QcAiAgentConversationDO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-07
 */
public interface QcAiAgentAdminMapper {
    List<QcAiAgentAdminChart> selectDialogueChart(QcAiAgentAdminQuery query);

    /**
     * 活跃用户数
     *
     * @param query
     * @return
     */
    List<QcAiAgentAdminChart> selectActiveUserChart(QcAiAgentAdminQuery query);

    /**
     * 消耗TOKEN数
     *
     * @param query
     * @return
     */
    List<QcAiAgentAdminChart> selectTokenChart(QcAiAgentAdminQuery query);

    List<QcAiAgentAdminChart> selectAverageDialogueChart(QcAiAgentAdminQuery query);

    /**
     * 查询对话记录列表
     *
     * @return
     */
    List<QcAiAgentConversationDO> selectConversationRecordList();

    /**
     * 查询所有租户id
     *
     * @return
     */
    List<Long> selectAllTenantIds();

    /**
     * 查询去重用户数
     *
     * @param query
     * @return
     */
    Long selectDistinctUserCount(QcAiAgentAdminQuery query);

    /**
     * 知识库问答成功率统计
     *
     * @param query
     * @return
     */
    List<QcAiAgentAdminChart> selectKnowledgeChart(QcAiAgentAdminQuery query);
}
