package com.qc.agent.app.agent.util;

import com.google.common.collect.Lists;

import java.util.List;
import java.util.function.Consumer;


/**
 * 数据库操作层公共方法
 *
 * <AUTHOR>
 * @date 2023-02-07
 */
public class DataBaseMapperUtils {

    private DataBaseMapperUtils() {
    }

    /**
     * 批量插入数量
     */
    public static final int LOOP_CACHE = 1000;

    /**
     * 批量插入
     * 分批插入，1000条插入一次
     *
     * @param list         实体类-list
     * @param listConsumer mapper的批量插入方法
     * @param <T>          实体类
     */
    public static <T> void batchInsert(List<T> list, Consumer<List<T>> listConsumer) {
        batchInsert(list, listConsumer, LOOP_CACHE);
    }

    /**
     * 批量插入
     * 分批插入，指定条数插入一次
     * 如果表字段比较多，建议把loopCount调小，防止sql过长
     *
     * @param list         实体类-list
     * @param listConsumer mapper的批量插入方法
     * @param <T>          实体类
     */
    public static <T> void batchInsert(List<T> list, Consumer<List<T>> listConsumer, int loopCount) {
        if (list != null && !list.isEmpty()) {
            if (list.size() > loopCount) {
                // list分割 每个LOOP_CACHE大小
                List<List<T>> loopList = Lists.partition(list, loopCount);
                for (List<T> listItem : loopList) {
                    listConsumer.accept(listItem);
                }
            } else {
                listConsumer.accept(list);
            }
        }
    }

    //    方法相同名称上做区分

    /**
     * 批量删除
     * 分批删除，指定条数删除一次
     *
     * @param list         实体类-list
     * @param listConsumer mapper的批量插入方法
     * @param <T>          实体类
     */
    public static <T> void batchDelete(List<T> list, Consumer<List<T>> listConsumer) {
        batchInsert(list, listConsumer);
    }

}
