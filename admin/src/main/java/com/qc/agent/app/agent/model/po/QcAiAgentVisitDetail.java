package com.qc.agent.app.agent.model.po;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025-04-09
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Data
public class QcAiAgentVisitDetail {
    /**
     * Primary key ID
     */
    private Long id;

    /**
     * Record status
     * <p>
     * Default value: '1' (active)
     * </p>
     */
    private String status;

    /**
     * Creator user ID
     */
    private Long creatorId;
    private Long userId;

    /**
     * Creator name
     */
    private String creatorName;

    /**
     * Record creation timestamp
     */
    private LocalDateTime createTime;

    /**
     * Last modifier user ID
     */
    private Long modifyierId;

    /**
     * Last modifier name
     */
    private String modifyierName;

    /**
     * Last modification timestamp
     */
    private LocalDateTime modifyTime;

    /**
     * Associated agent ID
     * <p>
     * References the AI agent this configuration belongs to.
     * </p>
     */
    private Long agentId;

    /**
     * Retrieval radius in meters
     * <p>
     * Defines the geographic search radius for visit data collection.
     * </p>
     */
    private Double retrieveRadius;

    /**
     * Search scope in days
     * <p>
     * Defines the temporal search window for visit data collection.
     * </p>
     */
    private Double searchScopeDays;
}
