package com.qc.agent.app.workflow.inner.mapper;

import com.qc.agent.app.agent.model.po.QcAiAgentConversationQuote;
import com.qc.agent.app.knowledge.model.query.KnowledgeQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-04-01
 */
public interface QcAiAgentConversationQuoteMapper {
    void batchInsert(@Param("list")List<QcAiAgentConversationQuote> list);

    List<QcAiAgentConversationQuote> selectListByConverstaionId(KnowledgeQuery query);
}
