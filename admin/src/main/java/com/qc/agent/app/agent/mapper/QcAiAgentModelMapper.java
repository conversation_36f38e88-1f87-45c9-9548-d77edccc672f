package com.qc.agent.app.agent.mapper;

import com.qc.agent.app.agent.model.po.CredentialRequest;
import com.qc.agent.app.agent.model.po.QcAiAgentModel;
import com.qc.agent.app.agent.model.vo.QcAiAgentModelVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface QcAiAgentModelMapper {

    int insert(QcAiAgentModel model);

    int deleteById(@Param("id") Long id);

    int update(QcAiAgentModel model);

    QcAiAgentModel selectById(@Param("id") Long id);

    List<QcAiAgentModelVO> selectAll();

    List<QcAiAgentModel> selectEnabledLLM();

    int remove(QcAiAgentModel model);

    int updateModelCredentials(CredentialRequest request);

    QcAiAgentModel selectByVendor(String vendor);

    List<QcAiAgentModelVO> selectPrivateModels();

    List<QcAiAgentModel> selectModelsByVendor(String vendor);

    int insertOrUpdate(QcAiAgentModel model);

    QcAiAgentModel selectByAgentId(@Param("agentId") Long agentId);
}
