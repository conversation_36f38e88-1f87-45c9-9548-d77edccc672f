package com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.service.tool;

import com.qc.agent.app.agent.model.po.QcAiAgentConversation;
import com.qc.agent.app.agent.util.DataBaseMapperUtils;
import com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.mapper.QcAiAgentRecommendQuestionsMapper;
import com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.model.domain.QcAiAgentRecommendQuestions;
import com.qc.agent.platform.register.UserManager;
import com.qc.agent.platform.util.UUIDUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * A utility component for managing recommended questions related to a conversation.
 * It provides methods to persist new recommended questions and retrieve existing ones from the database.
 *
 * <AUTHOR>
 * @date 2025-06-30
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RecommendQuestionTool {

    private final QcAiAgentRecommendQuestionsMapper qcAiAgentRecommendQuestionsMapper;

    /**
     * Persists a list of recommended questions to the database for a specific conversation.
     * Each question is associated with the current user and conversation context.
     *
     * @param conversation The conversation context for which the questions are being saved.
     * @param questions    The list of question strings to insert.
     * @param logPrefix    The logging prefix for tracing the request.
     */
    public void insertRecommendQuestions(final QcAiAgentConversation conversation, final List<String> questions, final String logPrefix) {
        final Long conversationId = conversation.getId();
        log.info("{} Attempting to insert recommended questions for conversationId: {}", logPrefix, conversationId);

        if (questions == null || questions.isEmpty()) {
            log.warn("{} No recommended questions provided to insert for conversationId: {}. Aborting.", logPrefix, conversationId);
            return;
        }

        final var tenantUser = UserManager.getTenantUser();
        final Long userId = tenantUser.getUserId();
        final String userName = tenantUser.getUserName();
        BigDecimal seq = BigDecimal.ZERO;
        final List<QcAiAgentRecommendQuestions> recordsToInsert = questions.stream()
                .map(question -> QcAiAgentRecommendQuestions.builder()
                        .id(UUIDUtils.getUUID2Long())
                        .status("1")
                        .creatorId(userId)
                        .createTime(LocalDateTime.now())
                        .creatorName(userName)
                        .conversationId(conversationId)
                        .content(question)
                        .seq(seq.add(BigDecimal.ONE))
                        .build())
                .toList();

        log.info("{} Preparing to batch-insert {} questions for conversationId: {}", logPrefix, recordsToInsert.size(), conversationId);
        DataBaseMapperUtils.batchInsert(recordsToInsert, qcAiAgentRecommendQuestionsMapper::batchInsert);
        log.info("{} Successfully batch-inserted {} questions for conversationId: {}", logPrefix, recordsToInsert.size(), conversationId);
    }

    /**
     * Retrieves all recommended questions associated with a given conversation.
     *
     * @param conversation The conversation context for which to retrieve questions.
     * @param logPrefix    The logging prefix for tracing the request.
     * @return A list of question strings. Returns an empty list if no questions are found.
     */
    public List<String> selectQuestions(final QcAiAgentConversation conversation, final String logPrefix) {
        final Long conversationId = conversation.getId();
        log.info("{} Selecting recommended questions for conversationId: {}", logPrefix, conversationId);

        final List<String> foundQuestions = qcAiAgentRecommendQuestionsMapper.selectQuestions(conversationId);

        log.info("{} Found {} recommended questions for conversationId: {}", logPrefix, foundQuestions.size(), conversationId);
        return foundQuestions;
    }
}