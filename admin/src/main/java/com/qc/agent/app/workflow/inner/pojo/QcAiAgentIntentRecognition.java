package com.qc.agent.app.workflow.inner.pojo;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 勤策ai agent意图识别表
 *
 * <AUTHOR>
 * @date 2025-05-27
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Data
public class QcAiAgentIntentRecognition {
    /**
     * 主键ID
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;

    /**
     * 状态
     */
    private String status;

    /**
     * 创建人ID
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改人ID
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long modifyierId;

    /**
     * 修改人名称
     */
    private String modifyierName;

    /**
     * 修改时间
     */
    private LocalDateTime modifyTime;

    /**
     * agent ID
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long agentId;

    /**
     * 类型 1：知识库 2：文件
     */
    private String type;

    /**
     * 意图名称
     */
    private String intentName;

    /**
     * 意图编码
     */
    private String intentCode;

    /**
     * 知识库-召回设置-最大召回数量
     */
    private Integer maxRecallCount;
    /**
     * 知识库-召回设置-最小匹配度
     */
    private Double minMatchThreshold;
    /**
     * 知识库-召回设置-qa最小匹配度
     */
    private Double qaMinMatchThreshold;
    /**
     * 知识库-召回设置-检索范围 1:仅引用知识库 2:引用知识库+模型通用知识库
     */
    private String searchScope;

    private Long tenantId;

    /**
     * 空结果回复
     */
    private String nullResultAnswer;

    /**
     * 关联知识库详情
     */
    private List<QcAiKnowledgeRelated> relateKnowledgeList;

    /**
     * 关联文件详情
     */
    private List<QcUploadFile> relateFileList;
}
