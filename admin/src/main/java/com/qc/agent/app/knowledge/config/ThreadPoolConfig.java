package com.qc.agent.app.knowledge.config;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.apache.tomcat.util.threads.ThreadPoolExecutor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025-07-12
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Configuration
public class ThreadPoolConfig {
    /**
     * 知识库文件导入专用线程池
     * 由Spring管理生命周期，避免在业务代码中手动创建和销毁。
     * @return ExecutorService Bean
     */
    @Bean("knowledgeImportExecutor")
    public ExecutorService knowledgeImportExecutor() {
        ThreadFactory threadFactory = new ThreadFactoryBuilder()
                .setNameFormat("knowledge-import-%d")
                .build();
        // 使用合理的参数配置线程池
        return new ThreadPoolExecutor(
                10, // 核心线程数
                20, // 最大线程数
                60L, TimeUnit.SECONDS, // 空闲线程存活时间
                new LinkedBlockingQueue<>(200), // 队列容量
                threadFactory,
                new ThreadPoolExecutor.CallerRunsPolicy() // 拒绝策略：调用者线程执行
        );
    }
}
