package com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.processor;

import com.alibaba.fastjson.JSONObject;
import com.qc.agent.app.agent.model.po.QcAiAgent;
import com.qc.agent.app.agent.model.po.QcAiAgentConversation;
import com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.model.enums.GoodsIntent;
import com.qc.agent.common.core.Message;
import com.qc.agent.lla.model.LLAConfig;
import com.qc.agent.lla.model.LLARequest;
import com.qc.agent.platform.sse.SseClient;

public interface IntentProcessor {
    Message process(LLARequest request, LLAConfig config, SseClient client, QcAiAgent agentModel, JSONObject entities);

    GoodsIntent getSupportedIntent();

    /**
     * 生成推荐
     *
     * @param request
     * @param config
     * @param agentModel
     * @param entities
     * @return
     */
    Message generateRecommend(LLARequest request, LLAConfig config, QcAiAgent agentModel, JSONObject entities, QcAiAgentConversation conversation);
}
