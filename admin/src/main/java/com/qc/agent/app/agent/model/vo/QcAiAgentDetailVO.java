package com.qc.agent.app.agent.model.vo;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.qc.agent.app.agent.model.po.QcAiAgentAuthorityDistributeDetail;
import com.qc.agent.app.workflow.inner.pojo.QcAiAgentIntentRecognition;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QcAiAgentDetailVO {

    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;

    /**
     * 智能体名称
     */
    private String name;

    /**
     * logo域名
     */
    private String logoDomain;

    /**
     * 智能体logo
     */
    private String logo;

    /**
     * 提示词
     */
    private String prompt;

    /**
     * 是否内置智能体（1：内置智能体，0：自定义智能体）
     */
    private String internalFlag;

    /**
     * 联网搜索
     */
    private String internetSearch;

    /**
     * 上下文检索数量
     */
    private BigDecimal contextSearchAmount;

    /**
     * 描述
     */
    private String description;

    /**
     * 开场白
     */
    private String introduction;

    /**
     * 常见问题
     */
    private String leadingQuestion;

    /**
     * 空结果回复
     */
    private String nullResultAnswer;

    /**
     * 报错回复
     */
    private String errorResultAnswer;

    /**
     * 模型id
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long modelId;

    /**
     * 模型名称
     */
    private String modelName;

    /**
     * 采样温度
     */
    private BigDecimal modelTemperature;

    /**
     * 核采样的概率阈值
     */
    private BigDecimal modelTopP;

    /**
     * 请求返回的最大 Token 数
     */
    private BigDecimal modelMaxTokens;
    /**
     * 知识库-召回设置-最大召回数量
     */
    private Integer maxRecallCount;
    /**
     * 知识库-召回设置-最小匹配度
     */
    private Double minMatchThreshold;
    /**
     * 知识库-召回设置-qa最小匹配度
     */
    private Double qaMinMatchThreshold;
    /**
     * 知识库-召回设置-检索范围 1:仅引用知识库 2:引用知识库+模型通用知识库
     */
    private String searchScope;
    /**
     * 考勤助手-将问题拆分为sql的提示词
     */
    private String splitSqlPrompt;
    /**
     * 业务提示词
     */
    private String bizPrompt;
    /**
     * 发布状态 1：已发布 0：未发布
     */
    private String publishFlag;

    /**
     * 意图识别是否启用 1：是 0：否
     */
    private String intentIsEnabled;

    private List<QcAiAgentAuthorityDistributeDetail> authorityDistributeDetailList;

    /**
     * 意图相关
     */
    private List<QcAiAgentIntentRecognition> intentList;

    /**
     * 知识库
     */
    private List<AgentKnowledge> knowledge;

    /**
     * 参数配置
     */
    private Map<String, String> extConfigs;

    /**
     * 洞察维度配置
     * 根据洞察模式返回不同的配置结构
     */
    private InsightConfigWorkspaceVO.InsightModeConfiguration insightDimensionConfigurations;

    /**
     * 洞察总结配置
     */
    private InsightSummaryConfigVO insightSummaryConfiguration;

    /**
     * 客户洞察可用配置选项
     */
    private InsightConfigWorkspaceVO.AvailableOptions insightAvailableOptions;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class AgentKnowledge {

        @JSONField(serializeUsing = ToStringSerializer.class)
        private Long id;

        /**
         * 知识库id
         */
        @JSONField(serializeUsing = ToStringSerializer.class)
        private Long knowledgeId;

        /**
         * 知识库名称
         */
        private String knowledgeName;
        /**
         * 头像全路径
         */
        private String knowledgeFaceFullPath;

        /**
         * 1:正常 0：已删除
         */
        private String status;

        /**
         * 1：发布 0：未发布/已取消发布
         */
        private String publishFlag;
    }
}
