package com.qc.agent.app.agent.service.impl;

import com.qc.agent.app.agent.mapper.CustomerVariableDataMapper;
import com.qc.agent.app.agent.model.entity.CustomerVariableData;
import com.qc.agent.app.agent.service.CustomerVariableDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 客户洞察变量数据服务实现类
 */
@Slf4j
@Service
public class CustomerVariableDataServiceImpl implements CustomerVariableDataService {

    @Resource
    private CustomerVariableDataMapper customerVariableDataMapper;

    @Override
    public List<CustomerVariableData> getVariableDataByCustomerId(Long customerId) {
        if (customerId == null) {
            return null;
        }
        return customerVariableDataMapper.selectByCustomerId(customerId);
    }

    @Override
    public List<CustomerVariableData> getVariableDataByConversationId(Long conversationId) {
        if (conversationId == null) {
            return null;
        }
        return customerVariableDataMapper.selectByConversationId(conversationId);
    }

    @Override
    public List<CustomerVariableData> getVariableDataByCustomerIdAndConversationId(Long customerId, Long conversationId) {
        if (customerId == null || conversationId == null) {
            return null;
        }
        return customerVariableDataMapper.selectByCustomerIdAndConversationId(customerId, conversationId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveCustomerVariableData(Long customerId, Long conversationId, Map<Long, String> variableDataMap) {
        if (customerId == null || conversationId == null || CollectionUtils.isEmpty(variableDataMap)) {
            return false;
        }

        // 先删除现有的数据
        customerVariableDataMapper.deleteByCustomerIdAndConversationId(customerId, conversationId);

        // 构建新的数据列表
        List<CustomerVariableData> dataList = new ArrayList<>();
        for (Map.Entry<Long, String> entry : variableDataMap.entrySet()) {
            CustomerVariableData data = new CustomerVariableData();
            data.setCustomerId(customerId);
            data.setConversationId(conversationId);
            data.setPromptVariableRecordId(entry.getKey());
            data.setVariableValue(entry.getValue());
            data.setStatus("1");
            data.setCreateTime(LocalDateTime.now());
            dataList.add(data);
        }

        // 批量插入
        return customerVariableDataMapper.batchInsert(dataList) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchSaveCustomerVariableData(List<CustomerVariableData> customerVariableDataList) {
        if (CollectionUtils.isEmpty(customerVariableDataList)) {
            return false;
        }

        // 设置默认值
        for (CustomerVariableData data : customerVariableDataList) {
            if (data.getStatus() == null) {
                data.setStatus("1");
            }
            if (data.getCreateTime() == null) {
                data.setCreateTime(LocalDateTime.now());
            }
        }

        return customerVariableDataMapper.batchInsert(customerVariableDataList) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCustomerVariableData(CustomerVariableData customerVariableData) {
        if (customerVariableData == null || customerVariableData.getId() == null) {
            return false;
        }

        customerVariableData.setModifyTime(LocalDateTime.now());
        return customerVariableDataMapper.updateById(customerVariableData) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteVariableDataByCustomerIdAndConversationId(Long customerId, Long conversationId) {
        if (customerId == null || conversationId == null) {
            return false;
        }
        return customerVariableDataMapper.deleteByCustomerIdAndConversationId(customerId, conversationId) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteVariableDataByConversationId(Long conversationId) {
        if (conversationId == null) {
            return false;
        }
        return customerVariableDataMapper.deleteByConversationId(conversationId) > 0;
    }

    @Override
    public Map<Long, String> getVariableDataMap(Long customerId, Long conversationId) {
        List<CustomerVariableData> dataList = getVariableDataByCustomerIdAndConversationId(customerId, conversationId);
        
        Map<Long, String> resultMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(dataList)) {
            for (CustomerVariableData data : dataList) {
                resultMap.put(data.getPromptVariableRecordId(), data.getVariableValue());
            }
        }
        
        return resultMap;
    }
}
