package com.qc.agent.app.workflow.inner.impl.customer_insight_assistant.service.processor;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 客户洞察处理器工厂
 * 
 * <AUTHOR>
 * @since 1.0.0
 * @version 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CustomerInsightProcessorFactory {

    private final List<CustomerInsightProcessor> processors;
    
    // 延迟初始化processorMap
    private Map<String, CustomerInsightProcessor> processorMap;

    /**
     * 初始化处理器映射
     */
    @PostConstruct
    public void initProcessorMap() {
        if (processorMap == null) {
            processorMap = processors.stream()
                    .collect(Collectors.toMap(
                            CustomerInsightProcessor::getSupportedIntent,
                            Function.identity()
                    ));
            log.info("初始化处理器映射完成，共加载 {} 个处理器", processors.size());
        }
    }

    /**
     * 根据意图类型获取对应的处理器
     * 
     * @param intentType 意图类型
     * @return 对应的处理器，如果不存在则返回null
     */
    public CustomerInsightProcessor getProcessor(String intentType) {
        // 确保processorMap已初始化
        if (processorMap == null) {
            initProcessorMap();
        }
        
        CustomerInsightProcessor processor = processorMap.get(intentType);
        if (processor == null) {
            log.warn("未找到意图类型 '{}' 对应的处理器", intentType);
        }
        return processor;
    }

    /**
     * 获取所有处理器
     * 
     * @return 所有处理器列表
     */
    public List<CustomerInsightProcessor> getAllProcessors() {
        return processors;
    }
} 