package com.qc.agent.app.agent.enums;

/**
 * 智能体ID枚举类
 * 用于标识不同的AI智能体
 */
public enum AgentId {

    /**
     * 商品话术助手
     */
    PRODUCT_RECOMMENDATION_ASSISTANT(6L, "商品话术助手"),

    /**
     * 客户洞察助手
     */
    CUSTOMER_INSIGHTS_ASSISTANT(8L, "客户洞察助手");

    private final Long id;
    private final String name;

    AgentId(Long id, String name) {
        this.id = id;
        this.name = name;
    }

    public Long getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    /**
     * 根据ID查找对应的枚举值
     *
     * @param id Agent ID
     * @return 对应的枚举值，如果未找到则返回null
     */
    public static AgentId fromId(Long id) {
        for (AgentId agentId : AgentId.values()) {
            if (agentId.getId().equals(id)) {
                return agentId;
            }
        }
        return null;
    }

    /**
     * 根据名称查找对应的枚举值
     *
     * @param name Agent名称
     * @return 对应的枚举值，如果未找到则返回null
     */
    public static AgentId fromName(String name) {
        for (AgentId agentId : AgentId.values()) {
            if (agentId.getName().equals(name)) {
                return agentId;
            }
        }
        return null;
    }
}
