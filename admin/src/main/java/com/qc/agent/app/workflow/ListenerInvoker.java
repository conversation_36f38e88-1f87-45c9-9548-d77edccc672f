package com.qc.agent.app.workflow;

import com.qc.agent.common.core.RequestHolder;
import com.qc.agent.common.core.TenantUser;
import com.qc.agent.lla.ILLACompletionListener;
import com.qc.agent.lla.model.LLARequest;
import com.qc.agent.lla.model.LLAResponse;
import com.qc.agent.lla.util.LLAUtils;

import java.util.List;

/**
 *
 *
 * <AUTHOR>
 * @date 2025/3/12 09:52:30
 *
 */
public class ListenerInvoker
{

    public static void invoke(LLAResponse response, LLARequest request){
        // 获取当前用户
        TenantUser tenantUser = RequestHolder.getThreadLocalUser();
        new Thread( ()->{
                // 设置异步线程内用户
                RequestHolder.setThreadLocalUser(tenantUser);
                // 完成后调用所有监听实现
                List<ILLACompletionListener> listeners = LLAUtils.getListeners();
                for(ILLACompletionListener listener : listeners)
                {
                    listener.onComplete(response, request);
                }
            }).start();
    }
}
