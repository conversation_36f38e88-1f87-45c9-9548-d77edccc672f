package com.qc.agent.app.knowledge.utils;

/**
 * <AUTHOR>
 * @date 2025-07-11
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
public class KnowledgeLogUtils {
    private KnowledgeLogUtils() {
    }

    public static String getLogPrefix(Long tenantId, Long collectionId, Long dataId) {
        return "【tenantId=" + tenantId + "," + "collectionId=" + collectionId + "," + "dataId" + dataId + "】";
    }


    public static String getLogPrefix(Long tenantId, Long collectionId) {
        return "【tenantId=" + tenantId + "," + "collectionId=" + collectionId + "】";
    }
}
