package com.qc.agent.app.workflow.inner;

import com.qc.agent.app.agent.model.po.QcAiAgent;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Configuration;

import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 代理调用器构建器
 *
 * <AUTHOR>
 * @date 2025/3/19 13:18:03
 *
 */
@Configuration
public class AgentInvokerBuilder implements ApplicationContextAware
{
    static Map<AgentType, IAgentInvoker> invokers = new ConcurrentHashMap<>();

    @Override
    public void setApplicationContext(@NotNull ApplicationContext applicationContext) throws BeansException
    {
        applicationContext.getBeansOfType(IAgentInvoker.class)
                .values()
                .forEach(agentInvoker -> invokers.put(agentInvoker.determineAgent(), agentInvoker));
    }

    public static IAgentInvoker getInvoker(QcAiAgent agent)
    {
        IAgentInvoker agentInvoker = invokers.get(agent.getId() > 10000 ? AgentType.DEFAULT : AgentType.getAgentType(String.valueOf(agent.getId())));

        return Optional.ofNullable(agentInvoker).orElseThrow(() -> new IllegalArgumentException("未找到指定类型的代理调用器"));
    }
}
