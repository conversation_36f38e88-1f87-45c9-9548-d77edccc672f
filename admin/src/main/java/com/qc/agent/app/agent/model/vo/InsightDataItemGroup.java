package com.qc.agent.app.agent.model.vo;

import java.io.Serializable;
import java.util.List;

import com.qc.agent.app.agent.model.entity.InsightDataItem;

import lombok.Data;

/**
 * 数据项分组视图对象
 * 用于表示按数据类型分组的数据项集合
 *
 * <AUTHOR>
 */
@Data
public class InsightDataItemGroup implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 数据类型代码
     * 例如：DETAIL（明细）、METRIC（指标）等
     */
    private String dataTypeCode;

    /**
     * 数据类型名称
     * 例如：明细、指标等
     */
    private String dataTypeName;

    /**
     * 该类型下的数据项列表
     */
    private List<InsightDataItem> dataItems;

    /**
     * 构造函数
     */
    public InsightDataItemGroup() {
    }

    /**
     * 构造函数
     * 
     * @param dataTypeCode 数据类型代码
     * @param dataTypeName 数据类型名称
     * @param dataItems    数据项列表
     */
    public InsightDataItemGroup(String dataTypeCode, String dataTypeName, List<InsightDataItem> dataItems) {
        this.dataTypeCode = dataTypeCode;
        this.dataTypeName = dataTypeName;
        this.dataItems = dataItems;
    }

    /**
     * 获取数据类型名称
     * 如果 dataTypeName 为空，则根据 dataTypeCode 返回默认名称
     * 
     * @return 数据类型名称
     */
    public String getDataTypeName() {
        if (dataTypeName != null && !dataTypeName.trim().isEmpty()) {
            return dataTypeName;
        }

        // 根据 dataTypeCode 返回默认名称
        if ("DETAIL".equals(dataTypeCode)) {
            return "明细";
        } else if ("INDICATOR".equals(dataTypeCode)) {
            return "指标";
        } else if ("METRIC".equals(dataTypeCode)) {
            return "指标";
        } else if ("DIMENSION".equals(dataTypeCode)) {
            return "维度";
        } else if ("ATTRIBUTE".equals(dataTypeCode)) {
            return "属性";
        } else {
            return dataTypeCode != null ? dataTypeCode : "未知类型";
        }
    }
}