package com.qc.agent.app.agent.mapper;

import com.qc.agent.app.agent.model.entity.QcAiSourceParamConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 数据源参数配置 Mapper 接口
 */
@Mapper
public interface QcAiSourceParamConfigMapper {

    /**
     * 根据ID查询参数配置
     *
     * @param id 主键ID
     * @return 参数配置
     */
    QcAiSourceParamConfig selectById(@Param("id") Long id);

    /**
     * 插入参数配置
     *
     * @param config 参数配置
     * @return 影响行数
     */
    int insert(QcAiSourceParamConfig config);

    /**
     * 根据ID更新参数配置
     *
     * @param config 参数配置
     * @return 影响行数
     */
    int updateById(QcAiSourceParamConfig config);

    /**
     * 根据ID删除参数配置
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 根据数据源ID查询参数配置
     *
     * @param sourceId 数据源ID
     * @return 参数配置列表
     */
    List<QcAiSourceParamConfig> selectBySourceId(@Param("sourceId") Long sourceId);

    /**
     * 根据数据源ID和分组编码查询参数配置
     *
     * @param sourceId 数据源ID
     * @param groupCode 分组编码
     * @return 参数配置列表
     */
    List<QcAiSourceParamConfig> selectBySourceIdAndGroupCode(@Param("sourceId") Long sourceId, 
                                                              @Param("groupCode") String groupCode);

    /**
     * 根据数据源ID查询所有分组信息（去重）
     *
     * @param sourceId 数据源ID
     * @return 分组信息列表
     */
    List<QcAiSourceParamConfig> selectGroupsBySourceId(@Param("sourceId") Long sourceId);
}
