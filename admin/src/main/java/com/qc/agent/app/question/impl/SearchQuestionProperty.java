package com.qc.agent.app.question.impl;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * @description：TODO
 * @author： sc
 * @create： 2024/3/7 15:48
 */
@Component
@Data
@ConfigurationProperties(prefix = "searchquestion")
public class SearchQuestionProperty {

    private Integer keepdays;

    private Double score;

}
