package com.qc.agent.app.question.impl.coze;

import com.alibaba.fastjson.JSONObject;
import com.qc.agent.platform.sse.SseMessage;
import com.qc.agent.platform.sse.SseMessageConverter;
import com.qc.agent.platform.sse.SseRequest;
import com.qc.agent.platform.sse.dto.MessageType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * @description：TODO
 * @author： sc
 * @create： 2024/5/31 8:43
 */
@Slf4j
public class CozeMessageConverter  implements SseMessageConverter<String> {
    @Override
    public SseMessage convert(String message, SseRequest request) {
        log.info("message:{}", message);

        SseMessage message1 = SseMessage.builder().messageType(MessageType.TEXT).build();
        if(StringUtils.isNotEmpty(message)){
            JSONObject jsonObject =  extractContentFromLine(message);
            if("done".equals(jsonObject.getString("event"))){
                message1.setEnd(true);
            }else{
                JSONObject messageObj = jsonObject.getJSONObject("message");

                if("answer".equals(messageObj.getString("type"))){
                    String content = messageObj.getString("content");
                    message1.setMessage(content);
                }else{
                    message1.setMessage("");
                }
            }
        }else{
            message1.setMessage("");
        }

        return message1;
    }

    private JSONObject extractContentFromLine(String line) {
        String dataPrefix = "data:{";

        if (line.startsWith(dataPrefix)) {
            return  JSONObject.parseObject(line.substring("data:".length()));
        }

        return null;
    }
}
