package com.qc.agent.app.agent.service.tool;

import com.qc.agent.app.agent.mapper.QcAiAgentConversationMapper;
import com.qc.agent.app.agent.model.dto.QcAiAgentChatDTO;
import com.qc.agent.app.agent.model.po.QcAiAgentConversation;
import com.qc.agent.app.agent.model.query.QcAiGoodsAssistantQuery;
import com.qc.agent.common.core.TenantUser;
import com.qc.agent.lla.model.LLAUsage;
import com.qc.agent.platform.register.UserManager;
import com.qc.agent.platform.util.UUIDUtils;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Optional;

/**
 * Utility component for creating and managing AI Agent conversation records.
 * Provides methods to assemble and insert conversation data into the database.
 *
 * <AUTHOR>
 * @date 2025-07-02
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class QcAiAgentConversationTool {

    private final QcAiAgentConversationMapper qcAiAgentConversationMapper;

    /**
     * Defines the status of a conversation record.
     */
    @Getter
    private enum ConversationRecordStatus {
        NORMAL("1"), // A standard conversation turn.
        PROMPT("2"); // A record representing a prompt-only interaction (e.g., system prompt evaluation).

        private final String value;

        ConversationRecordStatus(String value) {
            this.value = value;
        }

    }

    /**
     * Assembles a QcAiAgentConversation object from a chat DTO without persisting it.
     * This method is now an instance method for consistency within the Spring component.
     *
     * @param qcAiAgentChatDTO The data transfer object containing chat information.
     * @return A populated QcAiAgentConversation object.
     */
    public static QcAiAgentConversation assembleConversation(QcAiAgentChatDTO qcAiAgentChatDTO) {
        log.debug("Assembling conversation from DTO for user ID: {}", qcAiAgentChatDTO.getUserId());

        // Note: creatorId is from the DTO, while userId is the currently authenticated tenant user.
        // This can be useful for auditing if an admin is performing an action on behalf of a user.
        var currentUserId = Optional.ofNullable(UserManager.getTenantUser())
                .map(TenantUser::getUserId)
                .orElse(null);

        // Generate a new chat session ID if one is not provided.
        var chatSessionId = StringUtils.hasText(qcAiAgentChatDTO.getChatSessionId())
                ? qcAiAgentChatDTO.getChatSessionId()
                : UUIDUtils.getUUID2Long() + "";

        return QcAiAgentConversation.builder()
                .id(UUIDUtils.getUUID2Long())
                .status(ConversationRecordStatus.NORMAL.getValue())
                .creatorId(qcAiAgentChatDTO.getUserId())
                .creatorName(qcAiAgentChatDTO.getUserName())
                .createTime(LocalDateTime.now())
                .userId(currentUserId) // The user context performing the action
                .agentId(qcAiAgentChatDTO.getAgentId())
                .conversationStatus(QcAiAgentConversation.IN_CONVERSATION)
                .question(qcAiAgentChatDTO.getQuestion())
                .source(qcAiAgentChatDTO.getSource())
                .sessionId(qcAiAgentChatDTO.getSessionId())
                .chatSessionId(chatSessionId)
                .answer(qcAiAgentChatDTO.getAnswer())
                .answerTime(qcAiAgentChatDTO.getAnswerTime())
                .build();
    }

    /**
     * Creates and inserts a new conversation record into the database.
     *
     * @param query     The query object containing the user's question and context.
     * @param logPrefix A prefix for log messages to correlate related logs.
     * @return The persisted QcAiAgentConversation record.
     * @throws IllegalStateException if the user context cannot be found.
     */
    public QcAiAgentConversation assembleConversation(QcAiGoodsAssistantQuery query, String logPrefix) {
        var tenantUser = getCurrentUserOrThrow(logPrefix);
        Long userId = tenantUser.getUserId();
        String userName = tenantUser.getUserName();
        log.info("{} Attempting to insert new conversation for agentId: {} and userId: {}", logPrefix, query.getAgentId(), userId);

        return QcAiAgentConversation.builder()
                .id(UUIDUtils.getUUID2Long())
                .status(ConversationRecordStatus.NORMAL.getValue())
                .creatorId(userId)
                .creatorName(userName)
                .createTime(LocalDateTime.now())
                .userId(userId)
                .agentId(query.getAgentId())
                .conversationStatus(QcAiAgentConversation.IN_CONVERSATION)
                .question(query.getQuestion())
                .source(query.getSource())
                .sessionId(query.getSessionId())
                .chatSessionId(query.getChatSessionId()) // Use the session ID from the query
                .build();
    }

    /**
     * Creates and inserts a new conversation record into the database.
     *
     * @param query     The query object containing the user's question and context.
     * @param answer    The generated answer from the AI agent.
     * @param usage     Token usage statistics from the LLA model.
     * @param logPrefix A prefix for log messages to correlate related logs.
     * @return The persisted QcAiAgentConversation record.
     * @throws IllegalStateException if the user context cannot be found.
     */
    public QcAiAgentConversation insertConversation(QcAiGoodsAssistantQuery query, String answer, LLAUsage usage, String logPrefix) {
        var tenantUser = getCurrentUserOrThrow(logPrefix);
        Long userId = tenantUser.getUserId();
        String userName = tenantUser.getUserName();
        log.info("{} Attempting to insert new conversation for agentId: {} and userId: {}", logPrefix, query.getAgentId(), userId);

        QcAiAgentConversation record = QcAiAgentConversation.builder()
                .id(UUIDUtils.getUUID2Long())
                .status(ConversationRecordStatus.NORMAL.getValue())
                .creatorId(userId)
                .creatorName(userName)
                .createTime(LocalDateTime.now())
                .userId(userId)
                .agentId(query.getAgentId())
                .conversationStatus(QcAiAgentConversation.IN_CONVERSATION)
                .question(query.getQuestion())
                .source(query.getSource())
                .sessionId(query.getSessionId())
                .chatSessionId(query.getChatSessionId()) // Use the session ID from the query
                .answer(answer)
                .answerTime(LocalDateTime.now())
                // CORRECTED: Prompt tokens are for the question, completion tokens are for the answer.
                .questionToken(BigDecimal.valueOf(usage.getPromptTokens()))
                .answerToken(BigDecimal.valueOf(usage.getCompletionTokens()))
                .build();

        log.debug("{} Conversation record to be inserted: {}", logPrefix, record);
        int count = qcAiAgentConversationMapper.insert(record);

        if (count > 0) {
            log.info("{} Successfully inserted conversation with ID: {} for userId: {}", logPrefix, record.getId(), userId);
            return record;
        } else {
            // This case might warrant throwing an exception, as a failed DB insert is a significant issue.
            log.error("{} Failed to insert conversation into database for userId: {}. The insert operation returned 0.", logPrefix, userId);
            // Depending on desired behavior, could throw new RuntimeException("Failed to save conversation record");
            return null; // Keeping original behavior
        }
    }


    /**
     * Creates and inserts a prompt-only conversation record, typically for tracking costs
     * of system prompts or other non-user-facing interactions.
     *
     * @param agentId   The ID of the agent associated with the interaction.
     * @param usage     Token usage statistics from the LLA model.
     * @param logPrefix A prefix for log messages to correlate related logs.
     * @throws IllegalStateException if the user context cannot be found.
     */
    public void insertPromptRecord(Long agentId, LLAUsage usage, String logPrefix) {
        var tenantUser = getCurrentUserOrThrow(logPrefix);
        Long userId = tenantUser.getUserId();
        String userName = tenantUser.getUserName();
        log.info("{} Attempting to insert new prompt record for agentId: {} and userId: {}", logPrefix, agentId, userId);

        QcAiAgentConversation record = QcAiAgentConversation.builder()
                .id(UUIDUtils.getUUID2Long())
                .status(ConversationRecordStatus.PROMPT.getValue())
                .creatorId(userId)
                .creatorName(userName)
                .createTime(LocalDateTime.now())
                .userId(userId)
                .agentId(agentId)
                .conversationStatus(QcAiAgentConversation.CONVERSATION_SUCCESS) // Or a new dedicated status
                .source("web") // Or another source indicating system/prompt interaction
                // CORRECTED: Prompt tokens are for the question, completion tokens are for the answer.
                .questionToken(BigDecimal.valueOf(usage.getPromptTokens()))
                .answerToken(BigDecimal.valueOf(usage.getCompletionTokens()))
                .build();

        log.debug("{} Prompt record to be inserted: {}", logPrefix, record);
        int count = qcAiAgentConversationMapper.insert(record);

        if (count > 0) {
            log.info("{} Successfully inserted prompt record with ID: {} for userId: {}", logPrefix, record.getId(), userId);
        } else {
            log.error("{} Failed to insert prompt record into database for userId: {}. The insert operation returned 0.", logPrefix, userId);
        }
    }

    public void insertConversation(QcAiAgentConversation record,String logPrefix) {
        log.debug("{} conversation record to be inserted: {}", logPrefix, record);
        int count = qcAiAgentConversationMapper.insert(record);

        if (count > 0) {
            log.info("{} Successfully inserted conversation record with ID: {} ", logPrefix, record.getId());
        } else {
            log.error("{} Failed to insert conversation record into database. The insert operation returned 0.", logPrefix);
        }
    }

    /**
     * Updates an existing conversation record's question and answer.
     *
     * @param conversation The conversation object with updated fields.
     * @param logPrefix    A prefix for log messages.
     */
    public void updateConversation(QcAiAgentConversation conversation, String logPrefix) {
        log.info("{} Updating conversation with ID: {}", logPrefix, conversation.getId());
        int count = qcAiAgentConversationMapper.updateQuestionAndAnswer(conversation);
        if (count > 0) {
            log.info("{} Successfully updated conversation with ID: {}", logPrefix, conversation.getId());
        } else {
            log.error("{} Failed to update conversation with ID: {}. It may have been deleted.", logPrefix, conversation.getId());
        }
    }

    /**
     * Retrieves the current TenantUser from the security context or throws an exception.
     *
     * @param logPrefix for logging context
     * @return The non-null TenantUser.
     * @throws IllegalStateException if the TenantUser is not found in the context.
     */
    private TenantUser getCurrentUserOrThrow(String logPrefix) {
        TenantUser tenantUser = UserManager.getTenantUser();
        if (tenantUser == null) {
            log.error("{} Failed to process request: TenantUser not found in current context.", logPrefix);
            throw new IllegalStateException("User context not available. Cannot proceed.");
        }
        return tenantUser;
    }
}