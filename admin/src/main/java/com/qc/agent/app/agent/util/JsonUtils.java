package com.qc.agent.app.agent.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.util.StdDateFormat;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-25
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
public class JsonUtils {
    private static final ObjectMapper objectMapper = new ObjectMapper();

    static {
        // 注册 Java 8 日期时间模块
        objectMapper.registerModule(new JavaTimeModule()); // [[7]][[9]]

        // 美化输出
        objectMapper.enable(SerializationFeature.INDENT_OUTPUT);

        // 可选：自定义日期格式（如 ISO 8601）
        objectMapper.setDateFormat(new StdDateFormat());
    }

    /**
     * 单个实体转 JSON 字符串 [[5]]
     */
    public static String entityToJson(Object entity) {
        try {
            return objectMapper.writeValueAsString(entity);
        } catch (Exception e) {
            throw new RuntimeException("单实体转换失败", e);
        }
    }

    /**
     * 实体列表转 JSON 字符串 [[1]][[6]]
     */
    public static <T> String listToJson(List<T> entityList) {
        try {
            return objectMapper.writeValueAsString(entityList);
        } catch (Exception e) {
            throw new RuntimeException("列表转换失败", e);
        }
    }

    /**
     * JSON 字符串转 InputStream [[6]][[9]]
     */
    public static InputStream jsonToInputStream(String json) {
        return new ByteArrayInputStream(json.getBytes(StandardCharsets.UTF_8));
    }
}
