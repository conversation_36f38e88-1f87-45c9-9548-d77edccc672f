package com.qc.agent.app.agent.controller;

import com.qc.agent.app.agent.model.query.QcAiAgentAdminQuery;
import com.qc.agent.app.agent.service.QcAiAgentAdminService;
import com.qc.agent.common.core.Message;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025-03-07
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@RestController
@RequestMapping("/ai-agent/detail/analyze/")
public class QcAiAgentAdminController {

    @Resource
    private QcAiAgentAdminService qcAiAgentAdminService;

    @PostMapping("/chart")
    public Message chart(@RequestBody QcAiAgentAdminQuery query) {
        return Message.of().ok().data(qcAiAgentAdminService.chart(query));
    }


}
