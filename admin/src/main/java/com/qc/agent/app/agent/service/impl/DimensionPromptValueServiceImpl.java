package com.qc.agent.app.agent.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.qc.agent.app.agent.mapper.DimensionPromptValueMapper;
import com.qc.agent.app.agent.model.entity.DimensionPromptValue;
import com.qc.agent.app.agent.service.DimensionPromptValueService;

import lombok.extern.slf4j.Slf4j;

/**
 * 维度提示词片段值服务实现类
 */
@Slf4j
@Service
public class DimensionPromptValueServiceImpl implements DimensionPromptValueService {

    @Resource
    private DimensionPromptValueMapper dimensionPromptValueMapper;

    @Override
    public List<DimensionPromptValue> getPromptValuesByConfigId(Long configId) {
        if (configId == null) {
            return null;
        }
        return dimensionPromptValueMapper.selectByConfigId(configId);
    }

    @Override
    public DimensionPromptValue getPromptValueByConfigIdAndPromptFragmentId(Long configId,
            Long promptFragmentId) {
        if (configId == null || promptFragmentId == null) {
            return null;
        }
        return dimensionPromptValueMapper.selectByConfigIdAndPromptFragmentId(configId,
                promptFragmentId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean savePromptValue(DimensionPromptValue dimensionPromptValue) {
        if (dimensionPromptValue == null) {
            return false;
        }

        // 检查是否已存在相同的配置ID和提示词片段ID的记录
        DimensionPromptValue existingPromptValue = dimensionPromptValueMapper
                .selectByConfigIdAndPromptFragmentId(
                        dimensionPromptValue.getConfigId(), dimensionPromptValue.getPromptFragmentId());

        if (existingPromptValue == null) {
            // 新增
            dimensionPromptValue.setStatus("1");
            return dimensionPromptValueMapper.insert(dimensionPromptValue) > 0;
        } else {
            // 更新
            dimensionPromptValue.setId(existingPromptValue.getId());
            return dimensionPromptValueMapper.updateById(dimensionPromptValue) > 0;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean savePromptValues(List<DimensionPromptValue> dimensionPromptValues) {
        if (CollectionUtils.isEmpty(dimensionPromptValues)) {
            return false;
        }

        // 先删除现有的提示词片段值
        Long configId = dimensionPromptValues.get(0).getConfigId();
        if (configId != null) {
            dimensionPromptValueMapper.deleteByConfigId(configId);
        }

        // 批量插入新的提示词片段值
        for (DimensionPromptValue dimensionPromptValue : dimensionPromptValues) {
            dimensionPromptValue.setStatus("1");
        }
        return dimensionPromptValueMapper.batchInsert(dimensionPromptValues) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deletePromptValuesByConfigId(Long configId) {
        if (configId == null) {
            return false;
        }
        return dimensionPromptValueMapper.deleteByConfigId(configId) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deletePromptValueByConfigIdAndPromptFragmentId(Long configId,
            Long promptFragmentId) {
        if (configId == null || promptFragmentId == null) {
            return false;
        }
        return dimensionPromptValueMapper.deleteByConfigIdAndPromptFragmentId(configId,
                promptFragmentId) > 0;
    }
}