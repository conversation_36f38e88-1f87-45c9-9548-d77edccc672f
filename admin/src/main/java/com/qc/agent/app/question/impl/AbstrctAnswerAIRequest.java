package com.qc.agent.app.question.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.qc.agent.app.knowledge.mapper.KnowledgeFileMapper;
import com.qc.agent.app.knowledge.model.KnowledgeQuestionAnswerSettingInfo;
import com.qc.agent.app.knowledge.model.KnowledgeQuestionAnswerSettingParams;
import com.qc.agent.app.knowledge.model.QcKnowledgeFile;
import com.qc.agent.app.knowledge.service.KnowledgeQuestionAnswerSettingService;
import com.qc.agent.app.question.mapper.QcKnowledgeHistoryQuestionMapper;
import com.qc.agent.app.question.mapper.QcKnowledgeQuestionMapper;
import com.qc.agent.app.question.pojo.QcKnowledgeHistoryQuestion;
import com.qc.agent.app.question.pojo.QcKnowledgeQuestion;
import com.qc.agent.app.question.service.QcKnowledgeService;
import com.qc.agent.common.core.RequestHolder;
import com.qc.agent.common.util.UUIDUtils;
import com.qc.agent.platform.register.UserManager;
import com.qc.agent.platform.sse.AnswerAIRequest;
import com.qc.agent.platform.sse.AnswerAISubscriber;
import com.qc.agent.platform.sse.SseRequest;
import com.qc.agent.platform.sse.dto.AnswerAIConfig;
import com.qc.agent.platform.sse.dto.AnswerAIMessage;
import com.qc.agent.platform.util.ServiceCaller;
import com.qc.agent.redis.RedisClient;
import com.qc.agent.vectordb.pojo.ChatQA;
import com.qc.agent.vectordb.pojo.SearchContent;
import com.qc.agent.vectordb.pojo.VectorDbResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import reactor.core.publisher.Flux;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */

@Slf4j
public abstract class AbstrctAnswerAIRequest<T> implements AnswerAIRequest {

    public static final String PUBLIC_DATABASE_ID = "6111626644670100448";

    public static final String ANSWER_FUNC_CODE = "N00000058";

    public static final String ANSWER_FUNC_PERMISSION_KEY_PREFIX = "ai_agent:permission:answer_func:%s";

    @Resource
    private QcKnowledgeQuestionMapper qcKnowledgeQuestionMapper;
    @Resource
    private com.qc.agent.vectordb.tencentdb.tencentDbClient tencentDbClient;

    @Resource
    private QcKnowledgeHistoryQuestionMapper qcKnowledgeHistoryQuestionMapper;

    @Resource
    private KnowledgeQuestionAnswerSettingService knowledgeQuestionAnswerSettingService;

    @Resource
    private SearchQuestionProperty searchQuestionProperty;

    @Resource
    private QcKnowledgeService qcKnowledgeService;

    @Resource
    private ServiceCaller serviceCaller;

    @Resource
    private KnowledgeFileMapper knowledgeFileMapper;

    @Resource
    private RedisClient redisClient;


    @Override
    public void doRequest(SseRequest request, AnswerAISubscriber subscriber) {
        AnswerAIConfig config = Optional.ofNullable(request.getConfig()).orElse(AnswerAIConfig.builder().build());

        log.info("Answer AI config:{}", config);

        try {
            subscriber.addNextError(message -> {
                sendErrorAnswer(subscriber, request.getErrorAnswer());
            }).addBeforeSendAspect((message -> {
                if (message.isEnd()) {
                    message.setExtData(ChatUtil.queryExtData(request));
                }
            }));

            List<SearchContent> searchContents = querySearchContent(request.getQuestion(), config);

            if (CollectionUtils.isNotEmpty(searchContents) || "2".equals(request.getAnswerSearchRange())) {
                if (CollectionUtils.isNotEmpty(searchContents)) {
                    Map<String, String> fileTypeMapping = queryFileTypeMapping();
                    SearchContent qaAnswer = findQAAnswer(searchContents, fileTypeMapping, config.getQaSimilarityRatio());
                    if (qaAnswer != null && StringUtils.isNotEmpty(qaAnswer.getAppendix())) {
                        sendExistsAnswer(subscriber, qaAnswer.getAppendix());
                        return;
                    }
                }
                if (searchContents == null) {
                    searchContents = Lists.newArrayList();
                }
                qcKnowledgeQuestionMapper.updateReference(request.getQuestionId(), searchContents.size(), JSON.toJSONString(searchContents));
                request.setCallWords(searchContents);
                request.setHistories(querySessionHistory(Long.valueOf(request.getClientId()), request.getQuestionId()));
                request.setStartTime(System.currentTimeMillis());

                doAnswer(request, subscriber);
            } else {
                sendDefaultMessage(subscriber, config);
            }
        } catch (Exception e) {
            log.error("chat error", e);
            sendErrorAnswer(subscriber, request.getErrorAnswer());
        } finally {
            qcKnowledgeService.clearCache(request.getMsgId());
        }
    }

    protected List<AnswerAIMessage> buildMessage(SseRequest request) {
        return request.buildMessage(30);
    }

    private List<SearchContent> querySearchContent(String question, AnswerAIConfig config) {
        tencentDbClient.initClient();
        KnowledgeQuestionAnswerSettingInfo info = knowledgeQuestionAnswerSettingService.getInfo(new KnowledgeQuestionAnswerSettingParams());
        String databaseName = "db-" + UserManager.getTenantUser().getTenantId();
        String qaDatabaseName = "QA-database-" + UserManager.getTenantUser().getTenantId();
        // 防止没有qa库，需要先创建
        tencentDbClient.createQADatabase(qaDatabaseName);
        String publicDatabaseName = "db-" + PUBLIC_DATABASE_ID;
        int limit = 10;
        if (info != null && info.getEachSearchCount() > 0) {
            limit = info.getEachSearchCount();
        }
        VectorDbResponse response;
        if (checkHasAnswerPermission()) {
            response = tencentDbClient.searchFileContentV2(databaseName, qaDatabaseName, question, limit,
                    Optional.ofNullable(config).map(c -> config.getSimilarityRatio()).orElse(0.4),
                    Optional.ofNullable(config).map(c -> config.getQaSimilarityRatio()).orElse(0.95),
                    publicDatabaseName);
        } else {
            response = tencentDbClient.searchFileContentV2(databaseName, qaDatabaseName, question, limit,
                    Optional.ofNullable(config).map(c -> config.getSimilarityRatio()).orElse(0.4),
                    Optional.ofNullable(config).map(c -> config.getQaSimilarityRatio()).orElse(0.95));
        }
        return response.getData();
    }

    private SearchContent findQAAnswer(List<SearchContent> datas, Map<String, String> fileTypeMapping, double qaSimilarityRatio) {
        List<SearchContent> collect = datas.stream().filter(item -> "QA".equals(fileTypeMapping.get(item.getDocumentName()))).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)) {
            collect.sort(Comparator.comparing(item -> item.getScore(), Comparator.reverseOrder()));
            SearchContent searchContent = collect.get(0);
            if (searchContent.getScore() >= qaSimilarityRatio) {
                return searchContent;
            }
        }
        return null;
    }

    private Map<String, String> queryFileTypeMapping() {
        Map<String, String> fileTypeMapping = Maps.newHashMap();
        List<QcKnowledgeFile> list = knowledgeFileMapper.findAllFiles();
        if (CollectionUtils.isNotEmpty(list)) {
            fileTypeMapping = list.stream().collect(Collectors.toMap(item -> item.getFileName(), item -> item.getDataImpType(), (r1, r2) -> r2));
        }
        return fileTypeMapping;
    }

    private List<ChatQA> querySessionHistory(Long sessionId, Long questionId) {
        List<ChatQA> chatQAS = Lists.newArrayList();
        QcKnowledgeQuestion question = new QcKnowledgeQuestion();
        question.setSessionId(sessionId);
        question.setId(questionId);
        List<QcKnowledgeQuestion> qcKnowledgeQuestions = qcKnowledgeQuestionMapper.selectAnswerHistory(question);
        if (CollectionUtils.isNotEmpty(qcKnowledgeQuestions)) {
            if (qcKnowledgeQuestions.size() > 10) {
                qcKnowledgeQuestions = qcKnowledgeQuestions.subList(qcKnowledgeQuestions.size() - 10, qcKnowledgeQuestions.size());
            }
            for (QcKnowledgeQuestion qcKnowledgeQuestion : qcKnowledgeQuestions) {
                ChatQA chatQA = new ChatQA();
                chatQA.setQuestion(qcKnowledgeQuestion.getQuestionContent());
                chatQA.setAnswer(qcKnowledgeQuestion.getAnswerContent());
                chatQAS.add(chatQA);
            }
            insertHistoryQuestion(qcKnowledgeQuestions, questionId);
        }
        return chatQAS;
    }

    private void sendExistsAnswer(AnswerAISubscriber subscriber, String answer) {
        List<T> messages = Lists.newArrayList();
        messages.add(buildAnswer(answer));
        messages.add(buildAnswer(StringUtils.EMPTY));
        Flux<T> sseFlux = Flux.fromIterable(messages);
        sseFlux.subscribe(subscriber);
    }

    private void sendErrorAnswer(AnswerAISubscriber subscriber, String errorAnswer) {
        if (!subscriber.isStop()) {
            List<T> messages = Lists.newArrayList();
            if (!StringUtils.isNotEmpty(errorAnswer)) {
                errorAnswer = "非常感谢您的提问！目前由于咨询量较大，GPT正在全力以赴地处理每一个问题。请稍候片刻，感谢您的耐心等待！";
            }
            messages.add(buildAnswer(errorAnswer));
            messages.add(buildAnswer(StringUtils.EMPTY));
            Flux<T> sseFlux = Flux.fromIterable(messages);
            sseFlux.subscribe(subscriber);
            subscriber.stop();
        }
    }

    private void sendDefaultMessage(AnswerAISubscriber subscriber, AnswerAIConfig config) {
        if (!subscriber.isStop()) {
            List<T> messages = Lists.newArrayList();
            String defaultAnswer = StringUtils.EMPTY;
            if (config != null) {
                defaultAnswer = config.getDefaultAnswer();
            }
            if (!StringUtils.isNotEmpty(defaultAnswer)) {
                defaultAnswer = "抱歉，我无法回答该问题的准确信息。如果你有任何其他问题需要帮助，请随时告诉我。";
            }
            messages.add(buildAnswer(defaultAnswer));
            messages.add(buildAnswer(StringUtils.EMPTY));
            Flux<T> sseFlux = Flux.fromIterable(messages);
            sseFlux.subscribe(subscriber);
            subscriber.stop();
        }
    }

    private void insertHistoryQuestion(List<QcKnowledgeQuestion> qcKnowledgeQuestions, Long questionId) {
        List<QcKnowledgeHistoryQuestion> collect = qcKnowledgeQuestions.stream().map(item -> {
            QcKnowledgeHistoryQuestion historyQuestion = new QcKnowledgeHistoryQuestion();
            historyQuestion.setId(UUIDUtils.getUUID2Long());
            historyQuestion.setQuestionId(questionId);
            historyQuestion.setHistoryQuestionId(item.getId());
            return historyQuestion;
        }).collect(Collectors.toList());
        qcKnowledgeHistoryQuestionMapper.batchInsert(collect);
    }

    protected abstract T buildAnswer(String defaultAnswer);

    protected abstract void doAnswer(SseRequest request, AnswerAISubscriber subscriber);

    private boolean checkHasAnswerPermission() {
        String redisKey = String.format(ANSWER_FUNC_PERMISSION_KEY_PREFIX, UserManager.getTenantUser().getTenantId());
        Object permissionValue =redisClient.get(redisKey);
        if (permissionValue != null) {
            return "1".equals(permissionValue);
        } else {
            try {
                Map<String, String> extraHeaders = Maps.newHashMap();
                log.info("调用getLoginUser接口，Cookie：{}", RequestHolder.getRequestCookie());
                extraHeaders.put("Cookie", RequestHolder.getRequestCookie());
                String s = serviceCaller.postCall("/platform/param/v1/getLoginUser.do", null, extraHeaders);
                if (StringUtils.isNotEmpty(s)) {
                    JSONObject jsonObject = JSONObject.parseObject(s);
                    if (MapUtils.isNotEmpty(jsonObject)) {
                        JSONArray funcCodeList = jsonObject.getJSONArray("funcCodeList");
                        if (CollectionUtils.isNotEmpty(funcCodeList)) {
                            for (int i = 0; i < funcCodeList.size(); i++) {
                                JSONObject jsonObject1 = funcCodeList.getJSONObject(i);
                                if (jsonObject1 != null && ANSWER_FUNC_CODE.equals(jsonObject1.getString("funCode"))) {
                                    redisClient.set(redisKey, jsonObject1.getString("isEmpOrder"), 7 * 24 * 60 * 60);
                                    return "1".equals(jsonObject1.getString("isEmpOrder"));
                                }
                            }
                        }
                    }
                }
            } catch (Exception e) {
                log.error("远程调用appsvr getLoginUser.do接口异常", e);
            }
        }
        return false;
    }
}
