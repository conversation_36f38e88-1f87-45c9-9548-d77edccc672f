package com.qc.agent.app.agent.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.qc.agent.app.agent.model.entity.InsightSummaryConfig;

/**
 * 总结配置表 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InsightSummaryConfigMapper {

    /**
     * 根据ID查询总结配置
     */
    InsightSummaryConfig selectById(@Param("id") Long id);

    /**
     * 根据代理ID查询总结配置
     */
    InsightSummaryConfig selectByAgentId(@Param("agentId") Long agentId);

    /**
     * 根据代理ID和洞察模式查询总结配置
     */
    InsightSummaryConfig selectByAgentIdAndMode(@Param("agentId") Long agentId, @Param("insightMode") String insightMode);

    /**
     * 查询所有总结配置
     */
    List<InsightSummaryConfig> selectAll();

    /**
     * 插入总结配置
     */
    int insert(InsightSummaryConfig summaryConfig);

    /**
     * 更新总结配置
     */
    int updateById(InsightSummaryConfig summaryConfig);

    /**
     * 根据ID删除总结配置
     */
    int deleteById(@Param("id") Long id);

    /**
     * 根据代理ID删除总结配置
     */
    int deleteByAgentId(@Param("agentId") Long agentId);
}