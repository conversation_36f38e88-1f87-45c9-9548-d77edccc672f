package com.qc.agent.app.agent.service.impl;

import com.aliyuncs.utils.StringUtils;
import com.qc.agent.app.agent.mapper.QcAiAgentConversationMapper;
import com.qc.agent.app.agent.model.po.QcAiAgentConversation;
import com.qc.agent.lla.ILLACompletionListener;
import com.qc.agent.lla.model.LLARequest;
import com.qc.agent.lla.model.LLAResponse;
import com.qc.agent.lla.model.LLAUsage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Objects;

@Slf4j
@Component
public class AgentChatILLACompletionCallback implements ILLACompletionListener {

    @Resource
    private QcAiAgentConversationMapper qcAiAgentConversationMapper;

    @Override
    @Transactional // Consider if this operation should be atomic. If so, add this.
    public void onComplete(LLAResponse response, LLARequest request) {
        log.debug("ILLA completion callback received. Request ID: {}", request.getId());

        if (response == null) {
            log.warn("LLA response is null for request ID: {}. Skipping processing.", request.getId());
            return;
        }

        LLAUsage usage = response.getUsage();
        if (usage == null) {
            log.warn("LLA usage is null in response for request ID: {}. Skipping processing.", request.getId());
            return;
        }

        String requestIdStr = request.getId();
        if (StringUtils.isEmpty(requestIdStr)) { // Use Objects.isNull or String.isBlank() for Java 11+
            log.warn("Request ID is empty in LLARequest. Skipping processing.");
            return;
        }

        Long clientId;
        try {
            clientId = Long.valueOf(requestIdStr);
        } catch (NumberFormatException e) {
            log.warn("Invalid format for request ID: '{}'. Expected a Long. Skipping processing.", requestIdStr, e);
            return;
        }

        QcAiAgentConversation qcAiAgentConversation = qcAiAgentConversationMapper.selectById(clientId);
        if (qcAiAgentConversation == null) {
            log.warn("No QcAiAgentConversation found for client ID: {}. Skipping processing.", clientId);
            return;
        }

        log.info("Processing completion for conversation ID: {}, client ID (request ID): {}", qcAiAgentConversation.getId(), clientId);

        // Using Objects.requireNonNullElse for cleaner null handling with default values
        BigDecimal currentQuestionTokens = Objects.requireNonNullElse(qcAiAgentConversation.getQuestionToken(), BigDecimal.ZERO);
        BigDecimal currentAnswerTokens = Objects.requireNonNullElse(qcAiAgentConversation.getAnswerToken(), BigDecimal.ZERO);

        BigDecimal newPromptTokens = BigDecimal.valueOf(usage.getPromptTokens());
        BigDecimal newCompletionTokens = BigDecimal.valueOf(usage.getCompletionTokens());

        BigDecimal totalQuestionTokens = currentQuestionTokens.add(newPromptTokens);
        BigDecimal totalAnswerTokens = currentAnswerTokens.add(newCompletionTokens);

        LocalDateTime requestStartTime = request.getStart();
        LocalDateTime completionTime = LocalDateTime.now();
        BigDecimal durationSeconds = calculateSeconds(requestStartTime, completionTime);
        String content = response.getContent();
        if (request.isSkipAnswerUpdate()) {
            content = null;
        }

        try {
            int updatedRowCount = qcAiAgentConversationMapper.updateStreamChatDone(
                    clientId,
                    content,
                    totalQuestionTokens.intValue(),
                    totalAnswerTokens.intValue(),
                    durationSeconds,
                    QcAiAgentConversation.CONVERSATION_SUCCESS,
                    requestStartTime
            );

            if (updatedRowCount > 0) {
                log.info("Successfully updated conversation for client ID: {}. Rows affected: {}. " +
                                "Question tokens: {}, Answer tokens: {}, Duration: {}s",
                        clientId, updatedRowCount, totalQuestionTokens, totalAnswerTokens, durationSeconds);
            } else {
                // This case means the update statement ran without error, but no rows were changed.
                // This could happen if the clientId no longer exists or conditions weren't met.
                log.warn("Conversation update for client ID: {} executed, but no rows were affected. " +
                                "This might indicate the record was deleted or modified concurrently. " +
                                "Attempted to set Question tokens: {}, Answer tokens: {}, Duration: {}s",
                        clientId, totalQuestionTokens, totalAnswerTokens, durationSeconds);
            }
        } catch (Exception e) {
            log.error("Failed to update conversation for client ID: {}. Error: {}", clientId, e.getMessage(), e);
        }
    }

    /**
     * Calculates the duration in seconds between two LocalDateTime instances.
     * Returns BigDecimal.ZERO if start or end time is null.
     *
     * @param start The start time.
     * @param end   The end time.
     * @return The duration in seconds as a BigDecimal, or BigDecimal.ZERO if inputs are invalid.
     */
    private BigDecimal calculateSeconds(LocalDateTime start, LocalDateTime end) {
        if (start == null || end == null) {
            log.warn("Cannot calculate duration: start time ({}) or end time ({}) is null.", start, end);
            return BigDecimal.ZERO;
        }
        if (start.isAfter(end)) {
            log.warn("Start time ({}) is after end time ({}). Returning zero duration.", start, end);
            return BigDecimal.ZERO; // Or calculate absolute difference if that makes sense
        }

        Duration duration = Duration.between(start, end);
        return BigDecimal.valueOf(duration.getSeconds())
                .add(BigDecimal.valueOf(duration.getNano(), 9)) // 9 is the scale for nanoseconds
                .setScale(3, RoundingMode.HALF_UP); // Set precision to 3 decimal places, adjust as needed
    }
}