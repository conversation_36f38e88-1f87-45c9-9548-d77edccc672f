package com.qc.agent.app.question.service;

import com.qc.agent.app.question.pojo.*;
import com.qc.agent.vectordb.pojo.SearchContent;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.List;
import java.util.Map;

/**
 * @description：TODO
 * @author： sc
 * @create： 2024/1/23 9:52
 */
public interface QcKnowledgeService {
    List<SearchContent> search(QcKnowledgeQuestion question);

    List<QcKnowledgeQuestion> historyTest(QcKnowledgeQuestion question);

    void chat(SseEmitter emitter, String sessionId, String question);

    QcKnowledgeAnswerSession saveAnswerSession(QcKnowledgeAnswerSession qcKnowledgeAnswerSession);

    void deleteAnswerSession(QcKnowledgeAnswerSession qcKnowledgeAnswerSession);

    List<AnswerSessionView> listAnswerSession(QcKnowledgeAnswerSession qcKnowledgeAnswerSession);

    List<QcKnowledgeQuestion> listAnswerHistory(QcKnowledgeQuestion qcKnowledgeQuestion);

    SseEmitter streamChat(StreamChatRequest request);

    List<QcKnowledgeQuestion> listAnswerContext(QcKnowledgeQuestion qcKnowledgeQuestion);

    List<SearchContent> listAnswerReference(QcKnowledgeQuestion qcKnowledgeQuestion);

    void deleteQuestion(QcKnowledgeQuestion qcKnowledgeQuestion);

    void deleteAnswer(QcKnowledgeQuestion qcKnowledgeQuestion);

    void feedback(FeedBackRequest request);

    Map<String, Object> queryQuestionSummary(QcKnowledgeQuestion qcKnowledgeQuestion);

    void stopStreamChat(StreamChatRequest request);

    void clearCache(String msgId);
}
