package com.qc.agent.app.workflow.inner;

/**
 *
 *
 * <AUTHOR>
 * @date 2025/3/19 14:31:48
 *
 */
public enum AgentType
{
    /**
     * 默认类型
     */
    DEFAULT("0"),
    /**
     * 销售专家
     */
    FMCG("1"),
    /**
     * 拜访助手
     */
    VISIT("2"),
    /**
     * 铺货助手
     */
    SKU("3"),
    /**
     * 考勤助手
     */
    ATTENDANCE("4"),
    /**
     * 销售分析助手
     */
    SALES_ANALYSIS("5"),

    /**
     * 商品助手
     */
    PRODUCT_ASSISTANT("6"),

    /**
     * 销售分析助手BI
     */
    SALES_ANALYSIS_BI("7"),

    /**
     * 客户洞察分析
     */
    CUSTOMER_INSIGHT_ANALYSIS("8");

    private final String type;

    AgentType(String type)
    {
        this.type = type;
    }

    public String getType()
    {
        return type;
    }

    public static AgentType getAgentType(String type)
    {
        for (AgentType agentType : AgentType.values())
        {
            if (agentType.getType().equals(type))
            {
                return agentType;
            }
        }
        return null;
    }
}
