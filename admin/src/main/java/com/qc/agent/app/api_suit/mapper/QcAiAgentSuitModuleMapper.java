package com.qc.agent.app.api_suit.mapper;

import com.qc.agent.app.api_suit.model.po.QcAiAgentSuitModule;
import com.qc.agent.app.api_suit.model.vo.SuitModuleVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface QcAiAgentSuitModuleMapper {
    SuitModuleVO selectById(@Param("id") Long id);

    List<SuitModuleVO> selectBySuitId(@Param("suitId") Long suitId);

    int insert(QcAiAgentSuitModule record);

    int update(QcAiAgentSuitModule record);

    int delete(@Param("id") Long id);
}
