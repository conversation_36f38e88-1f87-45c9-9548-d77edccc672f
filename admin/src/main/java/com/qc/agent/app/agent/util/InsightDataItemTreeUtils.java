package com.qc.agent.app.agent.util;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.qc.agent.app.agent.model.entity.InsightDataItem;

/**
 * 数据项树形结构工具类
 * 
 * <AUTHOR>
 * @since 1.0.0
 * @version 1.0.0
 */
public class InsightDataItemTreeUtils {

    /**
     * 将平铺的数据项列表转换为树形结构
     * 
     * @param flatItems 平铺的数据项列表
     * @return 树形结构的数据项列表
     */
    public static List<InsightDataItem> buildTree(List<InsightDataItem> flatItems) {
        if (flatItems == null || flatItems.isEmpty()) {
            return new ArrayList<>();
        }

        // 1. 分析父子关系并设置parentId
        Map<String, InsightDataItem> itemCodeMap = new HashMap<>();
        List<InsightDataItem> processedItems = new ArrayList<>();

        // 先按itemCode排序，确保父级在子级之前处理
        flatItems.sort(Comparator.comparing(InsightDataItem::getItemCode));

        for (InsightDataItem item : flatItems) {
            String itemCode = item.getItemCode();
            if (itemCode == null) {
                continue;
            }

            // 查找父级数据项
            String parentCode = findParentCode(itemCode);
            if (parentCode != null && itemCodeMap.containsKey(parentCode)) {
                // 设置父级ID
                item.setParentId(itemCodeMap.get(parentCode).getId());
            } else {
                // 顶级节点
                item.setParentId(null);
            }

            itemCodeMap.put(itemCode, item);
            processedItems.add(item);
        }

        // 2. 构建树形结构
        return buildTreeStructure(processedItems);
    }

    /**
     * 构建树形结构
     * 
     * @param items 已设置parentId的数据项列表
     * @return 树形结构的数据项列表
     */
    private static List<InsightDataItem> buildTreeStructure(List<InsightDataItem> items) {
        Map<Long, InsightDataItem> idMap = new HashMap<>();
        List<InsightDataItem> rootItems = new ArrayList<>();

        // 创建ID映射
        for (InsightDataItem item : items) {
            idMap.put(item.getId(), item);
        }

        // 构建树形结构
        for (InsightDataItem item : items) {
            if (item.getParentId() == null) {
                // 根节点
                rootItems.add(item);
            } else {
                // 子节点，添加到父节点的children中
                InsightDataItem parent = idMap.get(item.getParentId());
                if (parent != null) {
                    if (parent.getChildren() == null) {
                        parent.setChildren(new ArrayList<>());
                    }
                    parent.getChildren().add(item);
                }
            }
        }

        return rootItems;
    }

    /**
     * 查找父级数据项的itemCode
     * 
     * @param itemCode 当前数据项的itemCode
     * @return 父级数据项的itemCode，如果没有父级则返回null
     */
    private static String findParentCode(String itemCode) {
        if (itemCode == null || !itemCode.contains(".")) {
            return null;
        }

        // 找到最后一个"."的位置
        int lastDotIndex = itemCode.lastIndexOf(".");
        if (lastDotIndex > 0) {
            return itemCode.substring(0, lastDotIndex);
        }

        return null;
    }

    /**
     * 将树形结构的数据项列表转换为平铺结构
     * 
     * @param treeItems 树形结构的数据项列表
     * @return 平铺的数据项列表
     */
    public static List<InsightDataItem> flattenTree(List<InsightDataItem> treeItems) {
        List<InsightDataItem> flatItems = new ArrayList<>();
        if (treeItems == null || treeItems.isEmpty()) {
            return flatItems;
        }

        for (InsightDataItem item : treeItems) {
            flatItems.add(item);
            if (item.getChildren() != null && !item.getChildren().isEmpty()) {
                flatItems.addAll(flattenTree(item.getChildren()));
            }
        }

        return flatItems;
    }

    /**
     * 按层级分组数据项
     * 
     * @param flatItems 平铺的数据项列表
     * @return 按层级分组的数据项Map，key为层级深度，value为该层级的数据项列表
     */
    public static Map<Integer, List<InsightDataItem>> groupByLevel(List<InsightDataItem> flatItems) {
        if (flatItems == null || flatItems.isEmpty()) {
            return new HashMap<>();
        }

        return flatItems.stream()
                .collect(Collectors.groupingBy(InsightDataItem::getLevel));
    }

    /**
     * 获取指定数据项的所有子级数据项（包括子级的子级）
     * 
     * @param item 指定的数据项
     * @return 所有子级数据项的列表
     */
    public static List<InsightDataItem> getAllChildren(InsightDataItem item) {
        List<InsightDataItem> allChildren = new ArrayList<>();
        if (item == null || item.getChildren() == null || item.getChildren().isEmpty()) {
            return allChildren;
        }

        for (InsightDataItem child : item.getChildren()) {
            allChildren.add(child);
            allChildren.addAll(getAllChildren(child));
        }

        return allChildren;
    }

    /**
     * 获取指定数据项的所有父级数据项
     * 
     * @param item     指定的数据项
     * @param allItems 所有数据项的列表
     * @return 所有父级数据项的列表（从根到直接父级）
     */
    public static List<InsightDataItem> getAllParents(InsightDataItem item, List<InsightDataItem> allItems) {
        List<InsightDataItem> parents = new ArrayList<>();
        if (item == null || item.getParentId() == null) {
            return parents;
        }

        Map<Long, InsightDataItem> idMap = allItems.stream()
                .collect(Collectors.toMap(InsightDataItem::getId, i -> i));

        Long currentParentId = item.getParentId();
        while (currentParentId != null) {
            InsightDataItem parent = idMap.get(currentParentId);
            if (parent != null) {
                parents.add(0, parent); // 在开头插入，保持从根到叶的顺序
                currentParentId = parent.getParentId();
            } else {
                break;
            }
        }

        return parents;
    }

    /**
     * 测试方法
     */
    public static void main(String[] args) {
        // 创建测试数据
        List<InsightDataItem> testItems = new ArrayList<>();

        InsightDataItem parent1 = new InsightDataItem();
        parent1.setId(1L);
        parent1.setItemCode("distributionOrderDetailFields");
        parent1.setItemName("分销订单明细");

        InsightDataItem child1 = new InsightDataItem();
        child1.setId(2L);
        child1.setItemCode("distributionOrderDetailFields.field1");
        child1.setItemName("分销订单明细-选择字段1");

        InsightDataItem child2 = new InsightDataItem();
        child2.setId(3L);
        child2.setItemCode("distributionOrderDetailFields.field2");
        child2.setItemName("分销订单明细-选择字段2");

        testItems.add(parent1);
        testItems.add(child1);
        testItems.add(child2);

        // 构建树形结构
        List<InsightDataItem> treeItems = buildTree(testItems);

        System.out.println("=== 树形结构构建成功 ===");
        System.out.println("根节点数量: " + treeItems.size());
        if (!treeItems.isEmpty()) {
            InsightDataItem root = treeItems.get(0);
            System.out.println("根节点: " + root.getItemCode() + " - " + root.getItemName());
            System.out.println("子节点数量: " + (root.getChildren() != null ? root.getChildren().size() : 0));
            if (root.getChildren() != null) {
                for (InsightDataItem child : root.getChildren()) {
                    System.out.println("  子节点: " + child.getItemCode() + " - " + child.getItemName());
                }
            }
        }
    }
}