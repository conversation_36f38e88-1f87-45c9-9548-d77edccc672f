package com.qc.agent.app.agent.model.po;

import com.qc.agent.app.agent.model.dto.QcAiAgentCommonParam;
import com.qc.agent.app.agent.model.dto.VisitAgentParamDTO;
import com.qc.agent.app.workflow.inner.pojo.QcAiAgentIntentRecognition;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QcAiAgent {

    /**
     * 主键 ID
     */
    private Long id;

    /**
     * 状态，默认值 '1'
     */
    private String status;

    /**
     * 创建人 ID
     */
    private Long creatorId;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改人 ID
     */
    private Long modifyierId;

    /**
     * 修改人姓名
     */
    private String modifyierName;

    /**
     * 修改时间
     */
    private LocalDateTime modifyTime;

    /**
     * 模型 ID
     */
    private Long modelId;
    /**
     * 模型名称
     */
    private String model;

    private String apiKey;

    private String vendor;

    private String modelEndPoint;

    /**
     * 排序值
     */
    private BigDecimal sequ;

    /**
     * 智能体名称
     */
    private String name;

    /**
     * 智能体 Logo
     */
    private String logo;

    /**
     * 描述
     */
    private String description;

    /**
     * 提示
     */
    private String prompt;

    /**
     * 引导问题
     */
    private String leadingQuestion;

    /**
     * 分类 ID
     */
    private Long categoryId;

    /**
     * 上下文检索数量
     */
    private BigDecimal contextSearchAmount;

    /**
     * 开场白
     */
    private String introduction;

    /**
     * 是否联网搜索（1：联网搜索，0：不联网）
     */
    private String internetSearch;
    /**
     * 联网搜索
     */
    public static final String INTERNET_SEARCH = "1";

    /**
     * 空结果回复
     */
    private String nullResultAnswer;

    /**
     * 报错结果回复
     */
    private String errorResultAnswer;

    /**
     * 数据获取 URL
     */
    private String dataFetchUrl;

    /**
     * 是否内置智能体（1：内置智能体，0：自定义智能体）
     */
    private String internalFlag;

    /**
     * 内置智能体
     */
    public static final String INTERNAL_AGENT = "1";

    /**
     * 自定义智能体
     */
    public static final String CUSTOM_AGENT = "0";

    /**
     * 是否已发布（1：已发布，0：未发布）
     */
    private String publishFlag;
    /**
     * 已发布
     */
    public static final String PUBLISHED = "1";
    /**
     * 待发布
     */
    public static final String PENDING_PUBLISH = "0";

    /**
     * 发布时间
     */
    private LocalDateTime publishTime;

    /**
     * 1：启用，0：停用
     */
    private String enable;

    /**
     * 采样温度
     */
    private BigDecimal modelTemperature;

    /**
     * 核采样的概率阈值
     */
    private BigDecimal modelTopP;

    /**
     * 请求返回的最大 Token 数
     */
    private BigDecimal modelMaxTokens;

    /**
     * 知识库-召回设置-最大召回数量
     */
    private Integer maxRecallCount;
    /**
     * 知识库-召回设置-最小匹配度
     */
    private Double minMatchThreshold;
    /**
     * 知识库-召回设置-qa最小匹配度
     */
    private Double qaMinMatchThreshold;
    /**
     * 知识库-召回设置-检索范围 1:仅引用知识库 2:引用知识库+模型通用知识库
     */
    private String searchScope;
    /**
     * 考勤助手-将问题拆分为sql的提示词
     */
    private String splitSqlPrompt;
    /**
     * 业务相关的提示词
     */
    private String bizPrompt;

    /**
     * 关联的知识库id
     */
    private List<Long> knowledgeIdList;

    private List<Long> deptIdList;


    /**
     * 1：新对话
     */
    private String isNew;

    /**
     * 对话id
     */
    private Long conversationId;

    /**
     * 拜访助手的参数
     */
    private VisitAgentParamDTO visitAgentParam;

    /**
     * 问题类型  1：拜访客户推荐 2：拜访数据查询 0：其他
     */
    private String questionType = "0";

    /**
     * 是否显示聊天内容 1：显示 0：不显示
     */
    private String showChatLogContentType;

    /**
     * 意图识别是否启用 1：是 0：否
     */
    private String intentIsEnabled;

    /**
     * 测试对话和正式对话都有的公共参数
     */
    private QcAiAgentCommonParam commonParam;

    /**
     * 意图识别相关
     */
    private List<QcAiAgentIntentRecognition> intentList;

    /**
     * 缓存标签，目前只支持kimi
     */
    private String cacheTag;


}
