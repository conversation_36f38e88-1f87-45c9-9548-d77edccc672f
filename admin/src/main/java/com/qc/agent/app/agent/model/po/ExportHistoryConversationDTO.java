package com.qc.agent.app.agent.model.po;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Builder
public class ExportHistoryConversationDTO {

    @ExcelProperty("提问时间")
    private LocalDateTime createTime;

    @ExcelProperty("提问用户")
    private String creatorName;

    @ExcelProperty("问题内容")
    private String question;

    @ExcelProperty("意图")
    private String intentName;

    @ExcelProperty("回答时间")
    private LocalDateTime answerTime;

    @ExcelProperty("回答内容")
    private String answer;

    @ExcelProperty("TOKENS")
    private BigDecimal tokens;

    @ExcelProperty("消耗时间")
    private BigDecimal conversationTime;

    @ExcelProperty("状态")
    private String conversationStatus;

    @ExcelProperty("反馈状态")
    private String evaluationType;

    @ExcelProperty("反馈内容")
    private String feedback;
}