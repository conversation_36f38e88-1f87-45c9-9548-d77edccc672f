package com.qc.agent.app.question.pojo;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;

/**
 * @description：TODO
 * @author： sc
 * @create： 2024/2/4 15:39
 */
public class AnswerSessionView extends QcKnowledgeAnswerSession{

    private String recentlyQuestion;

    @JSONField(serializeUsing= ToStringSerializer.class)
    private Long sessionId;

    public String getRecentlyQuestion() {
        return recentlyQuestion;
    }

    public void setRecentlyQuestion(String recentlyQuestion) {
        this.recentlyQuestion = recentlyQuestion;
    }

    public Long getSessionId() {
        return sessionId;
    }

    public void setSessionId(Long sessionId) {
        this.sessionId = sessionId;
    }
}
