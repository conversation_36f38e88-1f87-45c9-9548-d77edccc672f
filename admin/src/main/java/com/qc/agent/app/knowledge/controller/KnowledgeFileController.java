package com.qc.agent.app.knowledge.controller;

import com.qc.agent.app.knowledge.model.*;
import com.qc.agent.app.knowledge.service.KnowledgeFileService;
import com.qc.agent.common.core.Message;
import com.qc.agent.platform.pojo.AjaxPage;
import com.qc.agent.vectordb.pojo.SearchContent;
import com.qc.agent.vectordb.tencentdb.tencentDbClient;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @className KnowledgeFileController
 * @description TODO
 * @date 2024/1/22 9:52
 */
@RequestMapping("/ai-agent/knowledge/File")
@RestController
public class KnowledgeFileController {
    private final Log logger = LogFactory.getLog(KnowledgeFileController.class);

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private KnowledgeFileService knowledgeFileService;

    @Autowired
    tencentDbClient dbClient;

    @Autowired
    HttpMessageConverters httpMessageConverters;

    @RequestMapping("/parse.do")
    public Message parse(@RequestBody QcKnowledgeFileParams qcKnowledgeFileParams) {
        List<String> stringList = knowledgeFileService.parse(qcKnowledgeFileParams);
        return Message.of().data(stringList).ok();
    }

    @RequestMapping("/checkDuplicateFileName.do")
    public Message checkDuplicateFileName(@RequestBody BatchImpQcKnowledgeFileParams batchImpQcKnowledgeFileParams) {
        try {
            boolean isDuplicate = knowledgeFileService.checkDuplicateFileName(batchImpQcKnowledgeFileParams);
            return Message.of().data(isDuplicate).ok();
        } catch (Exception e) {
            Message data = Message.of().data(true).error(e.getMessage());
            return data;
        }
    }

    @RequestMapping("/searchContent.do")
    public Message searchContent(@RequestBody QcKnowledgeFileParams qcKnowledgeFileParams) {
        List<SearchContent> stringList = knowledgeFileService.searchContent(qcKnowledgeFileParams);
        return Message.of().data(stringList).ok();
    }

    @RequestMapping("/importFile.do")
    public Message importFile(@RequestBody BatchImpQcKnowledgeFileParams batchImpQcKnowledgeFileParams) {
        knowledgeFileService.batchInsertQcKnowledgeFile(batchImpQcKnowledgeFileParams);
        return Message.of().ok();
    }

    @RequestMapping("/queryList.do")
    public Message queryList(@RequestBody QcKnowledgeFileListParams qcKnowledgeFileParams) {
        AjaxPage data = knowledgeFileService.queryList(qcKnowledgeFileParams);
        return Message.of().data(data).ok();
    }

    @RequestMapping("/updateFileStatus.do")
    public Message updateFileStatus(@RequestBody QcKnowledgeFileParams qcKnowledgeFileParams) {
        return knowledgeFileService.updateFileStatus(qcKnowledgeFileParams);
    }

    @RequestMapping("/findFileName.do")
    public Message findFileName(@RequestBody FileNameQueryParam fileNameQueryParam) {
        Map<String, String> fileNameMapping = knowledgeFileService.findFileNameMapping(fileNameQueryParam);
        return Message.of().data(fileNameMapping).ok();
    }


    /**
     * 下载QA问答Excel模板
     * @param response
     */
    @RequestMapping("/downloadQAExcelTemplate.do")
    public void downloadQAExcelTemplate(HttpServletResponse response){
        try {
            response.setContentType("application/vnd.ms-excel");
            String fileName = new String("问题模板.xlsx".getBytes("GBK"), "ISO8859-1");
            response.setHeader("Content-Disposition", "attachment;filename=\"" + fileName + "\"");
            // 生成excel
            XSSFWorkbook wb = knowledgeFileService.generateImportTemplate();
            try {
                wb.write(response.getOutputStream());
                response.getOutputStream().close();
            } catch (IOException e) {
                logger.error("生成QA问答模板异常", e);
            }
        } catch (Exception e) {
            logger.error("生成导入模板失败", e);
        }
    }

    @PostMapping(value = "/uploadQA.do", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Message uploadQA(QaUploaderRequest request) {

        // 返回成功消息，也可以返回其它响应
        return knowledgeFileService.uploadQA(request);
    }

    /**
     * 保存QA
     * @param request 请求参数
     * @return 响应数据
     */
    @RequestMapping("/saveQA.do")
    public Message saveQA(@RequestBody QaSaveRequest request){
        knowledgeFileService.saveQA(request);
        return Message.of().ok();
    }

    /**
     * 获取excel qa问答内容分片
     * @param request 文件路径
     * @return 响应数据
     */
    @RequestMapping("/getChunks.do")
    public Message getChunks(@RequestBody ChunkRequest request){
        try {
            return Message.of().data(knowledgeFileService.getChunks(request.getFilePath())).ok();
        } catch (Exception e) {
            return Message.of().error(e.getMessage());
        }
    }



    /**
     * 获取excel qa问答内容
     * @param listExcelFileQAParams
     * @return
     */
    @RequestMapping("/listExcelFileQA.do")
    public Message listExcelFileQA(@RequestBody ListExcelFileQAParams listExcelFileQAParams){
        AjaxPage data = knowledgeFileService.listExcelFileQA(listExcelFileQAParams);
        return Message.of().data(data).ok();
    }

    /**
     * 新增qa问答内容
     * @param qcKnowledgeExcelData
     * @return
     */
    @RequestMapping("/addQA.do")
    public Message addQA(@RequestBody QcKnowledgeExcelData qcKnowledgeExcelData){
        Message message = knowledgeFileService.addExcelFileQA(qcKnowledgeExcelData);
        return message;
    }

    /**
     * 编辑qa问答内容
     * @param qcKnowledgeExcelData
     * @return
     */
    @RequestMapping("/editQA.do")
    public Message editQA(@RequestBody QcKnowledgeExcelData qcKnowledgeExcelData){
        Message message = knowledgeFileService.editExcelFileQA(qcKnowledgeExcelData);
        return message;
    }

    /**
     * 删除qa问答内容
     * @param qcKnowledgeExcelData
     * @return
     */
    @RequestMapping("/deleteQA.do")
    public Message deleteQA(@RequestBody QcKnowledgeExcelData qcKnowledgeExcelData){
        Message message = knowledgeFileService.deleteExcelFileQA(qcKnowledgeExcelData);
        return message;
    }

    /**
     * 获取无结构文档的分段
     * @param qcKnowledgeFile
     * @return
     */
    @RequestMapping("/findRawFileInfo.do")
    public Message findRawFileInfo(@RequestBody QcKnowledgeFile qcKnowledgeFile){
        Message message = knowledgeFileService.findRawFileInfo(qcKnowledgeFile);
        return message;
    }

}
