package com.qc.agent.app.agent.service;

import com.alibaba.fastjson.JSONObject;
import com.qc.agent.app.agent.model.dto.MainPromptDTO;

/**
 * 提示词服务接口
 */
public interface PromptService {

    /**
     * 根据维度编码和提示词类型获取主提示词
     *
     * @param configId
     * @param dimensionCode 维度编码
     * @param promptType    提示词类型
     * @return 主提示词DTO
     */
    MainPromptDTO getMainPromptValueByDimensionCodeAndType(Long configId, String dimensionCode, String promptType);

    MainPromptDTO getMainPromptByDimensionCodeAndType(String dimensionCode, String promptType);

    /**
     * 保存主提示词
     *
     * @param
     * @return 是否保存成功
     */
    boolean saveFragmentPrompt(JSONObject jsonObject, Long configId, String dimensionCode, String promptType);

    /**
     * 替换提示词中的变量为具体值
     *
     * @param configId 配置ID
     * @param dimensionCode 维度代码
     * @param promptType 提示词类型
     * @param originalPrompt 原始提示词内容
     * @return 替换后的提示词内容
     */
    String replacePromptVariables(Long configId, String dimensionCode, String promptType, String originalPrompt);
}