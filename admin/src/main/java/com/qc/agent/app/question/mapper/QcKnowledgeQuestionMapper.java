package com.qc.agent.app.question.mapper;

import com.qc.agent.app.question.pojo.FeedBackRequest;
import com.qc.agent.app.question.pojo.QcKnowledgeQuestion;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface QcKnowledgeQuestionMapper {
    int deleteByPrimaryKey(Long id);

    int insert(QcKnowledgeQuestion record);

    int insertSelective(QcKnowledgeQuestion record);

    QcKnowledgeQuestion selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(QcKnowledgeQuestion record);

    int updateByPrimaryKey(QcKnowledgeQuestion record);

    void updateAnswer(@Param("questionId") Long questionId, @Param("answer") String answer);

    void updateReference(@Param("questionId")Long questionId, @Param("referenceTotal")int referenceTotal, @Param("referenceContent")String referenceContent);

    List<QcKnowledgeQuestion> queryAnswerHistory(QcKnowledgeQuestion question);

    List<QcKnowledgeQuestion> selectAnswerHistory(QcKnowledgeQuestion question);

    List<QcKnowledgeQuestion> selectByDatabaseId(@Param("databaseId")Long databaseId);

    List<QcKnowledgeQuestion> queryAnswerContext(QcKnowledgeQuestion qcKnowledgeQuestion);

    int deleteQuestion(QcKnowledgeQuestion qcKnowledgeQuestion);

    int deleteAnswer(QcKnowledgeQuestion qcKnowledgeQuestion);

    int updateFeedbackInfo(FeedBackRequest request);

    int stopQuestion(@Param("stopMessage")String stopMessage, @Param("msgId")String msgId);

    void updateAnswerByMsgId(@Param("msgId") String msgId, @Param("answer") String answer);

    QcKnowledgeQuestion selectByMsgId(@Param("msgId")String msgId);
}
