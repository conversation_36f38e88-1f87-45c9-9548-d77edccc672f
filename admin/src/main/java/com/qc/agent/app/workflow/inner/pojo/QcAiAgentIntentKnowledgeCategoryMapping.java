package com.qc.agent.app.workflow.inner.pojo;

import lombok.Data;

/**
 * 知识库与类型映射表
 *
 * <AUTHOR>
 * @date 2025-05-27
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Data
public class QcAiAgentIntentKnowledgeCategoryMapping {
    /**
     * 主键ID
     */
    private Long id;

    private String status;

    /**
     * agent_id - qc_ai_agent的id
     */
    private Long agentId;

    /**
     * collection_id - qc_knowledge_info的id
     */
    private Long collectionId;

    /**
     * category_id - qc_knowledge_file_category的id
     */
    private Long categoryId;

    /**
     * qc_ai_agent_intent_knowledge_detail 的id
     */
    private Long relationId;
}
