package com.qc.agent.app.question.impl.deepseek;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.qc.agent.platform.sse.SseMessage;
import com.qc.agent.platform.sse.SseMessageConverter;
import com.qc.agent.platform.sse.SseRequest;
import com.qc.agent.platform.sse.dto.MessageType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

/**
 * @description：TODO
 * @author： sc
 * @create： 2024/7/20 14:12
 */
@Slf4j
public class DeepseekMessageConverter implements SseMessageConverter<String> {

    @Override
    public SseMessage convert(String message, SseRequest request) {
        if (!StringUtils.hasText(message)) {
            return SseMessage.builder().message(message).messageType(MessageType.TEXT).build();
        }
        if ("data: [DONE]".equals(message)) {
            return SseMessage.buildEndMessage();
        } else {
            JSONObject jsonObject = extractContentFromLine(message);
            StringBuilder result = new StringBuilder();
            if(jsonObject != null){
                JSONArray choices = jsonObject.getJSONArray("choices");
                for (int i = 0, size = choices.size(); i < size; i++) {
                    JSONObject choice = choices.getJSONObject(i);
                    JSONObject delta = choice.getJSONObject("delta");
                    if (delta != null && delta.get("content") != null) {
                        result.append(delta.getString("content"));
                    }
                }
            }
            return SseMessage.builder().message(result.toString()).messageType(MessageType.TEXT).build();
        }
    }

    private JSONObject extractContentFromLine(String line) {
        String dataPrefix = "data: {";

        if (line.startsWith(dataPrefix)) {
            return JSONObject.parseObject(line.substring("data: ".length()));
        }

        return null;
    }
}
