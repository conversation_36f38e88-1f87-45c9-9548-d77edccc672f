package com.qc.agent.app.question.impl.tencent;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.qc.agent.platform.sse.SseMessage;
import com.qc.agent.platform.sse.SseMessageConverter;
import com.qc.agent.platform.sse.SseRequest;
import com.qc.agent.platform.sse.dto.MessageType;
import org.springframework.util.StringUtils;

public class TencentHttpMessageConverter implements SseMessageConverter<String> {
    @Override
    public SseMessage convert(String data, SseRequest request) {
        if(!StringUtils.hasText(data)){
            return SseMessage.builder().message(data).messageType(MessageType.TEXT).build();
        }
        JSONObject responseData = JSON.parseObject(data.substring("data: ".length()));
        JSONObject choice = responseData.getJSONArray("Choices").getJSONObject(0);
        SseMessage message = SseMessage.builder().messageType(MessageType.TEXT).build();
        if (choice.getString("FinishReason").equals("stop")) {
            message.setEnd(true);
        }
        JSONObject delta = choice.getJSONObject("Delta");
        message.setMessage(delta.getString("Content"));
        return message;
    }
}
