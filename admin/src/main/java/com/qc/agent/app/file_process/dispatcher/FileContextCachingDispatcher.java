package com.qc.agent.app.file_process.dispatcher;

import com.qc.agent.app.file_process.service.FileContextCachingService;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-06-16
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Service
public class FileContextCachingDispatcher {
    private final Map<String, FileContextCachingService> aiClientMap;

    public FileContextCachingDispatcher(Map<String, FileContextCachingService> aiClientMap) {
        this.aiClientMap = aiClientMap;
    }

    public FileContextCachingService getClient(String type) {
        return aiClientMap.get(type + "FileContextCachingServiceImpl");
    }
}
