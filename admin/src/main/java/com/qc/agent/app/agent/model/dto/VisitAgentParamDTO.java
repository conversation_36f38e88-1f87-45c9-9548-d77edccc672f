package com.qc.agent.app.agent.model.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025-04-09
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Data
public class VisitAgentParamDTO {
    /**
     * Retrieval radius in meters 检索半径
     * <p>
     * Defines the geographic search radius for visit data collection.
     * </p>
     */
    private Double retrieveRadius;

    /**
     * Search scope in days  检索范围-天数
     * <p>
     * Defines the temporal search window for visit data collection.
     * </p>
     */
    private Double searchScopeDays;

    /**
     * 当前经纬度（31.982820,118.735176）
     */
    private String location;

    /**
     * 地址
     */
    private String address;

}
