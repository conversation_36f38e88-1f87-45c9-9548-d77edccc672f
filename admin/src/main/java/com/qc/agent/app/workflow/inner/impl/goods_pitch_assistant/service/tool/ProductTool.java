package com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.service.tool;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.qc.agent.app.agent.support.AgentBizDataSupport;
import com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.model.domain.Product;
import com.qc.agent.utils.StringSimilarityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;

/**
 * A utility tool to find product information by querying an external business service.
 * This class provides methods to fetch product lists, find the most similar product,
 * and extract product details from entities.
 *
 * <AUTHOR>
 * @date 2025-06-23
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ProductTool {

    // --- Constants ---
    private static final String QUERY_PRODUCT_URI = "/app/pd/aiagent/queryPdInfos.do";
    private static final String PRODUCT_NAME_KEY = "name";
    private static final String PRODUCT_ALIAS_NAME_KEY = "productName";
    private static final String SHORT_NAME_KEY = "shortName";
    private static final String BRAND_NAME_KEY = "brandName";
    private static final String BRAND_KEY = "brand";
    private static final String SEARCH_TEXT_KEY = "searchText";
    private static final String CM_ID_KEY = "cmId";
    private static final String NEED_PRICE_KEY = "needPrice";

    // --- Injected Fields ---
    @Value("${ai.agent.appsvr-domin-url}")
    private String appsvrDomainUrl;
    private final ObjectMapper objectMapper;

    // --- Public API Methods ---

    /**
     * Finds the brand for a given product name by locating the most similar product.
     *
     * @param productName The name of the product to search for.
     * @param logPrefix   The log prefix for consistent logging context.
     * @return An Optional containing the brand name if found, otherwise an empty Optional.
     */
    public Optional<String> findBrand(String productName, String logPrefix) {
        log.info("{} Attempting to find brand for product: '{}'", logPrefix, productName);
        return findMostSimilarProduct(productName, logPrefix)
                .flatMap(productJson -> {
                    var brand = Optional.ofNullable(productJson.getString(BRAND_NAME_KEY));
                    brand.ifPresentOrElse(
                            b -> log.info("{} Found brand '{}' for product '{}'", logPrefix, b, productName),
                            () -> log.warn("{} Found a similar product, but it has no brand name: '{}'", logPrefix, productName)
                    );
                    return brand;
                });
    }

    /**
     * Finds the single most relevant product based on a search term from the backend.
     *
     * @param productName The name of the product to search for.
     * @param logPrefix   The log prefix for consistent logging.
     * @return An Optional containing the best matching product JSONObject, or empty if no match is found.
     */
    public Optional<JSONObject> findMostSimilarProduct(String productName, String logPrefix) {
        log.info("{} Searching for the most similar product matching: '{}'", logPrefix, productName);
        return fetchProductData(productName, logPrefix)
                .flatMap(dataList -> findMostSimilarProduct(productName, dataList, logPrefix));
    }

    /**
     * Selects the single most relevant product from a given product list using similarity matching.
     *
     * @param productName The name of the product to match against.
     * @param dataList    The list of potential product matches.
     * @param logPrefix   The log prefix for consistent logging.
     * @return An Optional containing the best matching product JSONObject, or empty if no suitable match is found.
     */
    public Optional<JSONObject> findMostSimilarProduct(String productName, List<JSONObject> dataList, String logPrefix) {
        if (dataList == null || dataList.isEmpty()) {
            log.warn("{} No product candidates provided for similarity check against '{}'.", logPrefix, productName);
            return Optional.empty();
        }

        final JSONObject productMatch;
        if (dataList.size() > 1) {
            log.info("{} Multiple products found (count: {}). Finding the best match for '{}'.", logPrefix, dataList.size(), productName);
            Function<JSONObject, String> nameExtractor = item -> item.getString(PRODUCT_NAME_KEY);
            productMatch = StringSimilarityUtils.findMostSimilar(dataList, productName, nameExtractor);
        } else {
            log.info("{} Only one product candidate found. Selecting it as the match.", logPrefix);
            productMatch = dataList.get(0);
        }

        log.info("{} Selected product match: {}", logPrefix, productMatch.toJSONString());
        return Optional.of(productMatch);
    }

    /**
     * Finds a list of all relevant products based on a search term.
     *
     * @param productName The name of the product to search for.
     * @param logPrefix   The log prefix for consistent logging.
     * @return An Optional containing a list of all matching product JSONObjects, or empty if none are found.
     */
    public Optional<List<JSONObject>> findProductList(String productName, String logPrefix) {
        log.info("{} Finding all products matching search term: '{}'", logPrefix, productName);
        return fetchProductData(productName, logPrefix);
    }

    /**
     * Finds a product with an exact name match based on a search term and other criteria.
     *
     * @param productName The exact name of the product to search for.
     * @param cmId        The customer or context ID (optional).
     * @param needPrice   Flag indicating if price is needed (optional).
     * @param logPrefix   The log prefix for consistent logging.
     * @return An Optional containing the exactly matching product JSONObject, or empty if not found.
     */
    public Optional<JSONObject> findProduct(String productName, String cmId, String needPrice, String logPrefix) {
        log.info("{} Searching for an exact product match for '{}' with cmId: '{}', needPrice: '{}'",
                logPrefix, productName, cmId, needPrice);

        if (StringUtils.isBlank(productName)) {
            log.warn("{} Cannot search for a product with a blank name.", logPrefix);
            return Optional.empty();
        }

        return fetchProductData(productName, cmId, needPrice, logPrefix)
                .flatMap(productList -> {
                    log.info("{} Found {} potential products for '{}'. Filtering for an exact name match.",
                            logPrefix, productList.size(), productName);
                    return productList.stream()
                            .filter(product -> Objects.equals(product.getString(PRODUCT_NAME_KEY), productName))
                            .findFirst();
                });
    }

    /**
     * Extracts product name and brand from entities, enhancing it with data from the most similar product found.
     *
     * @param entities  The JSON object containing intent entities (expects "productName" and optionally "brand").
     * @param logPrefix The log prefix for consistent logging.
     * @return An Optional containing a {@link Product} record.
     * - If a similar product is found, returns its details (preferring shortName).
     * - If not found, falls back to the original details from the entities.
     * - Returns empty if productName is missing in entities.
     */
    public Optional<Product> extractProductInfo(JSONObject entities, String logPrefix) {
        if (entities == null || StringUtils.isBlank(entities.getString(PRODUCT_ALIAS_NAME_KEY))) {
            log.warn("{} Entities object is null or does not contain a productName. Cannot extract product info.", logPrefix);
            return Optional.empty();
        }

        var productName = entities.getString(PRODUCT_ALIAS_NAME_KEY);
        var originalBrand = entities.getString(BRAND_KEY);
        log.info("{} Extracting product info for productName: '{}', brand: '{}'", logPrefix, productName, originalBrand);

        return findMostSimilarProduct(productName, logPrefix)
                .map(foundProduct -> {
                    log.info("{} Found a similar product: {}. Using its details.", logPrefix, foundProduct.toJSONString());
                    // Prefer shortName if available, otherwise fall back to the main name.
                    var finalName = StringUtils.defaultIfBlank(foundProduct.getString(SHORT_NAME_KEY), foundProduct.getString(PRODUCT_NAME_KEY));
                    var finalBrand = foundProduct.getString(BRAND_NAME_KEY);
                    return new Product(finalName, finalBrand);
                })
                .or(() -> {
                    // This block executes if findMostSimilarProduct returned an empty Optional.
                    log.warn("{} No similar product found for '{}'. Falling back to input from entities.", logPrefix, productName);
                    return Optional.of(new Product(productName, originalBrand == null ? "" : originalBrand));
                });
    }

    // --- Private Helper Methods ---

    /**
     * Overloaded helper method to fetch product data with minimal parameters.
     */
    private Optional<List<JSONObject>> fetchProductData(String searchText, String logPrefix) {
        return fetchProductData(searchText, null, null, logPrefix);
    }

    /**
     * Core helper method to fetch product data from the backend API.
     * It builds the request, makes the API call, parses the response, and handles common errors.
     *
     * @param searchText The text to use in the product search.
     * @param cmId       The customer or context ID (optional).
     * @param needPrice  Flag indicating if price is needed (optional).
     * @param logPrefix  The log prefix for consistent logging.
     * @return An Optional containing the list of raw product JSONObjects, or empty on failure or no data.
     */
    private Optional<List<JSONObject>> fetchProductData(String searchText, String cmId, String needPrice, String logPrefix) {
        if (StringUtils.isBlank(searchText)) {
            log.warn("{} Search text is blank. Aborting product API call.", logPrefix);
            return Optional.empty();
        }

        var url = String.format("%s%s", appsvrDomainUrl, QUERY_PRODUCT_URI);
        var params = new JSONObject();
        params.put(SEARCH_TEXT_KEY, searchText);
        if (cmId != null) {
            params.put(CM_ID_KEY, cmId);
        }
        if (needPrice != null) {
            params.put(NEED_PRICE_KEY, needPrice);
        }

        log.info("{} Calling product search API. URL: {}, Params: {}", logPrefix, url, params.toJSONString());
        try {
            var data = AgentBizDataSupport.fetchBizData(url, params);

            if (StringUtils.isBlank(data)) {
                log.warn("{} Received empty or blank response from product API for search: '{}'", logPrefix, searchText);
                return Optional.empty();
            }

            var dataList = objectMapper.readValue(data, new TypeReference<List<JSONObject>>() {
            });

            if (dataList.isEmpty()) {
                log.warn("{} API response was parsed, but contained no product data for search: '{}'", logPrefix, searchText);
                return Optional.empty();
            }
            log.info("{} Successfully fetched and parsed {} product(s) for search: '{}'", dataList.size(), logPrefix, searchText);
            return Optional.of(dataList);

        } catch (JsonProcessingException e) {
            log.error("{} Failed to parse JSON response from product API for search: '{}'. Response snippet: {}",
                    logPrefix, searchText, StringUtils.abbreviate(e.getOriginalMessage(), 100), e);
            return Optional.empty();
        } catch (Exception e) {
            log.error("{} An unexpected error occurred while fetching product data for search: '{}'", logPrefix, searchText, e);
            return Optional.empty();
        }
    }
}