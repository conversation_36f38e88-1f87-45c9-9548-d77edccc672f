package com.qc.agent.app.workflow.inner.mapper;

import com.qc.agent.app.workflow.inner.pojo.QcAiAgentIntentRecognition;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-05-27
 */
public interface QcAiAgentIntentRecognitionMapper {
    QcAiAgentIntentRecognition selectById(Long id);

    List<QcAiAgentIntentRecognition> selectAll();

    int insert(QcAiAgentIntentRecognition record);

    int update(QcAiAgentIntentRecognition record);

    int deleteById(Long id);

    QcAiAgentIntentRecognition selectRelatedKnowledgeAndCategoryList(@Param("intentCode") String intentCode,
                                                                     @Param("agentId") Long agentId,
                                                                     @Param("userId") Long userId,
                                                                     @Param("deptIdList") List<Long> deptIdList,
                                                                     @Param("status") String status);

    /**
     * 批量插入
     *
     * @param list
     */
    void batchInsert(@Param("list") List<QcAiAgentIntentRecognition> list);

    void deleteByAgentId(@Param("agentId") Long agentId, @Param("status") String status);
}
