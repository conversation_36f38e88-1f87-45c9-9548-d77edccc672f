package com.qc.agent.app.workflow.inner.mapper;

import com.qc.agent.app.workflow.inner.pojo.QcAiAgentIntentKnowledgeDetail;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-05-27
 */
public interface QcAiAgentIntentKnowledgeDetailMapper {
    QcAiAgentIntentKnowledgeDetail selectById(Long id);

    List<QcAiAgentIntentKnowledgeDetail> selectAll();

    int insert(QcAiAgentIntentKnowledgeDetail record);

    int update(QcAiAgentIntentKnowledgeDetail record);

    int deleteById(Long id);

    /**
     * 批量插入
     *
     * @param list
     */
    void batchInsert(@Param("list") List<QcAiAgentIntentKnowledgeDetail> list);

    void deleteByAgentId(@Param("agentId") Long agentId,@Param("status")String status);
}
