package com.qc.agent.app.agent.model.vo;

import java.io.Serializable;
import java.util.List;

import com.alibaba.fastjson.annotation.JSONField;

import lombok.Data;

/**
 * 洞察查询业务视图对象
 * 用于查询详情接口中按查询业务（数据项）分组的数据结构
 *
 * <AUTHOR>
 */
@Data
public class InsightQueryBusinessVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 数据源 ID
     */
    private Long id;

    /**
     * 数据源名称
     */
    private String sourceName;

    /**
     * 数据源编码
     */
    private String sourceCode;

    /**
     * 接口 URL
     */
    private String apiUrl;

    /**
     * 请求方式（POST/GET）
     */
    private String httpMethod;

    /**
     * 数据源描述说明
     */
    private String description;

    private String status;
    private String belongDimensionCode;
    private String belongDimensionName;

    /**
     * 查询参数值 1-最近1个月 2-最近2个月 3-最近3个月
     */
    private String queryValue;

    /**
     * 数据源参数分组配置列表
     * 替代原有的 queryFieldName
     */
    private List<SourceParamGroupVO> paramGroups;

    /**
     * 选中的数据项（用于DETAIL类型数据项）
     * 当数据项类型为DETAIL时使用此字段，其children字段重命名为itemDetail
     */
    private InsightDataItemVO selectedDataItem;

    /**
     * 选中的数据项（用于METRIC类型数据项）
     * 当数据项类型为METRIC时使用此字段，直接存放数据项对象
     */
    @JSONField(name = "selectedDataItems")
    private InsightDataItemVO selectedDataItems;
}
