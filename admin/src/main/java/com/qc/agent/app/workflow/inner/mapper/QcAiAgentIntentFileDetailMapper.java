package com.qc.agent.app.workflow.inner.mapper;


import com.qc.agent.app.workflow.inner.pojo.QcAiAgentIntentFileDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-06-09
 */
@Mapper
public interface QcAiAgentIntentFileDetailMapper {
    /**
     * 批量插入文件详情记录
     *
     * @param list 插入的实体列表
     * @return 插入条数
     */
    int batchInsert(@Param("list") List<QcAiAgentIntentFileDetail> list);

    void deleteByAgentId(@Param("agentId") Long agentId, @Param("status") String status);
}
