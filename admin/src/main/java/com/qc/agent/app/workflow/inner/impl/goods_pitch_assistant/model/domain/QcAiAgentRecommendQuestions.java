package com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.model.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025-06-30
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QcAiAgentRecommendQuestions {
    /** The unique ID of the recommended question */
    private Long id;

    /** The status of the recommended question. Default is '1' */
    private String status;

    /** The ID of the creator of the recommendation */
    private Long creatorId;

    /** The name of the creator of the recommendation */
    private String creatorName;

    /** The time when the recommendation was created */
    private LocalDateTime createTime;

    /** The conversation ID associated with the recommendation */
    private Long conversationId;

    /** The content of the recommended question */
    private String content;
    /**
     * 排序号
     */
    private BigDecimal seq;
}
