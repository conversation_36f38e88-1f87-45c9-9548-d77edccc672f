package com.qc.agent.app.agent.service.impl;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.qc.agent.app.agent.model.entity.*;
import jodd.util.StringUtil;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.servlet.RequestToViewNameTranslator;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.qc.agent.app.agent.enums.DimensionCode;
import com.qc.agent.app.agent.mapper.DimensionPromptValueMapper;
import com.qc.agent.app.agent.mapper.InsightConfigStandardRelMapper;
import com.qc.agent.app.agent.mapper.InsightDataItemMapper;
import com.qc.agent.app.agent.mapper.InsightDataSourceMapper;
import com.qc.agent.app.agent.mapper.InsightDimensionConfigMapper;
import com.qc.agent.app.agent.mapper.InsightDimensionRefItemRelMapper;
import com.qc.agent.app.agent.mapper.InsightStandardMapper;
import com.qc.agent.app.agent.mapper.InsightSummaryConfigMapper;
import com.qc.agent.app.agent.mapper.MainPromptMapper;
import com.qc.agent.app.agent.mapper.PromptFragmentMapper;
import com.qc.agent.app.agent.mapper.QcAiAgentConversationMapper;
import com.qc.agent.app.agent.mapper.QcAiAgentMapper;
import com.qc.agent.app.agent.mapper.QcAiAgentModelMapper;
import com.qc.agent.app.agent.mapper.QcAiSourceParamConfigMapper;
import com.qc.agent.app.agent.model.dto.InsightConfigWorkspaceDTO;
import com.qc.agent.app.agent.model.dto.InsightDataSourceDTO;
import com.qc.agent.app.agent.model.dto.InsightDimensionConfigDTO;
import com.qc.agent.app.agent.model.dto.InsightStandardDTO;
import com.qc.agent.app.agent.model.dto.InsightSummaryConfigDTO;
import com.qc.agent.app.agent.model.dto.MainPromptDTO;
import com.qc.agent.app.agent.model.po.QcAiAgent;
import com.qc.agent.app.agent.model.po.QcAiAgentConversation;
import com.qc.agent.app.agent.model.po.QcAiAgentModel;
import com.qc.agent.app.agent.model.query.QcAiCustomerInsightAssistantQuery;
import com.qc.agent.app.agent.model.vo.ConversationVO;
import com.qc.agent.app.agent.model.vo.InsightConfigWorkspaceVO;
import com.qc.agent.app.agent.model.vo.InsightDataItemGroup;
import com.qc.agent.app.agent.model.vo.InsightDataSourceVO;
import com.qc.agent.app.agent.model.vo.InsightDimensionConfigVO;
import com.qc.agent.app.agent.model.vo.InsightSummaryConfigVO;
import com.qc.agent.app.agent.model.vo.SourceParamGroupVO;
import com.qc.agent.app.agent.model.vo.SourceParamVO;
import com.qc.agent.app.agent.service.CustomerInsightService;
import com.qc.agent.app.agent.service.PromptService;
import com.qc.agent.app.agent.service.customer_insight_intent.CustomerInsightIntentHandler;
import com.qc.agent.app.agent.util.InsightDataItemTreeUtils;
import com.qc.agent.app.workflow.inner.impl.customer_insight_assistant.model.CustomerInsightBusinessIntent;
import com.qc.agent.app.workflow.inner.impl.customer_insight_assistant.model.enums.CustomerInsightIntent;
import com.qc.agent.app.workflow.inner.impl.customer_insight_assistant.service.llm.CustomerInsightIntentClassifier;
import com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.service.tool.CustomerTool;
import com.qc.agent.app.workflow.inner.impl.tools.LLMTools;
import com.qc.agent.common.core.TenantUser;
import com.qc.agent.lla.model.LLAConfig;
import com.qc.agent.lla.model.LLAQa;
import com.qc.agent.lla.model.LLARequest;
import com.qc.agent.platform.register.UserManager;
import com.qc.agent.platform.util.UUIDUtils;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 客户洞察服务实现类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CustomerInsightServiceImpl implements CustomerInsightService {

    private final QcAiAgentMapper qcAiAgentMapper;
    private final QcAiAgentModelMapper qcAiAgentModelMapper;
    private final QcAiAgentConversationMapper qcAiAgentConversationMapper;

    private final CustomerInsightIntentClassifier customerInsightIntentClassifier;
    private final Map<CustomerInsightIntent, CustomerInsightIntentHandler> customerInsightIntentHandlerMap;
    private final CustomerTool customerTool;

    private static final String CONFIG_TYPE_DIMENSION = "DIMENSION";
    private static final String CONFIG_TYPE_SUMMARY = "SUMMARY";

    @Resource
    private InsightDimensionConfigMapper insightDimensionConfigMapper;
    @Resource
    private InsightDimensionRefItemRelMapper insightDimensionRefItemRelMapper;
    @Resource
    private InsightConfigStandardRelMapper insightConfigStandardRelMapper;
    @Resource
    private InsightDataItemMapper insightDataItemMapper;
    @Resource
    private InsightStandardMapper insightStandardMapper;
    @Resource
    private InsightDataSourceMapper insightDataSourceMapper;
    @Resource
    private QcAiSourceParamConfigMapper qcAiSourceParamConfigMapper;

    @Resource
    private InsightSummaryConfigMapper insightSummaryConfigMapper;

    @Resource
    private DimensionPromptValueMapper dimensionPromptValueMapper;

    @Resource
    private PromptService promptService;

    @Resource
    private MainPromptMapper mainPromptMapper;

    @Resource
    private PromptFragmentMapper promptFragmentMapper;
    @Autowired
    private RequestToViewNameTranslator requestToViewNameTranslator;

    @Override
    public Map<String, Object> intent(QcAiCustomerInsightAssistantQuery query) {
        final String logPrefix = LLMTools.getLogPrefix(query.getAgentId(), UUID.randomUUID().toString());
        log.info("{} ===== 开始处理客户洞察意图识别请求 =====", logPrefix);

        // 步骤 1: 处理特殊情况，如用户点击 "确定"
        return handleConfirmClick(query, logPrefix).orElseGet(() -> {

            // 步骤 2: 加载并验证执行所需的前提条件（Agent, Model）
            Prerequisites prerequisites = loadPrerequisites(query.getAgentId(), logPrefix);

            // 步骤 3: 分类并精炼用户意图
            CustomerInsightBusinessIntent finalIntent = classifyAndRefineIntent(query, prerequisites, logPrefix);

            // 步骤 4: 根据最终意图生成回答并持久化会话
            ConversationResult conversationResult = generateAnswerAndPersist(query, finalIntent, logPrefix);

            // 步骤 5: 构建并返回最终响应
            Map<String, Object> response = buildResponse(finalIntent, conversationResult, logPrefix);
            log.info("{} ===== 客户洞察意图识别请求处理完成 =====", logPrefix);
            return response;
        });
    }

    /**
     * 处理用户点击"确定"按钮的快捷路径
     */
    private Optional<Map<String, Object>> handleConfirmClick(QcAiCustomerInsightAssistantQuery query,
            String logPrefix) {
        if ("1".equals(query.getIsClick())) {
            log.info("{} 用户点击了确定按钮，更新上一个会话", logPrefix);

            // 更新上一个会话
            if (query.getConversationId() != null) {
                QcAiAgentConversation conversation = qcAiAgentConversationMapper.selectById(query.getConversationId());
                if (conversation != null) {
                    // 增加空指针判断，避免NPE
                    String customer = "";
                    try {
                        if (query.getCommonParam() != null
                                && query.getCommonParam().getInsightIntent() != null
                                && query.getCommonParam().getInsightIntent().entities() != null
                                && query.getCommonParam().getInsightIntent().entities()
                                        .get("customer") != null) {
                            customer = query.getCommonParam().getInsightIntent().entities().get("customer")
                                    .toString();
                        }
                    } catch (Exception e) {
                        log.error("{} 获取客户信息时发生异常: {}", logPrefix, e.getMessage(), e);
                        customer = "";
                    }
                    conversation.setAnswer("没问题，请提供需要洞察的客户，请选择或者直接告诉我都行。客户选择:" + customer);
                    conversation.setAnswerTime(LocalDateTime.now());
                    qcAiAgentConversationMapper.update(conversation);
                }
            }

            // 返回"其他"意图的响应
            // 使用Map.of创建不可变Map，更安全简洁
            Map<String, Object> response = Map.of("insightIntent", Map.of("type", CustomerInsightIntent.OTHER,
                    "entities", new JSONObject()),
                    "isDealer", customerTool.queryClientData(logPrefix),
                    "conversationId", String.valueOf(query.getConversationId()) // 确保类型一致
            );
            log.info("{} 构建响应完成: intent={}, conversationId={}",
                    logPrefix, CustomerInsightIntent.OTHER, query.getConversationId());
            return Optional.of(response);
        }
        return Optional.empty();
    }

    /**
     * 加载并验证Agent和Model是否存在。
     * 建议: 考虑为 "Not Found" 情况创建自定义的、更具体的业务异常。
     */
    private Prerequisites loadPrerequisites(Long agentId, String logPrefix) {
        log.debug("{} 正在加载Agent和Model...", logPrefix);
        QcAiAgent qcAiAgent = Optional.ofNullable(qcAiAgentMapper.selectById(agentId)).orElseThrow(() -> {
            log.error("{} 未能找到ID为: {} 的智能体，处理中断。", logPrefix, agentId);
            return new IllegalArgumentException("Agent not found with id: " + agentId);
        });

        QcAiAgentModel qcAiAgentModel = Optional.ofNullable(qcAiAgentModelMapper.selectById(qcAiAgent.getModelId()))
                .orElseThrow(() -> {
                    log.error("{} 未能找到ID为: {} 的模型，处理中断。", logPrefix, qcAiAgent.getModelId());
                    return new IllegalStateException("Model not found for agent with id: " + agentId);
                });

        log.debug("{} Agent和Model加载成功。", logPrefix);
        return new Prerequisites(qcAiAgent, qcAiAgentModel);
    }

    private LLARequest initLLARequest(QcAiCustomerInsightAssistantQuery query) {
        List<ConversationVO> conversationVOS = new ArrayList<>();
        if (!Objects.equals(query.getIsNew(), "1")) {
            conversationVOS = qcAiAgentConversationMapper.queryAgentConversation(query.getAgentId(),
                    UserManager.getTenantUser().getUserId(), 1, 0, "1");
        }
        return LLARequest.builder().sessionId(query.getSessionId())
                .content(query.getQuestion())
                .qas(Optional.ofNullable(conversationVOS).orElse(Lists.newArrayList())
                        .stream()
                        .map((ConversationVO item) -> LLAQa.builder().question(item.getQuestion())
                                .answer(item.getAnswer()).build())
                        .collect(Collectors.toList()))
                .build();

    }

    /**
     * 分类并精炼用户意图
     */
    private CustomerInsightBusinessIntent classifyAndRefineIntent(QcAiCustomerInsightAssistantQuery query,
            Prerequisites prerequisites, String logPrefix) {
        log.info("{} 开始意图分类，问题: {}", logPrefix, query.getQuestion());
        LLAConfig config = LLMTools.initLLAConfig(prerequisites.model());
        LLARequest request = initLLARequest(query);

        log.info("{} 向LLM发送初步意图分类请求...", logPrefix);
        // 使用意图分类器进行初步分类
        CustomerInsightBusinessIntent initialIntent = customerInsightIntentClassifier.classifyIntent(request, config,
                logPrefix);
        log.info("{} LLM初步分类意图为: '{}', 实体: {}", logPrefix, initialIntent.intent(), initialIntent.entities());

        // 根据意图类型查找对应的处理器进行精炼
        return Optional.ofNullable(customerInsightIntentHandlerMap.get(initialIntent.intent())).map(handler -> {
            log.info("{} 找到意图 '{}' 的特定处理器, 开始精炼...", logPrefix, initialIntent.intent());
            CustomerInsightBusinessIntent handledIntent = handler.handle(initialIntent, query, logPrefix);
            log.info("{} 处理器精炼后的意图为: '{}', 实体: {}", logPrefix, handledIntent.intent(), handledIntent.entities());
            return handledIntent;
        }).orElse(initialIntent);
    }

    /**
     * 根据最终意图生成回答并持久化会话
     */
    private ConversationResult generateAnswerAndPersist(QcAiCustomerInsightAssistantQuery query,
            CustomerInsightBusinessIntent finalIntent, String logPrefix) {
        // 创建会话记录
        QcAiAgentConversation conversation = new QcAiAgentConversation();
        conversation.setId(UUIDUtils.getUUID2Long());
        conversation.setAgentId(query.getAgentId());
        conversation.setQuestion(query.getQuestion());
        conversation.setCreateTime(LocalDateTime.now());
        conversation.setSessionId(query.getSessionId());
        conversation.setChatSessionId(query.getChatSessionId());
        conversation.setSource(query.getSource());
        conversation.setStatus("0");
        conversation.setConversationStatus("0");

        String answer = "";
        if (Objects.equals(finalIntent.intent(), CustomerInsightIntent.CUSTOMER_ANALYSIS_INPUT)) {
            // 如果customer字段为null，则取值为""
            String customerName = finalIntent.entities().getString("customer");
            if (customerName == null) {
                customerName = "";
            }
            answer = "没问题，请提供需要洞察的客户，请选择或者直接告诉我都行。客户选择:" + customerName;
        }

        // 更新会话记录
        conversation.setAnswer(answer);
        conversation.setAnswerTime(LocalDateTime.now());
        qcAiAgentConversationMapper.insert(conversation);

        return new ConversationResult(answer, conversation.getId().toString());
    }

    /**
     * 构建并返回最终响应
     */
    private Map<String, Object> buildResponse(CustomerInsightBusinessIntent finalIntent,
            ConversationResult conversationResult, String logPrefix) {
        // 使用Map.of创建不可变Map，更安全
        Map<String, Object> response = Map.of("insightIntent",
                Map.of("type", finalIntent.intent(),
                        "entities", finalIntent.entities()),
                "isDealer", customerTool.queryClientData(logPrefix),
                "conversationId", conversationResult.conversationId());

        log.info("{} 构建响应完成: intent={}, conversationId={}",
                logPrefix, finalIntent.intent(), conversationResult.conversationId());
        return response;
    }

    /**
     * 定义一个Record来封装Agent和Model，作为不可变数据载体。
     */
    private record Prerequisites(QcAiAgent agent, QcAiAgentModel model) {
    }

    /**
     * 用于封装答案和会话ID的结果类。
     */
    private record ConversationResult(String answer, String conversationId) {
    }

    /**
     * 用于封装当前用户信息的结果类。
     */
    private record CurrentUserInfo(Long userId, String userName) {
    }

    /**
     * 获取当前用户信息
     */
    private CurrentUserInfo getCurrentUserInfo() {
        TenantUser currentUser = UserManager.getTenantUser();
        return new CurrentUserInfo(currentUser.getUserId(), currentUser.getUserName());
    }

    /**
     * 获取当前时间戳
     */
    private Timestamp getCurrentTimestamp() {
        return new Timestamp(System.currentTimeMillis());
    }

    @Override
    public InsightConfigWorkspaceVO getWorkspaceByMode(Long agentId, String insightMode) {
        InsightConfigWorkspaceVO workspaceVO = new InsightConfigWorkspaceVO();

        // 1. 根据模式查询维度配置
        List<InsightDimensionConfig> dimensionConfigs = insightDimensionConfigMapper.selectByAgentIdAndMode(agentId, insightMode);

        // 2. 根据模式类型分组维度配置
        InsightConfigWorkspaceVO.InsightModeConfiguration modeConfiguration = buildModeConfiguration(insightMode, dimensionConfigs);

        workspaceVO.setCurrentDimensionConfiguration(modeConfiguration);

        // 3. 根据模式获取总结配置
        InsightSummaryConfig summaryConfig = insightSummaryConfigMapper.selectByAgentIdAndMode(agentId, insightMode);
        if (summaryConfig != null) {
            workspaceVO.setCurrentSummaryConfiguration(buildSummaryConfigVO(summaryConfig));
        }
        return workspaceVO;
    }

    private InsightConfigWorkspaceVO.InsightModeConfiguration buildModeConfiguration(String insightMode, List<InsightDimensionConfig> dimensionConfigs) {
        InsightConfigWorkspaceVO.InsightModeConfiguration modeConfiguration = new InsightConfigWorkspaceVO.InsightModeConfiguration();
        modeConfiguration.setInsightMode(insightMode);

        // 按维度类型分组
        Map<String, List<InsightDimensionConfig>> configsByType = dimensionConfigs.stream()
                .collect(Collectors.groupingBy(config -> config.getInsightModeType() != null ? config.getInsightModeType() : "0"));

        switch (insightMode) {
            case "0":
                // 客户模式：只处理客户维度配置
                modeConfiguration.setCustomerDimensionConfiguration(buildDimensionConfigVOs(configsByType.get("0")));
                break;
            case "1":
                // 终端经销商模式：处理终端和经销商维度配置
                modeConfiguration.setTerminalDimensionConfiguration(buildDimensionConfigVOs(configsByType.get("1")));
                modeConfiguration.setDealerDimensionConfiguration(buildDimensionConfigVOs(configsByType.get("2")));
                break;
            default:
                log.warn("Unsupported insightMode: {}", insightMode);
        }
        return modeConfiguration;
    }

    private List<InsightDimensionConfigVO> buildDimensionConfigVOs(List<InsightDimensionConfig> configs) {
        if (configs == null || configs.isEmpty()) {
            return Collections.emptyList();
        }

        return configs.stream()
                .map(this::buildDimensionConfigVOAndDataSources)
                .collect(Collectors.toList());
    }

    private InsightDimensionConfigVO buildDimensionConfigVOAndDataSources(InsightDimensionConfig dim) {
        InsightDimensionConfigVO dimVO = buildDimensionConfigVO(dim);
        return buildDimensionDataSources(dimVO, dim.getId());
    }

    /**
     * 构建维度配置的数据源信息
     */
    private InsightDimensionConfigVO buildDimensionDataSources(InsightDimensionConfigVO dimVO, Long dimensionConfigId) {
        // 查询维度关联的数据项关系
        List<InsightDimensionRefItemRel> dimensionItemRels = insightDimensionRefItemRelMapper.selectByDimensionConfigId(dimensionConfigId);

        // 按数据源分组收集选中的数据项
        Map<String, List<InsightDimensionRefItemRel>> dataSourceGroups = dimensionItemRels.stream()
                .collect(Collectors.groupingBy(rel -> {
                    InsightDataItem dataItem = insightDataItemMapper.selectById(rel.getRefDataItemId());
                    return dataItem != null ? dataItem.getQueryBusinessCode() : null;
                }));

        // 构建数据源VO列表 - 每个选中的数据项拆分为一个独立的dataSource
        List<InsightDataSourceVO> dataSourceVOs = new ArrayList<>();
        for (Map.Entry<String, List<InsightDimensionRefItemRel>> entry : dataSourceGroups.entrySet()) {
            String businessCode = entry.getKey();
            if (businessCode == null)
                continue;

            List<InsightDimensionRefItemRel> rels = entry.getValue();

            // 查询数据源基本信息（所有拆分的dataSource都使用相同的基本信息）
            InsightDataSource dataSourceInfo = null;
            List<InsightDataSource> dataSources = insightDataSourceMapper.selectBySourceCode(businessCode);
            if (!dataSources.isEmpty()) {
                dataSourceInfo = dataSources.get(0);
            }

            // 构建选中的数据项树形结构
            List<InsightDataItem> selectedItems = buildSelectedDataItemsTree(rels);

            // 为每个选中的数据项创建一个独立的dataSource
            for (InsightDataItem selectedItem : selectedItems) {
                InsightDataSourceVO dataSourceVO = new InsightDataSourceVO();
                dataSourceVO.setSourceCode(businessCode);

                // 设置数据源基本信息
                if (dataSourceInfo != null) {
                    dataSourceVO.setSelectedItemId(selectedItem.getId());
                    dataSourceVO.setSourceName(dataSourceInfo.getSourceName());
                    dataSourceVO.setApiUrl(dataSourceInfo.getApiUrl());
                    dataSourceVO.setHttpMethod(dataSourceInfo.getHttpMethod());
                    dataSourceVO.setDescription(dataSourceInfo.getDescription());
                    dataSourceVO.setBelongDimensionCode(dataSourceInfo.getBelongDimensionCode());
                    dataSourceVO.setBelongDimensionName(dataSourceInfo.getBelongDimensionName());
                    dataSourceVO.setDataTypeCode(selectedItem.getDataTypeCode());

                    List<QcAiSourceParamConfig> qcAiSourceParamConfigs = qcAiSourceParamConfigMapper
                            .selectGroupsBySourceId(dataSourceInfo.getId());
                    if (qcAiSourceParamConfigs != null && !qcAiSourceParamConfigs.isEmpty()) {
                        dataSourceVO.setGroupCode(qcAiSourceParamConfigs.get(0).getGroupCode());
                        dataSourceVO.setGroupName(qcAiSourceParamConfigs.get(0).getGroupName());
                    }
                    dataSourceVO.setQueryValue(rels.get(0).getQueryValue());

                }
                Optional.ofNullable(selectedItem.getChildren()).ifPresent(children -> {
                    dataSourceVO.setSelectedItemIdSubId(
                            children.stream().map(InsightDataItem::getId).collect(Collectors.toList()));
                });
                dataSourceVOs.add(dataSourceVO);
            }
        }

        dimVO.setDataSources(dataSourceVOs);
        return dimVO;
    }

    /**
     * 构建选中的数据项树形结构
     * 根据关系表中的数据，构建包含父子关系的树形结构
     */
    private List<InsightDataItem> buildSelectedDataItemsTree(List<InsightDimensionRefItemRel> rels) {
        if (rels == null || rels.isEmpty()) {
            return new ArrayList<>();
        }

        // 1. 获取所有选中的数据项
        List<InsightDataItem> selectedItems = new ArrayList<>();
        for (InsightDimensionRefItemRel rel : rels) {
            InsightDataItem dataItem = insightDataItemMapper.selectById(rel.getRefDataItemId());
            if (dataItem != null) {
                InsightDataItem itemCopy = new InsightDataItem();
                BeanUtils.copyProperties(dataItem, itemCopy);
                selectedItems.add(itemCopy);
            }
        }

        // 2. 使用 InsightDataItemTreeUtils 构建树形结构
        return InsightDataItemTreeUtils.buildTree(selectedItems);
    }

    /**
     * 构建数据源参数分组配置
     */
    private List<SourceParamGroupVO> buildSourceParamGroups(Long sourceId) {
        if (sourceId == null) {
            return new ArrayList<>();
        }

        // 1. 查询该数据源的所有参数配置
        List<QcAiSourceParamConfig> paramConfigs = qcAiSourceParamConfigMapper.selectBySourceId(sourceId);
        if (paramConfigs.isEmpty()) {
            return new ArrayList<>();
        }

        // 2. 按分组编码分组
        Map<String, List<QcAiSourceParamConfig>> groupMap = paramConfigs.stream()
                .collect(Collectors.groupingBy(QcAiSourceParamConfig::getGroupCode));

        // 3. 构建分组VO列表
        List<SourceParamGroupVO> paramGroups = new ArrayList<>();
        for (Map.Entry<String, List<QcAiSourceParamConfig>> entry : groupMap.entrySet()) {
            String groupCode = entry.getKey();
            List<QcAiSourceParamConfig> groupConfigs = entry.getValue();

            if (groupConfigs.isEmpty()) {
                continue;
            }

            SourceParamGroupVO groupVO = new SourceParamGroupVO();
            QcAiSourceParamConfig firstConfig = groupConfigs.get(0);

            groupVO.setGroupCode(groupCode);
            groupVO.setGroupName(firstConfig.getGroupName());
            groupVO.setDisplayOrder(firstConfig.getDisplayOrder());

            // 解析枚举值
            if (firstConfig.getGroupEnumValues() != null) {
                try {
                    List<SourceParamGroupVO.EnumValueVO> enumValues = JSONObject.parseArray(
                            firstConfig.getGroupEnumValues(), SourceParamGroupVO.EnumValueVO.class);
                    groupVO.setEnumValues(enumValues);
                } catch (Exception e) {
                    log.warn("解析参数分组枚举值失败，sourceId: {}, groupCode: {}, enumValues: {}",
                            sourceId, groupCode, firstConfig.getGroupEnumValues(), e);
                    groupVO.setEnumValues(new ArrayList<>());
                }
            } else {
                groupVO.setEnumValues(new ArrayList<>());
            }

            // 构建参数列表
            List<SourceParamVO> params = groupConfigs.stream()
                    .map(config -> {
                        SourceParamVO paramVO = new SourceParamVO();
                        paramVO.setParamCode(config.getParamCode());
                        paramVO.setGroupCode(config.getGroupCode());
                        paramVO.setDisplayOrder(config.getDisplayOrder());
                        return paramVO;
                    })
                    .sorted(Comparator.comparing(SourceParamVO::getDisplayOrder,
                            Comparator.nullsLast(Comparator.naturalOrder())))
                    .collect(Collectors.toList());

            groupVO.setParams(params);
            paramGroups.add(groupVO);
        }

        // 4. 按显示顺序排序
        paramGroups.sort(Comparator.comparing(SourceParamGroupVO::getDisplayOrder,
                Comparator.nullsLast(Comparator.naturalOrder())));

        return paramGroups;
    }

       @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveWorkspaceByMode(InsightConfigWorkspaceDTO workspaceDTO, String insightMode) {
        Long agentId = workspaceDTO.getAgentId();

        // === 1. 处理维度配置（新增、修改、删除） ===
        if (workspaceDTO.getDimensionConfigurations() != null) {
            // 获取当前已存在的维度配置（按模式查询）
            List<InsightDimensionConfig> existingConfigs = insightDimensionConfigMapper.selectByAgentIdAndMode(agentId, insightMode);
            List<Long> existingConfigIds = existingConfigs.stream()
                    .map(InsightDimensionConfig::getId)
                    .toList();

            // 收集所有请求中的配置
            List<InsightDimensionConfigDTO> allRequestConfigs = collectRequestConfigs(workspaceDTO.getDimensionConfigurations(), insightMode);

            // 获取请求中的配置ID
            List<Long> requestConfigIds = allRequestConfigs.stream()
                    .map(InsightDimensionConfigDTO::getId)
                    .filter(Objects::nonNull)
                    .toList();

            // 找出需要删除的配置（存在但请求中没有的）
            List<Long> configsToDelete = existingConfigIds.stream()
                    .filter(id -> !requestConfigIds.contains(id))
                    .collect(Collectors.toList());

            // 删除配置及其关联关系
            if (!CollectionUtils.isEmpty(configsToDelete)) {
                insightDimensionConfigMapper.deleteBatchIds(configsToDelete);
                insightDimensionRefItemRelMapper.deleteByDimensionConfigIds(configsToDelete);
                insightConfigStandardRelMapper.deleteByConfigIdsAndType(configsToDelete, CONFIG_TYPE_DIMENSION);
                dimensionPromptValueMapper.deleteByConfigIds(configsToDelete);

            }

            // 处理新增和修改的配置
            for (InsightDimensionConfigDTO dimensionDTO : allRequestConfigs) {
                if (dimensionDTO.getId() != null) {
                    // 修改现有配置
                    updateDimensionConfig(dimensionDTO, agentId, insightMode);
                } else {
                    // 新增配置
                    createDimensionConfig(dimensionDTO, agentId, insightMode);
                }
            }
        }

        // === 2. 处理总结配置 ===
        if (workspaceDTO.getSummaryConfiguration() != null) {
            InsightSummaryConfigDTO summaryDTO = workspaceDTO.getSummaryConfiguration();

            // 查询现有的总结配置（按模式查询）
            InsightSummaryConfig existingSummary = insightSummaryConfigMapper.selectByAgentIdAndMode(agentId,
                    insightMode);

            if (existingSummary != null) {
                // 删除现有的总结配置及其关联关系
                dimensionPromptValueMapper.deleteByConfigId(existingSummary.getId());
            }

            if (existingSummary != null) {
                // 修改现有配置
                updateSummaryConfig(summaryDTO, existingSummary.getId(), insightMode);
            } else {
                // 新增配置
                createSummaryConfig(summaryDTO, agentId, insightMode);
            }
        }

        return true;
    }

    private List<InsightDimensionConfigDTO> collectRequestConfigs(InsightConfigWorkspaceDTO.InsightModeConfigurationDTO modeConfig, String insightMode) {
        List<InsightDimensionConfigDTO> allRequestConfigs = new ArrayList<>();

        switch (insightMode) {
            case "0":
                // 客户模式：只收集客户维度配置
                collectDimensionConfigs(modeConfig.getCustomerDimensionConfiguration(), "0", allRequestConfigs);
                break;
            case "1":
                // 终端经销商模式：只收集终端和经销商维度配置
                collectDimensionConfigs(modeConfig.getTerminalDimensionConfiguration(), "1", allRequestConfigs);
                collectDimensionConfigs(modeConfig.getDealerDimensionConfiguration(), "2", allRequestConfigs);
                break;
            default:
                log.warn("Unsupported insightMode: {}", insightMode);
        }

        return allRequestConfigs;
    }

    private void collectDimensionConfigs(List<InsightDimensionConfigDTO> configs, String insightModeType, List<InsightDimensionConfigDTO> allRequestConfigs) {
        if (configs != null) {
            for (InsightDimensionConfigDTO config : configs) {
                config.setInsightModeType(insightModeType);
                allRequestConfigs.add(config);
            }
        }
    }


    /**
     * 管理配置的标准（支持新增、修改、删除标准及其关系）
     */
    private void manageDimensionStandards(Long configId, List<InsightStandardDTO> standards, String configType) {
        if (standards != null) {
            // 获取当前已存在的标准关系
            List<InsightConfigStandardRel> existingRels = insightConfigStandardRelMapper
                    .selectByConfigIdAndType(configId, configType);
            List<Long> existingStandardIds = existingRels.stream()
                    .map(InsightConfigStandardRel::getStandardId)
                    .toList();

            // 获取请求中的标准ID
            List<Long> requestStandardIds = standards.stream()
                    .map(InsightStandardDTO::getId)
                    .filter(Objects::nonNull)
                    .toList();

            // 找出需要删除的标准（存在但请求中没有的）
            List<Long> standardsToDelete = existingStandardIds.stream()
                    .filter(id -> !requestStandardIds.contains(id))
                    .toList();

            // 删除标准及其关联关系
            if (!CollectionUtils.isEmpty(standardsToDelete)) {
                // 删除关联关系
                insightConfigStandardRelMapper.deleteByConfigIdAndStandardIds(configId, standardsToDelete);
                // 删除标准本身
                insightStandardMapper.deleteBatchIds(standardsToDelete);
            }

            // 处理新增和修改的标准
            for (int i = 0; i < standards.size(); i++) {
                InsightStandardDTO standardDTO = standards.get(i);
                // 按照数组顺序自动设置排序
                standardDTO.setSortOrder(i + 1);

                if (standardDTO.getId() != null) {
                    // 修改现有标准
                    updateStandard(standardDTO);
                    // 确保关联关系存在
                    ensureStandardRelation(configId, standardDTO.getId(), configType);
                } else {
                    // 新增标准
                    createStandardAndRelation(configId, standardDTO, configType);
                }
            }
        }
    }

    /**
     * 新增标准并建立关联关系
     */
    private void createStandardAndRelation(Long configId, InsightStandardDTO standardDTO, String configType) {
        CurrentUserInfo userInfo = getCurrentUserInfo();
        InsightStandard newStandard = new InsightStandard();
        BeanUtils.copyProperties(standardDTO, newStandard);
        if (CONFIG_TYPE_DIMENSION.equals(configType)) {
            newStandard.setStandardTypeCode(CONFIG_TYPE_DIMENSION);
            newStandard.setStandardType("维度标准");
        } else if (CONFIG_TYPE_SUMMARY.equals(configType)) {
            newStandard.setStandardTypeCode(CONFIG_TYPE_SUMMARY);
            newStandard.setStandardType("总结标准");
        }
        Timestamp now = getCurrentTimestamp();
        newStandard.setStatus("1");
        newStandard.setCreatorId(userInfo.userId());
        newStandard.setCreatorName(userInfo.userName());
        newStandard.setCreateTime(now);
        newStandard.setModifyierId(userInfo.userId());
        newStandard.setModifyierName(userInfo.userName());
        newStandard.setModifyTime(now);
        insightStandardMapper.insert(newStandard);
        Long standardId = newStandard.getId();
        InsightConfigStandardRel rel = new InsightConfigStandardRel();
        rel.setConfigId(configId);
        rel.setStandardId(standardId);
        rel.setConfigType(configType);
        rel.setStatus("1");
        rel.setCreatorId(userInfo.userId());
        rel.setCreatorName(userInfo.userName());
        rel.setCreateTime(now);
        rel.setModifyierId(userInfo.userId());
        rel.setModifyierName(userInfo.userName());
        rel.setModifyTime(now);
        insightConfigStandardRelMapper.insert(rel);
    }

    /**
     * 更新标准
     */
    private void updateStandard(InsightStandardDTO standardDTO) {
        CurrentUserInfo userInfo = getCurrentUserInfo();

        // 先查询现有数据
        InsightStandard existingStandard = insightStandardMapper.selectById(standardDTO.getId());
        if (existingStandard == null) {
            throw new RuntimeException("标准不存在，ID: " + standardDTO.getId());
        }

        // 只更新非null的字段，保持其他字段不变
        InsightStandard standard = new InsightStandard();
        standard.setId(standardDTO.getId());

        // 只更新有值的字段
        if (standardDTO.getStandardName() != null) {
            standard.setStandardName(standardDTO.getStandardName());
        }
        if (standardDTO.getStandardDefinition() != null) {
            standard.setStandardDefinition(standardDTO.getStandardDefinition());
        }
        if (standardDTO.getSortOrder() != null) {
            standard.setSortOrder(standardDTO.getSortOrder());
        }

        // 设置修改相关字段
        standard.setModifyierId(userInfo.userId());
        standard.setModifyierName(userInfo.userName());
        standard.setModifyTime(getCurrentTimestamp());

        insightStandardMapper.updateStandardInfo(standard);
    }

    /**
     * 确保标准关联关系存在
     */
    private void ensureStandardRelation(Long configId, Long standardId, String configType) {
        CurrentUserInfo userInfo = getCurrentUserInfo();
        InsightConfigStandardRel existingRel = insightConfigStandardRelMapper.selectByConfigIdAndStandardId(configId,
                standardId);
        if (existingRel == null) {
            InsightConfigStandardRel rel = new InsightConfigStandardRel();
            rel.setConfigId(configId);
            rel.setStandardId(standardId);
            rel.setConfigType(configType);
            Timestamp now = getCurrentTimestamp();
            rel.setStatus("1");
            rel.setCreatorId(userInfo.userId());
            rel.setCreatorName(userInfo.userName());
            rel.setCreateTime(now);
            rel.setModifyierId(userInfo.userId());
            rel.setModifyierName(userInfo.userName());
            rel.setModifyTime(now);
            insightConfigStandardRelMapper.insert(rel);
        }
    }

    /**
     * 创建维度配置
     */
    private void createDimensionConfig(InsightDimensionConfigDTO dto, Long agentId, String insightMode) {
        CurrentUserInfo userInfo = getCurrentUserInfo();

        InsightDimensionConfig dimensionConfig = saveInsightDimensionConfig(dto, agentId, insightMode, userInfo);
        Long newDimensionConfigId = dimensionConfig.getId();

        // 保存提示词片段值（不修改配置表，只保存用户选择的值）
        promptService.saveFragmentPrompt(dto.getInterpretationPrompt(), newDimensionConfigId, dto.getDimensionCode(), "DIMENSION");
        saveDimensionDataItemRelations(newDimensionConfigId, dto.getDataSources());
        manageDimensionStandards(newDimensionConfigId, dto.getStandards(), CONFIG_TYPE_DIMENSION);
    }

    @NotNull
    private InsightDimensionConfig saveInsightDimensionConfig(InsightDimensionConfigDTO dto, Long agentId,
            String insightMode, CurrentUserInfo userInfo) {
        InsightDimensionConfig dimensionConfig = new InsightDimensionConfig();
        BeanUtils.copyProperties(dto, dimensionConfig);
        dimensionConfig.setAgentId(agentId);
        dimensionConfig.setInsightMode(insightMode);

        // 自动生成维度编码和名称（如果前端没有传递）
        if (dimensionConfig.getDimensionCode() == null) {
            // 根据数据项自动推导维度编码
            String dimensionCode = deriveDimensionCodeFromDataItems(dto.getDataSources());
            dimensionConfig.setDimensionCode(dimensionCode);
        }

        if (dimensionConfig.getDimensionName() == null) {
            // 根据维度编码生成维度名称
            String dimensionName = deriveDimensionNameFromCode(dimensionConfig.getDimensionCode());
            dimensionConfig.setDimensionName(dimensionName);
        }

        Timestamp now = getCurrentTimestamp();
        dimensionConfig.setId(UUIDUtils.getUUID2Long());
        dimensionConfig.setStatus("1");
        dimensionConfig.setCreatorId(userInfo.userId());
        dimensionConfig.setCreatorName(userInfo.userName());
        dimensionConfig.setCreateTime(now);
        dimensionConfig.setModifyierId(userInfo.userId());
        dimensionConfig.setModifyierName(userInfo.userName());
        dimensionConfig.setModifyTime(now);
        insightDimensionConfigMapper.insert(dimensionConfig);
        return dimensionConfig;
    }

    private void updateDimensionConfig(InsightDimensionConfigDTO dto, Long agentId, String insightMode) {
        CurrentUserInfo userInfo = getCurrentUserInfo();

        // 先查询现有数据
        InsightDimensionConfig existingConfig = insightDimensionConfigMapper.selectById(dto.getId());
        if (existingConfig == null) {
            throw new RuntimeException("维度配置不存在，ID: " + dto.getId());
        }

        updateDimensionConfig(dto, agentId, insightMode, userInfo);

        insightDimensionRefItemRelMapper.deleteByDimensionConfigId(dto.getId());
        saveDimensionDataItemRelations(dto.getId(), dto.getDataSources());
        insightConfigStandardRelMapper.deleteByConfigIdsAndType(Collections.singletonList(dto.getId()),
                CONFIG_TYPE_DIMENSION);
        manageDimensionStandards(dto.getId(), dto.getStandards(), CONFIG_TYPE_DIMENSION);

        updateFragmentValues(dto);
    }

    private void updateDimensionConfig(InsightDimensionConfigDTO dto, Long agentId, String insightMode,
            CurrentUserInfo userInfo) {
        // 只更新非null的字段，保持其他字段不变
        InsightDimensionConfig dimensionConfig = new InsightDimensionConfig();
        dimensionConfig.setId(dto.getId());
        dimensionConfig.setAgentId(agentId);

        // 如果指定了模式，则更新模式字段
        if (insightMode != null) {
            dimensionConfig.setInsightMode(insightMode);
        }

        // 只更新有值的字段
        if (dto.getDimensionCode() != null) {
            dimensionConfig.setDimensionCode(dto.getDimensionCode());
        }
        if (dto.getDimensionName() != null) {
            dimensionConfig.setDimensionName(dto.getDimensionName());
        }
        if (dto.getSortOrder() != null) {
            dimensionConfig.setSortOrder(dto.getSortOrder());
        }

        // 设置修改相关字段
        dimensionConfig.setModifyierId(userInfo.userId());
        dimensionConfig.setModifyierName(userInfo.userName());
        dimensionConfig.setModifyTime(getCurrentTimestamp());

        insightDimensionConfigMapper.updateById(dimensionConfig);
    }

    private void updateFragmentValues(InsightDimensionConfigDTO dto) {
        // 更新提示词片段值（维度配置相关）
        if (dto.getInterpretationPrompt() != null) {
            // 先删除现有的提示词片段值
            dimensionPromptValueMapper.deleteByConfigId(dto.getId());
            //查询提示词主体
            MainPrompt dimensionMainPrompt = mainPromptMapper.selectByDimensionCodeAndType(dto.getDimensionCode(), "DIMENSION");

            // 保存新的提示词片段值
            List<DimensionPromptValue> promptValues = new ArrayList<>();
            dto.getInterpretationPrompt().forEach((key, value) -> {
                DimensionPromptValue promptValue = new DimensionPromptValue();
                promptValue.setConfigId(dto.getId());
                promptValue.setPromptFragmentId(promptFragmentMapper.selectByKey(key, dimensionMainPrompt.getId()).getId());
                promptValue.setFragmentValue((String) value);
                promptValue.setStatus("1");
                promptValues.add(promptValue);
            });
            if (!promptValues.isEmpty()) {
                dimensionPromptValueMapper.batchInsert(promptValues);
            }
        }
    }

    /**
     * 保存维度数据项关系
     */
    private void saveDimensionDataItemRelations(Long configId, List<InsightDataSourceDTO> dataSources) {
        CurrentUserInfo userInfo = getCurrentUserInfo();

        if (dataSources != null) {
            int sortOrder = 1;
            for (int i = 0; i < dataSources.size(); i++) {
                InsightDataSourceDTO dataSource = dataSources.get(i);
                if (dataSource.getSelectedDataItemIds() != null) {
                    for (Long dataItemId : dataSource.getSelectedDataItemIds()) {
                        // 通过数据项ID查询数据项信息
                        InsightDataItem dataItem = insightDataItemMapper.selectById(dataItemId);
                        if (dataItem == null) {
                            log.warn("数据项不存在，ID: {}", dataItemId);
                            continue;
                        }

                        // 通过数据项的queryBusinessCode查询对应的数据源
                        List<InsightDataSource> dataSourceList = insightDataSourceMapper
                                .selectBySourceCode(dataItem.getQueryBusinessCode());
                        if (dataSourceList.isEmpty()) {
                            log.warn("数据源不存在，sourceCode: {}", dataItem.getQueryBusinessCode());
                            continue;
                        }

                        InsightDimensionRefItemRel rel = new InsightDimensionRefItemRel();
                        rel.setDimensionConfigId(configId);
                        rel.setRefDataItemId(dataItemId);
                        rel.setQueryValue(dataSource.getQueryValue());
                        // 确保 groupCode 不为空，设置默认值
                        String groupCode = dataSource.getGroupCode();
                        if (groupCode == null || groupCode.trim().isEmpty()) {
                            groupCode = "DEFAULT";
                            log.warn("数据源的groupCode为空，使用默认值: DEFAULT, dataItemId: {}", dataItemId);
                        }
                        rel.setGroupCode(groupCode);
                        rel.setSortOrder(sortOrder++);
                        rel.setStatus("1");
                        Timestamp now = getCurrentTimestamp();
                        rel.setCreatorId(userInfo.userId());
                        rel.setCreatorName(userInfo.userName());
                        rel.setCreateTime(now);
                        rel.setModifyierId(userInfo.userId());
                        rel.setModifyierName(userInfo.userName());
                        rel.setModifyTime(now);
                        insightDimensionRefItemRelMapper.insert(rel);
                    }
                }
            }
        }
    }

    /**
     * 创建总结配置
     */
    private void createSummaryConfig(InsightSummaryConfigDTO dto, Long agentId, String insightMode) {
        CurrentUserInfo userInfo = getCurrentUserInfo();
        InsightSummaryConfig summaryConfig = saveSummaryConfig(dto, agentId, insightMode, userInfo);
        Long newSummaryConfigId = summaryConfig.getId();

        promptService.saveFragmentPrompt(dto.getComprehensivePrompt(), newSummaryConfigId, DimensionCode.SUMMARY_COMPREHENSIVE.getCode(), DimensionCode.SUMMARY_COMPREHENSIVE.getCode());
        promptService.saveFragmentPrompt(dto.getSummaryAdvicePrompt(), newSummaryConfigId, DimensionCode.SUMMARY_ADVICE.getCode(), DimensionCode.SUMMARY_ADVICE.getCode());
        manageDimensionStandards(newSummaryConfigId, dto.getStandards(), CONFIG_TYPE_SUMMARY);
    }

    @NotNull
    private InsightSummaryConfig saveSummaryConfig(InsightSummaryConfigDTO dto, Long agentId, String insightMode,
            CurrentUserInfo userInfo) {
        InsightSummaryConfig summaryConfig = new InsightSummaryConfig();
        BeanUtils.copyProperties(dto, summaryConfig);
        summaryConfig.setAgentId(agentId);
        summaryConfig.setId(UUIDUtils.getUUID2Long());
        summaryConfig.setInsightMode(insightMode);
        Timestamp now = getCurrentTimestamp();
        summaryConfig.setStatus("1");
        summaryConfig.setCreatorId(userInfo.userId());
        summaryConfig.setCreatorName(userInfo.userName());
        summaryConfig.setCreateTime(now);
        summaryConfig.setModifyierId(userInfo.userId());
        summaryConfig.setModifyierName(userInfo.userName());
        summaryConfig.setModifyTime(now);
        insightSummaryConfigMapper.insert(summaryConfig);
        return summaryConfig;
    }

    private void updateSummaryConfig(InsightSummaryConfigDTO dto, Long configId, String insightMode) {
        CurrentUserInfo userInfo = getCurrentUserInfo();

        // 更新配置
        updateSummary(configId, insightMode, userInfo);

        // 更新标准关联
        insightConfigStandardRelMapper.deleteByConfigIdsAndType(
                Collections.singletonList(configId), CONFIG_TYPE_SUMMARY);
        manageDimensionStandards(configId, dto.getStandards(), CONFIG_TYPE_SUMMARY);

        dimensionPromptValueMapper.deleteByConfigId(configId);
        promptService.saveFragmentPrompt(dto.getComprehensivePrompt(), configId, DimensionCode.SUMMARY_COMPREHENSIVE.getCode(), DimensionCode.SUMMARY_COMPREHENSIVE.getCode());
        promptService.saveFragmentPrompt(dto.getSummaryAdvicePrompt(), configId, DimensionCode.SUMMARY_ADVICE.getCode(), DimensionCode.SUMMARY_ADVICE.getCode());
    }

    private void updateSummary(Long configId, String insightMode, CurrentUserInfo userInfo) {
        InsightSummaryConfig summaryConfig = new InsightSummaryConfig();
        summaryConfig.setId(configId);

        // 设置修改相关字段
        summaryConfig.setModifyierId(userInfo.userId());
        summaryConfig.setModifyierName(userInfo.userName());
        summaryConfig.setModifyTime(getCurrentTimestamp());
        summaryConfig.setInsightMode(insightMode);

        insightSummaryConfigMapper.updateById(summaryConfig);
    }

    private InsightDimensionConfigVO buildDimensionConfigVO(InsightDimensionConfig config) {
        InsightDimensionConfigVO vo = new InsightDimensionConfigVO();
        BeanUtils.copyProperties(config, vo);

        // 查询结构化提示词片段
        MainPromptDTO mainPromptDTO = promptService.getMainPromptValueByDimensionCodeAndType(config.getId(), config.getDimensionCode(), "DIMENSION");
        if (mainPromptDTO != null && mainPromptDTO.getFragments() != null) {
            vo.setInterpretationPromptFragments(mainPromptDTO.getFragments());
        }
        JSONObject interpretationPrompt = new JSONObject();
        if (mainPromptDTO != null && mainPromptDTO.getFragments() != null) {
            mainPromptDTO.getFragments().forEach(fragment -> {
                interpretationPrompt.put(fragment.getFragmentKey(),
                        StringUtil.isEmpty(fragment.getFragmentValue()) ? "" : fragment.getFragmentValue());
            });
        }
        vo.setInterpretationPrompt(interpretationPrompt);

        // 数据项现在通过数据源挂载，不再直接关联到维度
        // 查询维度关联的标准
        vo.setStandards(getStandardsForConfig(config.getId(), CONFIG_TYPE_DIMENSION));

        return vo;
    }

    private InsightSummaryConfigVO buildSummaryConfigVO(InsightSummaryConfig config) {
        InsightSummaryConfigVO vo = new InsightSummaryConfigVO();
        BeanUtils.copyProperties(config, vo);

        // 查询综合分析结构化提示词片段
        getSummaryPrompt(config, vo);

        vo.setStandards(getStandardsForConfig(config.getId(), CONFIG_TYPE_SUMMARY));
        return vo;
    }

    private void getSummaryPrompt(InsightSummaryConfig config, InsightSummaryConfigVO vo) {
        MainPromptDTO comprehensivePromptDTO = promptService.getMainPromptValueByDimensionCodeAndType(
                config.getId(), DimensionCode.SUMMARY_COMPREHENSIVE.getCode(), DimensionCode.SUMMARY_COMPREHENSIVE.getCode());
        if (comprehensivePromptDTO != null && comprehensivePromptDTO.getFragments() != null) {
            vo.setComprehensivePromptFragments(comprehensivePromptDTO.getFragments());
            JSONObject comprehensivePrompt = new JSONObject();
            comprehensivePromptDTO.getFragments().forEach(fragment -> {
                comprehensivePrompt.put(fragment.getFragmentKey(),
                        StringUtil.isEmpty(fragment.getFragmentValue()) ? "" : fragment.getFragmentValue());
            });
            vo.setComprehensivePrompt(comprehensivePrompt);
        }


        // 查询建议结构化提示词片段
        MainPromptDTO advicePromptDTO = promptService.getMainPromptValueByDimensionCodeAndType(
                config.getId(), DimensionCode.SUMMARY_ADVICE.getCode(), DimensionCode.SUMMARY_ADVICE.getCode());
        if (advicePromptDTO != null && advicePromptDTO.getFragments() != null) {
            vo.setSummaryAdvicePromptFragments(advicePromptDTO.getFragments());
            JSONObject advicePrompt = new JSONObject();
            advicePromptDTO.getFragments().forEach(fragment -> {
                advicePrompt.put(fragment.getFragmentKey(),
                        StringUtil.isEmpty(fragment.getFragmentValue()) ? "" : fragment.getFragmentValue());
            });
            vo.setSummaryAdvicePrompt(advicePrompt);
        }
    }

    private List<InsightStandard> getStandardsForConfig(Long configId, String configType) {
        List<InsightConfigStandardRel> relations = insightConfigStandardRelMapper.selectByConfigIdAndType(configId,
                configType);
        List<Long> standardIds = relations.stream()
                .map(InsightConfigStandardRel::getStandardId)
                .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(standardIds)) {
            return insightStandardMapper.selectBatchIds(standardIds);
        }
        return Collections.emptyList();
    }

    /**
     * 根据数据项推导维度编码
     */
    private String deriveDimensionCodeFromDataItems(List<InsightDataSourceDTO> dataSources) {

        // 获取第一个数据项，通过其queryBusinessCode推导维度编码
        for (InsightDataSourceDTO dataSource : dataSources) {
            if (dataSource.getSelectedDataItemIds() != null && !dataSource.getSelectedDataItemIds().isEmpty()) {
                Long firstDataItemId = dataSource.getSelectedDataItemIds().get(0);
                InsightDataItem dataItem = insightDataItemMapper.selectById(firstDataItemId);
                if (dataItem != null && dataItem.getQueryBusinessCode() != null) {
                    // 根据queryBusinessCode推导维度编码
                    return deriveDimensionCodeFromQueryBusinessCode(dataItem.getQueryBusinessCode());
                }
            }
        }

        return null;
    }

    /**
     * 根据queryBusinessCode推导维度编码
     */
    private String deriveDimensionCodeFromQueryBusinessCode(String queryBusinessCode) {
        // 使用枚举类的方法推导维度编码
        return Optional.ofNullable(queryBusinessCode)
                .map(DimensionCode::fromQueryBusinessCode)
                .map(DimensionCode::getCode)
                .orElse(null);
    }

    /**
     * 根据维度编码生成维度名称
     */
    private String deriveDimensionNameFromCode(String dimensionCode) {

        return Optional.ofNullable(dimensionCode)
                .map(DimensionCode::fromCode)
                .map(DimensionCode::getName)
                .orElse(null);
    }

    @Override
    public InsightConfigWorkspaceVO.AvailableOptions getAvailableOptions() {
        InsightConfigWorkspaceVO.AvailableOptions options = new InsightConfigWorkspaceVO.AvailableOptions();
        // 按维度分组数据源
        List<InsightDataSource> allDataSources = insightDataSourceMapper.selectAll();
        Map<String, List<InsightDataSource>> dataSourcesByDimension = allDataSources.stream()
                .collect(Collectors.groupingBy(InsightDataSource::getBelongDimensionCode));
        // 构建维度下的数据源结构
        List<InsightDimensionConfigVO> availableDimensionVOs = new ArrayList<>();
        for (Map.Entry<String, List<InsightDataSource>> entry : dataSourcesByDimension.entrySet()) {
            String dimensionCode = entry.getKey();
            List<InsightDataSource> dataSources = entry.getValue();
            InsightDimensionConfigVO dimensionVO = new InsightDimensionConfigVO();
            dimensionVO.setDimensionCode(dimensionCode);
            dimensionVO.setDimensionName(dataSources.get(0).getBelongDimensionName());
            List<InsightDataSourceVO> dataSourceVOs = dataSources.stream().map(dataSource -> {
                InsightDataSourceVO dsVO = new InsightDataSourceVO();
                BeanUtils.copyProperties(dataSource, dsVO);

                // 设置数据项
                List<InsightDataItem> items = insightDataItemMapper
                        .selectByQueryBusinessCode(dataSource.getSourceCode());
                List<InsightDataItem> treeItems = InsightDataItemTreeUtils.buildTree(items);
                Map<String, List<InsightDataItem>> itemsByTypeMap = treeItems.stream()
                        .collect(Collectors.groupingBy(InsightDataItem::getDataTypeCode));
                List<InsightDataItemGroup> itemsByType = itemsByTypeMap.entrySet().stream()
                        .map(mapEntry -> {
                            List<InsightDataItem> copiedItems = mapEntry.getValue().stream()
                                    .map(item -> {
                                        InsightDataItem copiedItem = new InsightDataItem();
                                        BeanUtils.copyProperties(item, copiedItem);
                                        return copiedItem;
                                    })
                                    .collect(Collectors.toList());
                            return new InsightDataItemGroup(mapEntry.getKey(), null, copiedItems);
                        })
                        .collect(Collectors.toList());
                dsVO.setDataItemsByType(itemsByType);

                // 设置参数分组配置
                List<SourceParamGroupVO> paramGroups = buildSourceParamGroups(dataSource.getId());
                dsVO.setParamGroups(paramGroups);

                return dsVO;
            }).collect(Collectors.toList());
            // 获取主提示词和提示词片段
            MainPromptDTO mainPromptDTO = promptService.getMainPromptByDimensionCodeAndType(dimensionCode, "DIMENSION");
            if (mainPromptDTO != null && mainPromptDTO.getFragments() != null) {
                dimensionVO.setInterpretationPromptFragments(mainPromptDTO.getFragments());
            }

            dimensionVO.setDataSources(dataSourceVOs);
            availableDimensionVOs.add(dimensionVO);
        }
        options.setDimensions(availableDimensionVOs);

        // 添加总结配置的可用选项
        InsightSummaryConfigVO summaryTemplate = new InsightSummaryConfigVO();

        // 查询综合分析结构化提示词片段
        MainPromptDTO comprehensivePromptDTO = promptService
                .getMainPromptByDimensionCodeAndType(DimensionCode.SUMMARY_COMPREHENSIVE.getCode(), DimensionCode.SUMMARY_COMPREHENSIVE.getCode());
        if (comprehensivePromptDTO != null && comprehensivePromptDTO.getFragments() != null) {
            summaryTemplate.setComprehensivePromptFragments(comprehensivePromptDTO.getFragments());
        }

        // 查询建议结构化提示词片段
        MainPromptDTO advicePromptDTO = promptService.getMainPromptByDimensionCodeAndType(DimensionCode.SUMMARY_ADVICE.getCode(),
                DimensionCode.SUMMARY_ADVICE.getCode());
        if (advicePromptDTO != null && advicePromptDTO.getFragments() != null) {
            summaryTemplate.setSummaryAdvicePromptFragments(advicePromptDTO.getFragments());
        }

        options.setSummaryTemplate(summaryTemplate);
        return options;
    }
}
