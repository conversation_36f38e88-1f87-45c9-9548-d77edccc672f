package com.qc.agent.app.agent.model.dto;

import com.qc.agent.vectordb.pojo.SearchContent;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-25
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class KnowledgeProcessResult {

    /**
     * 匹配到qa答案/参数【仅搜索知识库】开启且知识库没有匹配到内容时，直接返回，不走大模型
     */
    private Boolean directReturn;

    /**
     * 匹配到的知识库的内容
     */
    private String knowledgeContent;

    private List<SearchContent> searchContentList;

}
