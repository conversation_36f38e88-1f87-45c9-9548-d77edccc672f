package com.qc.agent.app.agent.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.qc.agent.app.agent.mapper.QcAiAgentMapper;
import com.qc.agent.app.agent.mapper.QcAiAgentModelMapper;
import com.qc.agent.app.agent.model.po.CredentialRequest;
import com.qc.agent.app.agent.model.po.QcAiAgentModel;
import com.qc.agent.app.agent.model.vo.QcAiAgentModelVO;
import com.qc.agent.app.agent.service.QcAiAgentModelService;
import com.qc.agent.app.workflow.WorkflowLLAClient;
import com.qc.agent.common.core.Message;
import com.qc.agent.common.util.UUIDUtils;
import com.qc.agent.lla.exception.LLAInvokerRequestException;
import com.qc.agent.lla.model.*;
import com.qc.agent.platform.register.UserManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

@Service
@Slf4j
public class QcAiAgentModelServiceImpl implements QcAiAgentModelService {

    @Resource
    private QcAiAgentModelMapper qcAiAgentModelMapper;

    @Resource
    private QcAiAgentMapper qcAiAgentMapper;

    @Override
    public int add(QcAiAgentModel model) {
        return qcAiAgentModelMapper.insert(model);
    }

    @Override
    public int delete(Long id) {
        return qcAiAgentModelMapper.deleteById(id);
    }

    @Override
    public Message update(QcAiAgentModel model) {
        if (model.getId() == null) {
            return Message.of().error("id不能为空");
        }
        model.setModifyierId(UserManager.getTenantUser().getUserId());
        model.setModifyierName(UserManager.getTenantUser().getUserName());
        qcAiAgentModelMapper.update(model);
        return Message.of().ok();
    }

    @Override
    public QcAiAgentModel getById(Long id) {
        return qcAiAgentModelMapper.selectById(id);
    }

    @Override
    public JSONObject list() {
        JSONObject result = new JSONObject();
        List<QcAiAgentModelVO> publicModelLibrary = qcAiAgentModelMapper.selectAll();
        List<QcAiAgentModelVO> privateModels = qcAiAgentModelMapper.selectPrivateModels();


        publicModelLibrary.stream().forEach(model -> {
            if (StringUtils.isNotEmpty(model.getModelName())) {
                model.setModelList(Arrays.stream(model.getModelName().split(",")).toList());
            } else {
                model.setModelList(new ArrayList<>());
            }
        });
        privateModels.stream().forEach(model -> {
            model.setModelList(Arrays.stream(new String[]{model.getModelName()}).toList());
        });

        List<QcAiAgentModelVO> myModels = new ArrayList<>();

        publicModelLibrary.stream().filter(QcAiAgentModelVO::checkIsMyModel).forEach(myModels::add);
        privateModels.stream().filter(QcAiAgentModelVO::checkIsMyModel).forEach(myModels::add);
        publicModelLibrary.removeAll(myModels);
        privateModels.removeAll(myModels);
        publicModelLibrary.addAll(privateModels);

        result.put("myModels", myModels);
        result.put("modelLibrary", publicModelLibrary);


        return result;
    }


    @Override
    public Message remove(QcAiAgentModel model) {
        int count = qcAiAgentModelMapper.remove(model);
        return Message.of().data(count).ok();
    }

    @Override
    public List<QcAiAgentModel> selectEnabledLLM() {
        return qcAiAgentModelMapper.selectEnabledLLM();
    }

    @Override
    public Message updateModelCredentials(CredentialRequest request) {
        if (LLAProvider.DOUBAO.getLLA().equals(request.getVendor())) {
            return loopCheckModelCredentials(request);
        }

        QcAiAgentModel qcAiAgentModel = qcAiAgentModelMapper.selectByVendor(request.getVendor());
        if (qcAiAgentModel == null) {
            return Message.of().error("当前供应商未维护模型数据，无法校验");
        }
        qcAiAgentModel.setKey(request.getApiKey());
        qcAiAgentModel.setSecret(request.getApiSecret());
        qcAiAgentModel.setAipApplicationId(request.getAipApplicationId());
        qcAiAgentModel.setAipUserId(request.getAipUserId());
        qcAiAgentModel.setModelEndPoint(request.getEndpoint());
        String question = String.format("背景：%s。 问题：%s", "密钥检验", "密钥是否有效");
        try {
            WorkflowLLAClient.send(initLLAConfig(qcAiAgentModel), initLLARequest(UUIDUtils.getUUID2Long(), question));
        } catch (LLAInvokerRequestException e) {
            return Message.of().error("模型秘钥不正确或余额不足，请输入有效秘钥。");
        }

        int count = qcAiAgentModelMapper.updateModelCredentials(request);
        return Message.of().ok().data(count);
    }

    private Message loopCheckModelCredentials(CredentialRequest request) {
        List<QcAiAgentModel> qcAiAgentModels = qcAiAgentModelMapper.selectModelsByVendor(request.getVendor());
        if (qcAiAgentModels == null || qcAiAgentModels.isEmpty()) {
            return Message.of().error("当前供应商未维护模型数据，无法校验");
        }
        for (int i = 0; i < qcAiAgentModels.size(); i++) {
            QcAiAgentModel qcAiAgentModel = qcAiAgentModels.get(i);
            qcAiAgentModel.setKey(request.getApiKey());
            qcAiAgentModel.setSecret(request.getApiSecret());
            String question = String.format("背景：%s。 问题：%s", "密钥检验", "密钥是否有效");
            try {
                WorkflowLLAClient.send(initLLAConfig(qcAiAgentModel), initLLARequest(UUIDUtils.getUUID2Long(), question));
                int count = qcAiAgentModelMapper.updateModelCredentials(request);
                return Message.of().ok().data(count);
            } catch (LLAInvokerRequestException e) {
                log.error("当前模型无法校验密钥", e);
            }
        }
        return Message.of().error("模型秘钥不正确，请输入有效秘钥。");
    }

    @Override
    public Message privacyModelRemove(QcAiAgentModel model) {
        qcAiAgentModelMapper.deleteById(model.getId());
        return Message.of().ok();
    }

    @Override
    public Message addPrivacyDeployment(QcAiAgentModel model) {
        model.setCreatorId(UserManager.getTenantUser().getUserId());
        model.setCreatorName(UserManager.getTenantUser().getUserName());
        model.setId(model.getId() == null ? UUIDUtils.getUUID2Long() : Long.valueOf(model.getId()));
        model.setEnable("1");
        model.setCreateTime(LocalDateTime.now());
        model.setVendor("privacy");
        model.setVendorName("私有化部署模型");
        model.setIcon("https://res.waiqin365.com/d/static/agent/privacy.png");
        model.setPrivacyDeployment("1");
        model.setStatus("1");
        model.setModel(model.getModelName());
        qcAiAgentModelMapper.insertOrUpdate(model);
        return Message.of().ok();
    }

    private LLAConfig initLLAConfig(QcAiAgentModel qcAiAgentModel) {
        return LLAConfig.builder()
                .apiKey(qcAiAgentModel.getKey())
                .secretKey(qcAiAgentModel.getSecret())
                .stream(true)
                .provider(LLAProvider.getEnumByValue(qcAiAgentModel.getVendor()))
                .model(qcAiAgentModel.getModel())
                .endpoint(qcAiAgentModel.getModelEndPoint())
                .aipApplicationId(qcAiAgentModel.getAipApplicationId())
                .aipUserId(qcAiAgentModel.getAipUserId())
                .endpoint(qcAiAgentModel.getModelEndPoint())
                .build();
    }

    private LLARequest initLLARequest(Long questionId, String question) {
        return LLARequest.builder()
                .id(Objects.toString(questionId))
                .content(question)
                .build();
    }
}
