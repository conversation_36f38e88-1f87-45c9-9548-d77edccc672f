package com.qc.agent.app.question.impl.coze;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.net.HttpHeaders;
import com.qc.agent.app.question.impl.AbstrctAnswerAIRequest;
import com.qc.agent.platform.sse.AnswerAISubscriber;
import com.qc.agent.platform.sse.SseMessageConverter;
import com.qc.agent.platform.sse.SseRequest;
import com.qc.agent.platform.sse.dto.AIVendors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.util.List;

/**
 * @description：TODO
 * @author： sc
 * @create： 2024/5/31 8:41
 */
@Component
@Slf4j
public class CozeChatbotImpl  extends AbstrctAnswerAIRequest {

    @Value("${ai.agent.coze-token}")
    private String token;

    @Value("${ai.agent.coze-botid}")
    private String botId;

    @Override
    protected Object buildAnswer(String defaultAnswer) {
        String content;
        JSONObject data = new JSONObject();
        data.put("event", "message");
        if(StringUtils.isNotEmpty(defaultAnswer)){
            JSONObject message = new JSONObject();
            message.put("role", "assistant");
            message.put("type", "answer");
            message.put("content", defaultAnswer);
            message.put("content_type", "text");
            data.put("message", message);
            content = data.toJSONString();
        }else{
            data.put("event", "done");
            content = data.toJSONString();
        }
        return String.format("data:%s", content);
    }

    @Override
    protected void doAnswer(SseRequest request, AnswerAISubscriber subscriber) {
        log.info("做coze问答,token:{}, botId:{}", token, botId);

        JSONObject param = new JSONObject();

        JSONArray jsonArray = buildHisMessages(request);

        JSONObject queryObj = null;
        if(CollectionUtils.isNotEmpty(jsonArray) && jsonArray.size() > 1){
            queryObj = (JSONObject) jsonArray.remove(jsonArray.size() - 1);
            param.put("chat_history", jsonArray);
        }
        param.put("bot_id", botId);
        param.put("user", "勤策");
        if(queryObj != null){
            param.put("query", queryObj.getString("content"));
        }else{
            param.put("query", request.getQuestion());
        }

        param.put("stream", true);

        log.info("调用问答接口，request：{}", param);

        HttpRequest httpRequest = HttpRequest.newBuilder()
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + token)
                .header("Content-Type", "application/json")
                .uri(URI.create("https://api.coze.cn/open_api/v2/chat"))
                .POST(HttpRequest.BodyPublishers.ofString(param.toJSONString()))
                .build();
        try
        {
            request.setStartTime(System.currentTimeMillis());
            HttpClient.newBuilder().build().sendAsync(httpRequest, HttpResponse.BodyHandlers.fromLineSubscriber(subscriber)).get();
        }
        catch(Exception e)
        {
            log.error("调用问答接口异常", e);
            throw new RuntimeException(e);
        }
    }

    private JSONArray buildHisMessages(SseRequest request){
        List list = buildMessage(request);
        if(CollectionUtils.isNotEmpty(list)){
            JSONArray jsonArray = JSONArray.parseArray(JSON.toJSONString(list));
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                if("user".equals(jsonObject.getString("role"))){
                    jsonObject.put("content_type", "text");
                }else if("assistant".equals(jsonObject.getString("role"))){
                    jsonObject.put("type", "answer");
                    jsonObject.put("content_type", "text");
                }
            }
            return jsonArray;
        }
        return new JSONArray();
    }

    @Override
    public AIVendors determineAIVendors() {
        return AIVendors.COZE;
    }

    @Override
    public SseMessageConverter determineMessageConverter() {
        return new CozeMessageConverter();
    }



}
