package com.qc.agent.app.agent.mapper;

import com.qc.agent.app.agent.model.po.AgentGoodsRecommend;
import com.qc.agent.app.agent.model.po.UnsoldGood;
import com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.model.domain.Product;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

public interface AgentGoodsRecommendMapper {
    /**
     * 插入或更新记录
     *
     * @param entity 实体对象
     */
    void upsert(AgentGoodsRecommend entity);

    /**
     * 根据门店ID和当天日期查询记录
     *
     * @param storeId 门店ID
     * @return 实体对象
     */
    AgentGoodsRecommend findByStoreIdAndDate(Long storeId);

    void deleteUnsoldByStoreId(Long storeId);

    void batchInsertOrUpdateUnsold(List<Product> goodsList);

    Set<Long> selectUnsoldIdsByStore(Long storeId);

    void batchDeleteStoreUnsoldRel(@Param("goodsIds")List<Long> goodsIds, @Param("storeId") Long storeId);
    void batchInsertStoreUnsoldRel(@Param("goodsIds")List<Long> goodsIds, @Param("storeId") Long storeId);
}