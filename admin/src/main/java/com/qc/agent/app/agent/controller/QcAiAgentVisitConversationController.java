package com.qc.agent.app.agent.controller;

import com.qc.agent.app.agent.model.dto.QcAiAgentChatDTO;
import com.qc.agent.app.agent.service.QcAiAgentVisitConversationBiz;
import com.qc.agent.common.core.Message;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025-04-09
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@RestController
@RequestMapping("/ai-agent/conversation/visit/")
public class QcAiAgentVisitConversationController {

    @Resource
    private QcAiAgentVisitConversationBiz qcAiAgentVisitConversationBiz;

    /**
     * 会话
     *
     * @return
     */
    @PostMapping("/chat")
    public Message chat(@RequestBody QcAiAgentChatDTO qcAiAgentChatDTO) {
        return Message.of().ok().data(qcAiAgentVisitConversationBiz.chat(qcAiAgentChatDTO));
    }


    /**
     * 查询未拜访客户记录
     *
     * @return
     */
    @PostMapping("/customer_visit_list")
    public Message customerVisitList(@RequestBody QcAiAgentChatDTO qcAiAgentChatDTO) {
        return Message.of().ok().data(qcAiAgentVisitConversationBiz.customerVisitList(qcAiAgentChatDTO));
    }

    /**
     * 添加至拜访计划
     *
     * @return
     */
    @PostMapping("/add_visit_plan")
    public Message addVisitPlan(@RequestBody QcAiAgentChatDTO qcAiAgentChatDTO) {
        return qcAiAgentVisitConversationBiz.addVisitPlan(qcAiAgentChatDTO);
    }
}
