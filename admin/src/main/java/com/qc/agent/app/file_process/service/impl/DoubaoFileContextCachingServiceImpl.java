package com.qc.agent.app.file_process.service.impl;

import com.qc.agent.app.file_process.pojo.FileContextCachingParam;
import com.qc.agent.app.file_process.pojo.QcAiParseFileResult;
import com.qc.agent.app.file_process.service.FileContextCachingService;
import com.qc.agent.lla.model.LLAMessage;
import com.qc.agent.lla.model.LLAProvider;
import com.qc.agent.lla.sdk.factory.AiClientFactory;
import com.qc.agent.lla.sdk.model.ModelParam;
import com.qc.agent.lla.sdk.model.context_cache.ContextCacheParam;
import com.qc.agent.lla.sdk.model.context_cache.ContextCacheResult;
import com.qc.agent.redis.RedisClient;
import com.qc.agent.utils.QcAiBusinessRedisKeyConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.Duration;
import java.util.List;

/**
 * Doubao（豆包）大模型文件上下文缓存服务实现。
 *
 * <AUTHOR>
 * @date 2025-06-16
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Slf4j
@Service("doubaoFileContextCachingServiceImpl")
@RequiredArgsConstructor
public class DoubaoFileContextCachingServiceImpl implements FileContextCachingService {

    // --- Constants for better maintainability ---
    private static final String VENDOR_DOUBAO = "Doubao";
    private static final String SUPPORTED_MODEL = "deepseek-v3-250324";
    private static final String CACHE_MODE_SESSION = "session";
    private static final Duration CONTEXT_CACHE_TTL = Duration.ofHours(1);
    public static final String REDIS_KEY_TEMPLATE = QcAiBusinessRedisKeyConstants.MODEL_CACHE_DOUBAO + "context-id:%s";


    /**
     * 注意: 此服务复用了 Kimi 服务中的文件处理逻辑。
     * 从设计角度看，更优的方案是将其抽取到一个独立的、可共享的组件中。
     */
    private final KimiFileContextCachingService kimiFileContextCachingService;

    private final RedisClient redisClient;


    @Override
    public QcAiParseFileResult getCacheInfo(FileContextCachingParam param) throws IOException {
        final var logPrefix = param.getLogPrefix();
        final var intentId = param.getIntentId();
        log.info("{} 开始处理文件上下文缓存流程, intentId: {}", logPrefix, intentId);

        // 1.检查当前请求的模型是否支持并配置了上下文缓存
        if (isDoubaoContextCacheSupported(param)) {
            log.info("{} 模型 '{}' (厂商: {}) 支持上下文缓存, 开始处理缓存逻辑",
                    logPrefix, param.getModel(), param.getVendor());
            return getOrCreateDoubaoContext(param);
        }

        // 2. 如果模型不支持，则直接返回处理好的文件消息列表，由后续流程处理
        log.warn("{} 模型 '{}' (厂商: {}) 在当前实现中未配置上下文缓存, 将直接返回文件内容",
                logPrefix, param.getModel(), param.getVendor());
        // . 将上传的文件转换为大模型可识别的消息格式
        final List<LLAMessage> messages = kimiFileContextCachingService.processAndCacheFiles(param, null);
        return new QcAiParseFileResult().setCacheInfoList(messages);
    }

    /**
     * 检查模型是否为支持上下文缓存的豆包模型。
     */
    private boolean isDoubaoContextCacheSupported(FileContextCachingParam param) {
        return VENDOR_DOUBAO.equals(param.getVendor()) && SUPPORTED_MODEL.equals(param.getModel());
    }

    /**
     * 获取或创建豆包模型的上下文缓存。
     *
     * @param param    请求参数
     * @return 包含 contextId 的解析结果
     */
    private QcAiParseFileResult getOrCreateDoubaoContext(FileContextCachingParam param) {
        final var logPrefix = param.getLogPrefix();
        final var intentId = param.getIntentId();
        final var redisKey = String.format(REDIS_KEY_TEMPLATE, intentId);

        // 尝试从 Redis 获取已存在的 contextId
        String cachedContextId = "";
        if (ObjectUtils.isNotEmpty(redisClient.get(redisKey))) {
            cachedContextId = redisClient.get(redisKey).toString();
        }
        if (StringUtils.isNotEmpty(cachedContextId)) {
            log.info("{} 缓存命中: 在 Redis 中找到已存在的豆包 contextId: '{}', intentId: {}",
                    logPrefix, cachedContextId, intentId);
            return new QcAiParseFileResult().setContextId(cachedContextId);
        }

        // 如果 Redis 中不存在，则调用 API 创建新的上下文
        log.info("{} 缓存未命中: 未找到豆包 contextId, intentId: {}. 准备创建新的上下文", logPrefix, intentId);
        return createNewDoubaoContext(param, redisKey);
    }

    /**
     * 调用 AI 客户端创建新的豆包上下文，并将其 ID 存入 Redis。
     *
     * @param param       请求参数
     * @param redisKey 用于存储新 contextId 的 Redisson key
     * @return 包含新 contextId 的解析结果
     */
    private QcAiParseFileResult createNewDoubaoContext(FileContextCachingParam param, String redisKey) {
        final var logPrefix = param.getLogPrefix();
        try {
            final List<LLAMessage> messages = kimiFileContextCachingService.processAndCacheFiles(param, null);
            var contextCacheParam = ContextCacheParam.build()
                    .withModel(param.getModelEndPoint())
                    .withMessages(messages)
                    .withMode(CACHE_MODE_SESSION)
                    .withTtl((int) CONTEXT_CACHE_TTL.toSeconds());

            var modelParam = ModelParam.build()
                    .withApiKey(param.getApiKey())
                    .withModel(param.getModel()) // 使用请求中的模型名
                    .withCacheParam(contextCacheParam);

            log.info("{} 正在调用 AI 客户端创建豆包上下文缓存, Endpoint: {}", logPrefix, param.getModelEndPoint());
            ContextCacheResult contextResult = AiClientFactory.getClient(LLAProvider.DOUBAO).createContext(modelParam);
            String newContextId = contextResult.getId();
            log.info("{} 成功创建新的豆包 contextId: '{}'. 正在将其存入 Redis...", logPrefix, newContextId);

            // 将新的 contextId 存入 Redis，并设置过期时间
            redisClient.set(redisKey, newContextId, 55 * 60);
            return new QcAiParseFileResult().setContextId(newContextId);
        } catch (Exception e) {
            // 对外部调用添加异常捕获和日志记录，增强系统稳定性
            log.error("{} 调用 AI 客户端创建豆包上下文缓存失败, intentId: {}", logPrefix, param.getIntentId(), e);
            // 抛出运行时异常，以便全局异常处理器捕获
            throw new RuntimeException(e.getMessage(), e);
        }
    }
}