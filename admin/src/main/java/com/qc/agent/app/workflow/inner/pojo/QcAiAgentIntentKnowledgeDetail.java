package com.qc.agent.app.workflow.inner.pojo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 勤策ai agent意图识别关联的知识库表
 *
 * <AUTHOR>
 * @date 2025-05-27
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Data
public class QcAiAgentIntentKnowledgeDetail {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 状态
     */
    private String status;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改人ID
     */
    private Long modifyierId;

    /**
     * 修改人名称
     */
    private String modifyierName;

    /**
     * 修改时间
     */
    private LocalDateTime modifyTime;

    /**
     * agent_id - qc_ai_agent的id
     */
    private Long agentId;

    /**
     * 意图id - qc_ai_agent_Intent_recognition的id
     */
    private Long intentId;

    /**
     * 知识库id - qc_knowledge_info的id
     */
    private Long collectionId;
}
