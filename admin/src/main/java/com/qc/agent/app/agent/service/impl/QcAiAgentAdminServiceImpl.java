package com.qc.agent.app.agent.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.qc.agent.app.agent.mapper.QcAiAgentAdminMapper;
import com.qc.agent.app.agent.model.po.QcAiAgentAdminChart;
import com.qc.agent.app.agent.model.query.QcAiAgentAdminQuery;
import com.qc.agent.app.agent.model.vo.AdminChartData;
import com.qc.agent.app.agent.model.vo.KnowledgeStats;
import com.qc.agent.app.agent.service.QcAiAgentAdminService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.List;

/**
 * Service for handling AI Agent administrative chart data.
 *
 * <AUTHOR>
 * @date 2025-03-07
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 *                      <p>
 *                      Refactored for Java 17, improved structure, and clarity.
 */
@Slf4j
@Service
public class QcAiAgentAdminServiceImpl implements QcAiAgentAdminService {

    @Resource
    private QcAiAgentAdminMapper qcAiAgentAdminMapper;

    // A constant formatter is more efficient and thread-safe.
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    // A constant decimal format is also efficient and thread-safe.
    private static final DecimalFormat RATIO_FORMATTER = new DecimalFormat("0.00");

    /**
     * A simple record to hold the date range calculation result.
     */
    private record DateRange(LocalDate start, LocalDate end, long daysBetween) {
    }

    /**
     * Gathers and computes statistics for the agent admin chart.
     * This method now orchestrates calls to smaller, focused helper methods.
     *
     * @param query The query parameters containing agentId, startDate, and endDate.
     * @return An {@link AdminChartData} object containing all computed statistics.
     */
    @Override
    public AdminChartData chart(QcAiAgentAdminQuery query) {
        log.info("Starting chart data analysis for query: {}", JSONObject.toJSONString(query));

        // 1. Calculate date range
        DateRange dateRange = calculateDateRange(query.getStartDate(), query.getEndDate());

        // 2. Fetch raw chart data from the database
        List<QcAiAgentAdminChart> dialogueList = qcAiAgentAdminMapper.selectDialogueChart(query);
        List<QcAiAgentAdminChart> activeUserList = qcAiAgentAdminMapper.selectActiveUserChart(query);
        List<QcAiAgentAdminChart> tokenList = qcAiAgentAdminMapper.selectTokenChart(query);
        List<QcAiAgentAdminChart> averageDialogueList = qcAiAgentAdminMapper.selectAverageDialogueChart(query);

        // 3. Calculate totals and aggregates
        long dialogueTotal = calculateSumFromChart(dialogueList);
        long tokenTotal = calculateSumFromChart(tokenList);
        Long activeUserTotal = qcAiAgentAdminMapper.selectDistinctUserCount(query);
        Double averageTotal = averageDialogueList.stream().mapToDouble(QcAiAgentAdminChart::getAverageCount).sum();
        String averageDialogue = calculateAverage(averageTotal, dateRange.daysBetween);

        // 4. Process knowledge-specific stats in a dedicated method
        KnowledgeStats knowledgeStats = calculateKnowledgeStats(query);

        log.debug("Calculated Totals: Dialogues={}, ActiveUsers={}, Tokens={}, AverageDialogues={}",
                dialogueTotal, activeUserTotal, tokenTotal, averageDialogue);

        // 5. Assemble the final response object
        AdminChartData result = new AdminChartData(
                dialogueList,
                dialogueTotal,
                activeUserList,
                activeUserTotal,
                tokenList,
                tokenTotal,
                averageDialogueList,
                averageDialogue,
                knowledgeStats);

        log.info("Successfully completed chart data analysis for agentId: {}", query.getAgentId());
        return result;
    }

    /**
     * Parses date strings and calculates the number of days in the range.
     *
     * @param startDateStr The start date string in 'yyyy-MM-dd' format.
     * @param endDateStr   The end date string in 'yyyy-MM-dd' format.
     * @return A {@link DateRange} record containing parsed dates and the day count.
     */
    private DateRange calculateDateRange(String startDateStr, String endDateStr) {
        LocalDate startDate = LocalDate.parse(startDateStr, DATE_FORMATTER);
        LocalDate endDate = LocalDate.parse(endDateStr, DATE_FORMATTER);
        // ChronoUnit.DAYS.between is exclusive of the end date, so add 1 for an
        // inclusive range.
        long daysBetween = ChronoUnit.DAYS.between(startDate, endDate) + 1;
        log.debug("Calculated date range: {} to {}, totaling {} days.", startDate, endDate, daysBetween);
        return new DateRange(startDate, endDate, daysBetween);
    }

    /**
     * Calculates statistics related to the knowledge base.
     *
     * @param query The query parameters.
     * @return A {@link KnowledgeStats} object.
     */
    private KnowledgeStats calculateKnowledgeStats(QcAiAgentAdminQuery query) {
        List<QcAiAgentAdminChart> knowledgeQaList = qcAiAgentAdminMapper.selectKnowledgeChart(query);
        if (CollectionUtils.isEmpty(knowledgeQaList)) {
            log.warn("No knowledge chart data found for query: {}", JSONObject.toJSONString(query));
            return new KnowledgeStats(List.of(), 0, 0, 0, "0.00");
        }

        long totalCount = knowledgeQaList.stream().mapToLong(QcAiAgentAdminChart::getTotalCount).sum();
        long kbAnswerCount = knowledgeQaList.stream().mapToLong(QcAiAgentAdminChart::getKbAnswerCount).sum();
        long dissatisfiedCount = knowledgeQaList.stream().mapToLong(QcAiAgentAdminChart::getDissatisfiedCount).sum();
        long outOfScopeCount = totalCount - kbAnswerCount;

        String solvedRatio = "0.00";
        if (totalCount > 0) {
            double ratio = (double) kbAnswerCount * 100 / totalCount;
            solvedRatio = RATIO_FORMATTER.format(ratio);
        }

        log.debug("Knowledge Stats: Total={}, KBAnswered={}, Dissatisfied={}, OutOfScope={}, SolvedRatio={}",
                totalCount, kbAnswerCount, dissatisfiedCount, outOfScopeCount, solvedRatio);

        return new KnowledgeStats(knowledgeQaList, kbAnswerCount, dissatisfiedCount, outOfScopeCount, solvedRatio);
    }

    /**
     * Calculates the sum of the 'count' field from a list of chart data.
     *
     * @param chartData List of {@link QcAiAgentAdminChart}.
     * @return The sum of all counts.
     */
    private long calculateSumFromChart(List<QcAiAgentAdminChart> chartData) {
        if (CollectionUtils.isEmpty(chartData)) {
            return 0;
        }
        return chartData.stream().mapToLong(QcAiAgentAdminChart::getCount).sum();
    }

    /**
     * Calculates the average value over a period of days.
     *
     * @param total The total amount.
     * @param days  The number of days.
     * @return A formatted string representing the average, e.g., "123.45".
     */
    private String calculateAverage(Double total, long days) {
        if (days <= 0 || total == 0) {
            return "0.00";
        }
        double average = total / days;
        // String.format is a clean way to format to two decimal places.
        return String.format("%.2f", average);
    }
}