package com.qc.agent.app.workflow.inner.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.qc.agent.app.agent.mapper.QcAiAgentConversationMapper;
import com.qc.agent.app.agent.mapper.QcAiAgentVisitDetailMapper;
import com.qc.agent.app.agent.model.dto.VisitAgentParamDTO;
import com.qc.agent.app.agent.model.po.QcAiAgent;
import com.qc.agent.app.agent.model.po.QcAiAgentVisitDetail;
import com.qc.agent.app.agent.service.impl.QcAiAgentVisitConversationBizImpl;
import com.qc.agent.app.agent.support.AgentBizDataSupport;
import com.qc.agent.app.workflow.WorkflowLLAClient;
import com.qc.agent.app.workflow.dataUtils;
import com.qc.agent.app.workflow.inner.AgentType;
import com.qc.agent.app.workflow.inner.impl.tools.LLMTools;
import com.qc.agent.app.workflow.inner.pojo.VisitInfo;
import com.qc.agent.common.core.Message;
import com.qc.agent.common.core.RequestHolder;
import com.qc.agent.lla.model.LLAConfig;
import com.qc.agent.lla.model.LLARequest;
import com.qc.agent.platform.register.UserManager;
import com.qc.agent.platform.sse.SseClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.util.Comparator;
import java.util.List;
import java.util.StringJoiner;
import java.util.stream.Collectors;

@Slf4j
@Component
public class VisitAssistant extends AbstractIAgentInvoker {

    // Constants
    private static final String NO_VISIT_DATA_MSG = "暂无符合推荐规则的客户，您可以尝试重新发起拜访客户推荐并调整推荐的参数。";
    private static final String MISSING_PARAMETER = "缺少拜访参数配置";
    private static final String MODEL_KEY_ERROR = "该AGENT使用的模型的KEY已失效，请联系创建者/管理员调整后重新发布后再使用。";
    private static final String DATE_PARSE_ERROR = "日期解析错误";
    private static final String JSON_PARSE_ERROR = "JSON解析错误";
    private static final String UNNAMED_CLIENT = "未命名客户";
    private static final String QUERY_DATA = "这个问题我收到啦，关于拜访数据的分析我正在学习中，敬请期待~目前我的主要技能是推荐拜访客户哦，如果要推荐拜访客户可以随时找我。";
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final int RECENT_VISIT_DAYS = 180;
    private static final int EXCLUDE_RECENT_DAYS = 7;

    @Resource
    private DefaultAgent defaultAgent;
    @Resource
    private QcAiAgentVisitDetailMapper visitDetailMapper;
    @Resource
    private QcAiAgentConversationMapper conversationMapper;
    @Resource
    private LLMTools llmTools;

    @Override
    public AgentType determineAgent() {
        return AgentType.VISIT;
    }

    @Override
    public Message invoke(QcAiAgent agent, LLAConfig config, LLARequest request, SseClient client) {
        try {
            // 基本参数检查
            if (agent == null || request == null) {
                return Message.of();
            }
            String logPrefix = String.format("[tenantId=%s, agentId=%s, requestId=%s]", UserManager.getTenantUser().getTenantId(),
                    agent.getId(), request.getId());
            String content = buildInitialContent(agent, request);

            // 使用switch表达式简化
            switch (agent.getQuestionType()) {
                case "1" -> { // 拜访客户推荐
                    var visitParam = agent.getVisitAgentParam();
                    if (visitParam == null) {
                        handleNoVisitData(request, client, MISSING_PARAMETER, logPrefix);
                        return Message.of();
                    }

                    var data = queryVisitData(visitParam, agent);
                    log.info("Fetched visit data, length: {}", data.length());

                    updateVisitDetails(visitParam, agent);
                    updateConversationAnswer(visitParam, agent);

                    var analysisResult = analyzeClientVisits(data);
                    if (isDataEmpty(analysisResult)) {
                        handleNoVisitData(request, client, NO_VISIT_DATA_MSG, logPrefix);
                        return Message.of();
                    }

                    request.setIsShowButton("1");
                    content = buildFinalContent(analysisResult, agent.getBizPrompt());
                }
                case "2" -> { // 拜访数据查询
                    handleNoVisitData(request, client, QUERY_DATA, logPrefix);
                    return Message.of();
                }
            }

            request.setContent(content);
            WorkflowLLAClient.sendAsync(config, request, client);
        } catch (Exception e) {
            log.error("处理请求失败", e);
            assert request != null;
            doErrorSend(MODEL_KEY_ERROR, request, null, client, false);
        }

        return Message.of();
    }

    private String buildInitialContent(QcAiAgent agent, LLARequest request) {
        return agent.getPrompt() + dataUtils.getCurrentDate() + request.getContent();
    }

    private String buildFinalContent(String data, String bizPrompt) {
        return String.format(bizPrompt, data) + dataUtils.getCurrentDate();
    }

    private void updateVisitDetails(VisitAgentParamDTO param, QcAiAgent agent) {
        var detail = new QcAiAgentVisitDetail();
        detail.setUserId(RequestHolder.getThreadLocalUser().getUserId());
        detail.setRetrieveRadius(param.getRetrieveRadius());
        detail.setSearchScopeDays(param.getSearchScopeDays());
        detail.setAgentId(agent.getId());
        visitDetailMapper.update(detail);
    }

    private void updateConversationAnswer(VisitAgentParamDTO param, QcAiAgent agent) {
        if (agent.getConversationId() != null) {
            String answer = QcAiAgentVisitConversationBizImpl.buildInitialAnswer(
                    param.getAddress(),
                    param.getRetrieveRadius(),
                    param.getSearchScopeDays());
            conversationMapper.updateAnswerById(agent.getConversationId(), answer);
        }
    }

    private boolean isDataEmpty(String data) {
        return StringUtils.isEmpty(data) || "{}".equals(data);
    }

    private void handleNoVisitData(LLARequest request, SseClient client, String message,String logPrefix) {
        simulateStreamSend(message, request, client);
        defaultAgent.completeClient(client, request, logPrefix);

        if (StringUtils.isNotEmpty(request.getId())) {
            var conversation = conversationMapper.selectById(Long.parseLong(request.getId()));
            if (conversation != null) {
                llmTools.updateConversationDuration(conversation, message, logPrefix);
            }
        }
    }

    public String queryVisitData(VisitAgentParamDTO param, QcAiAgent agent) {
        LocalDate endDate = LocalDate.now();
        LocalDate startDate = endDate.minusDays(param.getSearchScopeDays().intValue());

        var condition = new JSONObject();
        condition.put("dateStart", startDate.format(DATE_FORMATTER));
        condition.put("dateEnd", endDate.format(DATE_FORMATTER));
        condition.put("location", param.getLocation());
        condition.put("permission", "1");
        condition.put("nearByMax", param.getRetrieveRadius());

        return AgentBizDataSupport.fetchBizData(agent, condition);
    }

    public static String analyzeClientVisits(String jsonData) {
        if (StringUtils.isBlank(jsonData) || "{}".equals(jsonData)) {
            return StringUtils.EMPTY;
        }

        try {
            JSONObject jsonObject = JSON.parseObject(jsonData);
            List<VisitInfo> visitInfoList = processClientVisits(jsonObject);
            return formatVisitResults(visitInfoList);
        } catch (DateTimeParseException e) {
            log.error("拜访助手-日期解析错误", e);
            return DATE_PARSE_ERROR;
        } catch (JSONException e) {
            log.error("拜访助手-JSON解析错误", e);
            return JSON_PARSE_ERROR;
        } catch (Exception e) {
            log.error("拜访助手-处理数据时发生错误", e);
            return "处理数据时发生错误";
        }
    }

    public static List<VisitInfo> processClientVisits(JSONObject jsonObject) {
        LocalDate today = LocalDate.now();
        LocalDate recentThreshold = today.minusDays(EXCLUDE_RECENT_DAYS);
        LocalDate visitThreshold = today.minusDays(RECENT_VISIT_DAYS);

        return jsonObject.entrySet().stream()
                .filter(entry -> isValidClientEntry(entry.getKey()))
                .map(entry -> {
                    String[] parts = entry.getKey().split("\\|");
                    String clientName = parts.length > 1 ? parts[1] : UNNAMED_CLIENT;
                    String visitType = parts.length > 3 ? parts[3] : "0"; // 默认未拜访

                    long days = calculateVisitStats((JSONArray) entry.getValue(), recentThreshold, visitThreshold);

                    VisitInfo info = new VisitInfo();
                    info.setName(clientName);
                    info.setDay(String.valueOf(days));
                    info.setDesc(clientName + ", " + days + "天未拜访");
                    info.setVisitType(visitType);
                    return info;
                })
                .filter(info -> Long.parseLong(info.getDay()) > 0)
                .sorted(Comparator
                        .comparingLong((VisitInfo info) -> -Long.parseLong(info.getDay())) // 天数降序
                        .thenComparing(VisitInfo::getName) // 客户名称升序
                        .thenComparing(VisitInfo::getVisitType)) // visitType升序
                .limit(10)
                .collect(Collectors.toList());
    }

    private static String formatVisitResults(List<VisitInfo> results) {
        if (results.isEmpty()) {
            return StringUtils.EMPTY;
        }

        StringJoiner joiner = new StringJoiner("；");
        int[] index = {1};
        results.forEach(info -> {
            String status = switch (info.getVisitType()) {
                case "1" -> "已合作";
                case "2" -> "失访";
                default -> "未合作";
            };
            joiner.add(String.format("%d.%s，%s天未拜访（%s）",
                    index[0]++,
                    info.getName(),
                    info.getDay(),
                    status));
        });

        return joiner.toString();
    }

    private static boolean isValidClientEntry(String key) {
        return key.split("\\|").length >= 2;
    }

    private static long calculateVisitStats(JSONArray visits, LocalDate recentThreshold, LocalDate visitThreshold) {
        record VisitRecord(LocalDate date, String status, String type) {
        }

        var today = LocalDate.now();
        var visitRecords = visits.stream()
                .map(Object::toString)
                .<VisitRecord>mapMulti((visit, consumer) -> {
                    String[] parts = visit.split("\\|");
                    if (parts.length >= 3) {
                        consumer.accept(new VisitRecord(
                                LocalDate.parse(parts[0], DATE_FORMATTER),
                                parts[1],
                                parts[2]
                        ));
                    }
                })
                .toList();

        // 剔除最近一周已拜访过的客户和今日已在拜访计划中的客户
        var allValid = visitRecords.stream()
                .allMatch(visit -> {
                    boolean isTodayPlanned = visit.date().equals(today)
                            && !"1".equals(visit.status())
                            && "1".equals(visit.type());
                    boolean isRecentlyVisited = visit.date().isAfter(recentThreshold)
                            && "1".equals(visit.status());

                    return !isTodayPlanned && !isRecentlyVisited;
                });

        // 考虑最近180天内有拜访过的客户
        var anyValid = visitRecords.stream()
                .anyMatch(visit -> visit.date().isAfter(visitThreshold) && "1".equals(visit.status()));

        if (allValid && anyValid) {
            return visitRecords.stream()
                    .filter(visit -> "1".equals(visit.status()))
                    .mapToLong(visit -> ChronoUnit.DAYS.between(visit.date(), today))
                    .min()
                    .orElse(0L);
        }
        return 0L;
    }
}