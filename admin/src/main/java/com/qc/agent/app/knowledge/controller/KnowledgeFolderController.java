package com.qc.agent.app.knowledge.controller;

import com.google.common.collect.Lists;
import com.qc.agent.app.knowledge.model.QcKnowledgeFolder;
import com.qc.agent.app.knowledge.model.QcKnowledgeFolderParams;
import com.qc.agent.app.knowledge.service.KnowledgeFolderService;
import com.qc.agent.app.knowledge.utils.TreeUtils;
import com.qc.agent.common.core.Message;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @className KnowledgeFolderController
 * @description TODO
 * @date 2024/1/22 11:26
 */
@RequestMapping("/ai-agent/knowledge/folder")
@RestController
public class KnowledgeFolderController {

    private final Log logger = LogFactory.getLog(KnowledgeFolderController.class);

    @Autowired
    private KnowledgeFolderService knowledgeFolderService;

    @RequestMapping("/addOrUpdate.do")
    public Message addOrUpdate(@RequestBody QcKnowledgeFolderParams qcKnowledgeFolderParams){

        //名称校验
        String msg = checkParams(qcKnowledgeFolderParams);
        if (StringUtils.isNotEmpty(msg)) {
            return Message.of().error(msg);
        }

        //更新
        if (!Objects.isNull(qcKnowledgeFolderParams.getId()) && qcKnowledgeFolderParams.getId() > 0L) {
            knowledgeFolderService.updateQcKnowledgeFolder(qcKnowledgeFolderParams);
        } else {
            return Message.of().data(String.valueOf(knowledgeFolderService.insertQcKnowledgeFolder(qcKnowledgeFolderParams))).ok();
        }
        return Message.of().ok();
    }

    private String checkParams(QcKnowledgeFolderParams qcKnowledgeFolderParams){
        if (Objects.isNull(qcKnowledgeFolderParams.getParentId())) {
            return "父级id不能为空";
        }
        List<QcKnowledgeFolder> list = knowledgeFolderService.selectListByParentId(qcKnowledgeFolderParams.getParentId());
        if (list.stream().filter(item -> !item.getId().equals(qcKnowledgeFolderParams.getId())).map(QcKnowledgeFolder::getFolderName).collect(Collectors.toList()).contains(qcKnowledgeFolderParams.getFolderName())) {
            return "同级目录下不能存在相同文件名";
        }
        return "";
    }

    @RequestMapping("/getFolderTree.do")
    public Message detail(@RequestBody QcKnowledgeFolderParams qcKnowledgeFolderParams){
        //根节点的id定死为1
        List<QcKnowledgeFolder> list = knowledgeFolderService.selectDeepListByParentId(1L);
        //构建树
        QcKnowledgeFolder qcKnowledgeFolder = TreeUtils.convertTreeList(list,1L);
        //层级排序
        orderEntity(qcKnowledgeFolder);
        return Message.of().data(Lists.newArrayList(qcKnowledgeFolder)).ok();
    }

    @RequestMapping("/delete.do")
    public Message delete(@RequestBody QcKnowledgeFolderParams qcKnowledgeFolderParams){
        List<QcKnowledgeFolder> list = knowledgeFolderService.selectDeepListByParentId(qcKnowledgeFolderParams.getId());
        String[] strings =  list.stream().map(qcKnowledgeFolder -> String.valueOf(qcKnowledgeFolder.getId())).collect(Collectors.toList()).toArray(String[]::new);
        //删除子级文件夹
        knowledgeFolderService.deleteQcKnowledgeFolderByIds(strings);
        // todo  删除子级文件
        return Message.of().ok();
    }




    private void orderEntity(QcKnowledgeFolder qcKnowledgeFolder){
        if (CollectionUtils.isNotEmpty(qcKnowledgeFolder.getChildren())) {
            qcKnowledgeFolder.getChildren().stream().sorted((v1, v2) -> {
                if (StringUtils.isEmpty(v1.getSequence())){
                    return -1;
                }
                if (StringUtils.isEmpty(v2.getSequence())){
                    return -1;
                }
                Double sequence1 = Double.parseDouble(v1.getSequence());
                Double sequence2 = Double.parseDouble(v2.getSequence());
                if (sequence1 - sequence2 > 0d) {
                    return 1;
                } else {
                    return -1;
                }
            });
            qcKnowledgeFolder.getChildren().stream().peek(qcKnowledgeFolder1 -> orderEntity(qcKnowledgeFolder1));
        }
    }
}
