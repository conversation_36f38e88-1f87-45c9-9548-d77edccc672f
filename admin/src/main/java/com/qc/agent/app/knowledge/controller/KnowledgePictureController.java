package com.qc.agent.app.knowledge.controller;

import com.qc.agent.app.knowledge.model.query.KnowledgePictureQuery;
import com.qc.agent.app.knowledge.service.KnowledgePictureService;
import com.qc.agent.common.core.Message;
import lombok.RequiredArgsConstructor;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Map;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2025-07-11
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@RestController
@RequestMapping("/ai-agent/knowledge/picture/")
@RequiredArgsConstructor
public class KnowledgePictureController {

    private final KnowledgePictureService knowledgePictureService;

    @RequestMapping("/import_picture")
    public Message importPicture(@RequestBody KnowledgePictureQuery query){
        knowledgePictureService.importPicture(query);
        return Message.of().ok();
    }

    @RequestMapping("/detail")
    public Message detail(@RequestBody KnowledgePictureQuery query){
        Map<String,Object> dataMap = knowledgePictureService.detail(query);
        return Message.of().ok().data(dataMap);
    }


    @RequestMapping("/batch_insert")
    public Message batchInsert(@RequestBody KnowledgePictureQuery query){
        knowledgePictureService.batchInsert(query);
        return Message.of().ok();
    }

    @GetMapping("download-file")
    public void downloadFile(@RequestParam String url, HttpServletResponse response) {
        try (InputStream inputStream = new URL(url).openStream();
                ServletOutputStream outputStream = response.getOutputStream()) {

            // 解析文件名和类型
            String fileName = url.substring(url.lastIndexOf("/") + 1); // 例如 xxx.pdf
            String contentType = URLConnection.guessContentTypeFromName(fileName);
            if (contentType == null) {
                contentType = "application/octet-stream"; // fallback
            }

            // 设置响应头
            response.setContentType(contentType);
            response.setHeader("Content-Disposition",
                    "attachment; filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8) + "");

            // 拷贝流
            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            outputStream.flush();
        } catch (IOException e) {
            throw new RuntimeException("文件下载失败: " + url, e);
        }
    }



}
