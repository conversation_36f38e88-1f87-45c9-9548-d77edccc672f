package com.qc.agent.app.agent.service.customer_insight_intent;

import com.qc.agent.app.agent.model.query.QcAiCustomerInsightAssistantQuery;
import com.qc.agent.app.workflow.inner.impl.customer_insight_assistant.model.CustomerInsightBusinessIntent;
import com.qc.agent.app.workflow.inner.impl.customer_insight_assistant.model.enums.CustomerInsightIntent;

/**
 * 客户洞察意图处理器接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 * @version 1.0.0
 */
public interface CustomerInsightIntentHandler {
    
    /**
     * 获取支持的意图类型
     *
     * @return 支持的意图类型
     */
    CustomerInsightIntent getSupportedIntent();

    /**
     * 处理意图
     *
     * @param intent 业务意图
     * @param query 查询参数
     * @param logPrefix 日志前缀
     * @return 处理后的业务意图
     */
    CustomerInsightBusinessIntent handle(CustomerInsightBusinessIntent intent, QcAiCustomerInsightAssistantQuery query, String logPrefix);
} 