package com.qc.agent.app.agent.model.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QcAiAgentCategory {
    /**
     * 主键 ID
     */
    private Long id;

    /**
     * 状态，默认值 '1'
     */
    private String status;

    /**
     * 创建人 ID
     */
    private Long creatorId;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改人 ID
     */
    private Long modifyierId;

    /**
     * 修改人姓名
     */
    private String modifyierName;

    /**
     * 修改时间
     */
    private LocalDateTime modifyTime;

    /**
     * 分类名称
     */
    private String name;

}
