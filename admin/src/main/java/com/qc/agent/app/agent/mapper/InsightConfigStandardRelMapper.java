package com.qc.agent.app.agent.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.qc.agent.app.agent.model.entity.InsightConfigStandardRel;

/**
 * 配置标准关系表 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InsightConfigStandardRelMapper {

        /**
         * 根据ID查询关系
         */
        InsightConfigStandardRel selectById(@Param("id") Long id);

        /**
         * 根据配置ID和配置类型查询关系列表
         */
        List<InsightConfigStandardRel> selectByConfigIdAndType(@Param("configId") Long configId,
                        @Param("configType") String configType);

        /**
         * 根据配置ID列表和配置类型查询关系列表
         */
        List<InsightConfigStandardRel> selectByConfigIdsAndType(@Param("configIds") List<Long> configIds,
                        @Param("configType") String configType);

        /**
         * 插入关系
         */
        int insert(InsightConfigStandardRel rel);

        /**
         * 更新关系
         */
        int updateById(InsightConfigStandardRel rel);

        /**
         * 根据ID删除关系
         */
        int deleteById(@Param("id") Long id);

        /**
         * 根据配置ID和配置类型删除关系
         */
        int deleteByConfigIdAndType(@Param("configId") Long configId, @Param("configType") String configType);

        /**
         * 根据配置ID列表和配置类型批量删除关系
         */
        int deleteByConfigIdsAndType(@Param("configIds") List<Long> configIds, @Param("configType") String configType);

        /**
         * 根据配置ID和标准ID查询关系
         */
        InsightConfigStandardRel selectByConfigIdAndStandardId(@Param("configId") Long configId,
                        @Param("standardId") Long standardId);

        /**
         * 根据配置ID和标准ID删除关系
         */
        int deleteByConfigIdAndStandardId(@Param("configId") Long configId, @Param("standardId") Long standardId);

        /**
         * 根据配置ID和标准ID列表批量删除关系
         */
        int deleteByConfigIdAndStandardIds(@Param("configId") Long configId,
                        @Param("standardIds") List<Long> standardIds);
}