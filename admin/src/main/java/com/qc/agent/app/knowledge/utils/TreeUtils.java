package com.qc.agent.app.knowledge.utils;

import com.qc.agent.app.knowledge.model.ITreeEntity;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @className TreeUtils
 * @description 列表转树结构
 * @date 2024/1/22 19:23
 */
public class TreeUtils {

    public static <T extends ITreeEntity> T convertTreeList(List<T> tList, Long id){
        T t = null;
        for (T t1 : tList) {
            if (t1.getId() == id) {
                t = t1;
                getChirldrenList(tList,t);
                break;
            }
        }
        return t;
    }

    private static <T extends ITreeEntity> void getChirldrenList(List<T> tList, T t) {
        List<T> chirldrenList = new ArrayList<>();
        for (T t1: tList) {
            if (t.getId().equals(t1.getParentId())) {
                chirldrenList.add(t1);
            }
        }
        t.setChildren(chirldrenList);
        for (T t2 : chirldrenList) {
            getChirldrenList(tList,t2);
        }
    }

}
