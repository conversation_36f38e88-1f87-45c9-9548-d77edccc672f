package com.qc.agent.app.agent.controller;

import com.alibaba.fastjson.JSONObject;
import com.qc.agent.app.agent.model.dto.*;
import com.qc.agent.app.agent.model.po.QcAiAgentConversationParams;
import com.qc.agent.app.agent.model.query.ExtractConversationQuery;
import com.qc.agent.app.agent.model.query.QcAiAgentChatQuery;
import com.qc.agent.app.agent.model.vo.ConversationVO;
import com.qc.agent.app.agent.service.QcAiAgentConversationService;
import com.qc.agent.app.job.extract_conversation.impl.AiAgentExtractConversationJob;
import com.qc.agent.common.core.Message;
import com.qc.agent.common.core.RequestHolder;
import com.qc.agent.platform.pojo.SysDepartment;
import com.qc.agent.platform.user.service.AppServerUserService;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

@RestController
@RequestMapping("/ai-agent/conversation")
public class QcAiAgentConversationController {

    @Resource
    private QcAiAgentConversationService qcAiAgentConversationService;

    @Resource
    private AppServerUserService appServerUserService;

    @Resource
    private AiAgentExtractConversationJob aiAgentExtractConversationJob;

    /**
     * 会话
     *
     * @return
     */
    @PostMapping("/chat")
    public SseEmitter conversation(@RequestBody QcAiAgentChatDTO qcAiAgentChatDTO) {
        if (StringUtils.isEmpty(qcAiAgentChatDTO.getDeptId())) {
            Long deptId = appServerUserService.selectDeptIdByUserId(RequestHolder.getThreadLocalUser().getUserId());
            qcAiAgentChatDTO.setDeptId(deptId);
        }
        qcAiAgentChatDTO.setDeptIdList(appServerUserService.getParentDeptIds(qcAiAgentChatDTO.getDeptId()));
        SysDepartment deptInfo = appServerUserService.getDeptInfo(qcAiAgentChatDTO.getDeptId());
        qcAiAgentChatDTO.setDeptName(deptInfo == null ? null : deptInfo.getFullNames());
        return qcAiAgentConversationService.conversation(qcAiAgentChatDTO);
    }

    /**
     * 推荐
     *
     * @return
     */
    @PostMapping("/recommend-prompt")
    public Message recommendPrompt(@RequestBody QcAiAgentRecommendDTO recommendationDTO) {
        return qcAiAgentConversationService.recommendPrompt(recommendationDTO);
    }

    /**
     * 销售分析助手
     * @param qcAiAgentChatDTO
     * @return
     */
    @PostMapping("/sale_analysis_chat")
    public Message saleAnalysisChat(@RequestBody QcAiAgentChatDTO qcAiAgentChatDTO) {
        return qcAiAgentConversationService.saleAnalysisChat(qcAiAgentChatDTO);
    }

    @PostMapping("/stop")
    public Message stop(@RequestBody ChatStopDTO chatStopDTO) {
        qcAiAgentConversationService.stopChat(chatStopDTO);
        return Message.of().ok();
    }

    @PostMapping("/evaluate")
    public Message evaluateConversation(@RequestBody ConversationEvaluationDTO evaluationDTO) {
        return qcAiAgentConversationService.evaluateConversation(evaluationDTO);
    }

    /**
     * 调用KIMI模型默认生成三个问题
     *
     * @return
     */
    @PostMapping("/generate_default_question")
    public Message generateDefaultQuestion(@RequestBody QcAiAgentChatQuery query) {
        return Message.of().ok().data(qcAiAgentConversationService.generateDefaultQuestion(query));
    }

    /**
     * 会话测试
     *
     * @return
     */
    @PostMapping("/test-chat")
    public SseEmitter testConversation(@RequestBody QcAiAgentTestDTO qcAiAgentTestDTO) {
        if (StringUtils.isEmpty(qcAiAgentTestDTO.getDeptId())) {
            Long deptId = appServerUserService.selectDeptIdByUserId(RequestHolder.getThreadLocalUser().getUserId());
            qcAiAgentTestDTO.setDeptId(deptId);
        }
        qcAiAgentTestDTO.setDeptIdList(appServerUserService.getParentDeptIds(qcAiAgentTestDTO.getDeptId()));
        return qcAiAgentConversationService.testConversation(qcAiAgentTestDTO);
    }

    /**
     * 根据智能体id获取历史会话
     *
     * @param agentId 智能体id
     * @return
     */
    @GetMapping("/{agentId}/conversations")
    public Message queryAgentHistoryConversation(@PathVariable Long agentId,
                                                 @RequestParam(defaultValue = "1")
                                                 Integer page,
                                                 @RequestParam(defaultValue = "10")
                                                 Integer rows) {
        List<ConversationVO> data = qcAiAgentConversationService.queryAgentHistoryConversation(agentId, page, rows);
        return Message.of().ok().data(data);
    }

    /**
     * 根据智能体id获取历史会话
     *
     * @param agentId 智能体id
     * @return
     */
    @PostMapping("/{agentId}/client-conversations")
    public Message queryClientAgentHistoryConversation(@PathVariable Long agentId,
                                                       @RequestParam(defaultValue = "1")
                                                       Integer page,
                                                       @RequestParam(defaultValue = "10")
                                                       Integer rows) {
        List<ConversationVO> data = qcAiAgentConversationService.queryAgentHistoryConversation(agentId, page, rows);
        return Message.of().ok().data(data);
    }

    /**
     * 根据agentId获取历史记录
     *
     * @param model
     * @return
     */
    @PostMapping("/queryHistoryConversation.do")
    public Message queryHistoryConversation(@RequestBody QcAiAgentConversationParams model) {
        JSONObject jsonObject = qcAiAgentConversationService.queryHistoryConversation(model);
        return Message.of().data(jsonObject).ok();
    }

    /**
     * 导出历史会话数据接口
     */
    @PostMapping("/exportHistoryConversation.do")
    public void exportHistoryConversation(@RequestBody QcAiAgentConversationParams model, HttpServletResponse response) {
        qcAiAgentConversationService.exportHistoryConversation(model, response);
    }


    /**
     * 根据agentId获取历史记录
     *
     * @return
     */
    @PostMapping("/extract_conversation")
    public Message extractConversation(@RequestBody ExtractConversationQuery query) {
        aiAgentExtractConversationJob.extractConversationDataToOss(query);
        return Message.of().ok();
    }

    @GetMapping("/common-feedback")
    public Message getCommonFeedback(String feedbackType) {
        List<CommonFeedbackDTO> feedbackList = qcAiAgentConversationService.getCommonFeedback(feedbackType);
        return Message.of().ok().data(feedbackList);
    }

    @PostMapping("/common-feedback")
    public Message postCommonFeedback(@RequestBody JSONObject data) {
        List<CommonFeedbackDTO> feedbackList = qcAiAgentConversationService.getCommonFeedback(data.getString("feedbackType"));
        return Message.of().ok().data(feedbackList);
    }
}
