package com.qc.agent.app.agent.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.qc.agent.app.agent.model.entity.InsightStandard;

/**
 * 标准表 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InsightStandardMapper {

    /**
     * 根据ID查询标准
     */
    InsightStandard selectById(@Param("id") Long id);

    /**
     * 根据ID列表批量查询
     */
    List<InsightStandard> selectBatchIds(@Param("ids") List<Long> ids);

    /**
     * 查询所有标准
     */
    List<InsightStandard> selectAll();

    /**
     * 根据状态查询标准列表
     */
    List<InsightStandard> selectByStatus(@Param("status") String status);

    /**
     * 插入标准
     */
    int insert(InsightStandard standard);

    /**
     * 更新标准
     */
    int updateById(InsightStandard standard);

    /**
     * 更新标准（不更新类型相关字段）
     */
    int updateStandardInfo(InsightStandard standard);

    /**
     * 根据ID删除标准
     */
    int deleteById(@Param("id") Long id);

    /**
     * 根据ID列表批量删除标准
     */
    int deleteBatchIds(@Param("ids") List<Long> ids);
}