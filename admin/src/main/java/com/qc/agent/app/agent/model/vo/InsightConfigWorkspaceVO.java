package com.qc.agent.app.agent.model.vo;

import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * 洞察配置工作区视图对象
 *
 * <AUTHOR>
 */
@Data
public class InsightConfigWorkspaceVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 当前已保存的维度配置
     * 根据洞察模式返回不同的配置结构
     */
    private InsightModeConfiguration currentDimensionConfiguration;

    /**
     * 当前已保存的总结配置
     */
    private InsightSummaryConfigVO currentSummaryConfiguration;

    /**
     * 可用的配置选项
     */
    private AvailableOptions availableOptions;

    /**
     * 洞察模式配置
     * 根据不同的洞察模式返回相应的维度配置结构
     */
    @Data
    public static class InsightModeConfiguration implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 洞察模式：0-客户模式，1-终端经销商模式
         */
        private String insightMode;

        /**
         * 客户-0 终端-1 经销商-2
         */
        private String insightModeType;

        /**
         * 客户维度配置（仅客户模式有）
         * 包含客户相关的所有维度配置
         */
        private List<InsightDimensionConfigVO> customerDimensionConfiguration;

        /**
         * 终端维度配置（仅终端经销商模式有）
         * 包含终端相关的所有维度配置
         */
        private List<InsightDimensionConfigVO> terminalDimensionConfiguration;

        /**
         * 经销商维度配置（仅终端经销商模式有）
         * 包含经销商相关的所有维度配置
         */
        private List<InsightDimensionConfigVO> dealerDimensionConfiguration;
    }

    @Data
    public static class AvailableOptions implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 所有可用的维度及其包含的数据源列表
         */
        private List<InsightDimensionConfigVO> dimensions;

        /**
         * 可用的总结配置模板（包含提示词片段）
         */
        private InsightSummaryConfigVO summaryTemplate;
    }
}