package com.qc.agent.app.knowledge.model;

import java.util.List;
import java.util.Set;

/**
 * @description：TODO
 * @author： sc
 * @create： 2024/1/23 13:34
 */
public class QcKnowledgeFileListParams extends QcKnowledgeFileParams {

    /** 文件名列表 */
    Set<String> fileNames;

    List<String> categoryIds;

    List<String> fileStatuses;

    private Integer page = 1;

    private Integer rows = 100;

    private Integer limit;

    private Integer offset;


    public List<String> getCategoryIds() {
        return categoryIds;
    }

    public void setCategoryIds(List<String> categoryIds) {
        this.categoryIds = categoryIds;
    }

    public List<String> getFileStatuses() {
        return fileStatuses;
    }

    public void setFileStatuses(List<String> fileStatuses) {
        this.fileStatuses = fileStatuses;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getRows() {
        return rows;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Set<String> getFileNames() {
        return fileNames;
    }

    public void setFileNames(Set<String> fileNames) {
        this.fileNames = fileNames;
    }

    public void doPage(){
        if(page == null || page < 1){
            page = 1;
        }
        if(rows == null){
            rows = 100;
        }
        this.limit = rows;
        this.offset = (page - 1) * rows;
    }
}
