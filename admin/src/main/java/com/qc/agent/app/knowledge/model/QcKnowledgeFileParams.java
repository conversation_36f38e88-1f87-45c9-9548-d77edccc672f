package com.qc.agent.app.knowledge.model;

import java.util.Date;

/**
 * <AUTHOR>
 * @className QcKnowledgeFile
 * @description 文件接口入参
 * @date 2024/1/22 10:22
 */
public class QcKnowledgeFileParams {

    private Long id;

    /**  文件名称*/
    private String fileName;

    /**  文件路径*/
    private String fileUrl;


    private Long folderId;

    private Long collectionId;
    //分段
    private int segmentation;


    /**  文件分段长度*/
    private String segmentLength;
    private String fileComments;
    private String fileLogo;
    private String fileSize;
    private String fileStatus;

    public static final String DELETE_FILE_STATUS = "0";

    public static final String ENABLE_FILE_STATUS = "1";

    public static final String DISABLE_FILE_STATUS = "2";
    private String fileType;
    private Long createUserId;
    private String createUserName;
    private Long modifyUserId;
    private String modifyUserName;
    private String chunkSplitType;
    public static final String AUTO_CHUNK_SPLIT_TYPE = "0";
    public static final String DOUBLE_NEWLINE_CHUNK_SPLIT_TYPE = "1";
    private String viewCount;
    private String downCount;
    private Long databaseId;

    private Date createTime;
    private Date modifyTime;

    /**
     * 数据导入方式，RAW：无结构文档，QA：QA问答文档
     */
    private String dataImpType;

    private String categoryId;

    public String getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId;
    }

    public boolean qaImpType(){
        return "QA".equals(dataImpType);
    }




    public int getSegmentation() {
        return segmentation;
    }

    public String getChunkSplitType() {
        return chunkSplitType;
    }

    public void setChunkSplitType(String chunkSplitType) {
        this.chunkSplitType = chunkSplitType;
    }

    public void setSegmentation(int segmentation) {
        this.segmentation = segmentation;
    }

    public String getFileComments() {
        return fileComments;
    }

    public void setFileComments(String fileComments) {
        this.fileComments = fileComments;
    }

    public String getFileLogo() {
        return fileLogo;
    }

    public void setFileLogo(String fileLogo) {
        this.fileLogo = fileLogo;
    }

    public String getFileSize() {
        return fileSize;
    }

    public void setFileSize(String fileSize) {
        this.fileSize = fileSize;
    }

    public String getFileStatus() {
        return fileStatus;
    }

    public void setFileStatus(String fileStatus) {
        this.fileStatus = fileStatus;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }



    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }



    public String getModifyUserName() {
        return modifyUserName;
    }

    public void setModifyUserName(String modifyUserName) {
        this.modifyUserName = modifyUserName;
    }

    public String getViewCount() {
        return viewCount;
    }

    public void setViewCount(String viewCount) {
        this.viewCount = viewCount;
    }

    public String getDownCount() {
        return downCount;
    }

    public void setDownCount(String downCount) {
        this.downCount = downCount;
    }

    public Long getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Long createUserId) {
        this.createUserId = createUserId;
    }

    public Long getModifyUserId() {
        return modifyUserId;
    }

    public void setModifyUserId(Long modifyUserId) {
        this.modifyUserId = modifyUserId;
    }

    public Long getDatabaseId() {
        return databaseId;
    }

    public void setDatabaseId(Long databaseId) {
        this.databaseId = databaseId;
    }

    public Long getCollectionId() {
        return collectionId;
    }

    public void setCollectionId(Long collectionId) {
        this.collectionId = collectionId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getFolderId() {
        return folderId;
    }

    public void setFolderId(Long folderId) {
        this.folderId = folderId;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public String getSegmentLength() {
        return segmentLength;
    }

    public void setSegmentLength(String segmentLength) {
        this.segmentLength = segmentLength;
    }

    public String getDataImpType() {
        return dataImpType;
    }

    public void setDataImpType(String dataImpType) {
        this.dataImpType = dataImpType;
    }
}
