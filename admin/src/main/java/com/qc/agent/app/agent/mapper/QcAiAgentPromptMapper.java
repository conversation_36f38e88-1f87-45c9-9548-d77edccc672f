package com.qc.agent.app.agent.mapper;

import com.alibaba.fastjson.JSONObject;
import com.qc.agent.app.agent.model.po.QcAiAgentPrompt;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface QcAiAgentPromptMapper {

    int insert(QcAiAgentPrompt prompt);

    int deleteById(@Param("id") Long id, @Param("userId") Long userId, @Param("userName") String userName);

    int update(QcAiAgentPrompt prompt);

    QcAiAgentPrompt selectById(Long id);

    List<QcAiAgentPrompt> selectAll();

    List<QcAiAgentPrompt> queryPrompts(@Param("userId") Long userId, @Param("recommend") String recommend, @Param("mine") String mine);

    List<QcAiAgentPrompt> searchPrompts(JSONObject jsonObject);

    Long countByName(@Param("promptId") Long promptId, @Param("promptName") String promptName);
}
