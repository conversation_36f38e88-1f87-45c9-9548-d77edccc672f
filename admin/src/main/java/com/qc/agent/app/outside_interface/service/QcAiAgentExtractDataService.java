package com.qc.agent.app.outside_interface.service;

import com.qc.agent.app.outside_interface.pojo.query.QcAiAgentExtractDataQuery;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-03-17
 */
public interface QcAiAgentExtractDataService {
    /**
     * 调用master查询所有企业id
     *
     * @return
     */
    Map<String, Object> master(QcAiAgentExtractDataQuery query);

    /**
     * 按企业查询
     *
     * @param query
     * @return
     */
    Map<String, Object> tenant(QcAiAgentExtractDataQuery query);
}
