package com.qc.agent.app.agent.model.dto;

import com.qc.agent.app.workflow.inner.pojo.QcAiAgentIntentRecognition;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class QcAiAgentTestDTO {

    private Long deptId;

    private String deptName;

    /**
     * 测试对话用户id
     */
    private Long userId;

    /**
     * 测试对话用户名称
     */
    private String userName;

    private List<Long> deptIdList;

    /**
     *
     */
    private Long agentId;
    /**
     * 大模型id
     */
    private Long modelId;

    /**
     * 提示词
     */
    private String prompt;

    /**
     * 问题
     */
    private String question;

    /**
     * 关联的知识库id
     */
    private List<Long> knowledgeIdList;

    /**
     * 知识库-召回设置-最大召回数量
     */
    private Integer maxRecallCount;
    /**
     * 知识库-召回设置-最小匹配度
     */
    private Double minMatchThreshold;

    /**
     * 知识库-召回设置-qa最小匹配度
     */
    private Double qaMinMatchThreshold;
    /**
     * 知识库-召回设置-检索范围 1:仅引用知识库 2:引用知识库+模型通用知识库
     */
    private String searchScope;
    /**
     * sql拆分提示词
     */
    private String splitSqlPrompt;

    /**
     * 空结果回复
     */
    private String nullResultAnswer;

    /**
     * 采样温度
     */
    private BigDecimal modelTemperature;

    /**
     * 核采样的概率阈值
     */
    private BigDecimal modelTopP;

    /**
     * 请求返回的最大 Token 数
     */
    private BigDecimal modelMaxTokens;

    /**
     * 客户端生成的id
     */
    private String clientId;

    /**
     * 问题类型  1：拜访客户推荐 2：拜访数据查询 0：其他
     */
    private String questionType;

    /**
     * 拜访助手的参数
     */
    private VisitAgentParamDTO visitAgentParam;

    /**
     *  来源：web悬浮窗：web-levitate ； web端：web ； app端：app
     */
    private String source;

    /**
     * 对话 ID
     */
    private Long sessionId;

    /**
     * 前端传入的唯一id
     */
    private String chatSessionId;

    /**
     * 1：新对话
     */
    private String isNew;

    /**
     * 意图识别是否启用 1：是 0：否
     */
    private String intentIsEnabled;

    /**
     * 意图识别相关
     */
    private List<QcAiAgentIntentRecognition> intentList;

    /**
     * 测试对话和正式对话都有的公共参数
     */
    private QcAiAgentCommonParam commonParam;
}
