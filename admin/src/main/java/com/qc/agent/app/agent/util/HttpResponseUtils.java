package com.qc.agent.app.agent.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.qc.agent.utils.StringSimilarityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.springframework.ai.model.ModelOptionsUtils.OBJECT_MAPPER;

/**
 * <AUTHOR>
 * @date 2025-06-27
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Slf4j
public class HttpResponseUtils {
    private HttpResponseUtils() {

    }

    public static String parseResponseString(String response, String logPrefix) {
        return Optional.ofNullable(response)
                .filter(StringUtils::isNotEmpty)
                .map(resp -> {
                    try {
                        Map<?, ?> map = OBJECT_MAPPER.readValue(resp, Map.class);
                        return Optional.ofNullable(map.get("data"))
                                .map(Object::toString) // Use toString() safely
                                .orElse(StringUtils.EMPTY);
                    } catch (JsonProcessingException e) {
                        log.error("{} Failed to parse JSON response from  API. Response: {}", logPrefix, resp, e);
                        throw new RuntimeException(e); // Wrap in unchecked or re-throw checked
                    }
                })
                .orElse(StringUtils.EMPTY);
    }


    private record Product(String name, String cmId) {
    }

    public static String parseResponseList(String response, String name, String logPrefix) {
        return Optional.ofNullable(response)
                .filter(StringUtils::isNotEmpty)
                .map(resp -> {
                    try {
                        ObjectMapper objectMapper = new ObjectMapper();
                        JsonNode rootNode = objectMapper.readTree(response);
                        List<Product> products = new ArrayList<>();
                        JsonNode dataArray = rootNode.get("data");
                        if (dataArray.isArray()) {
                            for (JsonNode item : dataArray) {
                                Product product = new Product(item.get("name").asText(), item.get("id").asText());
                                products.add(product);
                            }
                        }
                        Product mostSimilar = StringSimilarityUtils.findMostSimilar(products, name, Product::name);
                        return Optional.ofNullable(mostSimilar)
                                .map(Product::cmId)
                                .orElse(StringUtils.EMPTY);
                    } catch (JsonProcessingException e) {
                        log.error("{} Failed to parse JSON response from  API. Response: {}", logPrefix, resp, e);
                        throw new RuntimeException(e); // Wrap in unchecked or re-throw checked
                    }
                })
                .orElse(StringUtils.EMPTY);
    }
}
