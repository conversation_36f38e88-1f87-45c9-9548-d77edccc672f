package com.qc.agent.app.question.mapper;

import com.qc.agent.app.question.pojo.QcKnowledgeHistoryQuestion;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface QcKnowledgeHistoryQuestionMapper {
    int deleteByPrimaryKey(Long id);

    int insert(QcKnowledgeHistoryQuestion record);

    int insertSelective(QcKnowledgeHistoryQuestion record);

    QcKnowledgeHistoryQuestion selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(QcKnowledgeHistoryQuestion record);

    int updateByPrimaryKey(QcKnowledgeHistoryQuestion record);

    void batchInsert(List<QcKnowledgeHistoryQuestion> collect);

    Long countContext(@Param("questionId") Long questionId);

    List<Long> queryContextQuestionId(@Param("questionId") Long questionId);
}
