package com.qc.agent.app.agent.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.qc.agent.app.agent.mapper.QcAiAgentConversationMapper;
import com.qc.agent.app.agent.mapper.QcAiAgentMapper;
import com.qc.agent.app.agent.mapper.QcAiAgentModelMapper;
import com.qc.agent.app.agent.model.dto.*;
import com.qc.agent.app.agent.model.po.*;
import com.qc.agent.app.agent.model.query.QcAiAgentChatQuery;
import com.qc.agent.app.agent.model.vo.ConversationVO;
import com.qc.agent.app.agent.service.QcAiAgentConversationService;
import com.qc.agent.app.agent.service.tool.QcAiAgentConversationTool;
import com.qc.agent.app.agent.support.AgentBizDataSupport;
import com.qc.agent.app.agent.util.ContextUtil;
import com.qc.agent.app.workflow.WorkflowLLAClient;
import com.qc.agent.app.workflow.inner.AgentInvokerBuilder;
import com.qc.agent.app.workflow.inner.IAgentInvoker;
import com.qc.agent.app.workflow.inner.impl.tools.LLMTools;
import com.qc.agent.common.core.Message;
import com.qc.agent.lla.ILLACompletionListener;
import com.qc.agent.lla.model.*;
import com.qc.agent.platform.register.UserManager;
import com.qc.agent.platform.sse.SseClient;
import com.qc.agent.platform.sse.SseEmitterClient;
import com.qc.agent.platform.sse.SseMessage;
import com.qc.agent.platform.sse.dto.MessageType;
import com.qc.agent.platform.util.UUIDUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

@Service
@Slf4j
public class QcAiAgentConversationServiceImpl implements QcAiAgentConversationService {

    private final ExecutorService chatThreadPool = Executors.newFixedThreadPool(20);

    @Resource
    private QcAiAgentConversationMapper qcAiAgentConversationMapper;

    @Resource
    private QcAiAgentMapper qcAiAgentMapper;

    @Resource
    private QcAiAgentModelMapper qcAiAgentModelMapper;

    @Value("${ai.agent.appsvr-domin-url}")
    private String appsvrDominUrl;

    @Resource
    private LLAKeyProperties llaKeyProperties;

    @Resource
    private SseClient client;

    @Autowired
    private ILLACompletionListener llaCompletionListener;


    @Override
    public List<ConversationVO> queryAgentHistoryConversation(Long agentId, Integer page, Integer rows) {
        Long userId = UserManager.getTenantUser().getUserId();
        Integer offset = (page - 1) * rows;
        return qcAiAgentConversationMapper.queryAgentHistoryConversation(agentId, userId, rows, offset, "1");
    }

    @Override
    public SseEmitter conversation(QcAiAgentChatDTO qcAiAgentChatDTO) {

        QcAiAgent qcAiAgent = qcAiAgentMapper.selectById(qcAiAgentChatDTO.getAgentId());
        QcAiAgentModel qcAiAgentModel = qcAiAgentModelMapper.selectById(qcAiAgent.getModelId());

        SseEmitterClient testClient = SseEmitterClient.newBuilder();
        if (qcAiAgentModel == null) {
            doKeyExceptionSend(testClient);
            return testClient.getEmitter();
        }

        assembleQcAgentParam(qcAiAgent, qcAiAgentModel);
        assembleQcAgentParam(qcAiAgent, qcAiAgentChatDTO);

        IAgentInvoker invoker = AgentInvokerBuilder.getInvoker(qcAiAgent);

        SseEmitter emitter = client.subscribe(qcAiAgentChatDTO.getClientId());
        chatThreadPool.submit(ContextUtil.wrapWithContext((() -> {
            QcAiAgentConversation qcAiAgentConversation = initQcAiAgentConversation(qcAiAgentChatDTO);
            LLARequest llaRequest = initLLARequest(qcAiAgentConversation.getId(),
                    qcAiAgentChatDTO.getSessionId(),
                    qcAiAgentChatDTO.getQuestion(),
                    qcAiAgent,
                    qcAiAgentChatDTO.getUserId(),
                    qcAiAgentChatDTO.getUserName(),
                    qcAiAgentChatDTO.getDeptId(),
                    qcAiAgentChatDTO.getDeptName(),
                    qcAiAgentChatDTO.getClientId(), "1");
            LLAConfig llaConfig = initLLAConfig(qcAiAgent, qcAiAgentModel, QcAiAgent.INTERNAL_AGENT.equals(qcAiAgent.getInternalFlag()));
            invoker.invoke(qcAiAgent, llaConfig, llaRequest, client);
        })));

        return emitter;
    }


    @Override
    public void exportHistoryConversation(QcAiAgentConversationParams model, HttpServletResponse response) {
        try {
            QcAiAgent qcAiAgent = qcAiAgentMapper.selectById(model.getAgentId());

            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");

            // 生成文件名
            String fileName = generateFileName(qcAiAgent.getName());
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");

            // 分页查询配置
            model.setRows(1000); // 每页1000条数据
            Long total = (long) qcAiAgentConversationMapper.queryHistoryConversationsCount(model);
            int totalPages = (int) Math.ceil(total * 1.0 / model.getRows());

            // 使用EasyExcel分页写入
            EasyExcel.write(response.getOutputStream(), ExportHistoryConversationDTO.class)
                    .sheet("会话记录")
                    .registerWriteHandler(new CustomColumnWidthStyleStrategy())
                    .doWrite(() -> {
                        List<ExportHistoryConversationDTO> allData = new ArrayList<>();
                        for (int pageNo = 1; pageNo <= totalPages; pageNo++) {
                            model.setPage(pageNo);
                            List<ConversationVO> conversationList = qcAiAgentConversationMapper.queryHistoryConversations(model);
                            if (conversationList != null) {
                                allData.addAll(conversationList.stream()
                                        .map(this::convertToExportDTO)
                                        .collect(Collectors.toList()));
                            }
                        }
                        return allData;
                    });

        } catch (IOException e) {
            log.error("导出会话记录失败", e);
        }
    }

    @Override
    public Message saleAnalysisChat(QcAiAgentChatDTO qcAiAgentChatDTO) {
        Message message = Message.of();
        message.setCode("1");
        QcAiAgent qcAiAgent = qcAiAgentMapper.selectById(qcAiAgentChatDTO.getAgentId());
        qcAiAgent.setDeptIdList(qcAiAgentChatDTO.getDeptIdList());
        qcAiAgent.setIsNew(qcAiAgentChatDTO.getIsNew());
        if (qcAiAgentChatDTO.getQuestionType() != null) {
            qcAiAgent.setQuestionType(qcAiAgentChatDTO.getQuestionType());
        }
        qcAiAgent.setVisitAgentParam(qcAiAgentChatDTO.getVisitAgentParam());
        qcAiAgent.setConversationId(qcAiAgentChatDTO.getConversationId());
        qcAiAgent.setCommonParam(qcAiAgentChatDTO.getCommonParam());
        QcAiAgentModel qcAiAgentModel = qcAiAgentModelMapper.selectById(qcAiAgent.getModelId());

        SseEmitterClient testClient = SseEmitterClient.newBuilder();
        if (qcAiAgentModel == null) {
            doKeyExceptionSend(testClient);
            return message;
        }

        qcAiAgent.setDataFetchUrl(String.format("%s/%s", appsvrDominUrl, qcAiAgent.getDataFetchUrl()));
        QcAiAgentConversation qcAiAgentConversation = initQcAiAgentConversation(qcAiAgentChatDTO);
        LLARequest llaRequest = initLLARequest(qcAiAgentConversation.getId(),
                qcAiAgentChatDTO.getSessionId(),
                qcAiAgentChatDTO.getQuestion(),
                qcAiAgent,
                qcAiAgentChatDTO.getUserId(),
                qcAiAgentChatDTO.getUserName(),
                qcAiAgentChatDTO.getDeptId(),
                qcAiAgentChatDTO.getDeptName(),
                qcAiAgentChatDTO.getClientId(), "1");
        LLAConfig llaConfig = initLLAConfig(qcAiAgent, qcAiAgentModel, QcAiAgent.INTERNAL_AGENT.equals(qcAiAgent.getInternalFlag()));
        String question = llaRequest.getContent();
        JSONObject param = new JSONObject();
        param.put("question", question);
        param.put("chatId", llaRequest.getSessionId());
        param.put("agentId", "1");
        String data = AgentBizDataSupport.fetchBizData(qcAiAgent, param);
        if (ObjectUtil.isEmpty(data)) {
            message.setData("");
            return message;
        }
        JSONObject resultData = JSONObject.parseObject(data);
        // 建议
//        JSONArray resultArray = resultData.getJSONArray("result");
//        if (CollectionUtil.isNotEmpty(resultArray)) {
//            llaRequest.setContent(String.format(qcAiAgent.getPrompt(), resultArray.toJSONString()) + dataUtils.getCurrentDate() + question);
//            LLAResponse llaRep = WorkflowLLAClient.send(llaConfig, llaRequest);
//            resultData.put("suggestion", llaRep.getContent());
//        }
        JSONObject tokenUsage = resultData.getJSONObject("tokenUsage");

        resultData.remove("tokenUsage");
        message.setData(resultData);
        LLAResponse llaResponse = LLAResponse.builder().usage(new LLAUsage()).content(data).build();
        LLAUsage llaUsage = new LLAUsage();
        llaUsage.setCompletionTokens(tokenUsage.getInteger("outputTokenCount"));
        llaUsage.setPromptTokens(tokenUsage.getInteger("inputTokenCount"));
        llaUsage.setTotalTokens(tokenUsage.getInteger("totalTokenCount"));
        llaResponse.setUsage(llaUsage);
        llaCompletionListener.onComplete(llaResponse, llaRequest);
        return message;
    }

    private String generateFileName(String agentName) throws IOException {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        String formattedDate = LocalDateTime.now().format(formatter);
        String randomNumber = String.valueOf((int) (Math.random() * 10000));
        return URLEncoder.encode(agentName + formattedDate + randomNumber, StandardCharsets.UTF_8.toString()).replace("\\+", "%20");
    }

    private ExportHistoryConversationDTO convertToExportDTO(ConversationVO conversation) {
        return ExportHistoryConversationDTO.builder()
                .createTime(conversation.getCreateTime())
                .creatorName(conversation.getCreatorName())
                .question(conversation.getQuestion())
                .answerTime(conversation.getAnswerTime())
                .answer(conversation.getAnswer())
                .tokens(conversation.getTokens())
                .conversationTime(conversation.getConversationTime())
                .conversationStatus("-1".equals(conversation.getConversationStatus()) ? "失败" : "成功")
                .evaluationType("1".equals(conversation.getEvaluationType()) ? "满意" : "0".equals(conversation.getEvaluationType()) ? "不满意" : "未反馈")
                .feedback(conversation.getFeedback())
                .intentName(conversation.getIntentName())
                .build();
    }

    public LLAConfig initLLAConfig(QcAiAgent qcAiAgent, QcAiAgentModel qcAiAgentModel, boolean isInnerAgent) {
        LLAConfig build = LLAConfig.builder()
                .apiKey(qcAiAgentModel.getKey())
                .secretKey(qcAiAgentModel.getSecret())
                .stream(true)
                .provider(LLAProvider.getEnumByValue(qcAiAgentModel.getVendor()))
                .model(qcAiAgentModel.getModel())
                .thinkingOptions(LLAThinkingOptions.builder()
                        .type(qcAiAgentModel.getThinking())
                        .build())
                .endpoint(qcAiAgentModel.getModelEndPoint())
                .aipApplicationId(qcAiAgentModel.getAipApplicationId())
                .aipUserId(qcAiAgentModel.getAipUserId())
                .build();
        if (qcAiAgent != null) {
            build.setModelTemperature(qcAiAgent.getModelTemperature());
            build.setModelTopP(qcAiAgent.getModelTopP());
            build.setModelMaxTokens(qcAiAgent.getModelMaxTokens());
        } else {
            build.setModelTemperature(new BigDecimal("0.5"));
            build.setModelTopP(new BigDecimal("0.5"));
            build.setModelMaxTokens(new BigDecimal("1000"));
        }
        if (isInnerAgent) {
            setDefaultKey(build, qcAiAgentModel, qcAiAgent.getId());
        }
        return build;
    }

    private void setDefaultKey(LLAConfig build, QcAiAgentModel qcAiAgentModel, Long agentId) {
        LLAProvider providerEnum = LLAProvider.getEnumByValue(qcAiAgentModel.getVendor());
        if (providerEnum != null) {
            switch (providerEnum) {
                case ALI:
                    build.setApiKey(llaKeyProperties.getAliApikey());
                    break;
                case COZE:
                    build.setApiKey(llaKeyProperties.getCozeToken());
                    build.setSecretKey(llaKeyProperties.getCozeBotid());
                    break;
                case KIMI:
                    build.setApiKey(llaKeyProperties.getKimiApikey());
                    break;
                case BAIDU:
                    build.setApiKey(llaKeyProperties.getBaiduApikey());
                    build.setSecretKey(llaKeyProperties.getBaiduSecretkey());
                    break;
                case ZHIPU:
                    build.setApiKey(llaKeyProperties.getZhipuApikey());
                    break;
                case DOUBAO:
                    if (agentId != 6) {
                        build.setApiKey(llaKeyProperties.getDoubaoApikey());
                    } else {
                        build.setApiKey(llaKeyProperties.getDoubaoSecondApikey());
                    }
                    break;
                case OPENAI:
                    build.setApiKey(llaKeyProperties.getOpenaiApikey());
                    break;
                case TENCENT:
                    build.setApiKey(llaKeyProperties.getTencentSecretid());
                    build.setSecretKey(llaKeyProperties.getTencentSecretkey());
                    break;
                case DEEPSEEK:
                    build.setApiKey(llaKeyProperties.getDeepseekApikey());
                    break;
                default:
                    break;
            }
        }
    }

    private LLARequest initLLARequest(Long questionId, Long sessionId, String question, QcAiAgent qcAiAgent, Long userId,
                                      String userName, Long deptId, String deptName, String clientId, String status) {
        List<ConversationVO> conversationVOS = new ArrayList<>();
        if (!Objects.equals(qcAiAgent.getIsNew(), "1")) {
            conversationVOS = qcAiAgentConversationMapper.queryAgentConversation(qcAiAgent.getId(),
                    UserManager.getTenantUser().getUserId(), 1, 0, status);
        }
        return LLARequest.builder()
                .id(questionId == null ? null : Objects.toString(questionId))
                .sessionId(sessionId)
                .clientId(clientId)
                .content(question)
                .userId(userId)
                .userName(userName)
                .deptId(deptId)
                .deptName(deptName)
                .qas(Optional.ofNullable(conversationVOS)
                        .orElse(Lists.newArrayList())
                        .stream()
                        .map(item -> LLAQa.builder()
                                .question(item.getQuestion())
                                .answer(item.getAnswer())
                                .build())
                        .collect(Collectors.toList()))
                .build();
    }

    public QcAiAgentConversation initQcAiAgentConversation(QcAiAgentChatDTO qcAiAgentChatDTO) {
        if (qcAiAgentChatDTO.getConversationId() != null) {
            QcAiAgentConversation conversation = qcAiAgentConversationMapper.selectById(qcAiAgentChatDTO.getConversationId());
            // 答案为空说明先走了判断意图的接口，预先生成了会话记录（为了保存判断意图的token），但是没有生成答案,此时直接用预先生成的会话记录即可
            if (StringUtils.isEmpty(conversation.getAnswer())) {
                return conversation;
            }
        }
        QcAiAgentConversation qcAiAgentConversation = QcAiAgentConversationTool.assembleConversation(qcAiAgentChatDTO);
        qcAiAgentConversationMapper.insert(qcAiAgentConversation);
        return qcAiAgentConversation;
    }

    @Override
    public Message evaluateConversation(ConversationEvaluationDTO evaluationDTO) {
        qcAiAgentConversationMapper.updateEvaluation(evaluationDTO);
        return Message.of().ok();
    }

    @Override
    public List<CommonFeedbackDTO> getCommonFeedback(String feedbackType) {
        return qcAiAgentConversationMapper.getCommonFeedback(feedbackType);
    }

    @Override
    public void stopChat(ChatStopDTO chatStopDTO) {
        client.removeSse(chatStopDTO.getClientId());
    }

    @Override
    public SseEmitter testConversation(QcAiAgentTestDTO qcAiAgentTestDTO) {
        QcAiAgentModel qcAiAgentModel = qcAiAgentModelMapper.selectById(qcAiAgentTestDTO.getModelId());
        if (qcAiAgentModel == null) {
            return null;
        }
        QcAiAgent qcAiAgent = qcAiAgentMapper.selectById(qcAiAgentTestDTO.getAgentId());
        assemblyQcAgentParam(qcAiAgentTestDTO, qcAiAgent);
        assembleQcAgentParam(qcAiAgent, qcAiAgentModel);

        SseEmitter emitter = client.subscribe(qcAiAgentTestDTO.getClientId());
        chatThreadPool.submit(ContextUtil.wrapWithContext((() -> {
            // 插入会话记录-状态为删除
            QcAiAgentConversation qcAiAgentConversation = initQcAiAgentConversation(qcAiAgentTestDTO);
            IAgentInvoker invoker = AgentInvokerBuilder.getInvoker(qcAiAgent);
            invoker.invoke(qcAiAgent, initLLAConfig(qcAiAgent, qcAiAgentModel, "1".equals(qcAiAgent.getInternalFlag())),
                    initLLARequest(qcAiAgentConversation.getId(),
                            qcAiAgentConversation.getSessionId(),
                            qcAiAgentTestDTO.getQuestion(), qcAiAgent,
                            qcAiAgentTestDTO.getUserId(), qcAiAgentTestDTO.getUserName(),
                            qcAiAgentTestDTO.getDeptId(), qcAiAgentTestDTO.getDeptName(),
                            qcAiAgentTestDTO.getClientId(), "0"), client);
        })));
        return emitter;
    }

    private void assembleQcAgentParam(QcAiAgent qcAiAgent, QcAiAgentModel qcAiAgentModel) {
        if (Objects.equals(qcAiAgentModel.getVendor(), "Kimi") && !Objects.equals(qcAiAgentModel.getModel(), "kimi-latest")) {
            // 按单个用户有文件上传1000个的限制，暂不使用kimi的缓存服务
            // qcAiAgent.setCacheTag(UserManager.getTenantUser().getTenantId() + "-" + qcAiAgent.getId() + "-cache");
        }
        qcAiAgent.setModel(qcAiAgentModel.getModel());
        qcAiAgent.setApiKey(qcAiAgentModel.getKey());
        qcAiAgent.setVendor(qcAiAgentModel.getVendor());
        qcAiAgent.setModelEndPoint(qcAiAgentModel.getModelEndPoint());
    }

    private void assembleQcAgentParam(QcAiAgent qcAiAgent, QcAiAgentChatDTO qcAiAgentChatDTO) {
        qcAiAgent.setDeptIdList(qcAiAgentChatDTO.getDeptIdList());
        qcAiAgent.setIsNew(qcAiAgentChatDTO.getIsNew());
        if (qcAiAgentChatDTO.getQuestionType() != null) {
            qcAiAgent.setQuestionType(qcAiAgentChatDTO.getQuestionType());
        }
        qcAiAgent.setVisitAgentParam(qcAiAgentChatDTO.getVisitAgentParam());
        qcAiAgent.setConversationId(qcAiAgentChatDTO.getConversationId());
        qcAiAgent.setCommonParam(qcAiAgentChatDTO.getCommonParam());
        qcAiAgent.setDataFetchUrl(String.format("%s/%s", appsvrDominUrl, qcAiAgent.getDataFetchUrl()));
    }

    public QcAiAgentConversation initQcAiAgentConversation(QcAiAgentTestDTO qcAiAgentChatDTO) {
        QcAiAgentConversation qcAiAgentConversation = assembleConversation(qcAiAgentChatDTO);
        qcAiAgentConversationMapper.insert(qcAiAgentConversation);
        return qcAiAgentConversation;
    }

    public QcAiAgentConversation assembleConversation(QcAiAgentTestDTO qcAiAgentChatDTO) {
        return QcAiAgentConversation.builder()
                .id(UUIDUtils.getUUID2Long())
                .status("0")
                .creatorId(qcAiAgentChatDTO.getUserId())
                .creatorName(qcAiAgentChatDTO.getUserName())
                .createTime(LocalDateTime.now())
                .userId(UserManager.getTenantUser().getUserId())
                .agentId(qcAiAgentChatDTO.getAgentId())
                .conversationStatus(QcAiAgentConversation.IN_CONVERSATION)
                .question(qcAiAgentChatDTO.getQuestion())
                .source(qcAiAgentChatDTO.getSource())
                .sessionId(qcAiAgentChatDTO.getSessionId())
                .chatSessionId(qcAiAgentChatDTO.getChatSessionId() == null ? UUIDUtils.getUUID2Long() + "" : qcAiAgentChatDTO.getChatSessionId())
                .build();
    }

    private void assemblyQcAgentParam(QcAiAgentTestDTO qcAiAgentTestDTO, QcAiAgent qcAiAgent) {
        qcAiAgent.setSplitSqlPrompt(qcAiAgentTestDTO.getSplitSqlPrompt());
        qcAiAgent.setPrompt(qcAiAgentTestDTO.getPrompt());
        qcAiAgent.setModelId(qcAiAgentTestDTO.getModelId());
        qcAiAgent.setModelTemperature(qcAiAgentTestDTO.getModelTemperature());
        qcAiAgent.setKnowledgeIdList(qcAiAgentTestDTO.getKnowledgeIdList());
        qcAiAgent.setMaxRecallCount(qcAiAgentTestDTO.getMaxRecallCount());
        qcAiAgent.setMinMatchThreshold(qcAiAgentTestDTO.getMinMatchThreshold());
        qcAiAgent.setQaMinMatchThreshold(qcAiAgentTestDTO.getQaMinMatchThreshold());
        qcAiAgent.setSearchScope(qcAiAgentTestDTO.getSearchScope());
        qcAiAgent.setNullResultAnswer(qcAiAgentTestDTO.getNullResultAnswer());
        qcAiAgent.setModelMaxTokens(qcAiAgentTestDTO.getModelMaxTokens());
        qcAiAgent.setModelTopP(qcAiAgentTestDTO.getModelTopP());
        qcAiAgent.setModelTemperature(qcAiAgentTestDTO.getModelTemperature());
        qcAiAgent.setDeptIdList(qcAiAgentTestDTO.getDeptIdList());
        if (qcAiAgentTestDTO.getQuestionType() != null) {
            qcAiAgent.setQuestionType(qcAiAgentTestDTO.getQuestionType());
        }
        qcAiAgent.setVisitAgentParam(qcAiAgentTestDTO.getVisitAgentParam());
        qcAiAgent.setIsNew(qcAiAgentTestDTO.getIsNew());
        qcAiAgent.setCommonParam(qcAiAgentTestDTO.getCommonParam());
        if (qcAiAgentTestDTO.getIntentIsEnabled() != null) {
            qcAiAgent.setIntentIsEnabled(qcAiAgentTestDTO.getIntentIsEnabled());
        }
        if (qcAiAgentTestDTO.getIntentList() != null) {
            qcAiAgent.setIntentList(qcAiAgentTestDTO.getIntentList());
        }
        qcAiAgent.setDataFetchUrl(String.format("%s/%s", appsvrDominUrl, qcAiAgent.getDataFetchUrl()));
    }

    private void doKeyExceptionSend(SseEmitterClient client) {
        String[] split = "该AGENT使用的模型的KEY已失效，请联系创建者/管理员调整后重新发布后再使用。".split("");
        for (String s : split) {
            client.send(Message.of().ok().data(SseMessage.builder().message(s).messageType(MessageType.TEXT).isEnd(false).build()));
        }
        client.send(Message.of().ok().data(SseMessage.builder().messageType(MessageType.TEXT).isEnd(true).build()));
        try {
            Thread.sleep(500);
        } catch (InterruptedException e) {
            log.error("sleep异常", e);
        }
        client.complete();
    }

    @Override
    public JSONObject queryHistoryConversation(QcAiAgentConversationParams model) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("rows", qcAiAgentConversationMapper.queryHistoryConversations(model));
        jsonObject.put("total", qcAiAgentConversationMapper.queryHistoryConversationsCount(model));
        return jsonObject;
    }

    public static final String CONDITION_PROMPT = "紧密相关，可以引发进一步的讨论。\n"
            + "每句话只包含一个问题。\n"
            + "推荐你有能力回答的问题。"
            + "推荐的问题不少于三个。"
            + "你的回答中只包含问题。\n";

    @Override
    public String generateDefaultQuestion(QcAiAgentChatQuery query) {
        log.info("调用KiMI生成默认问题");
        LLARequest request = LLARequest.builder()
                .id(Objects.toString(UUIDUtils.getUUID2Long()))
                .content("问题应该与" + query.getDesc() + CONDITION_PROMPT)
                .build();
        // KIMI
        QcAiAgentModel qcAiAgentModel = qcAiAgentModelMapper.selectById(93L);
        LLAResponse conditionResponse = WorkflowLLAClient.send(LLMTools.initLLAConfig(qcAiAgentModel), request, false);
        log.info("调用KiMI生成默认问题 conditionResponse:{}", JSON.toJSONString(conditionResponse));
        // 使用正则表达式分割问题
        List<String> list = extractFirstThreeQuestions(conditionResponse.getContent());
        return convertListToString(list);
    }

    public static List<String> extractFirstThreeQuestions(String input) {
        List<String> result = new ArrayList<>();
        String[] questionArray = input.split("\\d+\\.\\s");

        for (int i = 1; i <= 3 && i < questionArray.length; i++) {
            // 只保留问题内容，去除序号和多余换行
            String questionContent = questionArray[i].trim().replaceAll("\n+", "");
            result.add(questionContent);
        }
        return result;
    }

    public static String convertListToString(List<String> list) {
        StringBuilder sb = new StringBuilder();
        sb.append("[");

        for (int i = 0; i < list.size(); i++) {
            sb.append("\"")
                    .append(list.get(i).trim()) // 去除多余空格
                    .append("\"");

            if (i < list.size() - 1) {
                sb.append(","); // 最后一个元素后不加逗号
            }
        }

        sb.append("]");
        return sb.toString();
    }

    @Override
    public Message recommendPrompt(QcAiAgentRecommendDTO recommendationDTO) {
        QcAiAgent qcAiAgent = qcAiAgentMapper.selectById(recommendationDTO.getAgentId());
        QcAiAgentModel qcAiAgentModel = qcAiAgentModelMapper.selectById(qcAiAgent.getModelId());
        qcAiAgent.setCommonParam(recommendationDTO.getCommonParam());
        IAgentInvoker invoker = AgentInvokerBuilder.getInvoker(qcAiAgent);
        QcAiAgentConversation qcAiAgentConversation = qcAiAgentConversationMapper.selectById(recommendationDTO.getConversationId());
        return invoker.recommendPrompt(
                qcAiAgent,
                LLMTools.initLLAConfig(qcAiAgentModel),
                initLLARequest(
                        recommendationDTO.getAgentId(),
                        qcAiAgentConversation)
                , qcAiAgentConversation);
    }

    private LLARequest initLLARequest(Long agentId, QcAiAgentConversation qcAiAgentConversation) {
        List<ConversationVO> conversationVOS = qcAiAgentConversationMapper.queryAgentConversation(agentId,
                UserManager.getTenantUser().getUserId(), 1, 0, "1");
        return LLARequest.builder()
                .id(qcAiAgentConversation.getId() + "")
                .content(qcAiAgentConversation.getQuestion())
                .qas(Optional.ofNullable(conversationVOS)
                        .orElse(Lists.newArrayList())
                        .stream()
                        .map(item -> LLAQa.builder()
                                .question(item.getQuestion())
                                .answer(item.getAnswer())
                                .build())
                        .collect(Collectors.toList()))
                .build();
    }
}
