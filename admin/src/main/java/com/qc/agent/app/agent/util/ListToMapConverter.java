package com.qc.agent.app.agent.util;

import org.apache.commons.beanutils.BeanUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-04-02
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
public class ListToMapConverter {
    public static <T> List<Map<String, Object>> toPropertyMapList(List<T> list) {
        List<Map<String, Object>> result = new ArrayList<>();
        if (list == null || list.isEmpty()) {
            return result;
        }

        for (T obj : list) {
            Map<String, Object> map = new HashMap<>();
            try {
                // 使用 BeanUtils 获取所有属性
                map.putAll(BeanUtils.describe(obj));
            } catch (Exception e) {
                throw new RuntimeException("属性描述失败", e);
            }
            // 移除多余的 "class" 属性
            map.remove("class");
            result.add(map);
        }

        return result;
    }
}
