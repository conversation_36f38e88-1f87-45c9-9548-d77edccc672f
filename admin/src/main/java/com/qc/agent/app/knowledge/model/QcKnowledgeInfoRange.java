package com.qc.agent.app.knowledge.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QcKnowledgeInfoRange {
    /**
     * 主键 ID
     */
    private Long id;

    /**
     * 状态 (默认 '1')
     */
    private String status;

    /**
     * 创建人 ID
     */
    private Long creatorId;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改人 ID
     */
    private Long modifyierId;

    /**
     * 修改人姓名
     */
    private String modifyierName;

    /**
     * 修改时间
     */
    private LocalDateTime modifyTime;

    /**
     * 部门或员工 ID
     */
    private Long rangeId;

    /**
     * 部门或员工名称
     */
    private String rangeName;

    /**
     * 范围类型 (1：部门，2：员工)
     */
    private String rangeType;

    /**
     * 知识库id
     */
    private Long knowledgeId;
}
