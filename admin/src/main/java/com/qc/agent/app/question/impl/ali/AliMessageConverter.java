package com.qc.agent.app.question.impl.ali;

import com.alibaba.fastjson.JSONObject;
import com.qc.agent.platform.sse.SseMessage;
import com.qc.agent.platform.sse.SseMessageConverter;
import com.qc.agent.platform.sse.SseRequest;
import com.qc.agent.platform.sse.dto.MessageType;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class AliMessageConverter implements SseMessageConverter<String> {
    @Override
    public SseMessage convert(String content, SseRequest request) {
        if(content.startsWith("data:")){
            JSONObject jsonObject =  JSONObject.parseObject(content.substring("data:".length()));
            JSONObject output = jsonObject.getJSONObject("output");
            SseMessage message = SseMessage.builder().message(output.getString("text")).messageType(MessageType.TEXT).build();
            if("stop".equals(output.getString("finish_reason"))){
                message.setEnd(true);
            }
            return message;
        }else{
            return SseMessage.builder().message("").messageType(MessageType.TEXT).build();
        }

    }

}
