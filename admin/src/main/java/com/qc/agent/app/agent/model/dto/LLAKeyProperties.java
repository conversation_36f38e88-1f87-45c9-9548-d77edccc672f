package com.qc.agent.app.agent.model.dto;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Data
public class LLAKeyProperties {

    @Value("${ai.agent.zhipu-apikey}")
    private String zhipuApikey;

    @Value("${ai.agent.tencent-secretid}")
    private String tencentSecretid;

    @Value("${ai.agent.tencent-secretkey}")
    private String tencentSecretkey;

    @Value("${ai.agent.baidu-apikey}")
    private String baiduApikey;

    @Value("${ai.agent.baidu-secretkey}")
    private String baiduSecretkey;

    @Value("${ai.agent.ali-apikey}")
    private String aliApikey;

    @Value("${ai.agent.coze-token}")
    private String cozeToken;

    @Value("${ai.agent.coze-botid}")
    private String cozeBotid;

    @Value("${ai.agent.doubao-apikey}")
    private String doubaoApikey;

    @Value("${ai.agent.doubao-second-apikey}")
    private String doubaoSecondApikey;

    @Value("${ai.agent.openai-apikey}")
    private String openaiApikey;

    @Value("${ai.agent.kimi-apikey}")
    private String kimiApikey;

    @Value("${ai.agent.deepseek-apikey}")
    private String deepseekApikey;

}
