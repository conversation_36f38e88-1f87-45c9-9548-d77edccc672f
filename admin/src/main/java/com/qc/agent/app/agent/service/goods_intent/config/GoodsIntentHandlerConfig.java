package com.qc.agent.app.agent.service.goods_intent.config;

import com.qc.agent.app.agent.service.goods_intent.GoodsIntentHandler;
import com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.model.enums.GoodsIntent;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-06-26
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Configuration
public class GoodsIntentHandlerConfig {
    @Bean
    public Map<GoodsIntent, GoodsIntentHandler> goodsIntentHandlerMap(List<GoodsIntentHandler> handlerList) {
        return handlerList.stream()
                .collect(Collectors.toMap(
                        GoodsIntentHandler::getSupportedIntent,
                        Function.identity()
                ));
    }
}
