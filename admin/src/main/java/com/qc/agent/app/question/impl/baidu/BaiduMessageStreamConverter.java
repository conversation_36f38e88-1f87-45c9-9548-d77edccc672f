package com.qc.agent.app.question.impl.baidu;

import com.alibaba.fastjson.JSONObject;
import com.qc.agent.platform.sse.SseMessage;
import com.qc.agent.platform.sse.SseMessageConverter;
import com.qc.agent.platform.sse.SseRequest;
import com.qc.agent.platform.sse.dto.MessageType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

@Slf4j
public class BaiduMessageStreamConverter implements SseMessageConverter<String> {
    @Override
    public SseMessage convert(String content, SseRequest request) {
        if(!StringUtils.hasText(content)){
            return SseMessage.builder().message(content).messageType(MessageType.TEXT).build();
        }
        JSONObject jsonObject =  extractContentFromLine(content);

        String result = jsonObject.getString("result");
        SseMessage message = SseMessage.builder().message(result).messageType(MessageType.TEXT).build();
        if(Boolean.valueOf(jsonObject.getString("is_end"))){
            message.setEnd(true);
        }
         return message;
    }



    private JSONObject extractContentFromLine(String line) {
        String dataPrefix = "data: {";

        if (line.startsWith(dataPrefix)) {
            return  JSONObject.parseObject(line.substring("data: ".length()));
        }

        return null;
    }

}
