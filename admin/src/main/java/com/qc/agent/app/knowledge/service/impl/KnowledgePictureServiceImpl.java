package com.qc.agent.app.knowledge.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.qc.agent.app.agent.util.DataBaseMapperUtils;
import com.qc.agent.app.agent.util.JsonUtils;
import com.qc.agent.app.knowledge.mapper.KnowledgeFileMapper;
import com.qc.agent.app.knowledge.mapper.QcKnowledgeExcelDataMapper;
import com.qc.agent.app.knowledge.model.QcKnowledgeExcelData;
import com.qc.agent.app.knowledge.model.QcKnowledgeFileParams;
import com.qc.agent.app.knowledge.model.query.KnowledgePictureQuery;
import com.qc.agent.app.knowledge.service.KnowledgePictureService;
import com.qc.agent.app.knowledge.utils.KnowledgeLogUtils;
import com.qc.agent.app.workflow.inner.pojo.QcUploadFile;
import com.qc.agent.common.core.RequestHolder;
import com.qc.agent.common.core.TenantUser;
import com.qc.agent.common.util.UUIDUtils;
import com.qc.agent.lla.model.LLAResponse;
import com.qc.agent.lla.sdk.model_supplier.impl.DoubaoClient;
import com.qc.agent.platform.register.UserManager;
import com.qc.agent.utils.FileUtils;
import com.qc.agent.vectordb.tencentdb.VectorDBService;
import com.tencent.tcvectordb.model.DocField;
import com.tencent.tcvectordb.model.Document;
import com.tencent.tcvectordb.model.param.dml.DeleteParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.dromara.x.file.storage.core.FileInfo;
import org.dromara.x.file.storage.core.FileStorageService;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicLong;

import static com.qc.agent.app.constants.CommonConstants.QA_COLLECTION_PREFIX;
import static com.qc.agent.app.constants.CommonConstants.QA_DATABASE_PREFIX;

/**
 * Knowledge Picture Service Implementation.
 * Handles the import, analysis, vectorization, and persistence of picture-based
 * knowledge.
 * This service uses an asynchronous, transactional approach for processing each
 * picture.
 *
 * <AUTHOR>
 * @date 2025-07-11
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Slf4j
@Service
@Lazy
public class KnowledgePictureServiceImpl implements KnowledgePictureService {

    private static final String KEY_URL = "url";
    private static final String KEY_CONTENT = "content";
    private static final String KEY_LIST = "list";
    private static final String CONTENT_JSON_KEY = "content";

    private final FileStorageService fileStorageService;
    private final DoubaoClient doubaoClient;
    private final ObjectMapper objectMapper;
    private final VectorDBService vectorDBService;
    private final ExecutorService knowledgeImportExecutor;
    private final KnowledgeFileMapper knowledgeFileMapper;
    private final QcKnowledgeExcelDataMapper qcKnowledgeExcelDataMapper;

    private final String ossBasePath;
    private final String ossDomain;
    private final String ossPlatform;

    public KnowledgePictureServiceImpl(
            FileStorageService fileStorageService,
            DoubaoClient doubaoClient,
            ObjectMapper objectMapper,
            VectorDBService vectorDBService,
            KnowledgeFileMapper knowledgeFileMapper,
            QcKnowledgeExcelDataMapper qcKnowledgeExcelDataMapper,
            @Qualifier("knowledgeImportExecutor") ExecutorService knowledgeImportExecutor,
            @Value("${dromara.x-file-storage.aliyun-oss[0].base-path}") String ossBasePath,
            @Value("${dromara.x-file-storage.aliyun-oss[0].domain}") String ossDomain,
            @Value("${dromara.x-file-storage.aliyun-oss[0].platform}") String ossPlatform) {
        this.fileStorageService = fileStorageService;
        this.doubaoClient = doubaoClient;
        this.objectMapper = objectMapper;
        this.vectorDBService = vectorDBService;
        this.knowledgeFileMapper = knowledgeFileMapper;
        this.qcKnowledgeExcelDataMapper = qcKnowledgeExcelDataMapper;
        this.knowledgeImportExecutor = knowledgeImportExecutor;
        this.ossBasePath = ossBasePath;
        this.ossDomain = ossDomain;
        this.ossPlatform = ossPlatform;
    }

    @Override
    public void importPicture(KnowledgePictureQuery query) {
        var picturePathList = query.getPicturePathList();
        if (picturePathList == null || picturePathList.isEmpty()) {
            log.warn("No pictures to import for collectionId: {}. Aborting.", query.getCollectionId());
            return;
        }

        final TenantUser tenantUser = UserManager.getTenantUser();
        final var logPrefix = KnowledgeLogUtils.getLogPrefix(tenantUser.getTenantId(), query.getCollectionId());
        log.info("{} Starting to import {} pictures.", logPrefix, picturePathList.size());
        Map<String, Long> fileName2FileRecordIdMap = batchInsertFileRecord(query.getPicturePathList(),
                query.getCollectionId(), UserManager.getTenantUser());
        List<CompletableFuture<Void>> futures = picturePathList.stream()
                .map(file -> processSinglePictureAsync(file, query.getCollectionId(), tenantUser,
                        fileName2FileRecordIdMap, logPrefix)
                        // 为每个异步任务单独添加异常处理，防止一个失败导致整体中断
                        .exceptionally(ex -> {
                            log.error("{} Failed to process picture: {}", logPrefix, file, ex);
                            return null; // 返回 null 以便 allOf 可以继续
                        }))
                .toList();

        // 不阻塞主线程，而是在所有任务完成后（无论成功或失败）执行一个动作
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .whenComplete((result, throwable) -> {
                    if (throwable != null) {
                        // 这是 allOf 本身的异常，例如在组合 future 时出错，比较罕见
                        log.error("{} An unexpected error occurred while waiting for picture imports to complete.",
                                logPrefix, throwable);
                    } else {
                        // 所有任务都已执行完毕（成功或通过 exceptionally 处理了失败）
                        log.info("{} Finished processing all pictures for collectionId: {}.", logPrefix,
                                query.getCollectionId());
                    }
                });

        // 主线程在这里立即返回，上面的 whenComplete 中的代码将在后台线程池中执行
        log.info("{} Picture import request accepted for collectionId: {}. Processing will continue in the background.",
                logPrefix, query.getCollectionId());
    }

    private Map<String, Long> batchInsertFileRecord(List<QcUploadFile> list, Long collectionId,
            TenantUser currentUser) {
        Map<String, Long> fileName2FileRecordIdMap = new HashMap<>();
        List<QcKnowledgeFileParams> insertList = new ArrayList<>();
        for (QcUploadFile qcUploadFile : list) {
            knowledgeFileMapper.deleteByFileName(qcUploadFile.getName(), collectionId);
            var qcKnowledgeFileParams = new QcKnowledgeFileParams();
            qcKnowledgeFileParams.setId(UUIDUtils.getUUID2Long());
            qcKnowledgeFileParams.setFileStatus("3");
            qcKnowledgeFileParams.setCreateTime(new Date());
            qcKnowledgeFileParams.setModifyTime(new Date());
            qcKnowledgeFileParams.setCreateUserId(currentUser.getUserId());
            qcKnowledgeFileParams.setCreateUserName(currentUser.getUserName());
            qcKnowledgeFileParams.setFolderId(1L);
            qcKnowledgeFileParams.setCollectionId(collectionId);
            qcKnowledgeFileParams.setFileName(qcUploadFile.getName());
            qcKnowledgeFileParams.setFileType(qcUploadFile.getExt());
            qcKnowledgeFileParams.setFileUrl("knowledgeBase/" + collectionId + "/" + qcUploadFile.getName());
            qcKnowledgeFileParams.setFileSize(String.valueOf(qcUploadFile.getSize()));
            qcKnowledgeFileParams.setDataImpType("PICTURE");
            insertList.add(qcKnowledgeFileParams);
            fileName2FileRecordIdMap.put(qcKnowledgeFileParams.getFileName(), qcKnowledgeFileParams.getId());
        }
        DataBaseMapperUtils.batchInsert(insertList, knowledgeFileMapper::batchInsert);
        return fileName2FileRecordIdMap;
    }

    private CompletableFuture<Void> processSinglePictureAsync(QcUploadFile qcUploadFile, Long collectionId,
            TenantUser tenantUser, Map<String, Long> fileName2FileRecordIdMap, String logPrefix) {
        return CompletableFuture.runAsync(() -> {
            RequestHolder.setThreadLocalUser(tenantUser);
            try {
                log.info("{} [Async Start] Processing picture: {}", logPrefix, qcUploadFile.getName());
                processAndPersistPicture(qcUploadFile, collectionId, fileName2FileRecordIdMap, logPrefix);
                log.info("{} [Async Success] Successfully processed picture: {}", logPrefix, qcUploadFile.getName());
            } catch (Exception e) {
                log.error("{} [Async Fail] Failed to process picture: {}. Error: {}", logPrefix, qcUploadFile.getName(),
                        e.getMessage(), e);
            } finally {
                RequestHolder.clear();
            }
        }, knowledgeImportExecutor);
    }

    /**
     * Processes a single picture, including content extraction, vectorization, and
     * database persistence.
     * This method is marked as @Transactional to ensure data consistency between
     * the vectorDB and the relational DB.
     *
     * @param qcUploadFile The picture file to process.
     * @param collectionId The ID of the knowledge collection.
     * @param logPrefix    The prefix for logging messages.
     */
    public void processAndPersistPicture(QcUploadFile qcUploadFile, Long collectionId,
            Map<String, Long> fileName2FileRecordIdMap, String logPrefix) {
        try {
            // Step 1: Get picture content, either from cache or by AI analysis.
            String content = getPictureContent(qcUploadFile, logPrefix);

            if (content != null && !content.isBlank()) {
                // Step 2: Assemble vector documents from the content.
                String[] splitContents = content.split("\\n\\n");
                DocumentResult docResult = assembleDocuments(splitContents, qcUploadFile.getName());
                if (docResult.list().isEmpty()) {
                    log.warn("{} No valid document segments generated from picture: {}. Skipping database operations.",
                            logPrefix, qcUploadFile.getName());
                    return;
                }

                // Step 3: Upsert documents into the vector database.
                log.info("{} Upserting {} document segments into vectorDB for picture: {}", logPrefix,
                        docResult.list().size(), qcUploadFile.getName());
                final TenantUser currentUser = RequestHolder.getThreadLocalUser();
                vectorDBService.upsertDocuments(QA_DATABASE_PREFIX + currentUser.getTenantId(),
                        QA_COLLECTION_PREFIX + collectionId,
                        docResult.list(), logPrefix);

                // Step 4: Update the file record in the relational database.
                updateKnowledgeFileRecord(qcUploadFile, collectionId, splitContents, currentUser,
                        fileName2FileRecordIdMap,
                        docResult.content2IdMap());
            } else {
                log.warn("{} No content extracted from picture: {}. Skipping database operations.", logPrefix,
                        qcUploadFile.getName());
            }
        } catch (JsonProcessingException e) {
            log.error("{} Failed to process JSON during picture analysis for file: {}", logPrefix,
                    qcUploadFile.getName(), e);
            throw new RuntimeException("JSON processing error for file " + qcUploadFile.getName(), e);
        }
    }

    private static final String PROMPT = """
        完整提取图片中的内容，并展示为良好，清晰的结构
        """;        

    private String getPictureContent(QcUploadFile qcUploadFile, String logPrefix) throws JsonProcessingException {
        var uri = buildFileUri(qcUploadFile);
        var parsedFilePath = FileUtils.generateParsedFilePath(uri);
        var fullPath = ossBasePath + parsedFilePath;
        var info = new FileInfo().setPlatform(ossPlatform).setFilename(fullPath);

        if (fileStorageService.exists(info)) {
            log.info("{} Cache hit for parsed content of picture: {}", logPrefix,
                    qcUploadFile.getName());
            return readContentFromCache(fullPath, logPrefix);
        }

        log.info("{} Cache miss. Analyzing picture with AI model: {}", logPrefix,
                qcUploadFile.getName());
        verifyPictureExists(uri, logPrefix);
        LLAResponse response = doubaoClient.analyzeImageByUrl(qcUploadFile.getUrl(), PROMPT);
        String content = response.getContent();

        if (content != null && !content.isBlank()) {
            var jsonContent = objectMapper.writeValueAsString(Map.of(CONTENT_JSON_KEY, content));
            uploadParsedContent(parsedFilePath, jsonContent, logPrefix);
        }
        return content;
    }

    private String buildFileUri(QcUploadFile qcUploadFile) {
        var tenantId = UserManager.getTenantUser().getTenantId();
        return Objects.toString(qcUploadFile.getBasePath(), "")
                + tenantId + "/"
                + Objects.toString(qcUploadFile.getPath(), "")
                + Objects.toString(qcUploadFile.getName(), "");
    }

    private String readContentFromCache(String fullPath, String logPrefix) {
        try {
            var fileInfo = fileStorageService.getFileInfoByUrl(ossDomain + fullPath);
            byte[] contentBytes = fileStorageService.download(fileInfo).bytes();
            Map<?, ?> map = objectMapper.readValue(contentBytes, Map.class);
            return (String) map.get(CONTENT_JSON_KEY);
        } catch (IOException | ClassCastException e) {
            log.error("{} Failed to read or parse cached content. Will re-analyze. Path: {}. Error: {}", logPrefix,
                    fullPath, e.getMessage());
            return null;
        }
    }

    private void updateKnowledgeFileRecord(QcUploadFile qcUploadFile, Long collectionId, String[] splitContents,
            TenantUser currentUser, Map<String, Long> fileName2FileRecordIdMap, Map<String, Long> content2IdMap) {
        log.info("Updating knowledge file record for file: {} in collection: {}", qcUploadFile.getName(), collectionId);
        QcKnowledgeFileParams updateParam = new QcKnowledgeFileParams();
        updateParam.setId(fileName2FileRecordIdMap.get(qcUploadFile.getName()));
        updateParam.setViewCount(String.valueOf(splitContents.length));
        updateParam.setFileStatus("1");
        int updateCount = knowledgeFileMapper.updateFile(updateParam);
        if (updateCount > 0) {
            log.info("Successfully update new knowledge file record with id: {}", updateParam.getId());
            insertQcKnowledgeExcelData(splitContents, updateParam.getId(), content2IdMap, currentUser);
        } else {
            log.error("Failed to update new knowledge file record for file: {}. Transaction will be rolled back.",
                    qcUploadFile.getName());
            throw new RuntimeException("Database insertion failed for knowledge file: " + qcUploadFile.getName());
        }
    }

    private void insertQcKnowledgeExcelData(String[] splitContents, Long fileId, Map<String, Long> content2IdMap,
            TenantUser currentUser) {
        if (splitContents == null || splitContents.length == 0) {
            return;
        }
        log.info("Preparing to batch insert {} QA data records for fileId: {}", splitContents.length, fileId);
        long sequ = 0L;
        List<QcKnowledgeExcelData> dataList = new ArrayList<>();
        for (String content : splitContents) {
            String trimmed = content.trim();
            if (!trimmed.isEmpty()) {
                var data = new QcKnowledgeExcelData();
                data.setId(content2IdMap.get(trimmed));
                data.setStatus("1");
                data.setCreateTime(new Date());
                data.setCreateUserId(currentUser.getUserId());
                data.setCreateUserName(currentUser.getUserName());
                data.setQuestion(trimmed);
                data.setAnswer(trimmed);
                data.setFileId(fileId);
                data.setSequ(sequ++);
                dataList.add(data);
            }
        }
        DataBaseMapperUtils.batchInsert(dataList, qcKnowledgeExcelDataMapper::batchInsert);
        log.info("Successfully batch inserted {} QA data records for fileId: {}", dataList.size(), fileId);
    }

    private record DocumentResult(List<Document> list, Map<String, Long> content2IdMap) {
    }

    private DocumentResult assembleDocuments(String[] splitContents, String sourceFileName) {
        List<Document> documents = new ArrayList<>();
        Map<String, Long> content2IdMap = new HashMap<>();
        for (String segment : splitContents) {
            String trimmed = segment.trim();
            if (!trimmed.isEmpty()) {
                long id = UUIDUtils.getUUID2Long();
                Document doc = Document.newBuilder()
                        .addDocField(new DocField("id", String.valueOf(id)))
                        .addDocField(new DocField("question", trimmed))
                        .addDocField(new DocField("answer", trimmed))
                        .addDocField(new DocField("fileStatus", "1"))
                        .addDocField(new DocField("timeStamp", System.currentTimeMillis()))
                        .addDocField(new DocField("docName", sourceFileName))
                        .addDocField((new DocField("categoryId", "-1")))
                        .build();
                documents.add(doc);
                content2IdMap.put(trimmed, id);
            }
        }
        return new DocumentResult(documents, content2IdMap);
    }

    private void uploadParsedContent(String parsedPath, String jsonContent, String logPrefix) {
        try (InputStream is = JsonUtils.jsonToInputStream(jsonContent)) {
            FileInfo upload = fileStorageService.of(is)
                    .setPlatform(ossPlatform)
                    .setPath(FilenameUtils.getPath(parsedPath))
                    .setSaveFilename(FilenameUtils.getName(parsedPath))
                    .setContentType("application/json")
                    .upload();
            log.info("{} Parsed content successfully cached to: {}", logPrefix, upload.getUrl());
        } catch (Exception e) {
            log.warn("{} Failed to upload parsed content cache for path: {}. Processing will continue.", logPrefix,
                    parsedPath, e);
        }
    }

    private void verifyPictureExists(String uri, String logPrefix) {
        var originalInfo = new FileInfo().setPlatform(ossPlatform).setFilename(uri);
        if (!fileStorageService.exists(originalInfo)) {
            log.error("{} Original file does not exist in storage: {}", logPrefix, uri);
            throw new RuntimeException("Original file not found in storage: " + uri);
        }
    }

    @Override
    public Map<String, Object> detail(KnowledgePictureQuery query) {
        final var tenantUser = UserManager.getTenantUser();
        final var logPrefix = KnowledgeLogUtils.getLogPrefix(tenantUser.getTenantId(), query.getCollectionId());
        log.info("{} Starting to fetch details for knowledge file ID: {}", logPrefix, query.getId());

        final var qcFile = knowledgeFileMapper.queryById(query.getId());
        if (qcFile == null) {
            log.warn("{} Knowledge file with ID: {} not found in the database.", logPrefix, query.getId());
            throw new IllegalArgumentException("Knowledge file not found with ID: " + query.getId());
        }
        log.info("{} Found knowledge file record: '{}' (ID: {})", logPrefix, qcFile.getFileName(), qcFile.getId());

        final List<QcKnowledgeExcelData> qaDataList = qcKnowledgeExcelDataMapper.findByFileId(query.getId());
        log.info("{} Found {} associated QA data records for file ID: {}", logPrefix, qaDataList.size(), query.getId());

        // Step 3: Reconstruct the path to the cached parsed content and read it.
        // The path is derived from the stored fileUrl, matching the logic used during
        // import.

        final String fullCachePath = ossBasePath + tenantUser.getTenantId() + "/" + qcFile.getFileUrl();
        var parsedFilePath = FileUtils.generateParsedFilePath(fullCachePath);
        var fullPath = ossBasePath + parsedFilePath;
        log.info("{} Attempting to read parsed content from cache path: {}", logPrefix, fullCachePath);
        final String content = Optional.ofNullable(readContentFromCache(fullPath, logPrefix)).orElse("");

        final String publicImageUrl = ossDomain + "ai-agent/" + tenantUser.getTenantId() + "/"
                + qcFile.getFileUrl();

        log.info("{} Successfully prepared detail response for file ID: {}", logPrefix, query.getId());

        return Map.of(
                KEY_URL, publicImageUrl,
                KEY_CONTENT, content,
                KEY_LIST, qaDataList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchInsert(KnowledgePictureQuery query) {
        final var tenantUser = UserManager.getTenantUser();
        final var logPrefix = KnowledgeLogUtils.getLogPrefix(tenantUser.getTenantId(), query.getCollectionId());
        log.info("{} Starting to replace segments for file ID: {}", logPrefix, query.getId());

        final var qcKnowledgeFile = knowledgeFileMapper.queryById(query.getId());
        if (qcKnowledgeFile == null) {
            log.error("{} Aborting: Knowledge file with ID {} not found.", logPrefix, query.getId());
            throw new IllegalArgumentException("Knowledge file not found with ID: " + query.getId());
        }

        final var qaDatabase = QA_DATABASE_PREFIX + tenantUser.getTenantId();
        final var collectionName = QA_COLLECTION_PREFIX + query.getCollectionId();

        log.info("{} Preparing to delete old data for file: {}", logPrefix, qcKnowledgeFile.getFileName());
        final var oldQaDataList = qcKnowledgeExcelDataMapper.findByFileId(query.getId());

        if (oldQaDataList != null && !oldQaDataList.isEmpty()) {
            final var oldDocumentIds = oldQaDataList.stream()
                    .map(data -> String.valueOf(data.getId()))
                    .toList();
            log.info("{} Deleting {} old vector documents from collection: {}", logPrefix, oldDocumentIds.size(),
                    collectionName);
            var deleteParam = DeleteParam.newBuilder().withDocumentIds(oldDocumentIds).build();
            vectorDBService.delete(qaDatabase, collectionName, deleteParam, logPrefix);

            log.info("{} Deleting {} old QA records from relational database for file ID: {}", logPrefix,
                    oldQaDataList.size(), query.getId());
            qcKnowledgeExcelDataMapper.deleteByFileId(query.getId());
        } else {
            log.info("{} No old QA data found for file ID: {}. Skipping deletion.", logPrefix, query.getId());
        }

        final var newSegmentList = query.getSegmentList();
        if (newSegmentList == null || newSegmentList.isEmpty()) {
            log.warn("{} No new segments provided for file ID: {}. Operation finished after deletion.", logPrefix,
                    query.getId());
            knowledgeFileMapper.updateViewCount(query.getId(), 0);
            return;
        }

        log.info("{} Preparing to insert {} new segments for file: {}", logPrefix, newSegmentList.size(),
                qcKnowledgeFile.getFileName());

        final var newQuestions = newSegmentList.stream().map(QcKnowledgeExcelData::getQuestion).toArray(String[]::new);
        final var docResult = assembleDocuments(newQuestions, qcKnowledgeFile.getFileName());

        var sequence = new AtomicLong(0);
        newSegmentList.forEach(data -> {
            data.setId(docResult.content2IdMap().get(data.getQuestion()));
            data.setStatus("1");
            data.setCreateUserId(tenantUser.getUserId());
            data.setCreateUserName(tenantUser.getUserName());
            data.setCreateTime(new Date());
            data.setFileId(query.getId());
            data.setSequ(sequence.getAndIncrement());
        });

        log.info("{} Batch inserting {} new QA records into relational database.", logPrefix, newSegmentList.size());
        DataBaseMapperUtils.batchInsert(newSegmentList, qcKnowledgeExcelDataMapper::batchInsert);

        if (!docResult.list().isEmpty()) {
            log.info("{} Upserting {} new vector documents into collection: {}", logPrefix, docResult.list().size(),
                    collectionName);
            vectorDBService.upsertDocuments(qaDatabase, collectionName, docResult.list(), logPrefix);
        }

        knowledgeFileMapper.updateViewCount(query.getId(), newSegmentList.size());
        log.info("{} Successfully replaced segments for file ID: {}", logPrefix, query.getId());
    }
}