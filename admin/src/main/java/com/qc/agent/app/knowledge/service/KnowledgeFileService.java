package com.qc.agent.app.knowledge.service;

import com.qc.agent.app.knowledge.model.*;
import com.qc.agent.common.core.Message;
import com.qc.agent.platform.pojo.AjaxPage;

import com.qc.agent.vectordb.pojo.SearchContent;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @className KnowledgeFileService
 * @description TODO
 * @date 2024/1/23 10:52
 */
public interface KnowledgeFileService {

    int insertQcKnowledgeFile(QcKnowledgeFileParams qcKnowledgeFileParams);

    int batchInsertQcKnowledgeFile(BatchImpQcKnowledgeFileParams batchImpQcKnowledgeFileParams);

    AjaxPage queryList(QcKnowledgeFileListParams qcKnowledgeFileParams);

    Message updateFileStatus(QcKnowledgeFileParams qcKnowledgeFileParams);
    List<String> parse(QcKnowledgeFileParams qcKnowledgeFileParams);
    List<SearchContent> searchContent(QcKnowledgeFileParams qcKnowledgeFileParams);

    Map<String, String> findFileNameMapping(FileNameQueryParam fileNameQueryParam);

    void saveQA(QaSaveRequest request);

    List<String[]> getChunks(String filePath);

    AjaxPage listExcelFileQA(ListExcelFileQAParams listExcelFileQAParams);

    Message addExcelFileQA(QcKnowledgeExcelData qcKnowledgeExcelData);

    Message editExcelFileQA(QcKnowledgeExcelData qcKnowledgeExcelData);

    Message deleteExcelFileQA(QcKnowledgeExcelData qcKnowledgeExcelData);

    XSSFWorkbook generateImportTemplate();

    Message uploadQA(QaUploaderRequest request);

    Message findRawFileInfo(QcKnowledgeFile qcKnowledgeFile);

    boolean checkDuplicateFileName(BatchImpQcKnowledgeFileParams batchImpQcKnowledgeFileParams);
}
