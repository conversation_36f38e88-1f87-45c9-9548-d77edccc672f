package com.qc.agent.app.agent.service.goods_intent.impl;

import com.alibaba.fastjson.JSONObject;
import com.qc.agent.app.agent.model.query.QcAiGoodsAssistantQuery;
import com.qc.agent.app.agent.service.goods_intent.GoodsIntentHandler;
import com.qc.agent.app.agent.service.goods_intent.impl.tool.IntentTool;
import com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.model.dto.BusinessIntent;
import com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.model.enums.GoodsIntent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Handles the {@code CUSTOMER_SCHEME} intent.
 * <p>
 * This handler checks if the user has provided exactly one of the required pieces of
 * information for a customer scheme inquiry: either a product name or a customer name.
 * <p>
 * If this condition is met, it signifies that the system needs to gather more data.
 * The handler then transforms the intent to {@code CUSTOMER_SCHEME_INPUT}.
 * If a customer name was provided, it enriches the entities by finding and adding the
 * corresponding customer ID (cmId). This new intent signals the workflow to prompt the
 * user for the missing information (e.g., the product name if the customer was given).
 *
 * <AUTHOR>
 * @date 2025-06-26
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CustomerSchemeHandlerImpl implements GoodsIntentHandler {

    private final IntentTool intentTool;

    @Override
    public GoodsIntent getSupportedIntent() {
        return GoodsIntent.CUSTOMER_SCHEME;
    }

    /**
     * Processes the initial {@code CUSTOMER_SCHEME} intent.
     * <p>
     * It uses a clear, functional chain to determine if the intent should be transitioned.
     * If the entities contain exactly one key input, it delegates to a helper method
     * to perform the transformation and enrichment.
     *
     * @param intent    The business intent recognized from user input.
     * @param query     The original user query context.
     * @param logPrefix A string prefix for structured logging.
     * @return A new {@code BusinessIntent} of type {@code CUSTOMER_SCHEME_INPUT} if criteria are met;
     * otherwise, the original, unmodified intent is returned.
     */
    @Override
    public BusinessIntent handle(BusinessIntent intent, QcAiGoodsAssistantQuery query, String logPrefix) {
        log.info("{} [Handler:CustomerScheme] >> Handling intent '{}' with entities: {}", logPrefix, intent.type(), intent.entities());
        if (query.getCmId() != null && intent.entities() != null) {
            intent.entities().put("cmId", query.getCmId());
        }
        // A declarative chain to check, transform, or pass through the intent.
        return Optional.ofNullable(intent.entities())
                // 1. Map: If the condition is met, transform the intent.
                .map(entities -> enrichAndDetermineNextIntent(entities, intent, logPrefix))
                // 2. OrElseGet: If entities are null or the condition fails, return the original intent.
                .orElseGet(() -> {
                    log.info("{} [Handler:CustomerScheme] Condition not met. Returning original intent '{}'.", logPrefix, intent.type());
                    return intent;
                });
    }

    /**
     * Enriches entities with IDs and decides whether the intent should be transitioned.
     *
     * @param entities  The JSON object of entities from the original intent.
     * @param logPrefix A string prefix for structured logging.
     * @return A new {@link BusinessIntent} reflecting the next step.
     */
    private BusinessIntent enrichAndDetermineNextIntent(JSONObject entities, BusinessIntent initialIntent, String logPrefix) {
        // An "exact match" occurs when a lookup for an entity returns a single, unambiguous result.
        // We use AtomicInteger to count exact matches from within lambda expressions.
        var exactMatchCounter = new AtomicInteger(0);

        intentTool.enrichWithProductInfo(entities, exactMatchCounter, logPrefix);
        if (entities.get("cmId") != null) {
            intentTool.enrichWithCustomerInfoById(entities, logPrefix, "cmId");
        } else {
            intentTool.enrichWithCustomerInfo(entities, exactMatchCounter, logPrefix, "customer");
        }

        log.info("{} [Handler:SalesInto] Total exact matches found: {}", logPrefix, exactMatchCounter.get());

        // DECISION LOGIC:
        // If we have 2 exact matches (product and channel), we have high confidence to proceed.
        // Otherwise, the information is ambiguous or incomplete, so we transition to
        // SALES_INTO_INPUT to ask the user for clarification.
        if (exactMatchCounter.get() == 2) {
            log.info("{} [Handler:SalesInto] Sufficient information found. Retaining intent as SALES_INTO.", logPrefix);
            return BusinessIntent.of(GoodsIntent.CUSTOMER_SCHEME, entities, initialIntent.usage());
        } else {
            log.info("{} [Handler:SalesInto] Insufficient or ambiguous information. Transitioning to SALES_INTO_INPUT.", logPrefix);
            return BusinessIntent.of(GoodsIntent.CUSTOMER_SCHEME_INPUT, entities, initialIntent.usage());
        }
    }
}