package com.qc.agent.app.agent.util;


import java.util.function.Function;

/**
 * 支持链式 let 调用风格的封装类。
 */
public class Let<T> {

    private final T value;

    private Let(T value) {
        this.value = value;
    }

    /**
     * 包装一个值
     *
     * @param value 值
     * @param <T>   类型
     * @return Let 包装对象
     */
    public static <T> Let<T> of(T value) {
        return new Let<>(value);
    }

    /**
     * 执行函数并返回结果
     *
     * @param func 函数
     * @param <R>  结果类型
     * @return 执行结果
     */
    public <R> R let(Function<T, R> func) {
        return func.apply(value);
    }

    /**
     * 支持链式 map 操作，返回 Let 包装
     *
     * @param mapper 转换函数
     * @param <R>    结果类型
     * @return Let<R>
     */
    public <R> Let<R> map(Function<T, R> mapper) {
        return new Let<>(mapper.apply(value));
    }

    /**
     * 获取最终值
     */
    public T get() {
        return value;
    }
}

