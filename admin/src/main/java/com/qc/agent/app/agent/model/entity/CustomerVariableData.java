package com.qc.agent.app.agent.model.entity;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 客户洞察变量数据实体类
 */
@Data
public class CustomerVariableData {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 状态：1-有效，0-无效
     */
    private String status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime modifyTime;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 提示词变量记录ID，关联qc_ai_prompt_variable_record表
     */
    private Long promptVariableRecordId;

    /**
     * 对话ID，关联qc_ai_agent_conversation表
     */
    private Long conversationId;

    /**
     * 变量值
     */
    private String variableValue;
}
