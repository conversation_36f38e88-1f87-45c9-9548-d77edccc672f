package com.qc.agent.app.agent.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.qc.agent.app.agent.mapper.QcAiAgentKnowledgeMapper;
import com.qc.agent.app.agent.model.dto.QcAiAgentDeptDTO;
import com.qc.agent.app.agent.model.dto.QcAiAgentKnowledgeAuthorityDTO;
import com.qc.agent.app.agent.model.dto.QcAiAgentUserDTO;
import com.qc.agent.app.agent.model.po.QcAiAgentConversationQuote;
import com.qc.agent.app.agent.model.query.QcAiAgentKnowledgeQuery;
import com.qc.agent.app.agent.service.QcAiAgentKnowledgeService;
import com.qc.agent.platform.pojo.QcDeptInfo;
import com.qc.agent.platform.pojo.QcUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.util.StringUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-03-26
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Slf4j
@Service
public class QcAiAgentKnowledgeServiceImpl implements QcAiAgentKnowledgeService {

    @Resource
    private QcAiAgentKnowledgeMapper qcAiAgentKnowledgeMapper;


    @Override
    public Map<String, Object> authorityDetail(QcAiAgentKnowledgeQuery query) {
        log.info("查询知识库权限详情,参数:{}", query);
        Map<String, Object> resultMap = new HashMap<>(1 << 4);
        Set<String> resultList = selectedUserAndDeptIntersection(query.getList());

        extraIntersection(resultList, query.getUserInfoList());
        resultMap.put("intersection", resultList);
        resultMap.put("rows", query.getList());
        resultMap.put("showChatLogContentType", StringUtils.isEmpty(query.getShowChatLogContentType()) ? "1" : query.getShowChatLogContentType());
        return resultMap;
    }

    /**
     * 人员和部门
     *
     * @param resultSet
     */
    private void extraIntersection(Set<String> resultSet, List<QcUserInfo> userInfoList) {
        if (CollectionUtil.isNotEmpty(userInfoList)) {
            for (QcUserInfo userInfo : userInfoList) {
                Set<String> deptNameSet = userInfo.getDeptInfoList().stream().map(QcDeptInfo::getName).collect(Collectors.toSet());
                List<QcUserInfo> restList = userInfoList.stream().filter(item -> !Objects.equals(userInfo.getUserId(), item.getUserId())).toList();
                for (QcUserInfo qcUserInfo : restList) {
                    Set<String> currentDeptNameSet = qcUserInfo.getDeptInfoList().stream().map(QcDeptInfo::getName).collect(Collectors.toSet());
                    deptNameSet.retainAll(currentDeptNameSet);
                    if (deptNameSet.isEmpty()) {
                        break;
                    }
                }
                resultSet.add(userInfo.getUserName());
            }
        }
    }

    /**
     * 获取已选择的用户和部门的交集
     *
     * @param list
     * @return
     */
    private Set<String> selectedUserAndDeptIntersection(List<QcAiAgentKnowledgeAuthorityDTO> list) {
        // 获取用户ID交集
        List<Long> userIdIntersection = list.stream()
                .map(dto -> dto.getUserList().stream()  // 遍历每个DTO的用户列表
                        .map(QcAiAgentUserDTO::getId)       // 提取用户ID
                        .collect(Collectors.toSet()))       // 转为Set
                .reduce((set1, set2) -> {              // 求交集
                    set1.retainAll(set2);
                    return set1;
                })
                .orElse(Collections.emptySet())        // 处理空集合情况
                .stream().toList();                     // 转为List<Long>

        // 获取部门ID交集
        List<Long> deptIdIntersection = list.stream()
                .map(dto -> dto.getDeptList().stream()  // 遍历每个DTO的部门列表
                        .map(QcAiAgentDeptDTO::getId)       // 提取部门ID
                        .collect(Collectors.toSet()))       // 转为Set
                .reduce((set1, set2) -> {              // 求交集
                    set1.retainAll(set2);
                    return set1;
                })
                .orElse(Collections.emptySet())        // 处理空集合情况
                .stream().toList();                     // 转为List<Long>

        // 构建最终结果对象
        List<String> userNameList = list.stream()
                .flatMap(dto -> dto.getUserList().stream())  // 展开所有用户
                .filter(user -> userIdIntersection.contains(user.getId()))  // 过滤交集ID
                .map(QcAiAgentUserDTO::getName)
                .distinct()                                   // 去重
                .toList();
        Set<String> resultList = new HashSet<>(userNameList);

        List<String> deptNameList = list.stream()
                .flatMap(dto -> dto.getDeptList().stream())   // 展开所有部门
                .filter(dept -> deptIdIntersection.contains(dept.getId()))  // 过滤交集ID
                .map(QcAiAgentDeptDTO::getName)
                .distinct()                                   // 去重
                .toList();
        resultList.addAll(deptNameList);
        return resultList;
    }

    @Override
    public Map<String, Object> quoteDetail(QcAiAgentKnowledgeQuery query) {
        log.info("查询知识库引用详情,参数:{}", query);
        Map<String, Object> resultMap = new HashMap<>(1 << 4);
        List<QcAiAgentConversationQuote> list = qcAiAgentKnowledgeMapper.selectQuoteList(query);
        resultMap.put("rows", list);
        return resultMap;
    }
}
