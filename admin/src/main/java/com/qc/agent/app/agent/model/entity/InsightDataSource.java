package com.qc.agent.app.agent.model.entity;

import java.io.Serializable;

import com.qc.agent.platform.pojo.BaseEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 数据源表
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InsightDataSource extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 数据源 ID，主键
     */
    private Long id;

    /**
     * 数据源名称
     */
    private String sourceName;

    /**
     * 数据源编码，全局唯一标识
     */
    private String sourceCode;

    /**
     * API 接口地址
     */
    private String apiUrl;

    /**
     * HTTP 请求方法
     */
    private String httpMethod;

    /**
     * 描述
     */
    private String description;

    /**
     * 数据源从属维度编码
     */
    private String belongDimensionCode;

    /**
     * 数据源从属维度名称
     */
    private String belongDimensionName;


}