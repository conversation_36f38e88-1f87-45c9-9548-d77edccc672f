package com.qc.agent.app.knowledge.mapper;

import com.qc.agent.app.knowledge.model.KnowledgeRange;
import com.qc.agent.app.knowledge.model.QcKnowledgeInfoRange;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface QcKnowledgeInfoRangeMapper {

    QcKnowledgeInfoRange getById(Long id);


    void insert(QcKnowledgeInfoRange qcKnowledgeInfoRange);

    void insertBatch(@Param("list") List<QcKnowledgeInfoRange> qcKnowledgeInfoRange);

    void update(QcKnowledgeInfoRange qcKnowledgeInfoRange);

    void delete(Long id);

    void deleteByKnowledgeId(@Param("knowledgeId") Long knowledgeId);

    List<KnowledgeRange> getByKnowledgeId(@Param("knowledgeId") Long knowledgeId, @Param("rangeType") String rangeType);
}
