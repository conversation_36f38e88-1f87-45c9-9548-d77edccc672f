package com.qc.agent.app.api_suit.service.impl;

import com.qc.agent.app.api_suit.mapper.QcAiAgentSuitModuleMapper;
import com.qc.agent.app.api_suit.model.dto.SuitModuleDTO;
import com.qc.agent.app.api_suit.model.po.QcAiAgentSuitModule;
import com.qc.agent.app.api_suit.model.vo.SuitModuleVO;
import com.qc.agent.app.api_suit.service.QcAiAgentSuitModuleService;
import com.qc.agent.platform.util.UUIDUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

@Service
public class QcAiAgentSuitModuleServiceImpl implements QcAiAgentSuitModuleService {

    @Resource
    private QcAiAgentSuitModuleMapper qcAiAgentSuitModuleMapper;

    @Override
    public SuitModuleVO getById(Long id) {
        return qcAiAgentSuitModuleMapper.selectById(id);
    }

    @Override
    public List<SuitModuleVO> list(Long suitId) {
        return qcAiAgentSuitModuleMapper.selectBySuitId(suitId);
    }

    @Override
    public void save(SuitModuleDTO dto) {
        if (dto.getId() == null) {
            QcAiAgentSuitModule record = QcAiAgentSuitModule.builder()
                    .id(UUIDUtils.getUUID2Long())
                    .status("1")
                    .createTime(LocalDateTime.now())
                    .creatorId(dto.getUserId())
                    .creatorName(dto.getUserName())
                    .suitId(dto.getSuitId())
                    .name(dto.getName())
                    .describe(dto.getDescribe())
                    .sequ(dto.getSequ())
                    .build();
            qcAiAgentSuitModuleMapper.insert(record);
        } else {
            QcAiAgentSuitModule record = QcAiAgentSuitModule.builder()
                    .id(dto.getId())
                    .modifyTime(LocalDateTime.now())
                    .modifyierId(dto.getUserId())
                    .modifyierName(dto.getUserName())
                    .name(dto.getName())
                    .describe(dto.getDescribe())
                    .sequ(dto.getSequ())
                    .build();
            qcAiAgentSuitModuleMapper.update(record);
        }
    }

    @Override
    public void delete(Long id) {
        qcAiAgentSuitModuleMapper.delete(id);
    }
}
