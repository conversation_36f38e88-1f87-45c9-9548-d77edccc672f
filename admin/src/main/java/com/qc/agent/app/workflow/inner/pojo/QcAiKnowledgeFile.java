package com.qc.agent.app.workflow.inner.pojo;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025-05-28
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Data
public class QcAiKnowledgeFile {

    /**
     * collection_id - qc_knowledge_info的id
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long collectionId;
    /**
     * 分类id
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
   private Long categoryId;
    /**
     * 文档原始名称
     */
   private String docName;
    /**
     * 分类名称
     */
   private String categoryName;
}
