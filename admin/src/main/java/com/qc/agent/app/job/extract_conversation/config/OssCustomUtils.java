package com.qc.agent.app.job.extract_conversation.config;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.ListObjectsRequest;
import com.aliyun.oss.model.OSSObjectSummary;
import com.aliyun.oss.model.ObjectListing;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-28
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Slf4j
@Configuration
@AllArgsConstructor
public class OssCustomUtils {

    // 改为从配置读取
    private static String endpoint;
    private static String accessKeyId;
    private static String accessKeySecret;
    private static String bucketName;
    private static String basePath;

    // 注入配置（静态字段需要setter注入）
    @Autowired
    public void setOssConfig(
            @Value("${dromara.x-file-storage.aliyun-oss[0].end-point}") String ep,
            @Value("${dromara.x-file-storage.aliyun-oss[0].access-key}") String ak,
            @Value("${dromara.x-file-storage.aliyun-oss[0].secret-key}") String sk,
            @Value("${dromara.x-file-storage.aliyun-oss[0].bucket-name}") String bn,
            @Value("${dromara.x-file-storage.aliyun-oss[0].base-path}") String bp
    ) {
        endpoint = ep;
        accessKeyId = ak;
        accessKeySecret = sk;
        bucketName = bn;
        basePath = bp;
    }


    /**
     * 删除指定目录下的所有文件
     *
     * @param directoryPath OSS 目录路径，例如 "agent-conversation/2023-10-01/"
     */
    public static void deleteFilesInDirectory(String directoryPath) {
        // 创建 OSS 客户端
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);

        try {
            // 列出目录下的所有文件
            ObjectListing objectListing = ossClient.listObjects(new ListObjectsRequest(bucketName).withPrefix(basePath + directoryPath));
            List<OSSObjectSummary> objectSummaries = objectListing.getObjectSummaries();

            if (objectSummaries.isEmpty()) {
                log.info("No files found in directory: {}", directoryPath);
                return;
            }

            // 删除文件
            for (OSSObjectSummary objectSummary : objectSummaries) {
                String key = objectSummary.getKey();
                ossClient.deleteObject(bucketName, key);
                log.info("Deleted file: {}", key);
            }
        } catch (Exception e) {
            log.error("Error deleting files from OSS directory: {}", directoryPath, e);
        } finally {// 关闭 OSS 客户端
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }
}
