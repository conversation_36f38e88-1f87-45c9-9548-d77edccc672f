package com.qc.agent.app.agent.mapper;

import com.qc.agent.app.agent.model.po.QcAiAgentVisitDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2025-04-09
 */
@Mapper
public interface QcAiAgentVisitDetailMapper {
      QcAiAgentVisitDetail selectByAgentId(@Param("agentId") Long agentId, @Param("userId") Long userId);

      void update(QcAiAgentVisitDetail qcAiAgentVisitDetail);

      void insert(QcAiAgentVisitDetail qcAiAgentVisitDetail);
}
