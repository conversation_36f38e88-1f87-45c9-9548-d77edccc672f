package com.qc.agent.app.agent.model.entity;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 提示词变量记录实体类
 */
@Data
public class PromptVariableRecord {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 状态：1-有效，0-无效
     */
    private String status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime modifyTime;

    /**
     * 配置ID，关联qc_ai_dimension_config.id
     */
    private Long configId;

    /**
     * 维度代码
     */
    private String dimensionCode;

    /**
     * 提示词类型
     */
    private String promptType;

    /**
     * 提示词片段ID，关联qc_ai_prompt_fragment.id
     */
    private Long promptFragmentId;

    /**
     * 变量的key（如OrderSatisfactionRate）
     */
    private String variableKey;

    /**
     * 变量在该片段中的出现顺序
     */
    private Integer variableOrder;
}
