package com.qc.agent.app.knowledge.model;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @className KnowledgeQuestionAnswerSettingParams
 * @description TODO
 * @date 2024/2/6 16:11
 */
@Data
public class KnowledgeQuestionAnswerSettingParams {
    private Long id;

    private String threshold;

    /**
     * qa相似度
     */
    private String qaThreshold;

    /**
     * 欢迎词
     */
    private String promptWord;

    private String defaultAnswer;


    private Date modifyTime;


    private Long createUserId;


    private String createUserName;

    private Long modifyUserId;


    private String modifyUserName;

    private Date createTime;

    private int eachSearchCount;
    private int engineType;

    /**
     * 功能图标
     */
    private String functionLogo;

    /**
     * 问答检索范围
     */
    private String answerSearchRange;

    /**
     * 模型prompt
     */
    private String prompt;

    /**
     * 是否显示引用文档
     */
    private String showReference;

    /**
     * 空搜索回复
     */
    private String errorAnswer;

}
