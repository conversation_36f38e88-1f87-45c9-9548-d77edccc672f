package com.qc.agent.app.agent.service.impl;

import java.util.List;
import lombok.extern.slf4j.Slf4j;
import com.qc.agent.platform.datasource.model.DatasourceConfig;
import com.qc.agent.platform.register.service.TenantRegisterHook;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;


@Slf4j
@Component
public class AgentLeadingQuestionHook implements TenantRegisterHook {
    @Resource
    private LeadingQuestionService service;

    @Override
    public void afterRegister(DatasourceConfig config) {
        List<String> products = service.queryProductInfo();
        if (!products.isEmpty()) {
            String questions = service.generateQuestions(products);
            service.saveQuestions(config, questions);
        }
    }

}
