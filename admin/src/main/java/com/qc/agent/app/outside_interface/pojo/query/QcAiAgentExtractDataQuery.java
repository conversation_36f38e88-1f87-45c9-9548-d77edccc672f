package com.qc.agent.app.outside_interface.pojo.query;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025-03-17
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Data
public class QcAiAgentExtractDataQuery {

    private Long page;

    private Long rows;

    private Long offset;
    /**
     * 企业id
     */
    private Long tenantId;
    /**
     * 开始日期
     */
    private String startDate;
    /**
     * 结束日期
     */
    private String endDate;

    /**
     * offset
     *
     * @return
     */
    public Long getOffset() {
        if (page != null && rows != null) {
            return (page - 1) * rows;
        }
        return offset;
    }
}
