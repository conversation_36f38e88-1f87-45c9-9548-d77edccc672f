package com.qc.agent.app.api_suit.service.impl;

import com.qc.agent.app.api_suit.mapper.QcAiAgentSuitMapper;
import com.qc.agent.app.api_suit.model.dto.SuitDTO;
import com.qc.agent.app.api_suit.model.po.QcAiAgentSuit;
import com.qc.agent.app.api_suit.model.vo.SuitVO;
import com.qc.agent.app.api_suit.service.QcAiAgentSuitService;
import com.qc.agent.platform.util.UUIDUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

@Service
public class QcAiAgentSuitServiceImpl implements QcAiAgentSuitService {

    @Resource
    private QcAiAgentSuitMapper mapper;

    @Override
    public SuitVO getById(Long id) {
        return mapper.selectById(id);
    }

    @Override
    public List<SuitVO> list() {
        return mapper.selectAll();
    }

    @Override
    public void save(SuitDTO dto) {
        if (dto.getId() == null) {
            QcAiAgentSuit record = QcAiAgentSuit.builder()
                    .id(UUIDUtils.getUUID2Long())
                    .status("1")
                    .createTime(LocalDateTime.now())
                    .creatorId(dto.getUserId())
                    .creatorName(dto.getUserName())
                    .name(dto.getName())
                    .describe(dto.getDescribe())
                    .sequ(dto.getSequ())
                    .build();
            record.setSequ(dto.getSequ());
            mapper.insert(record);
        } else {
            QcAiAgentSuit record = QcAiAgentSuit.builder()
                    .id(dto.getId())
                    .modifyTime(LocalDateTime.now())
                    .modifyierId(dto.getUserId())
                    .modifyierName(dto.getUserName())
                    .name(dto.getName())
                    .describe(dto.getDescribe())
                    .sequ(dto.getSequ())
                    .build();
            mapper.update(record);
        }

    }

    @Override
    public void delete(Long id) {
        mapper.delete(id);
    }
}
