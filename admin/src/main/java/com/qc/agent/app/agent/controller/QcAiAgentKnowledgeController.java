package com.qc.agent.app.agent.controller;

import com.qc.agent.app.agent.mapper.QcAiAgentKnowledgeMapper;
import com.qc.agent.app.agent.mapper.QcAiAgentMapper;
import com.qc.agent.app.agent.model.dto.QcAiAgentKnowledgeAuthorityDTO;
import com.qc.agent.app.agent.model.dto.QcAiAgentUserDTO;
import com.qc.agent.app.agent.model.query.QcAiAgentKnowledgeQuery;
import com.qc.agent.app.agent.service.QcAiAgentKnowledgeService;
import com.qc.agent.common.core.Message;
import com.qc.agent.platform.pojo.QcUserInfo;
import com.qc.agent.platform.user.service.AppServerUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-26
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Slf4j
@RestController
@RequestMapping("/ai-agent/agents/knowledge")
public class QcAiAgentKnowledgeController {

    @Resource
    private QcAiAgentKnowledgeService qcAiAgentKnowledgeService;

    @Resource
    private AppServerUserService appServerUserService;

    @Resource
    private QcAiAgentKnowledgeMapper qcAiAgentKnowledgeMapper;

    @Resource
    private QcAiAgentMapper qcAiAgentMapper;

    @PostMapping("/authority_detail")
    public Message authorityDetail(@RequestBody QcAiAgentKnowledgeQuery query) {
        List<QcAiAgentKnowledgeAuthorityDTO> list = qcAiAgentKnowledgeMapper.selectAssociatedKnowledgeAuthorityList(query);
        List<Long> userIdList = list.stream().map(QcAiAgentKnowledgeAuthorityDTO::getUserList).flatMap(Collection::stream).map(QcAiAgentUserDTO::getId).toList();
        List<QcUserInfo> userInfoList = appServerUserService.getAllDeptList(userIdList);
        query.setShowChatLogContentType(qcAiAgentMapper.selectById(query.getAgentId()).getShowChatLogContentType());
        query.setList(list);
        query.setUserInfoList(userInfoList);
        return Message.of().ok().data(qcAiAgentKnowledgeService.authorityDetail(query));
    }

    @PostMapping("/quote_detail")
    public Message quoteDetail(@RequestBody QcAiAgentKnowledgeQuery query) {
        return Message.of().ok().data(qcAiAgentKnowledgeService.quoteDetail(query));
    }

}
