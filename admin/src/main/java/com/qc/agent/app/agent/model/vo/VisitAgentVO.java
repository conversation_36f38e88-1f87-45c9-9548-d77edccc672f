package com.qc.agent.app.agent.model.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025-04-09
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Data
@Builder
public class VisitAgentVO {

    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;
    /**
     * 问题类型  1：拜访客户推荐 2：拜访数据查询 0：其他
     */
    private String isVisit;

    /**
     * 答案
     */
    private String answer;

    /**
     * Retrieval radius in meters 检索半径
     * <p>
     * Defines the geographic search radius for visit data collection.
     * </p>
     */
    private Double retrieveRadius;

    /**
     * Search scope in days  检索范围-天数
     * <p>
     * Defines the temporal search window for visit data collection.
     * </p>
     */
    private Double searchScopeDays;




}
