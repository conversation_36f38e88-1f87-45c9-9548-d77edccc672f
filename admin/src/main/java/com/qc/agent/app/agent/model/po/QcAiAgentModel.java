package com.qc.agent.app.agent.model.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QcAiAgentModel {

    /**
     * 主键 ID
     */
    private Long id;

    /**
     * 状态，默认值 '1'
     */
    private String status;

    /**
     * 创建人 ID
     */
    private Long creatorId;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改人 ID
     */
    private Long modifyierId;

    /**
     * 修改人姓名
     */
    private String modifyierName;

    /**
     * 修改时间
     */
    private LocalDateTime modifyTime;

    /**
     * 大模型厂商
     */
    private String vendor;

    private String vendorName;

    /**
     * 大模型api参数
     */
    private String model;

    private String modelEndPoint;

    /**
     * 大模型名称
     */
    private String modelName;

    private List<String> modelList;

    /**
     * API 调用 key
     */
    private String key;

    /**
     * API 调用 secret
     */
    private String secret;

    private String apiKeyConfigUrl;

    /**
     * 是否启用 0: 否 1: 是
     */
    private String enable;

    /**
     * 图标地址
     */
    private String icon;

    /**
     * 接口调用地址
     */
    private String apiUrl;

    /**
     * 接口调用端口
     */
    private String apiPort;

    /**
     * 是否私有化部署 0否 1是
     */
    private String privacyDeployment;

    /**
     * 自定义 Header: aip-application-id
     */
    private String aipApplicationId;

    /**
     * 自定义 Header: aip-user-id
     */
    private String aipUserId;

    /**
     * 是否开启思考参数 enable: 开启，disable: 关闭
     */
    private String thinking;

    public boolean checkIsMyModel() {
        return "1".equals(enable);
    }
}
