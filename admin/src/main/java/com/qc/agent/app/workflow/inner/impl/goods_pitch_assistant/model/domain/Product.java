package com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.model.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @date 2025-06-23
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class Product {
    private String id;
    private String name;
    private String brand;
    private String tagValues;
    private String createTime;
    private String submitTime;
    private String shortName;

    @Builder.Default
    private transient LocalDateTime createTimeObj = null;

    public Product(String name, String brand) {
        this.name = name;
        this.brand = brand;
    }


    public Product(String name, String id, String tagValues) {
        this.name = name;
        this.id = id;
        this.tagValues = tagValues;
    }
}
