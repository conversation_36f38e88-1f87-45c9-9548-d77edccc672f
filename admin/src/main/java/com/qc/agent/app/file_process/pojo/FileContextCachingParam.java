package com.qc.agent.app.file_process.pojo;

import com.qc.agent.app.workflow.inner.pojo.QcUploadFile;
import com.qc.agent.lla.sdk.model.ModelParam;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-06-16
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Data
public class FileContextCachingParam {
    private List<QcUploadFile> fileList;
    private String cacheTag;
    private String model;
    private Long tenantId;
    private String logPrefix;

    private String apiKey;

    private String vendor;

    private String modelEndPoint;

    private Long agentId;

    private Long intentId;

    private ModelParam modelParam;

    public static FileContextCachingParam build() {
        return new FileContextCachingParam();
    }


    public FileContextCachingParam withModelEndPoint(String modelEndPoint) {
        this.modelEndPoint = modelEndPoint;
        return this;
    }

    public FileContextCachingParam withAgentId(Long agentId) {
        this.agentId = agentId;
        return this;
    }

    public FileContextCachingParam withIntentId(Long intentId) {
        this.intentId = intentId;
        return this;
    }

    public FileContextCachingParam withFileList(List<QcUploadFile> fileList) {
        this.fileList = fileList;
        return this;
    }

    public FileContextCachingParam withCacheTag(String cacheTag) {
        this.cacheTag = cacheTag;
        return this;
    }

    public FileContextCachingParam withModel(String model) {
        this.model = model;
        return this;
    }

    public FileContextCachingParam withTenantId(Long tenantId) {
        this.tenantId = tenantId;
        return this;
    }

    public FileContextCachingParam withLogPrefix(String logPrefix) {
        this.logPrefix = logPrefix;
        return this;
    }

    public FileContextCachingParam withApiKey(String apiKey) {
        this.apiKey = apiKey;
        return this;
    }

    public FileContextCachingParam withVendor(String vendor) {
        this.vendor = vendor;
        return this;
    }
}
