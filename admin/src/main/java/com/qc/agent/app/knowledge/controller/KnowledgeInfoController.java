package com.qc.agent.app.knowledge.controller;

import com.alibaba.fastjson.JSONObject;
import com.qc.agent.app.knowledge.model.*;
import com.qc.agent.app.knowledge.model.query.KnowledgeQuery;
import com.qc.agent.app.knowledge.service.KnowledgeInfoService;
import com.qc.agent.common.core.Message;
import com.qc.agent.platform.user.service.AppServerUserService;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @className KnowledgeInfoController
 * @description TODO
 * @date 2024/1/22 11:26
 */
@RequestMapping("/ai-agent/knowledge/info")
@RestController
public class KnowledgeInfoController {

    private final Log logger = LogFactory.getLog(KnowledgeInfoController.class);

    @Autowired
    private KnowledgeInfoService knowledgeInfoService;

    @Resource
    private AppServerUserService appServerUserService;

    @RequestMapping("/queryList.do")
    public Message queryList(@RequestBody QcKnowledgeInfoParams qcKnowledgeInfoParams){
        qcKnowledgeInfoParams.setParentDeptIds(appServerUserService.getParentDeptIds(qcKnowledgeInfoParams.getCurrentDeptId()));
        List<QcKnowledgeInfoExt> data =  knowledgeInfoService.selectQcKnowledgeInfoList(qcKnowledgeInfoParams);
        return Message.of().data(data).ok();
    }

    @RequestMapping("/querySummary.do")
    public Message querySummary(@RequestBody QcKnowledgeInfoParams qcKnowledgeInfoParams){
        Map<String, Object> data = knowledgeInfoService.queryQcKnowledgeSummary(qcKnowledgeInfoParams);
        return Message.of().data(data).ok();
    }

    @RequestMapping("/addOrUpdate.do")
    public Message addOrUpdate(@RequestBody QcKnowledgeInfoParams qcKnowledgeInfoParams){
        //更新
        if (!Objects.isNull(qcKnowledgeInfoParams.getId()) && qcKnowledgeInfoParams.getId() > 0L) {
            knowledgeInfoService.updateQcKnowledgeInfo(qcKnowledgeInfoParams);
        } else {
            return Message.of().data(String.valueOf(knowledgeInfoService.insertQcKnowledgeInfo(qcKnowledgeInfoParams))).ok();
        }
        return Message.of().ok();
    }

    @RequestMapping("/delete.do")
    public Message delete(@RequestBody QcKnowledgeInfoParams qcKnowledgeInfoParams){
        return knowledgeInfoService.deleteQcKnowledgeInfoById(qcKnowledgeInfoParams.getId());
    }

    @RequestMapping("/detail.do")
    public Message detail(@RequestBody QcKnowledgeInfoParams qcKnowledgeInfoParams){
        QcKnowledgeInfo qcKnowledgeInfo = knowledgeInfoService.selectQcKnowledgeInfoById(qcKnowledgeInfoParams.getId());
        return Message.of().data(qcKnowledgeInfo).ok();
    }

    @RequestMapping("/enable.do")
    public Message enable(@RequestParam Long id, Long userId, String userName){
        knowledgeInfoService.enable(id, userId, userName);
        return Message.of().ok();
    }

    @RequestMapping("/disable.do")
    public Message disable(@RequestParam Long id, Long userId, String userName){
        knowledgeInfoService.disable(id, userId, userName);
        return Message.of().ok();
    }

    @RequestMapping("/publish.do")
    public Message publish(@RequestBody KnowledgePublishParam knowledgePublishParam){
        knowledgeInfoService.publish(knowledgePublishParam);
        return Message.of().ok();
    }

    @RequestMapping("/deactivate.do")
    public Message deactivate(@RequestParam Long id, Long userId, String userName){
        knowledgeInfoService.deactivate(id, userId, userName);
        return Message.of().ok();
    }

    @RequestMapping("/quote_list")
    public Message quoteList(@RequestBody KnowledgeQuery query){
        return Message.of().ok().data(knowledgeInfoService.quoteList(query));
    }


    @RequestMapping("/queryCategorySetting.do")
    public Message queryCategorySetting(@RequestBody JSONObject json) {
        List<QcKnowledgeFileCategory> data = knowledgeInfoService.queryCategorySetting(json.getString("collectionId"));
        return Message.of().data(data).ok();
    }


    @RequestMapping("/modifyCategorySetting.do")
    public Message modifyCategorySetting(@RequestBody QcKnowledgeCategoryWrapperParam qcKnowledgeCategoryWrapperParam) {
        knowledgeInfoService.modifyCategorySetting(qcKnowledgeCategoryWrapperParam);
        return Message.of().ok();
    }

    @RequestMapping("/checkCategorySetting.do")
    public Message checkCategorySetting(@RequestBody QcKnowledgeCategoryWrapperParam qcKnowledgeCategoryWrapperParam) {
        return Message.of().data(knowledgeInfoService.checkCategorySetting(qcKnowledgeCategoryWrapperParam)).ok();
    }
}
