package com.qc.agent.app.question.impl.deepseek;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.net.HttpHeaders;
import com.qc.agent.app.question.impl.AbstrctAnswerAIRequest;
import com.qc.agent.common.util.UUIDUtils;
import com.qc.agent.platform.sse.AnswerAISubscriber;
import com.qc.agent.platform.sse.SseMessageConverter;
import com.qc.agent.platform.sse.SseRequest;
import com.qc.agent.platform.sse.dto.AIVendors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;

/**
 * @description：Deepseek问答实现
 * @author： fengjun
 * @create： 2025/2/8 13:30
 */
@Component
@Slf4j
public class DeepseekChatbotImpl extends AbstrctAnswerAIRequest {

    @Value("${ai.agent.deepseek-apikey}")
    private String deepseekApiKey;

    private static final String PROVIDER_DOMAIN = "https://api.deepseek.com";

    @Override
    protected String buildAnswer(String defaultAnswer) {
        String content;
        if (StringUtils.isNotEmpty(defaultAnswer)) {
            JSONObject data = new JSONObject();
            JSONArray choices = new JSONArray();
            JSONObject choice = new JSONObject();
            JSONObject delta = new JSONObject();
            delta.put("content", defaultAnswer);
            choice.put("delta", delta);
            choices.add(choice);
            data.put("id", UUIDUtils.getUUID2Long());
            data.put("choices", choices);
            content = data.toJSONString();
        } else {
            content = "[DONE]";
        }
        return String.format("data: %s", content);
    }

    @Override
    protected void doAnswer(SseRequest request, AnswerAISubscriber subscriber) {
        log.info("做Deepseek问答,deepseekApiKey:{}", deepseekApiKey);

        JSONObject requestBody = buildRequestBody(request);

        log.info("调用问答接口，request：{}", requestBody);

        HttpRequest httpRequest = HttpRequest.newBuilder()
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + deepseekApiKey)
                .header("Content-Type", "application/json")
                .uri(URI.create(URI.create(PROVIDER_DOMAIN) + "/chat/completions"))
                .POST(HttpRequest.BodyPublishers.ofString(requestBody.toJSONString()))
                .build();
        try {
            request.setStartTime(System.currentTimeMillis());
            HttpClient.newBuilder().build().sendAsync(httpRequest, HttpResponse.BodyHandlers.fromLineSubscriber(subscriber)).get();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @NotNull
    private JSONObject buildRequestBody(SseRequest request) {
        JSONObject param = new JSONObject();
        param.put("messages", buildMessage(request));
        param.put("stream", true);
        param.put("model", "deepseek-reasoner");
        return param;
    }

    @Override
    public AIVendors determineAIVendors() {
        return AIVendors.DEEPSEEK;
    }

    @Override
    public SseMessageConverter determineMessageConverter() {
        return new DeepseekMessageConverter();
    }
}
