package com.qc.agent.app.agent.service.customer_insight_intent.config;

import com.qc.agent.app.agent.service.customer_insight_intent.CustomerInsightIntentHandler;
import com.qc.agent.app.workflow.inner.impl.customer_insight_assistant.model.enums.CustomerInsightIntent;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 客户洞察意图处理器配置类
 * 
 * <AUTHOR>
 * @since 1.0.0
 * @version 1.0.0
 */
@Configuration
public class CustomerInsightIntentHandlerConfig {
    
    /**
     * 创建客户洞察意图处理器映射
     * 
     * @param handlerList 处理器列表
     * @return 意图到处理器的映射
     */
    @Bean
    public Map<CustomerInsightIntent, CustomerInsightIntentHandler> customerInsightIntentHandlerMap(List<CustomerInsightIntentHandler> handlerList) {
        return handlerList.stream()
                .collect(Collectors.toMap(
                        CustomerInsightIntentHandler::getSupportedIntent,
                        Function.identity()
                ));
    }
} 