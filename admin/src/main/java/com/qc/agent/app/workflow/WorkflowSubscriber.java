package com.qc.agent.app.workflow;

import cn.hutool.core.lang.Assert;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.qc.agent.common.core.Message;
import com.qc.agent.common.core.RequestHolder;
import com.qc.agent.common.core.TenantUser;
import com.qc.agent.lla.ILLASubscriber;
import com.qc.agent.lla.exception.LLAInvokerRequestException;
import com.qc.agent.lla.model.LLARequest;
import com.qc.agent.lla.model.LLAResponse;
import com.qc.agent.platform.sse.SseClient;
import com.qc.agent.platform.sse.SseMessage;
import com.qc.agent.platform.sse.dto.MessageType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 工作流订阅者
 *
 * <AUTHOR>
 * @date 2025/3/6 20:12:22
 */
@Slf4j
public class WorkflowSubscriber implements ILLASubscriber {
    // SSE Client
    SseClient client;

    // 企业用户
    TenantUser tenantUser;

    // 传输数据
    LLARequest request;

    // 流式内容追加器
    StringBuilder content;

    /**
     * 构建订阅者
     *
     * @return 订阅者
     */
    public static WorkflowSubscriber of(SseClient client) {
        WorkflowSubscriber subscriber = new WorkflowSubscriber();
        subscriber.client = client;
        subscriber.tenantUser = RequestHolder.getThreadLocalUser();
        return subscriber;
    }

    /**
     * 构建订阅者
     *
     * @param client  客户端
     * @param request 请求参数
     * @return 订阅者
     */
    public static WorkflowSubscriber of(SseClient client, LLARequest request) {
        WorkflowSubscriber subscriber = of(client);
        subscriber.request = request;
        return subscriber;
    }

    /**
     * 传输数据
     *
     * @param request 传输数据
     * @return 订阅者
     */
    public WorkflowSubscriber request(LLARequest request) {
        this.request = request;
        return this;
    }

    @Override
    public void onSubscribe() {
        Assert.notNull(request, "request is null");
        Assert.notNull(client, "client is null");

        ILLASubscriber.super.onSubscribe();
        this.content = new StringBuilder();
    }

    /**
     * 接收消息
     *
     * @param item 消息
     */
    @Override
    public void onNext(LLAResponse item) {
        try {
            Map<String, Object> extData = new HashMap<>();
            if (StringUtils.isNotEmpty(request.getClientId())) {
                extData.put("id", request.getClientId());
            }
            if (request.getQuoteCount() != null) {
                extData.put("quoteCount", request.getQuoteCount());
            }
            if (request.getIsShowButton() != null) {
                extData.put("isShowButton", request.getIsShowButton());
            }
            if (request.getId() != null) {
                extData.put("conversationId", request.getId());
            }
            // 构建消息体
            SseMessage message = SseMessage.builder().message(item.getContent()).extData(extData).messageType(MessageType.TEXT).isEnd(item.getStop()).build();
            // 发送消息
            SseEmitter sse = client.send(request.getClientId(), Message.of().data(message).ok());
            if (StringUtils.isEmpty(content) && request.getStart() == null) {
                request.setStart(LocalDateTime.now());
            }
            // 追加内容（页面终止输出会停止追加）
            if (client.exists(request.getClientId())) {
                content.append(item.getContent());
            } else {
                // 如果问答被终止，则直接结束调当前问答。
                if (sse != null) {
                    sse.send(SseMessage.builder().messageType(MessageType.TEXT).isEnd(true).build());
                    // 停止SSE
                    sse.complete();
                }
            }
            // 完成后触发监听器
            if (item.getStop()) {
                RequestHolder.setThreadLocalUser(tenantUser);
                item.setContent(content.toString());
                ListenerInvoker.invoke(item, request);
            }

        } catch (IllegalStateException ie) {
            log.error(ie.getMessage());
        } catch (Exception e) {
            log.error("subscriber message error.", e);
            client.complete(request.getClientId());
        }
    }

    @Override
    public void onError(Throwable throwable) {
        if (throwable instanceof LLAInvokerRequestException) {
            sendErrorMessage(client, throwable.getMessage());
        }
        client.complete(request.getClientId());
        log.error("subscriber message error.", throwable);
    }

    @Override
    public void onComplete() {
        client.complete(request.getClientId());
    }

    private void sendErrorMessage(SseClient client, String message) {
        String content;
        try {
            var map = new ObjectMapper().readValue(message, Map.class);
            content = Objects.toString(map.get("message"),
                    "该AGENT使用的模型的KEY已失效，请联系创建者/管理员调整后重新发布后再使用。");
        } catch (JsonProcessingException e) {
            content = e.getMessage();
        }

        content.chars()
                .mapToObj(c -> String.valueOf((char) c))
                .forEach(s -> client.send(
                        request.getClientId(),
                        Message.of().ok().data(
                                SseMessage.builder()
                                        .message(s)
                                        .messageType(MessageType.TEXT)
                                        .isEnd(false)
                                        .build()
                        )
                ));

        client.send(request.getClientId(), Message.of().ok().data(
                SseMessage.builder()
                        .messageType(MessageType.TEXT)
                        .isEnd(true)
                        .build()
        ));
        client.complete(request.getClientId());
    }



}
