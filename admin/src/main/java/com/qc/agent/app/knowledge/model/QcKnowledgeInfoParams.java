package com.qc.agent.app.knowledge.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @className QcKnowledgeInfo
 * @description TODO
 * @date 2024/1/22 11:27
 */
public class QcKnowledgeInfoParams {

    /**
     * $column.columnComment
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;

    private Long currentUserId;

    private Long currentDeptId;

    private List<Long> parentDeptIds;

    /**
     * 1：查询我的，2：公共知识库，3：管理知识库
     */
    private String searchFlag;

    public static final String SEARCH_MINE = "1";
    public static final String SEARCH_PUBLIC = "2";
    public static final String SEARCH_MANAGE = "3";

    public static final String SEARCH_AGENT_SAVE = "4";

    private String status;

    private Long userId;

    private String userName;

    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long creatorId;

    private String creatorName;

    @J<PERSON><PERSON>ield(serializeUsing = ToStringSerializer.class)
    private Long modifyierId;

    private String modifyierName;
    private Date createTime;
    private Date modifyTime;


    private String indexModel;

    private Long maxTokens;


    private String knowledgeFace;


    private String knowledgeName;


    private String knowledgeDes;

    private String dataImpType;

    private String enable;

    public static final String ENABLE_STATUS = "1";
    public static final String DISABLE_STATUS = "0";

    private String publishFlag;


    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Long getModifyierId() {
        return modifyierId;
    }

    public void setModifyierId(Long modifyierId) {
        this.modifyierId = modifyierId;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getIndexModel() {
        return indexModel;
    }

    public void setIndexModel(String indexModel) {
        this.indexModel = indexModel;
    }

    public Long getMaxTokens() {
        return maxTokens;
    }

    public void setMaxTokens(Long maxTokens) {
        this.maxTokens = maxTokens;
    }

    public String getKnowledgeFace() {
        return knowledgeFace;
    }

    public void setKnowledgeFace(String knowledgeFace) {
        this.knowledgeFace = knowledgeFace;
    }

    public String getKnowledgeName() {
        return knowledgeName;
    }

    public void setKnowledgeName(String knowledgeName) {
        this.knowledgeName = knowledgeName;
    }

    public String getKnowledgeDes() {
        return knowledgeDes;
    }

    public void setKnowledgeDes(String knowledgeDes) {
        this.knowledgeDes = knowledgeDes;
    }

    public String getDataImpType() {
        return dataImpType;
    }

    public void setDataImpType(String dataImpType) {
        this.dataImpType = dataImpType;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public String getModifyierName() {
        return modifyierName;
    }

    public void setModifyierName(String modifyierName) {
        this.modifyierName = modifyierName;
    }

    public String getEnable() {
        return enable;
    }

    public void setEnable(String enable) {
        this.enable = enable;
    }

    public Long getCurrentUserId() {
        return currentUserId;
    }

    public void setCurrentUserId(Long currentUserId) {
        this.currentUserId = currentUserId;
    }

    public String getSearchFlag() {
        return searchFlag;
    }

    public void setSearchFlag(String searchFlag) {
        this.searchFlag = searchFlag;
    }

    public Long getCurrentDeptId() {
        return currentDeptId;
    }

    public void setCurrentDeptId(Long currentDeptId) {
        this.currentDeptId = currentDeptId;
    }

    public List<Long> getParentDeptIds() {
        return parentDeptIds;
    }

    public void setParentDeptIds(List<Long> parentDeptIds) {
        this.parentDeptIds = parentDeptIds;
    }

    public String getPublishFlag() {
        return publishFlag;
    }

    public void setPublishFlag(String publishFlag) {
        this.publishFlag = publishFlag;
    }
}
