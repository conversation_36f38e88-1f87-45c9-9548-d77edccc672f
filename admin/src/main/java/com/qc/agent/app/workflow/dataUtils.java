package com.qc.agent.app.workflow;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.TextStyle;
import java.util.Locale;

public class dataUtils {
    public static JSONObject extractConditionResponse(String content) {
        int startIndex = content.indexOf("###") + 3;
        int endIndex = content.indexOf("###", startIndex);
        if (startIndex == -1 || endIndex == -1) {
            throw new IllegalArgumentException("无法找到###之间的内容");
        }
        String jsonString = content.substring(startIndex, endIndex).trim();
        return JSON.parseObject(jsonString);
    }

    public static String getCurrentDate() {
        LocalDate today = LocalDate.now();
        String dayOfWeek = today.getDayOfWeek().getDisplayName(TextStyle.FULL, Locale.CHINA);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日");
        String formattedDate = today.format(formatter);
        return "今天是" + formattedDate + dayOfWeek + "，";
    }
}
