package com.qc.agent.app.knowledge.service;

import com.qc.agent.app.knowledge.model.KnowledgeQuestionAnswerSettingInfo;
import com.qc.agent.app.knowledge.model.KnowledgeQuestionAnswerSettingParams;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @className KnowledgeQuestionAnswerSettingService
 * @description TODO
 * @date 2024/2/6 16:16
 */
public interface KnowledgeQuestionAnswerSettingService {

    KnowledgeQuestionAnswerSettingInfo getInfo(KnowledgeQuestionAnswerSettingParams params);
    int save(KnowledgeQuestionAnswerSettingParams params);
    int update(KnowledgeQuestionAnswerSettingParams params);

    List<Map<String, Object>> findAiVendors(Map<String, Object> params);
}
