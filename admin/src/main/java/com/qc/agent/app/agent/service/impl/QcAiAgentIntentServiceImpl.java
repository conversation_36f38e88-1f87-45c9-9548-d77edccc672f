package com.qc.agent.app.agent.service.impl;

import com.qc.agent.app.agent.mapper.QcAiAgentModelMapper;
import com.qc.agent.app.agent.model.po.QcAiAgentModel;
import com.qc.agent.app.agent.model.query.QcAiAgentIntentQuery;
import com.qc.agent.app.agent.service.QcAiAgentIntentService;
import com.qc.agent.app.agent.service.tool.QcAiAgentConversationTool;
import com.qc.agent.app.workflow.WorkflowLLAClient;
import com.qc.agent.common.exception.BizException;
import com.qc.agent.lla.model.LLAConfig;
import com.qc.agent.lla.model.LLAProvider;
import com.qc.agent.lla.model.LLARequest;
import com.qc.agent.lla.model.LLAResponse;
import com.qc.agent.platform.register.UserManager;
import com.qc.agent.platform.util.UUIDUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-05-29
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class QcAiAgentIntentServiceImpl implements QcAiAgentIntentService {

    private static final String INTENT_RECOGNITION_PROMPT_PREFIX = """
            你是一个专业的用户意图识别助手，负责从用户的自然语言输入中识别其核心意图。
            🎯 你的任务：
            结合 当前用户输入 与（如果提供）历史问答上下文，判断用户此刻的真实目的。仅返回结构化的 JSON 格式响应。
            如下所示：
            {
              "intent": "<意图编码>"
            }
            你需要识别以下意图类型：
            """;

    private static final String INTENT_RECOGNITION_PROMPT_SUFFIX = """
            现在请分析以下用户输入，并返回识别到的意图，如果用户的输入和列举出的意图都不匹配，则归入其他（OTHER），格式必须为 JSON：
            """;

    private static final String GENERATE_EXAMPLE_PROMPT_PREFIX = """
            请根据以下用户的意图，意图名称(意图编码),生成一些示例(每个意图都生成一个示例)：
            """;

    private static final String GENERATE_EXAMPLE_PROMPT_SUFFIX = """
            格式为
            示例：
            【示例 1】
            用户输入：意图1
            输出：
            {
              "intent": "<意图编码>"
            }
            """;

    private static final BigDecimal DEFAULT_MODEL_TEMPERATURE = new BigDecimal("0.5");
    private static final BigDecimal DEFAULT_MODEL_TOP_P = new BigDecimal("0.5");
    private static final BigDecimal DEFAULT_MODEL_MAX_TOKENS = new BigDecimal("1000");


    private final QcAiAgentModelMapper qcAiAgentModelMapper;
    private final QcAiAgentConversationTool qcAiAgentConversationTool;

    // Helper method to construct the log prefix
    private String getLogPrefix(Long agentId) {
        // For now, using a placeholder string as UserManager is not provided in the context of this code:
        Long tenantId = UserManager.getTenantUser().getTenantId();

        String agentIdForLog = (agentId != null) ? agentId.toString() : "N/A";
        return String.format("[intent_generate_prompt,tenant_id=%s,agent_id=%s] ", tenantId, agentIdForLog);
    }


    @Override
    public Map<String, Object> generatePrompt(QcAiAgentIntentQuery query) {
        Long agentId = (query != null) ? query.getAgentId() : null;
        String logPrefix = getLogPrefix(agentId);

        log.debug("{}Attempting to generate intent prompt for query: {}", logPrefix, query);

        if (query == null || CollectionUtils.isEmpty(query.getIntentList())) {
            log.warn("{}Intent list is null or empty in the query: {}. Cannot generate intent prompt.", logPrefix, query);
            throw new BizException("Intent list cannot be null or empty for prompt generation.");
        }

        if (log.isDebugEnabled()) {
            String intentsPreview = query.getIntentList().stream()
                    .limit(5)
                    .collect(Collectors.joining(", "));
            if (query.getIntentList().size() > 5) {
                intentsPreview += "...";
            }
            log.debug("{} Intent list: [{}] (total size: {})", logPrefix, intentsPreview, query.getIntentList().size());
        }

        Map<String, Object> resultMap = new HashMap<>(16);
        StringBuilder intentPromptBuilder = new StringBuilder();
        int intentCodeCounter = 0;
        for (String intent : query.getIntentList()) {
            String intentCode = ++intentCodeCounter +"";
            if (Objects.equals(intent, "其他")) {
                intentCode = "OTHER";
            }
            intentPromptBuilder.append(intent)
                    .append("(")
                    .append(intentCode)
                    .append(")\n");
        }

        log.debug("{}Fetching AI Agent Model.", logPrefix);
        QcAiAgentModel qcAiAgentModel = qcAiAgentModelMapper.selectByAgentId(query.getAgentId());

        if (qcAiAgentModel == null) {
            log.warn("{}AI Agent Model not found for agent ID: {}. use kimi-moonshot-v1-auto.", logPrefix, agentId);
            qcAiAgentModel = qcAiAgentModelMapper.selectById(93L);
        }

        log.debug("{}Generating full prompt with examples using LLM.", logPrefix);
        String prompt = generatePrompt(intentPromptBuilder.toString(), qcAiAgentModel, query.getAgentId()); // Pass agentId

        if (log.isDebugEnabled()) {
            log.debug("{}Generated final prompt. Length: {}. Snippet: '{}...'", logPrefix, prompt.length(), prompt.substring(0, Math.min(prompt.length(), 100)).replace("\n", "\\n"));
        }

        resultMap.put("prompt", prompt);
        log.debug("{}Finished generating intent prompt.", logPrefix);
        return resultMap;
    }


    public String generatePrompt(String intentListWithCodes, QcAiAgentModel qcAiAgentModel, Long agentId) {
        String logPrefix = getLogPrefix(agentId);

        if (log.isDebugEnabled()) {
            log.debug("{}generatePromptInternal - Entry. Intent list (snippet): '{}...'", logPrefix, intentListWithCodes.substring(0, Math.min(intentListWithCodes.length(), 100)).replace("\n", "\\n"));
        }

        // initLLAConfig will also need agentId for its own logging, or use qcAiAgentModel.getAgentId()
        LLAConfig llaConfig = initLLAConfig(qcAiAgentModel, agentId); // qcAiAgentModel might be null if not found

        String exampleGenRequestId = String.valueOf(UUIDUtils.getUUID2Long());
        String exampleGenContent = GENERATE_EXAMPLE_PROMPT_PREFIX +
                intentListWithCodes +
                GENERATE_EXAMPLE_PROMPT_SUFFIX;

        LLARequest llaRequest = LLARequest.builder()
                .id(exampleGenRequestId)
                .content(exampleGenContent)
                .build();

        if (log.isDebugEnabled()) {
            log.debug("{}LLARequest for example generation (ID: {}). Content length: {}. Content snippet: '{}...'", logPrefix, llaRequest.getId(), llaRequest.getContent().length(), llaRequest.getContent().substring(0, Math.min(llaRequest.getContent().length(), 200)).replace("\n", "\\n"));
        }

        LLAResponse conditionResponse;
        try {
            log.info("{}Sending request to LLM for example generation. Provider: {}, Model: {}", logPrefix, llaConfig.getProvider(), llaConfig.getModel()); // LLAConfig itself might be null if qcAiAgentModel was null
            conditionResponse = WorkflowLLAClient.send(llaConfig, llaRequest, false);
        } catch (Exception e) {
            log.error("{}Error calling LLM for example generation: {}", logPrefix, e.getMessage(), e);
            throw new BizException("Failed to generate intent examples from LLM for agent ID " + agentId, e);
        }

        String exampleContent = "";
        if (conditionResponse == null) {
            log.warn("{}LLM returned a null response object for example generation. Proceeding with no examples.", logPrefix);
        } else {
            String rawExampleContent = conditionResponse.getContent();
            if (rawExampleContent == null || rawExampleContent.isBlank()) {
                log.warn("{}LLM returned empty or blank content for example generation. Proceeding with no examples.", logPrefix);
            } else {
                if (log.isDebugEnabled()) {
                    log.debug("{}LLM response for examples received. Content length: {}. Snippet: '{}...'", logPrefix, rawExampleContent.length(), rawExampleContent.substring(0, Math.min(rawExampleContent.length(), 200)).replace("\n", "\\n"));
                }
                exampleContent = "\n" + rawExampleContent.trim() + "\n";
            }
            qcAiAgentConversationTool.insertPromptRecord(agentId, conditionResponse.getUsage(), logPrefix);
        }

        String finalPrompt = INTENT_RECOGNITION_PROMPT_PREFIX +
                intentListWithCodes +
                exampleContent +
                INTENT_RECOGNITION_PROMPT_SUFFIX;
        log.debug("{}Composed final intent recognition prompt template. Length: {}", logPrefix, finalPrompt.length());
        return finalPrompt;
    }

    // Made this method non-static to use getLogPrefix, or pass logPrefix as parameter.
    // Using qcAiAgentModel.getAgentId() internally for the prefix.
    public LLAConfig initLLAConfig(QcAiAgentModel qcAiAgentModel, Long agentId) {
        Objects.requireNonNull(qcAiAgentModel, "QcAiAgentModel cannot be null for LLAConfig initialization. Agent details might be missing or not found.");

        String logPrefix = getLogPrefix(agentId);

        log.debug("{}Initializing LLAConfig. Model: {}, Provider: {}", logPrefix, qcAiAgentModel.getModel(), qcAiAgentModel.getVendor());

        LLAProvider provider = LLAProvider.getEnumByValue(qcAiAgentModel.getVendor());
        if (provider == null) {
            log.error("{}Invalid LLAProvider vendor string: {}", logPrefix, qcAiAgentModel.getVendor());
            throw new BizException("Invalid LLAProvider specified: " + qcAiAgentModel.getVendor() + " for agentId: " + agentId);
        }

        LLAConfig config = LLAConfig.builder()
                .apiKey(qcAiAgentModel.getKey())
                .secretKey(qcAiAgentModel.getSecret())
                .stream(true)
                .provider(provider)
                .endpoint(qcAiAgentModel.getModelEndPoint())
                .model(qcAiAgentModel.getModel())
                .build();

        config.setModelTemperature(DEFAULT_MODEL_TEMPERATURE);
        config.setModelTopP(DEFAULT_MODEL_TOP_P);
        config.setModelMaxTokens(DEFAULT_MODEL_MAX_TOKENS);

        log.debug("{}LLAConfig initialized successfully. Provider: {}, Model: {}, Temperature: {}, TopP: {}, MaxTokens: {}", logPrefix, config.getProvider(), config.getModel(), config.getModelTemperature(), config.getModelTopP(), config.getModelMaxTokens());
        return config;
    }
}