package com.qc.agent.app.agent.controller;

import com.alibaba.fastjson.JSONObject;
import com.qc.agent.app.agent.model.dto.AgentPromptSaveDTO;
import com.qc.agent.app.agent.model.dto.PromptOptimizedDTO;
import com.qc.agent.app.agent.model.po.QcAiAgentPrompt;
import com.qc.agent.app.agent.service.QcAiAgentPromptService;
import com.qc.agent.common.core.Message;
import com.qc.agent.common.exception.BizException;
import com.qc.agent.platform.register.UserManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/ai-agent/agent-prompt")
@Slf4j
public class QcAiAgentPromptController {

    @Resource
    private QcAiAgentPromptService qcAiAgentPromptService;

    @PostMapping("/save")
    public Message addPrompt(@RequestBody AgentPromptSaveDTO agentPromptSaveDTO) {
        try {
            qcAiAgentPromptService.savePrompt(agentPromptSaveDTO);
        } catch (BizException e) {
            return Message.of().error(e.getMessage());
        }
        return Message.of().ok();
    }

    @GetMapping("/delete/{id}")
    public Message deletePrompt(@PathVariable Long id, Long userId, String userName) {
        qcAiAgentPromptService.deletePrompt(id, userId, userName);
        return Message.of().ok();
    }

    @GetMapping("/{id}")
    public Message getPromptById(@PathVariable Long id) {
        QcAiAgentPrompt data = qcAiAgentPromptService.getPromptById(id);
        return Message.of().data(data).ok();
    }

    @GetMapping("/all")
    public Message getAllPrompts() {
        List<QcAiAgentPrompt> data = qcAiAgentPromptService.getAllPrompts();
        return Message.of().data(data).ok();
    }

    @GetMapping("/query")
    public Message queryPrompts(String recommend, String mine) {
        List<QcAiAgentPrompt> data = qcAiAgentPromptService.queryPrompts(recommend, mine);
        return Message.of().data(data).ok();
    }

    @PostMapping("/search.do")
    public Message searchPrompts(@RequestBody JSONObject jsonObject) {
        jsonObject.put("userId", UserManager.getTenantUser().getUserId());
        List<QcAiAgentPrompt> data = qcAiAgentPromptService.searchPrompts(jsonObject);
        return Message.of().data(data).ok();
    }


    /**
     * 自动优化提示词
     * @param dto
     * @return
     */
    @PostMapping("/optimizedPrompt.do")
    public SseEmitter optimizedPrompt(@RequestBody PromptOptimizedDTO dto) {
        SseEmitter sseEmitter = qcAiAgentPromptService.optimizedPrompt(dto);
        return sseEmitter;
    }
}
