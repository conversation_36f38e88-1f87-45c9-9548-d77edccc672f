package com.qc.agent.app.agent.service.impl;

import com.qc.agent.app.agent.mapper.PromptVariableRecordMapper;
import com.qc.agent.app.agent.model.entity.PromptVariableRecord;
import com.qc.agent.app.agent.service.PromptVariableRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 提示词变量记录服务实现类
 */
@Slf4j
@Service
public class PromptVariableRecordServiceImpl implements PromptVariableRecordService {

    @Resource
    private PromptVariableRecordMapper promptVariableRecordMapper;

    @Override
    public List<PromptVariableRecord> getVariableRecordsByConfigId(Long configId) {
        if (configId == null) {
            return null;
        }
        return promptVariableRecordMapper.selectByConfigId(configId);
    }

    @Override
    public List<PromptVariableRecord> getVariableRecordsByConfigIdAndDimensionCodeAndPromptType(
            Long configId, String dimensionCode, String promptType) {
        if (configId == null || dimensionCode == null || promptType == null) {
            return null;
        }
        return promptVariableRecordMapper.selectByConfigIdAndDimensionCodeAndPromptType(
                configId, dimensionCode, promptType);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateVariableValues(Long configId, String dimensionCode, String promptType, 
                                       Map<String, String> variableValues) {
        // 注意：PromptVariableRecord表只是配置表，不存储具体的变量值
        // 具体的变量值应该存储在CustomerVariableData表中
        // 这个方法现在主要用于更新变量记录的配置信息，而不是变量值
        if (configId == null || dimensionCode == null || promptType == null) {
            return false;
        }

        // 获取现有的变量记录
        List<PromptVariableRecord> existingRecords = promptVariableRecordMapper
                .selectByConfigIdAndDimensionCodeAndPromptType(configId, dimensionCode, promptType);

        if (CollectionUtils.isEmpty(existingRecords)) {
            return false;
        }

        // 更新变量记录的修改时间（如果需要的话）
        for (PromptVariableRecord record : existingRecords) {
            record.setModifyTime(LocalDateTime.now());
            promptVariableRecordMapper.updateById(record);
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteVariableRecordsByConfigId(Long configId) {
        if (configId == null) {
            return false;
        }
        return promptVariableRecordMapper.deleteByConfigId(configId) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteVariableRecordsByConfigIdAndDimensionCodeAndPromptType(
            Long configId, String dimensionCode, String promptType) {
        if (configId == null || dimensionCode == null || promptType == null) {
            return false;
        }
        return promptVariableRecordMapper.deleteByConfigIdAndDimensionCodeAndPromptType(
                configId, dimensionCode, promptType) > 0;
    }
}
