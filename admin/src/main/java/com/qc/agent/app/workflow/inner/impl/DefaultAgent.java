package com.qc.agent.app.workflow.inner.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.qc.agent.app.agent.mapper.QcAiAgentConversationMapper;
import com.qc.agent.app.agent.mapper.QcAiAgentKnowledgeMapper;
import com.qc.agent.app.agent.model.dto.KnowledgeProcessResult;
import com.qc.agent.app.agent.model.po.QcAiAgent;
import com.qc.agent.app.agent.model.po.QcAiAgentConversation;
import com.qc.agent.app.agent.model.po.QcAiAgentConversationQuote;
import com.qc.agent.app.agent.service.impl.QcAiAgentServiceImpl;
import com.qc.agent.app.agent.util.DataBaseMapperUtils;
import com.qc.agent.app.file_process.pojo.FileContextCachingParam;
import com.qc.agent.app.file_process.pojo.QcAiParseFileResult;
import com.qc.agent.app.file_process.service.FileContextCachingService;
import com.qc.agent.app.workflow.WorkflowLLAClient;
import com.qc.agent.app.workflow.inner.AgentType;
import com.qc.agent.app.workflow.inner.impl.tools.LLMTools;
import com.qc.agent.app.workflow.inner.mapper.QcAiAgentConversationQuoteMapper;
import com.qc.agent.app.workflow.inner.mapper.QcAiAgentIntentRecognitionMapper;
import com.qc.agent.app.workflow.inner.pojo.QcAiAgentIntentRecognition;
import com.qc.agent.app.workflow.inner.pojo.QcAiKnowledgeFile;
import com.qc.agent.app.workflow.inner.pojo.QcAiKnowledgeRelated;
import com.qc.agent.common.core.Message;
import com.qc.agent.common.core.RequestHolder;
import com.qc.agent.common.core.TenantUser;
import com.qc.agent.lla.model.*;
import com.qc.agent.platform.register.UserManager;
import com.qc.agent.platform.sse.SseClient;
import com.qc.agent.platform.sse.SseMessage;
import com.qc.agent.platform.sse.dto.MessageType;
import com.qc.agent.platform.util.UUIDUtils;
import com.qc.agent.vectordb.pojo.SearchContent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.qc.agent.app.constants.CommonConstants.*;

/**
 * 默认智能体实现类
 *
 * <AUTHOR>
 * @date 2025/3/19 15:42:07
 */
@Slf4j
@Component
public class DefaultAgent extends AbstractIAgentInvoker {

    // 常量定义
    private static final String DEFAULT_NO_ANSWER = "抱歉，我无法回答该问题的准确信息。如果您有任何其他问题需要帮助，请随时告诉我。";
    private static final String SUCCESS_STATUS = "1";
    private static final int DEFAULT_MAX_RECALL_COUNT = 5;
    private static final double DEFAULT_MIN_MATCH_THRESHOLD = 0.5;
    private static final double DEFAULT_QA_MIN_MATCH_THRESHOLD = 0.9;
    private static final String DEFAULT_SEARCH_SCOPE = "2"; // "2" means search knowledge and then LLM
    private static final String SEARCH_SCOPE_KNOWLEDGE_ONLY = "1"; // "1" means search knowledge only



    @Resource
    private QcAiAgentKnowledgeMapper qcAiAgentKnowledgeMapper;
    @Resource
    private com.qc.agent.vectordb.tencentdb.tencentDbClient tencentDbClient;
    @Resource
    private QcAiAgentConversationMapper qcAiAgentConversationMapper;
    @Resource
    private QcAiAgentConversationQuoteMapper qcAiAgentConversationQuoteMapper;
    @Resource
    private QcAiAgentIntentRecognitionMapper qcAiAgentIntentRecognitionMapper;

    @Resource
    private QcAiAgentServiceImpl qcAiAgentServiceImpl;

    @Resource
    private Map<String, FileContextCachingService> fileContextCachingServiceMap;

    @Resource
    private LLMTools llmTools;

    @Override
    public AgentType determineAgent() {
        return AgentType.DEFAULT;
    }


    @Override
    public Message invoke(QcAiAgent qcAiAgent, LLAConfig config, LLARequest request, SseClient client) {
        long startTime = System.currentTimeMillis();
        Long agentId = qcAiAgent.getId();
        String requestId = request.getId();
        TenantUser currentUser = RequestHolder.getThreadLocalUser();

        Long tenantId = (currentUser != null) ? currentUser.getTenantId() : null;
        String logPrefix = String.format("[tenantId=%s, agentId=%s, requestId=%s]", tenantId, agentId, requestId);
        String originalQuestion = request.getContent();

        log.info("{} INVOKE_ENTRY - AgentType: DEFAULT, Question: '{}'", logPrefix, StringUtils.truncate(originalQuestion, 100));

        if (currentUser == null) {
            log.error("{} INVOKE_ERROR - Current user is null. Cannot proceed.", logPrefix);
            LLMTools.directReturnMessage(client, request, logPrefix, "用户未登录或会话已失效");
            return Message.of().error("用户未登录或会话已失效");
        }

        try {
            log.info("{} INVOKE_STEP - User identified. TenantId: {}", logPrefix, tenantId);

            QcAiAgentIntentRecognition intent = Optional.ofNullable(
                    relatedKnowledgeList(qcAiAgent, config, request, logPrefix, tenantId)
            ).orElseGet(() -> {
                log.warn("{} INVOKE_STEP - Intent recognition returned null. Using fallback intent.", logPrefix);
                QcAiAgentIntentRecognition fallback = new QcAiAgentIntentRecognition();
                fallback.setSearchScope(qcAiAgent.getSearchScope());
                fallback.setNullResultAnswer(qcAiAgent.getNullResultAnswer());
                return fallback;
            });

            log.info("{} INVOKE_STEP - Intent processed. MaxRecallCount={}, MinMatchThreshold={}, SearchScope={}",
                    logPrefix, intent.getMaxRecallCount(), intent.getMinMatchThreshold(), intent.getSearchScope());

            if (intent.getId() != null) {
                qcAiAgentConversationMapper.updateIntentId(intent.getId(), Long.parseLong(requestId));
            }

            KnowledgeProcessResult result = intent.getRelateKnowledgeList().isEmpty()
                    ? handleNoKnowledge(intent, client, request, logPrefix, qcAiAgent)
                    : searchKnowledgeContent(intent, request, client, logPrefix, config);

            if (result.getDirectReturn()) {
                log.info("{} INVOKE_END - Direct return after knowledge search. Total time: {}ms",
                        logPrefix, System.currentTimeMillis() - startTime);
                return Message.of().ok();
            }

            buildRequest(request, qcAiAgent, request.getContent(), result, tenantId, logPrefix);
            log.info("{} INVOKE_STEP - Sending request to LLM asynchronously.", logPrefix);
            WorkflowLLAClient.sendAsync(config, request, client);

            log.info("{} INVOKE_EXIT - LLM call initiated asynchronously. Total time: {}ms",
                    logPrefix, System.currentTimeMillis() - startTime);
            return Message.of().ok();
        } catch (Exception e) {
            log.error("{} INVOKE_ERROR - Unexpected failure. Total time: {}ms", logPrefix,
                    System.currentTimeMillis() - startTime, e);
            LLMTools.directReturnMessage(client, request, logPrefix, e.getMessage());
            return Message.of().error("处理请求时发生错误");
        }
    }

    private KnowledgeProcessResult handleNoKnowledge(QcAiAgentIntentRecognition intent, SseClient client,
                                                     LLARequest request, String logPrefix,
                                                     QcAiAgent qcAiAgent) throws IOException {
        log.info("{} INVOKE_STEP - No related knowledge found. SearchScope: {}", logPrefix, intent.getSearchScope());
        KnowledgeProcessResult result;
        // 没有关联知识库，判断是否关联了文件
        if (CollectionUtils.isNotEmpty(intent.getRelateFileList())) {
            FileContextCachingParam fileContextCachingParam = FileContextCachingParam.build()
                    .withFileList(intent.getRelateFileList())
                    .withApiKey(qcAiAgent.getApiKey())
                    .withCacheTag(qcAiAgent.getCacheTag())
                    .withModel(qcAiAgent.getModel())
                    .withTenantId(intent.getTenantId())
                    .withVendor(qcAiAgent.getVendor())
                    .withAgentId(qcAiAgent.getId())
                    .withIntentId(intent.getId())
                    .withModelEndPoint(qcAiAgent.getModelEndPoint())
                    .withLogPrefix(logPrefix);
            String implName = LLAProvider.valueOf(qcAiAgent.getVendor().toUpperCase()).getFileParseServiceImplName();
            FileContextCachingService fileContextCachingService = fileContextCachingServiceMap.get(implName);
            QcAiParseFileResult parseResult = fileContextCachingService.getCacheInfo(fileContextCachingParam);
            request.setCacheInfoList(parseResult.getCacheInfoList());
            request.setContextId(parseResult.getContextId());
            Long finalConvId =Long.parseLong(request.getId());
        
            List<QcAiAgentConversationQuote> quoteList = intent.getRelateFileList().stream().map(file->createQuote(file.getName(), finalConvId, logPrefix)).toList();
            log.debug("{} HANDLE_FILE_SEARCH_RESULTS_STEP - Generated {} quote objects", logPrefix, quoteList.size());
            try {
                log.info("{} HANDLE_FILE_SEARCH_RESULTS_STEP - Updating conversation {} reference with {} quotes and search results JSON.",
                        logPrefix, finalConvId, quoteList.size());
                qcAiAgentConversationMapper.updateReference(finalConvId, quoteList.size(), "");
    
                log.info("{} HANDLE_FILE_SEARCH_RESULTS_STEP - Batch inserting {} quotes into DB.", logPrefix, quoteList.size());
                DataBaseMapperUtils.batchInsert(quoteList, qcAiAgentConversationQuoteMapper::batchInsert);
                request.setQuoteCount(quoteList.size());
                log.debug("{} HANDLE_FILE_SEARCH_RESULTS_STEP - DB operations for quotes completed successfully.", logPrefix);
            } catch (Exception e) {
                log.error("{} HANDLE_FILE_SEARCH_RESULTS_ERROR - Failed DB operations for quotes (updateReference or batchInsert). ConvId: {}",
                        logPrefix, finalConvId, e);
            }
    
            log.debug("{} HANDLE_FILE_SEARCH_RESULTS_EXIT - Finished handling search results.", logPrefix);    
            return new KnowledgeProcessResult(false, "", List.of());
        } else {
            result = KnowledgeContentIsEmptyAndReturnNullAnswer(intent, client, request, logPrefix);
        }
        log.info("{} INVOKE_STEP - Proceeding without knowledge content.", logPrefix);
        return result;
    }

    private QcAiAgentConversationQuote createQuote(String docName, Long conversationId, String logPrefix) {
        var quote = new QcAiAgentConversationQuote();
        quote.setId(UUIDUtils.getUUID2Long());
        quote.setStatus(SUCCESS_STATUS); // "1" for success

        TenantUser tenantUser = UserManager.getTenantUser();
        if (tenantUser != null && tenantUser.getUserId() != null) {
            quote.setCreatorId(tenantUser.getUserId());
        } else {
            log.warn("{} CREATE_QUOTE_WARN - UserManager.getTenantUser() or UserId is null. CreatorId will be null for quote. ConversationId: {}",
                    logPrefix, conversationId);
        }

        quote.setCreateTime(LocalDateTime.now());
        quote.setConversationId(conversationId);
        quote.setDocName(StringUtils.truncate(docName, 255));
        return quote;
    }


    public QcAiAgentIntentRecognition relatedKnowledgeList(QcAiAgent qcAiAgent, LLAConfig config,
                                                           LLARequest request, String logPrefix,
                                                           Long tenantId) {
        log.info("{} RELATED_KNOWLEDGE_LIST_ENTRY - AgentId: {}, TenantId: {}, IntentEnabled: {}",
                logPrefix, qcAiAgent.getId(), tenantId, isIntentEnabled(qcAiAgent) ? "1" : "N/A");

        QcAiAgentIntentRecognition intent = isIntentEnabled(qcAiAgent)
                ? handleIntentRecognition(qcAiAgent, config, request, logPrefix, tenantId)
                : createDefaultIntentForAgent(qcAiAgent, tenantId, logPrefix);

        // Post-processing and fallback defaults
        if (intent == null) {
            intent = new QcAiAgentIntentRecognition();
        }
        intent.setAgentId(Optional.ofNullable(intent.getAgentId()).orElse(qcAiAgent.getId()));
        intent.setNullResultAnswer(Optional.ofNullable(intent.getNullResultAnswer()).filter(StringUtils::isNotBlank).orElse(qcAiAgent.getNullResultAnswer()));
        intent.setMaxRecallCount(getOrDefault(intent.getMaxRecallCount(), qcAiAgent.getMaxRecallCount(), DEFAULT_MAX_RECALL_COUNT, logPrefix, "MaxRecallCount"));
        intent.setMinMatchThreshold(getOrDefault(intent.getMinMatchThreshold(), qcAiAgent.getMinMatchThreshold(), DEFAULT_MIN_MATCH_THRESHOLD, logPrefix, "MinMatchThreshold"));
        intent.setQaMinMatchThreshold(getOrDefault(intent.getQaMinMatchThreshold(), qcAiAgent.getQaMinMatchThreshold(), DEFAULT_QA_MIN_MATCH_THRESHOLD, logPrefix, "QaMinMatchThreshold"));
        intent.setSearchScope(StringUtils.defaultIfBlank(intent.getSearchScope(), StringUtils.defaultIfBlank(qcAiAgent.getSearchScope(), DEFAULT_SEARCH_SCOPE)));
        intent.setRelateKnowledgeList(Optional.ofNullable(intent.getRelateKnowledgeList()).orElseGet(ArrayList::new));
        intent.setTenantId(tenantId);

        log.info("{} RELATED_KNOWLEDGE_LIST_EXIT - Final Intent: MaxRecallCount={}, MinMatchThreshold={}, QaMinMatchThreshold={}, SearchScope={}, RelatedKnowledgeSize={}",
                logPrefix, intent.getMaxRecallCount(), intent.getMinMatchThreshold(), intent.getQaMinMatchThreshold(), intent.getSearchScope(), intent.getRelateKnowledgeList().size());
        return intent;
    }

    private <T> T getOrDefault(T value, T agentValue, T defaultValue, String logPrefix, String fieldName) {
        T finalValue = Optional.ofNullable(value).orElse(Optional.ofNullable(agentValue).orElse(defaultValue));
        log.debug("{} RELATED_KNOWLEDGE_LIST_DETAIL - Using default/agent {}: {}", logPrefix, fieldName, finalValue);
        return finalValue;
    }

    private boolean isIntentEnabled(QcAiAgent agent) {
        return agent.getIntentIsEnabled() != null && "1".equals(agent.getIntentIsEnabled());
    }

    /**
     * Handles the intent recognition process by querying an LLM and then fetching related knowledge.
     *
     * @param agent     The AI agent configuration.
     * @param config    The LLA configuration.
     * @param request   The original LLA request from the user.
     * @param logPrefix A prefix for logging to correlate messages for a single request.
     * @param tenantId  The ID of the tenant.
     * @return A QcAiAgentIntentRecognition object with related knowledge, or a default if recognition fails.
     */
    private QcAiAgentIntentRecognition handleIntentRecognition(QcAiAgent agent, LLAConfig config,
                                                               LLARequest request, String logPrefix, Long tenantId) {
        log.info("{} INTENT_RECOGNITION_START - Intent recognition enabled for original question: '{}'",
                logPrefix, StringUtils.truncate(request.getContent(), 100));

        // 1. Prepare a new request for the intent recognition LLM call to avoid mutating the original request.
        LLARequest intentRequest = createIntentRequest(agent, request);

        // 2. Send request to LLM
        log.debug("{} INTENT_RECOGNITION_SEND - Sending intent prompt to LLM: '{}'",
                logPrefix, StringUtils.truncate(intentRequest.getContent(), 200));
        LLAResponse response = WorkflowLLAClient.send(config, intentRequest, true);

        // 3. Process LLM response
        String responseJson = Optional.ofNullable(response)
                .map(LLAResponse::getContent)
                .orElse("");

        if (StringUtils.isBlank(responseJson)) {
            log.warn("{} INTENT_RECOGNITION_WARN - LLM returned a blank response. Falling back to default knowledge.", logPrefix);
            return createDefaultIntentForAgent(agent, tenantId, logPrefix);
        }

        log.info("{} INTENT_RECOGNITION_RESPONSE - Received LLM response for intent: '{}'", logPrefix, responseJson);

        // 4. Parse intent and fetch knowledge from DB
        try {
            var intentKeyword = parseIntentFromResponse(responseJson);

            if (intentKeyword.isPresent()) {
                String keyword = intentKeyword.get();
                log.info("{} INTENT_RECOGNITION_SUCCESS - Parsed intent keyword: '{}'. Fetching related knowledge.", logPrefix, keyword);
                return fetchKnowledgeForIntent(agent, keyword, logPrefix);
            } else {
                log.warn("{} INTENT_RECOGNITION_WARN - Intent keyword was missing in the parsed JSON. Falling back to default. JSON: {}", logPrefix, responseJson);
            }
        } catch (JSONException e) {
            log.error("{} INTENT_RECOGNITION_ERROR - Failed to parse LLM response as JSON. Response: {}. Error: {}",
                    logPrefix, responseJson, e.getMessage(), e);
        }

        // 5. Fallback to default if any step above fails
        return createDefaultIntentForAgent(agent, tenantId, logPrefix);
    }

    /**
     * Creates a new LLARequest specifically for the intent recognition call.
     * This avoids side effects on the original request object.
     */
    private LLARequest createIntentRequest(QcAiAgent agent, LLARequest originalRequest) {
        var history = buildQaHistory(originalRequest.getQas());

        // Using Java 17 Text Blocks for cleaner multi-line strings
        String intentPrompt = """
                %s
                用户问题：%s
                """.formatted(history, originalRequest.getContent());

        // Create a new request object instead of mutating the original one
        return LLARequest.builder()
                .id(originalRequest.getId())
                .skipAnswerUpdate(true)
                .clientId(originalRequest.getClientId())
                .system(agent.getSplitSqlPrompt())
                .content(intentPrompt)
                .start(LocalDateTime.now())
                .build();
    }

    /**
     * Builds a formatted string from the list of previous questions and answers.
     */
    private String buildQaHistory(List<LLAQa> qas) {
        if (CollectionUtils.isEmpty(qas)) {
            return "历史问答记录：无";
        }
        return "历史问答记录：\n" + qas.stream()
                .map(qa -> "问：%s\n答：%s".formatted(qa.getQuestion(), qa.getAnswer()))
                .collect(Collectors.joining("\n"));
    }

    /**
     * Parses the intent keyword from the LLM's JSON response.
     * It also cleans up common markdown artifacts.
     *
     * @param responseJson The raw JSON string from the LLM.
     * @return An Optional containing the intent keyword if found.
     * @throws JSONException if the string is not valid JSON.
     */
    private Optional<String> parseIntentFromResponse(String responseJson) throws JSONException {
        // Sanitize the response by removing markdown JSON fences
        String cleanJson = responseJson.trim().replaceAll("(?s)```json\\s*|\\s*```", "");
        if (StringUtils.isBlank(cleanJson)) {
            return Optional.empty();
        }

        JSONObject json = JSON.parseObject(cleanJson);
        return Optional.ofNullable(json.getString("intent"))
                .filter(StringUtils::isNotBlank);
    }

    /**
     * Fetches related knowledge from the database based on the recognized intent keyword.
     */
    private QcAiAgentIntentRecognition fetchKnowledgeForIntent(QcAiAgent agent, String intentKeyword, String logPrefix) {
        // The logic for determining 'status' is now more direct.
        var status = "1";
        if (CollectionUtils.isNotEmpty(agent.getIntentList())) {
            log.info("{} - Agent has pre-defined intents. Saving intent recognition record for analysis.", logPrefix);
            status = "0";
            // The service call is better named to reflect its purpose if it's for logging/analysis
            qcAiAgentServiceImpl.saveIntentList(agent.getIntentList(), agent, "0");
        }

        var dbResult = qcAiAgentIntentRecognitionMapper.selectRelatedKnowledgeAndCategoryList(
                intentKeyword,
                agent.getId(),
                UserManager.getTenantUser().getUserId(),
                agent.getDeptIdList(),
                status
        );

        if (dbResult != null) {
            log.info("{} - Successfully fetched related knowledge from database for intent '{}' (Result ID: {}, Code: {}).",
                    logPrefix, intentKeyword, dbResult.getId(), dbResult.getIntentCode());
        } else {
            log.warn("{} - No related knowledge found in database for intent keyword '{}', agent ID {}, user ID {}, depts {}.",
                    logPrefix, intentKeyword, agent.getId(), UserManager.getTenantUser().getUserId(), agent.getDeptIdList());
        }
        return dbResult;
    }


    private QcAiAgentIntentRecognition createDefaultIntentForAgent(QcAiAgent qcAiAgent, Long tenantId, String logPrefix) {
        log.info("{} CREATE_DEFAULT_INTENT - Creating default intent for agentId: {}", logPrefix, qcAiAgent.getId());
        QcAiAgentIntentRecognition defaultIntent = new QcAiAgentIntentRecognition();
        defaultIntent.setTenantId(tenantId);
        defaultIntent.setAgentId(qcAiAgent.getId());
        defaultIntent.setNullResultAnswer(qcAiAgent.getNullResultAnswer());

        List<QcAiKnowledgeRelated> relatedKnowledgeList = CollectionUtils.isEmpty(qcAiAgent.getKnowledgeIdList())
                ? qcAiAgentKnowledgeMapper.selectAssociatedKnowledgeList(qcAiAgent.getId()
                , qcAiAgent.getDeptIdList(), UserManager.getTenantUser().getUserId())
                : qcAiAgentKnowledgeMapper.selectKnowledgeList(qcAiAgent.getKnowledgeIdList()
                , qcAiAgent.getDeptIdList(), UserManager.getTenantUser().getUserId());
        defaultIntent.setRelateKnowledgeList(relatedKnowledgeList);
        log.info("{} CREATE_DEFAULT_INTENT - Associated {} knowledge items by default.", logPrefix, relatedKnowledgeList.size());

        defaultIntent.setMaxRecallCount(qcAiAgent.getMaxRecallCount());
        defaultIntent.setMinMatchThreshold(qcAiAgent.getMinMatchThreshold());
        defaultIntent.setQaMinMatchThreshold(qcAiAgent.getQaMinMatchThreshold());
        defaultIntent.setSearchScope(qcAiAgent.getSearchScope());
        return defaultIntent;
    }

    public KnowledgeProcessResult searchKnowledgeContent(
            QcAiAgentIntentRecognition intent,
            LLARequest request,
            SseClient client,
            String logPrefix, // Pass logPrefix
            LLAConfig config // Pass config

    ) {
        String requestId = request.getId();
        log.info("{} SEARCH_KNOWLEDGE_ENTRY - RequestId: {}", logPrefix, requestId);

        KnowledgeProcessResult result = processKnowledgeContent(client, request, intent, config, logPrefix); // Pass logPrefix and config

        if (result.getDirectReturn()) {
            try {
                long conversationIdNum = Long.parseLong(requestId);
                QcAiAgentConversation conversation = qcAiAgentConversationMapper.selectById(conversationIdNum);
                if (conversation != null) {
                    log.debug("{} SEARCH_KNOWLEDGE_STEP - Conversation ID {} found. Updating duration.", logPrefix, requestId);
                    llmTools.updateConversationDuration(conversation, result.getKnowledgeContent(), logPrefix); // Pass logPrefix
                } else {
                    log.warn("{} SEARCH_KNOWLEDGE_WARN - Conversation not found for ID {} while trying to update duration. Skipping update.", logPrefix, requestId);
                }
            } catch (NumberFormatException e) {
                log.warn("{} SEARCH_KNOWLEDGE_WARN - Invalid conversation ID format '{}'. Cannot parse to long for duration update.",
                        logPrefix, requestId, e);
            } catch (Exception e) {
                log.error("{} SEARCH_KNOWLEDGE_ERROR - Error updating conversation duration for ID {}.", logPrefix, requestId, e);
            }
        } else {
            log.info("{} SEARCH_KNOWLEDGE_STEP - No knowledge content found or result is empty. Skipping conversation duration update based on knowledge content.", logPrefix);
        }
        log.info("{} SEARCH_KNOWLEDGE_EXIT - Search process finished. DirectReturn: {}, ContentPresent: {}",
                logPrefix, result.getDirectReturn(), StringUtils.isNotEmpty(result.getKnowledgeContent()));
        return result;
    }



    private void buildRequest(LLARequest request, QcAiAgent agent, String question, KnowledgeProcessResult result, Long tenantId, String logPrefix) {
        log.debug("{} BUILD_FINAL_QUESTION_ENTRY - AgentId: {}, TenantId: {}, OriginalQuestionLength: {}, KnowledgeContentLength: {}",
                logPrefix, agent.getId(), tenantId, question.length(), StringUtils.length(result.getKnowledgeContent()));
        request.setCacheInfoList(request.getCacheInfoList() == null ? List.of() : request.getCacheInfoList());
        String effectiveKnowledgeContent = StringUtils.defaultString(result.getKnowledgeContent());
        String finalQuestion;

        if (StringUtils.isNotEmpty(agent.getPrompt())) {
            log.debug("{} BUILD_FINAL_QUESTION_STEP - Using agent prompt. Prompt length: {}", logPrefix, agent.getPrompt().length());
            finalQuestion = String.format("%s问题：%s",
                    effectiveKnowledgeContent.isEmpty() ? "。" : "\n已知信息：\n" + effectiveKnowledgeContent + "\n\n",
                    question);
            request.setSystem(String.format("背景：%s", agent.getPrompt()));
            request.setContent(finalQuestion);
        } else {
            log.debug("{} BUILD_FINAL_QUESTION_STEP - No agent prompt. Constructing question with/without knowledge.", logPrefix);
            finalQuestion = effectiveKnowledgeContent.isEmpty() ? question : "已知信息：\n" + effectiveKnowledgeContent + "\n\n问题：" + question;
            request.setContent(finalQuestion);
        }

        log.info("{} BUILD_FINAL_QUESTION_EXIT - Built final question. Length: {}. KnowledgeUsed: {}",
                logPrefix, finalQuestion.length(), !effectiveKnowledgeContent.isEmpty());
    }

    private KnowledgeProcessResult KnowledgeContentIsEmptyAndReturnNullAnswer(QcAiAgentIntentRecognition intent,
                                                                              SseClient client,
                                                                              LLARequest request,
                                                                              String logPrefix) {
        log.info("{} EMPTY_KNOWLEDGE_NULL_ANSWER_ENTRY - SearchScope: {}, NullResultAnswer provided: {}",
                logPrefix, intent.getSearchScope(), StringUtils.isNotBlank(intent.getNullResultAnswer()));

        if (SEARCH_SCOPE_KNOWLEDGE_ONLY.equals(intent.getSearchScope())) {
            String resultContent = StringUtils.isNotBlank(intent.getNullResultAnswer())
                    ? intent.getNullResultAnswer()
                    : DEFAULT_NO_ANSWER;
            log.info("{} EMPTY_KNOWLEDGE_NULL_ANSWER_STEP - Search scope is KNOWLEDGE_ONLY. Returning specific null answer: '{}'",
                    logPrefix, StringUtils.truncate(resultContent, 100));

            var kpr = new KnowledgeProcessResult(true, resultContent, List.of()); // Direct return, content is the null answer
            kpr.setDirectReturn(true); // Explicitly set for clarity

            simulateStreamSend(resultContent, client, 0, request.getId(), request.getClientId(), logPrefix);
            completeClient(client, request, logPrefix);
            log.info("{} EMPTY_KNOWLEDGE_NULL_ANSWER_EXIT - Direct return with default/null answer (SearchScope KNOWLEDGE_ONLY).", logPrefix);
            return kpr;
        }

        log.info("{} EMPTY_KNOWLEDGE_NULL_ANSWER_EXIT - No knowledge related, but SearchScope allows LLM. Proceeding without knowledge search. DirectReturn=false.", logPrefix);
        return new KnowledgeProcessResult(false, "", List.of()); // Not a direct return, no knowledge content
    }

    private KnowledgeProcessResult processKnowledgeContent(
            SseClient client,
            LLARequest request,
            QcAiAgentIntentRecognition intent,
            LLAConfig config, // Pass config
            String logPrefix
    ) {
        log.info("{} PROCESS_KNOWLEDGE_ENTRY - SearchScope: {}, NumRelatedKnowledge: {}",
                logPrefix, intent.getSearchScope(), intent.getRelateKnowledgeList().size());

        log.info("{} PROCESS_KNOWLEDGE_STEP - Starting QA search.", logPrefix);
        KnowledgeProcessResult qaResult = handleQASearch(intent, request, client, config, logPrefix); // Pass config and logPrefix
        log.info("{} PROCESS_KNOWLEDGE_STEP - QA search completed. DirectReturn: {}, ContentPresent: {}",
                logPrefix, qaResult.getDirectReturn(), StringUtils.isNotEmpty(qaResult.getKnowledgeContent()));

        if (qaResult.getDirectReturn()) {
            log.info("{} PROCESS_KNOWLEDGE_EXIT - QA search resulted in direct return. Result from QA search.", logPrefix);
            return qaResult;
        }

        log.info("{} PROCESS_KNOWLEDGE_STEP - QA search did not direct return. Starting file/document search.", logPrefix);
        KnowledgeProcessResult fileResult = handleFileSearch(qaResult, intent, request, client, logPrefix); // Pass logPrefix
        log.info("{} PROCESS_KNOWLEDGE_STEP - File search completed. DirectReturn: {}, ContentPresent: {}",
                logPrefix, fileResult.getDirectReturn(), StringUtils.isNotEmpty(fileResult.getKnowledgeContent()));
        log.info("{} PROCESS_KNOWLEDGE_EXIT - Returning result from file search.", logPrefix);
        return fileResult;
    }

    private Map<String, Set<String>> collection2CategoryIdListMap(QcAiAgentIntentRecognition intent) {
        return intent.getRelateKnowledgeList().stream()
                .collect(Collectors.toMap(
                        k -> QA_COLLECTION_PREFIX + k.getCollectionId(),
                        k -> k.getRelateDocList().stream()
                                .map(QcAiKnowledgeFile::getCategoryId)
                                .filter(Objects::nonNull)
                                .map(String::valueOf)
                                .collect(Collectors.toSet()),
                        (v1, v2) -> {
                            v1.addAll(v2); // 合并 Set
                            return v1;
                        }
                ));
    }

    /**
     * A data-transfer object to hold the context needed for a QA search.
     */
    private record QASearchContext(
            List<String> qaCollectionViews,
            Map<Long, String> knowledgeIdToNameMap,
            Map<String, Set<String>> collectionToCategoryIdsMap
    ) {
    }

    /**
     * Handles a QA search with a multi-step fallback strategy.
     * This method orchestrates the search process, delegating specific tasks to helper methods.
     */
    public KnowledgeProcessResult handleQASearch(
            QcAiAgentIntentRecognition intent,
            LLARequest request,
            SseClient client,
            LLAConfig config,
            String logPrefix
    ) {
        log.info("{} QA_SEARCH_START - TenantId: {}, Question: '{}'",
                logPrefix, intent.getTenantId(), StringUtils.truncate(request.getContent(), 100));

        // Step 1: Prepare all necessary context (collections, maps) for the search.
        var searchContext = prepareQASearchContext(intent, logPrefix);
        if (searchContext == null) {
            log.warn("{} QA_SEARCH_ABORT - No QA collection views found.", logPrefix);
            return new KnowledgeProcessResult(false, "", List.of());
        }

        // Step 2: Perform the initial search with the original question.
        var initialSearchResults = searchQA(intent, request.getContent(), searchContext, logPrefix);

        // Step 3: Try to find a high-confidence answer from the initial search.
        var highConfidenceResult = findHighConfidenceResult(initialSearchResults, intent, request, client, "Initial QA", logPrefix);
        if (highConfidenceResult.isPresent()) {
            return highConfidenceResult.get();
        }
        log.info("{} INITIAL_SEARCH_PRIMARY_FAIL - No results above primary threshold. Proceeding to combined search.", logPrefix);

        // Step 4: Attempt to generate a better "combined" question using conversation history.
        var combinedQuestionOpt = LLMTools.generateCombinedQuestion(config, request, logPrefix);
        request.setContent(combinedQuestionOpt.orElse(request.getContent()));

        // If a combined question couldn't be generated, fallback to using the initial results with a lower threshold.
        if (combinedQuestionOpt.isEmpty()) {
            return checkFallbackOnInitialResults(initialSearchResults, intent, logPrefix)
                    .orElse(new KnowledgeProcessResult(false, "", List.of())); // Final failure
        }

        // Step 5: Execute the search again with the new, combined question.
        var combinedSearchResults = searchQA(intent, combinedQuestionOpt.get(), searchContext, logPrefix);

        // Step 6: Process the results of the combined search, checking both high and low thresholds.
        return processCombinedSearchResults(combinedSearchResults, intent, request, client, logPrefix);
    }

    /**
     * Prepares the context (views, maps) required for the QA search.
     * Returns null if no valid QA collections are found.
     */
    private QASearchContext prepareQASearchContext(QcAiAgentIntentRecognition intent, String logPrefix) {
        var qaCollectionViews = intent.getRelateKnowledgeList().stream()
                .filter(k -> k.getCollectionId() != null && ("QA".equals(k.getDataImpType()) || "PICTURE".equals(k.getDataImpType()) 
                || k.getDataImpType() == null))
                .map(k -> QA_COLLECTION_PREFIX + k.getCollectionId())
                .distinct()
                .toList();

        if (qaCollectionViews.isEmpty()) {
            return null;
        }

        var knowledgeIdToNameMap = intent.getRelateKnowledgeList().stream()
                .filter(item -> item.getName() != null)
                .collect(Collectors.toMap(QcAiKnowledgeRelated::getCollectionId, QcAiKnowledgeRelated::getName, (n1, n2) -> n1));

        var collectionToCategoryIdsMap = collection2CategoryIdListMap(intent);

        log.info("{} QA_SEARCH_VIEWS - Using {} QA collections: {}", logPrefix, qaCollectionViews.size(), qaCollectionViews);
        return new QASearchContext(qaCollectionViews, knowledgeIdToNameMap, collectionToCategoryIdsMap);
    }




    /**
     * Filters search results against the high-confidence QA threshold.
     * If results are found, processes them and returns an Optional<KnowledgeProcessResult>.
     */
    private Optional<KnowledgeProcessResult> findHighConfidenceResult(
            List<SearchContent> searchResults,
            QcAiAgentIntentRecognition intent,
            LLARequest request,
            SseClient client,
            String resultType,
            String logPrefix
    ) {
        double qaThreshold = intent.getQaMinMatchThreshold();
        log.info("{} SEARCH_FILTER - Found {} raw results. Applying primary threshold: {}",
                logPrefix, searchResults.size(), qaThreshold);

        var highConfidenceResults = filterAndSortResults(searchResults, qaThreshold, intent.getMaxRecallCount());

        if (CollectionUtils.isNotEmpty(highConfidenceResults)) {
            log.info("{} SEARCH_SUCCESS - Found {} results above primary threshold. Processing.", logPrefix, highConfidenceResults.size());
            return Optional.of(processAndReturnResults(highConfidenceResults, intent, request, client, resultType, logPrefix));
        }
        return Optional.empty();
    }


    /**
     * Processes the results from the combined question search, checking both high and low thresholds.
     */
    private KnowledgeProcessResult processCombinedSearchResults(
            List<SearchContent> combinedResults,
            QcAiAgentIntentRecognition intent,
            LLARequest request,
            SseClient client,
            String logPrefix
    ) {
        // 1. Check against high threshold
        var highConfidenceResult = findHighConfidenceResult(combinedResults, intent, request, client, "Combined QA", logPrefix);
        if (highConfidenceResult.isPresent()) {
            return highConfidenceResult.get();
        }
        log.info("{} COMBINED_SEARCH_PRIMARY_FAIL - No results above primary threshold. Checking fallback.", logPrefix);

        // 2. Check against low (fallback) threshold
        double fallbackThreshold = intent.getMinMatchThreshold();
        var fallbackCombinedResults = filterAndSortResults(combinedResults, fallbackThreshold, intent.getMaxRecallCount());

        if (CollectionUtils.isNotEmpty(fallbackCombinedResults)) {
            log.info("{} COMBINED_SEARCH_FALLBACK_SUCCESS - Found {} results with fallback threshold. Returning as context.",
                    logPrefix, fallbackCombinedResults.size());
            return processFallbackResults(fallbackCombinedResults);
        }

        log.info("{} QA_SEARCH_END - All search attempts failed to produce a result.", logPrefix);
        return new KnowledgeProcessResult(false, "", List.of());
    }

    /**
     * Checks the initial search results against the lower fallback threshold.
     * This is triggered when the combined search cannot be set up.
     */
    private Optional<KnowledgeProcessResult> checkFallbackOnInitialResults(
            List<SearchContent> allInitialResults,
            QcAiAgentIntentRecognition intent,
            String logPrefix) {

        double fallbackThreshold = intent.getMinMatchThreshold();
        log.info("{} INITIAL_RESULTS_FALLBACK_CHECK - Checking {} initial results against fallback threshold: {}",
                logPrefix, allInitialResults.size(), fallbackThreshold);

        var fallbackInitialResults = filterAndSortResults(allInitialResults, fallbackThreshold, intent.getMaxRecallCount());

        if (CollectionUtils.isNotEmpty(fallbackInitialResults)) {
            log.info("{} INITIAL_RESULTS_FALLBACK_SUCCESS - Found {} results with fallback threshold. Returning as context.",
                    logPrefix, fallbackInitialResults.size());
            return Optional.of(processFallbackResults(fallbackInitialResults));
        }

        log.info("{} INITIAL_RESULTS_FALLBACK_FAIL - No initial results met the fallback threshold.", logPrefix);
        return Optional.empty();
    }

    /**
     * A reusable helper for filtering and sorting search results.
     */
    private List<SearchContent> filterAndSortResults(List<SearchContent> results, double threshold, Integer maxRecallCount) {
        return results.stream()
                .filter(sc -> sc.getScore() >= threshold)
                .sorted(Comparator.comparing(SearchContent::getScore, Comparator.reverseOrder()))
                .limit(maxRecallCount)
                .toList();
    }

    /**
     * Processes results that met the fallback threshold, returning them as context.
     */
    private KnowledgeProcessResult processFallbackResults(List<SearchContent> results) {
        String resultContext = results.stream()
                .map(SearchContent::getAppendix)
                .collect(Collectors.joining(", "));
        return new KnowledgeProcessResult(false, resultContext, results);
    }



    /**
     * Executes a single QA search against the vector database.
     */
    private List<SearchContent> searchQA(
            QcAiAgentIntentRecognition intent,
            String question,
            QASearchContext searchContext,
            String logPrefix
    ) {
        List<SearchContent> results = tencentDbClient.searchQAContent(
                QA_DATABASE_PREFIX + intent.getTenantId(),
                searchContext.qaCollectionViews, question,
                searchContext.knowledgeIdToNameMap,
                searchContext.collectionToCategoryIdsMap,
                intent.getMaxRecallCount(),
                logPrefix
        );
        log.info("{} QA_SEARCH_EXECUTE - QA search for '{}' found {} results.",
                logPrefix, StringUtils.truncate(question, 80), results.size());
        return results;
    }




    private KnowledgeProcessResult processAndReturnResults(
            List<SearchContent> searchContents,
            QcAiAgentIntentRecognition intent,
            LLARequest request,
            SseClient client,
            String searchType, // "Initial QA" or "Combined QA"
            String logPrefix
    ) {
        log.info("{} PROCESS_QA_RESULTS_ENTRY - SearchType: '{}', Found {} entries. Processing top result for direct answer.",
                logPrefix, searchType, searchContents.size());

        // Assuming the first result is the most relevant for direct QA answer
        SearchContent topResult = searchContents.get(0);
        String qaContent = topResult.getAppendix(); // The answer part of QA pair
        if (StringUtils.isBlank(qaContent)) {
            log.warn("{} PROCESS_QA_RESULTS_WARN - Top QA result for SearchType '{}' has blank appendix (answer). RequestId: {}. Score: {}. Original question in SearchContent: '{}'",
                    logPrefix, searchType, request.getId(), topResult.getScore(), StringUtils.truncate(request.getContent(), 100));
        }

        log.info("{} PROCESS_QA_RESULTS_STEP - Top result for '{}' search: Score={}, KnowledgeName='{}', DocName='{}'. Answer to be sent: '{}'",
                logPrefix, searchType, topResult.getScore(), topResult.getKnowledgeName(), topResult.getDocumentName(), StringUtils.truncate(qaContent, 100));

        // Even for direct QA answer, we might want to record all retrieved searchContents as quotes for reference
        handleSearchResults(searchContents, request, intent, logPrefix); // This will set quotes in the request for logging/DB

        log.info("{} PROCESS_QA_RESULTS_STEP - Simulating stream send for direct QA answer. SearchType: '{}'.", logPrefix, searchType);
        try {
            // Quote count for SSE message should reflect what was actually found and potentially relevant
            // handleSearchResults already sets request.setQuoteCount based on searchContents and maxRecallCount
            int quoteCountForSse = request.getQuoteCount() != null ? request.getQuoteCount() : 0;
            simulateStreamSend(qaContent, client, quoteCountForSse, request.getId(), request.getClientId(), logPrefix);
            completeClient(client, request, logPrefix);
            log.debug("{} PROCESS_QA_RESULTS_STEP - Stream send completed and client finalized for SearchType: '{}'.", logPrefix, searchType);
        } catch (Exception e) {
            log.error("{} PROCESS_QA_RESULTS_ERROR - Error during SSE stream send or client completion for SearchType: '{}'.",
                    logPrefix, searchType, e);
        }

        log.info("{} PROCESS_QA_RESULTS_EXIT - {} processing complete. DirectReturn=true.", logPrefix, searchType);
        return new KnowledgeProcessResult(true, qaContent, List.of());
    }

    /**
     * A data-transfer object to hold the context needed for a file search.
     *
     * @param collectionViews   List of collection view names to search in (e.g., "v_collection_123").
     * @param collectionToDocs  Map of collection view name to a list of specific document names to filter by.
     * @param knowledgeIdToName Map of raw knowledge collection ID to its friendly name.
     */
    private record FileSearchContext(
            List<String> collectionViews,
            Map<String, List<String>> collectionToDocs,
            Map<Long, String> knowledgeIdToName
    ) {
    }


    private KnowledgeProcessResult handleFileSearch(
            KnowledgeProcessResult result,
            QcAiAgentIntentRecognition intent,
            LLARequest request,
            SseClient client,
            String logPrefix
    ) {
        // --- 1. Parameter Extraction & Entry Log ---
        var tenantId = intent.getTenantId();
        var question = request.getContent();
        var maxRecall = intent.getMaxRecallCount();

        log.info("{} FILE_SEARCH_ENTRY - TenantId: {}, MaxRecallCount: {}, Question: '{}'",
                logPrefix, tenantId, maxRecall, StringUtils.truncate(question, 100));

        // --- 2. Prepare context for searching (collections, doc names, etc.) ---
        var searchContext = prepareFileSearchContext(intent, logPrefix);

        // If there are no collections to search, decide whether to stop or proceed with prior results.
        if (searchContext.collectionViews().isEmpty()) {
            log.warn("{} FILE_SEARCH_SKIP - No 'RAW' type knowledge sources found to search in.", logPrefix);
            if (result.getSearchContentList().isEmpty()) {
                return KnowledgeContentIsEmptyAndReturnNullAnswer(intent, client, request, logPrefix);
            }
            // If there were prior results (e.g., from QA), we can process them without a file search.
            return processAndFinalizeResults(client, Collections.emptyList(), result, intent, request, logPrefix);
        }

        // --- 3. Execute the actual vector search ---
        var fileSearchResults = executeVectorSearch(tenantId, question, intent, searchContext, logPrefix);
        log.info("{} FILE_SEARCH_RESULT - Found {} raw items from file search.", logPrefix, fileSearchResults.size());

        // --- 4. Process and combine all results ---
        return processAndFinalizeResults(client, fileSearchResults, result, intent, request, logPrefix);
    }

    /**
     * Prepares the context for file search by extracting collection views, document filters,
     * and knowledge name mappings from the intent.
     */
    private FileSearchContext prepareFileSearchContext(QcAiAgentIntentRecognition intent, String logPrefix) {
        // Filter for raw file/document knowledge sources first for efficiency.
        final List<QcAiKnowledgeRelated> rawKnowledgeList = intent.getRelateKnowledgeList().stream()
                .filter(item -> item.getCollectionId() != null &&
                        ("RAW".equals(item.getDataImpType()) || item.getDataImpType() == null))
                .toList();
        log.debug("{} FILE_SEARCH_PREP - Found {} 'RAW' type knowledge sources.", logPrefix, rawKnowledgeList.size());

        // 1. Create collection view names for the search query.
        final List<String> collectionViews = rawKnowledgeList.stream()
                .map(item -> COLLECTION_VIEW_PREFIX + item.getCollectionId())
                .distinct()
                .toList();

        // 2. Create a map of collections to their associated document names for filtering.
        final Map<String, List<String>> collectionToDocs = rawKnowledgeList.stream()
                .collect(Collectors.toMap(
                        k -> COLLECTION_VIEW_PREFIX + k.getCollectionId(),
                        k -> k.getRelateDocList().stream()
                                .map(QcAiKnowledgeFile::getDocName)
                                .filter(Objects::nonNull)
                                .distinct()
                                .toList(),
                        (existing, replacement) -> existing
                ));

        // 3. Create a map of all knowledge IDs to their names for easy lookup.
        final Map<Long, String> knowledgeIdToName = intent.getRelateKnowledgeList().stream()
                .filter(item -> item.getName() != null)
                .collect(Collectors.toMap(
                        QcAiKnowledgeRelated::getCollectionId,
                        QcAiKnowledgeRelated::getName,
                        (existing, replacement) -> existing
                ));
        log.debug("{} FILE_SEARCH_PREP - Created knowledgeIdToName map with {} entries.", logPrefix, knowledgeIdToName.size());
        return new FileSearchContext(collectionViews, collectionToDocs, knowledgeIdToName);
    }

    /**
     * Executes the vector search against the Tencent DB client.
     * Handles exceptions and returns an empty list on failure.
     */
    private List<SearchContent> executeVectorSearch(
            long tenantId,
            String question,
            QcAiAgentIntentRecognition intent,
            FileSearchContext context,
            String logPrefix
    ) {
        log.info("{} FILE_SEARCH_EXEC - Searching in {} collections: {}. Docs specified: {}",
                logPrefix, context.collectionViews().size(), context.collectionViews(), !context.collectionToDocs().isEmpty());

        try {
            return tencentDbClient.searchNoStructureFileContent(
                    DB_PREFIX + tenantId,
                    context.collectionViews(),
                    question,
                    intent.getMinMatchThreshold(),
                    intent.getMaxRecallCount(),
                    context.knowledgeIdToName(),
                    context.collectionToDocs(),
                    logPrefix
            );
        } catch (Exception e) {
            log.error("{} FILE_SEARCH_FAILURE - Exception during file content search. TenantId: {}", logPrefix, tenantId, e);
            return Collections.emptyList(); // Gracefully handle failure
        }
    }

    /**
     * Combines search results, sorts by score, limits the count, and formats the final result.
     * If no content is found, triggers the fallback behavior.
     */
    private KnowledgeProcessResult processAndFinalizeResults(
            SseClient client,
            List<SearchContent> fileSearchResults,
            KnowledgeProcessResult initialResult,
            QcAiAgentIntentRecognition intent,
            LLARequest request,
            String logPrefix
    ) {
        var combinedSearchList = new ArrayList<>(initialResult.getSearchContentList());
        combinedSearchList.addAll(fileSearchResults);

        if (combinedSearchList.isEmpty()) {
            log.warn("{} FILE_SEARCH_EMPTY - No content found. Triggering fallback.", logPrefix);
            return KnowledgeContentIsEmptyAndReturnNullAnswer(intent, client, request, logPrefix); // Pass null for client if not needed
        }

        var finalSearchContents = combinedSearchList.stream()
                .sorted(Comparator.comparingDouble(SearchContent::getScore).reversed())
                .limit(intent.getMaxRecallCount())
                .toList();

        log.debug("{} FILE_SEARCH_POST_PROCESS - Combined and limited results from {} to {}.",
                logPrefix, combinedSearchList.size(), finalSearchContents.size());

        handleSearchResults(finalSearchContents, request, intent, logPrefix);

        StringJoiner joiner = new StringJoiner("\n\n---\n\n");
        for (SearchContent item : finalSearchContents) {
            if (StringUtils.isNotEmpty(item.getAppendix())) {
                joiner.add("问：" + item.getContent() + "\n答：" + item.getAppendix() + "；");
            } else {
                joiner.add(item.getContent());
            }
        }
        String combinedContent = joiner.toString();

        log.info("{} FILE_SEARCH_SUCCESS - Combined content length is {}. Passing to LLM.",
                logPrefix, combinedContent.length());
        return new KnowledgeProcessResult(false, combinedContent, finalSearchContents);
    }

    private void handleSearchResults(List<SearchContent> searchContents, LLARequest request,
                                     QcAiAgentIntentRecognition intent, String logPrefix) {
        log.debug("{} HANDLE_SEARCH_RESULTS_ENTRY - Processing {} search results. MaxRecallCount from intent: {}",
                logPrefix, CollectionUtils.size(searchContents), intent.getMaxRecallCount());

        if (CollectionUtils.isEmpty(searchContents)) {
            log.info("{} HANDLE_SEARCH_RESULTS_WARN - No search results found. Skipping quote processing.", logPrefix);
            return;
        }

        // Limit to maxRecallCount specified by the intent/agent settings
        request.setQuoteCount(searchContents.size());
        log.info("{} HANDLE_SEARCH_RESULTS_STEP - Effective quote count to process: {}", logPrefix, request.getQuoteCount());

        final Long finalConvId;
        try {
            finalConvId = Long.parseLong(request.getId());
        } catch (NumberFormatException e) {
            log.error("{} HANDLE_SEARCH_RESULTS_ERROR - Invalid request ID '{}', cannot parse to Long for conversation ID. Skipping quote persistence.",
                    logPrefix, request.getId(), e);
            return;
        }

        // Pass logPrefix
        List<QcAiAgentConversationQuote> quoteList = searchContents.stream()
                .map(content -> createQuote(content, finalConvId, logPrefix)) // Ensure createQuote didn't return null
                .toList();
        log.debug("{} HANDLE_SEARCH_RESULTS_STEP - Generated {} quote objects", logPrefix, quoteList.size());

        try {
            log.info("{} HANDLE_SEARCH_RESULTS_STEP - Updating conversation {} reference with {} quotes and search results JSON.",
                    logPrefix, finalConvId, request.getQuoteCount());
            String searchContentsJson = JSON.toJSONString(new ArrayList<>(searchContents)); // Limit JSON for safety
            qcAiAgentConversationMapper.updateReference(finalConvId, request.getQuoteCount(), searchContentsJson);

            log.info("{} HANDLE_SEARCH_RESULTS_STEP - Batch inserting {} quotes into DB.", logPrefix, quoteList.size());
            DataBaseMapperUtils.batchInsert(quoteList, qcAiAgentConversationQuoteMapper::batchInsert);
            log.debug("{} HANDLE_SEARCH_RESULTS_STEP - DB operations for quotes completed successfully.", logPrefix);
        } catch (Exception e) {
            log.error("{} HANDLE_SEARCH_RESULTS_ERROR - Failed DB operations for quotes (updateReference or batchInsert). ConvId: {}",
                    logPrefix, finalConvId, e);
        }

        log.debug("{} HANDLE_SEARCH_RESULTS_EXIT - Finished handling search results.", logPrefix);
    }

    private QcAiAgentConversationQuote createQuote(SearchContent content, Long conversationId, String logPrefix) {
        var quote = new QcAiAgentConversationQuote();
        quote.setId(UUIDUtils.getUUID2Long());
        quote.setStatus(SUCCESS_STATUS); // "1" for success

        TenantUser tenantUser = UserManager.getTenantUser();
        if (tenantUser != null && tenantUser.getUserId() != null) {
            quote.setCreatorId(tenantUser.getUserId());
        } else {
            log.warn("{} CREATE_QUOTE_WARN - UserManager.getTenantUser() or UserId is null. CreatorId will be null for quote. ConversationId: {}",
                    logPrefix, conversationId);
        }

        quote.setCreateTime(LocalDateTime.now());
        quote.setConversationId(conversationId);
        quote.setKnowledgeName(StringUtils.truncate(content.getKnowledgeName(), 255));
        quote.setDocName(StringUtils.truncate(content.getDocumentName(), 255));
        quote.setContent(StringUtils.isBlank(content.getAppendix()) ? content.getContent() : content.getAppendix());
        quote.setScore(content.getScore());
        return quote;
    }

    protected void simulateStreamSend(String content, SseClient client, int quoteCount, String conversationId, String clientId, String logPrefix) {
        log.debug("{} SIMULATE_STREAM_SEND_ENTRY - ClientId: {}, ConversationId: {}, QuoteCount: {}, Content length: {}",
                logPrefix, clientId, conversationId, quoteCount, StringUtils.length(content));

        if (client == null) {
            log.warn("{} SIMULATE_STREAM_SEND_WARN - SseClient is null. Skipping SSE send. ClientId: {}, ConversationId: {}",
                    logPrefix, clientId, conversationId);
            return;
        }
        if (StringUtils.isBlank(clientId)) {
            log.warn("{} SIMULATE_STREAM_SEND_WARN - ClientId is blank. Skipping SSE send. ConversationId: {}",
                    logPrefix, conversationId);
            return;
        }

        try {
            if (StringUtils.isEmpty(content)) {
                log.info("{} SIMULATE_STREAM_SEND_INFO - Content is empty. Sending a single empty message chunk. ClientId: {}, ConversationId: {}",
                        logPrefix, clientId, conversationId);
                return; // Proceed to completeClient typically
            }

            String[] split = content.split(""); // Character by character
            log.debug("{} SIMULATE_STREAM_SEND_STEP - Starting to stream {} characters. ClientId: {}, ConversationId: {}",
                    logPrefix, split.length, clientId, conversationId);
            for (int i = 0; i < split.length; i++) {
                String s = split[i];
                client.send(clientId, Message.of().ok()
                        .data(SseMessage.builder()
                                .messageType(MessageType.TEXT)
                                .message(s) // Using .message() as per original code
                                .extData(Map.of("conversationId", StringUtils.defaultString(conversationId), "quoteCount", quoteCount))
                                .isEnd(false)
                                .build()));
                if (i % 100 == 0 && i > 0) { // Log progress for very long messages
                    log.debug("{} SIMULATE_STREAM_SEND_PROGRESS - Sent {} characters. ClientId: {}, ConversationId: {}",
                            logPrefix, i, clientId, conversationId);
                }
                Thread.sleep(10); // Small delay
            }
            log.debug("{} SIMULATE_STREAM_SEND_EXIT - Finished streaming content. ClientId: {}, ConversationId: {}",
                    logPrefix, clientId, conversationId);
        } catch (InterruptedException e) {
            log.warn("{} SIMULATE_STREAM_SEND_WARN - Thread interrupted during simulateStreamSend. ClientId: {}, ConversationId: {}",
                    logPrefix, clientId, conversationId, e);
            Thread.currentThread().interrupt(); // Restore interruption status
        } catch (Exception e) {
            log.error("{} SIMULATE_STREAM_SEND_ERROR - Failed to send SSE message. ClientId: {}, ConversationId: {}",
                    logPrefix, clientId, conversationId, e);
        }
    }

    public void completeClient(SseClient client, LLARequest request, String logPrefix) {
        log.debug("{} COMPLETE_CLIENT_ENTRY - Attempting to complete client. RequestId: {}, ClientId: {}",
                logPrefix, request != null ? request.getId() : "N/A", request != null ? request.getClientId() : "N/A");

        if (client == null) {
            log.warn("{} COMPLETE_CLIENT_WARN - SseClient is null. Cannot complete. RequestId: {}, ClientId: {}",
                    logPrefix, request != null ? request.getId() : "N/A", request != null ? request.getClientId() : "N/A");
            return;
        }
        if (request == null || StringUtils.isBlank(request.getClientId())) {
            log.warn("{} COMPLETE_CLIENT_WARN - LLARequest or ClientId is null/blank. Cannot send end message or complete client. RequestId: {}",
                    logPrefix, request != null ? request.getId() : "N/A");
            return;
        }

        String clientId = request.getClientId();
        String conversationId = request.getId();
        int quoteCount = Optional.ofNullable(request.getQuoteCount()).orElse(0);
        log.info("{} COMPLETE_CLIENT_STEP - Sending end message to ClientId: {}, ConversationId: {}, QuoteCount: {}",
                logPrefix, clientId, conversationId, quoteCount);

        try {
            client.send(clientId, Message.of().ok()
                    .data(SseMessage.builder()
                            .messageType(MessageType.TEXT) // End message might not need TEXT type, but consistent with stream
                            .message(null) // No actual text content for the end signal itself
                            .extData(Map.of(
                                    "conversationId", StringUtils.defaultString(conversationId),
                                    "quoteCount", quoteCount))
                            .isEnd(true)
                            .build()));
            client.complete(clientId);
            log.info("{} COMPLETE_CLIENT_EXIT - Client completed successfully. ClientId: {}, ConversationId: {}",
                    logPrefix, clientId, conversationId);
        } catch (Exception e) {
            log.error("{} COMPLETE_CLIENT_ERROR - Failed to send end message or complete client. ClientId: {}, ConversationId: {}",
                    logPrefix, clientId, conversationId, e);
        }
    }


}