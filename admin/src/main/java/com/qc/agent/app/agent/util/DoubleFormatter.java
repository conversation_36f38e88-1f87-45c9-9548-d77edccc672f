package com.qc.agent.app.agent.util;

/**
 * <AUTHOR>
 * @date 2025-04-22
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
public class DoubleFormatter {
    public static Object format(Double value) {
        if (value == null) {
            return null;
        }

        // 检查是否为整数（即小数部分为0）
        if (value % 1 == 0) {
            // 如果是整数，返回Integer类型
            return value.intValue();
        } else {
            // 如果不是整数，保持原状返回Double
            return value;
        }
    }
}
