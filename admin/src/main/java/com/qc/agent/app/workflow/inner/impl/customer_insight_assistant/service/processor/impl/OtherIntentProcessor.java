package com.qc.agent.app.workflow.inner.impl.customer_insight_assistant.service.processor.impl;

import com.alibaba.fastjson.JSONObject;
import com.qc.agent.app.workflow.inner.impl.customer_insight_assistant.model.CustomerInsightBusinessIntent;
import com.qc.agent.app.workflow.inner.impl.customer_insight_assistant.service.processor.CustomerInsightProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 其他意图处理器
 * 
 * <AUTHOR>
 * @since 1.0.0
 * @version 1.0.0
 */
@Slf4j
@Component
public class OtherIntentProcessor implements CustomerInsightProcessor {

    @Override
    public String generateAnswer(CustomerInsightBusinessIntent intent, String logPrefix) {
        log.info("{} 使用其他意图处理器生成回答", logPrefix);
        
        return generateDefaultAnswer();
    }

    @Override
    public String getSupportedIntent() {
        return "OTHER";
    }

    /**
     * 生成默认回答
     */
    private String generateDefaultAnswer() {
        return "我理解您想了解客户洞察信息。请提供更具体的客户信息或明确您想了解的分析维度，我可以为您提供更精准的分析。\n\n" +
                "您可以询问：\n" +
                "1. 客户特征分析 - 例如：\"沃尔玛的客户特征是什么？\"\n" +
                "2. 购买行为洞察 - 例如：\"家乐福的购买行为有什么特点？\"\n" +
                "3. 价值评估 - 例如：\"7-11的价值等级是什么？\"\n" +
                "4. 客户画像构建 - 例如：\"请分析这个客户的完整画像\"\n\n" +
                "请提供具体的客户名称，我将为您提供专业的分析报告。";
    }
} 