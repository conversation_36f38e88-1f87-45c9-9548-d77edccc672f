package com.qc.agent.app.agent.model.entity;

import java.io.Serializable;
import java.util.List;

import com.qc.agent.platform.pojo.BaseEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 维度配置表
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InsightDimensionConfig extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 维度配置 ID，主键
     */
    private Long id;

    /**
     * 关联的智能体 ID
     */
    private Long agentId;

    /**
     * 维度编码（如 ORDER、DISPLAY 等）
     */
    private String dimensionCode;

    /**
     * 维度展示名称
     */
    private String dimensionName;

    /**
     * 排序顺序
     */
    private Integer sortOrder;

    /**
     * 洞察模式：0-客户模式，1-终端经销商模式
     */
    private String insightMode;

    /**
     * 洞察模式类型：CUSTOMER-客户，TERMINAL-终端，DEALER-经销商
     */
    private String insightModeType;

    private List<InsightDataSource> dataSources;
}