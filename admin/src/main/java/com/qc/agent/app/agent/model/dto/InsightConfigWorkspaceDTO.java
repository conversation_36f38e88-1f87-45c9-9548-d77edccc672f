package com.qc.agent.app.agent.model.dto;

import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * 洞察配置工作区数据传输对象
 *
 * <AUTHOR>
 */
@Data
public class InsightConfigWorkspaceDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 智能体ID
     */
    private Long agentId;

    /**
     * 维度配置（支持新的分组结构）
     * 根据洞察模式包含不同类型的维度配置
     */
    private InsightModeConfigurationDTO dimensionConfigurations;

    /**
     * 总结配置，以此为准进行更新
     */
    private InsightSummaryConfigDTO summaryConfiguration;

    /**
     * 洞察模式配置DTO
     * 用于接收前端发送的分组维度配置
     */
    @Data
    public static class InsightModeConfigurationDTO implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 洞察模式：0-客户模式，1-终端经销商模式
         */
        private String insightMode;

        /**
         * 客户维度配置列表
         */
        private List<InsightDimensionConfigDTO> customerDimensionConfiguration;

        /**
         * 终端维度配置列表（仅终端经销商模式）
         */
        private List<InsightDimensionConfigDTO> terminalDimensionConfiguration;

        /**
         * 经销商维度配置列表（仅终端经销商模式）
         */
        private List<InsightDimensionConfigDTO> dealerDimensionConfiguration;
    }

}