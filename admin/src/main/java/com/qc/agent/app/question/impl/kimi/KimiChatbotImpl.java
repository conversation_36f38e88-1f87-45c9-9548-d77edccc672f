package com.qc.agent.app.question.impl.kimi;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.net.HttpHeaders;
import com.qc.agent.app.question.impl.AbstrctAnswerAIRequest;
import com.qc.agent.common.util.UUIDUtils;
import com.qc.agent.platform.sse.AnswerAISubscriber;
import com.qc.agent.platform.sse.SseMessageConverter;
import com.qc.agent.platform.sse.SseRequest;
import com.qc.agent.platform.sse.dto.AIVendors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;

/**
 * @description：TODO
 * @author： sc
 * @create： 2024/7/20 14:11
 */
@Component
@Slf4j
public class KimiChatbotImpl extends AbstrctAnswerAIRequest {

    @Value("${ai.agent.kimi-apikey}")
    private String kimiApiKey;

    private static final String PROVIDER_DOMAIN = "https://api.moonshot.cn";

    @Override
    protected String buildAnswer(String defaultAnswer) {
        String content;
        if (StringUtils.isNotEmpty(defaultAnswer)) {
            JSONObject data = new JSONObject();
            JSONArray choices = new JSONArray();
            JSONObject choice = new JSONObject();
            JSONObject delta = new JSONObject();
            delta.put("content", defaultAnswer);
            choice.put("delta", delta);
            choices.add(choice);
            data.put("id", UUIDUtils.getUUID2Long());
            data.put("choices", choices);
            content = data.toJSONString();
        } else {
            content = "[DONE]";
        }
        return String.format("data: %s", content);
    }

    @Override
    protected void doAnswer(SseRequest request, AnswerAISubscriber subscriber) {
        log.info("做Kimi问答,kimiApiKey:{}", kimiApiKey);

        JSONObject requestBody = buildRequestBody(request);

        log.info("调用问答接口，request：{}", requestBody);

        HttpRequest httpRequest = HttpRequest.newBuilder()
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + kimiApiKey)
                .header("Content-Type", "application/json")
                .uri(URI.create(URI.create(PROVIDER_DOMAIN) + "/v1/chat/completions"))
                .POST(HttpRequest.BodyPublishers.ofString(requestBody.toJSONString()))
                .build();
        try {
            request.setStartTime(System.currentTimeMillis());
            HttpClient.newBuilder().build().sendAsync(httpRequest, HttpResponse.BodyHandlers.fromLineSubscriber(subscriber)).get();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @NotNull
    private JSONObject buildRequestBody(SseRequest request) {
        JSONObject param = new JSONObject();
        param.put("messages", buildMessage(request));
        param.put("stream", true);
        param.put("model", "moonshot-v1-8k");
        return param;
    }

    @Override
    public AIVendors determineAIVendors() {
        return AIVendors.KIMI;
    }

    @Override
    public SseMessageConverter determineMessageConverter() {
        return new KimiMessageConverter();
    }
}
