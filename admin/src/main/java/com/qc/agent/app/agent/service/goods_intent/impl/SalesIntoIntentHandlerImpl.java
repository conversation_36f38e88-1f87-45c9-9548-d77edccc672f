package com.qc.agent.app.agent.service.goods_intent.impl;

import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.qc.agent.app.agent.model.query.QcAiGoodsAssistantQuery;
import com.qc.agent.app.agent.service.goods_intent.GoodsIntentHandler;
import com.qc.agent.app.agent.service.goods_intent.impl.tool.IntentTool;
import com.qc.agent.app.agent.support.AgentBizDataSupport;
import com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.model.dto.BusinessIntent;
import com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.model.enums.GoodsIntent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Handles the {@code SALES_INTO} intent.
 * <p>
 * This handler's primary role is to validate if the user has provided enough specific information
 * (e.g., an exact product name and an exact sales channel) to proceed with a sales inquiry.
 * If the provided information is ambiguous or incomplete, it transitions the intent to
 * {@code SALES_INTO_INPUT} to prompt the user for clarification.
 * <p>
 * Regardless of the outcome, it enriches the intent's entities with corresponding internal IDs
 * (e.g., productId, brandId, cmId) based on the names provided by the user.
 *
 * <AUTHOR>
 * @date 2025-06-26
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SalesIntoIntentHandlerImpl implements GoodsIntentHandler {

    private final IntentTool intentTool;

    @Value("${ai.agent.appsvr-domin-url}")
    private String appsvrDomainUrl;

    @Override
    public GoodsIntent getSupportedIntent() {
        return GoodsIntent.SALES_INTO;
    }

    /**
     * Processes the {@code SALES_INTO} intent by enriching entities and determining the next logical step.
     * <p>
     * It uses {@link Optional} to safely process the intent's entities. If entities are present,
     * it delegates to a helper method to perform enrichment and decide whether to transition
     * the intent to {@code SALES_INTO_INPUT}.
     *
     * @param intent    The business intent recognized from user input.
     * @param query     The original user query context (part of the interface, but unused here).
     * @param logPrefix A string prefix for structured logging.
     * @return A new {@code BusinessIntent}, potentially transitioned to {@code SALES_INTO_INPUT} if the
     * provided information is insufficient. Otherwise, returns the original intent type with enriched entities.
     */
    @Override
    public BusinessIntent handle(BusinessIntent intent, QcAiGoodsAssistantQuery query, String logPrefix) {
        log.info("{} [Handler:SalesInto] >> Starting to handle intent '{}' with entities: {}",
                logPrefix, intent.type(), intent.entities());
        if (StringUtils.isEmpty(intent.entities().getString("salesChannel"))) {
            Optional.ofNullable(query.getCmId()).ifPresent(cmId -> intent.entities().put("cmId", cmId));
        }

        return Optional.ofNullable(intent.entities())
                .map(entities -> enrichAndDetermineNextIntent(entities, intent, logPrefix))
                .orElseGet(() -> {
                    log.warn("{} [Handler:SalesInto] Entities are null. No action taken, returning original intent.", logPrefix);
                    return intent;
                });
    }

    /**
     * Enriches entities with IDs and decides whether the intent should be transitioned.
     *
     * @param entities  The JSON object of entities from the original intent.
     * @param logPrefix A string prefix for structured logging.
     * @return A new {@link BusinessIntent} reflecting the next step.
     */
    private BusinessIntent enrichAndDetermineNextIntent(JSONObject entities, BusinessIntent initialIntent, String logPrefix) {
        // An "exact match" occurs when a lookup for an entity returns a single, unambiguous result.
        // We use AtomicInteger to count exact matches from within lambda expressions.
        var exactMatchCounter = new AtomicInteger(0);

        intentTool.enrichWithProductInfo(entities, exactMatchCounter, logPrefix);
        intentTool.enrichWithCustomerTypeInfo(entities, exactMatchCounter, logPrefix, "salesChannel");
        intentTool.enrichWithCustomerInfoForSalesInto(entities, exactMatchCounter, logPrefix, "customer");

        if (StringUtils.isNotBlank(entities.getString("cmId")) && StringUtils.isBlank(entities.getString("customer")) && StringUtils.isEmpty(entities.getString("salesChannel"))) {
            JSONObject storeType = queryStoreType(entities.getString("cmId"));
            if (storeType != null) {
                entities.put("salesChannel", storeType.getString("typeName"));
                entities.put("salesChannelId", storeType.getString("typeId"));
                exactMatchCounter.incrementAndGet();
            }
        }


        log.info("{} [Handler:SalesInto] Total exact matches found: {}", logPrefix, exactMatchCounter.get());

        // DECISION LOGIC:
        // If we have 2 exact matches (product and channel), we have high confidence to proceed.
        // Otherwise, the information is ambiguous or incomplete, so we transition to
        // SALES_INTO_INPUT to ask the user for clarification.
        if (exactMatchCounter.get() == 2) {
            log.info("{} [Handler:SalesInto] Sufficient information found. Retaining intent as SALES_INTO.", logPrefix);
            return BusinessIntent.of(GoodsIntent.SALES_INTO, entities, initialIntent.usage());
        } else {
            log.info("{} [Handler:SalesInto] Insufficient or ambiguous information. Transitioning to SALES_INTO_INPUT.", logPrefix);
            return BusinessIntent.of(GoodsIntent.SALES_INTO_INPUT, entities, initialIntent.usage());
        }
    }

    private JSONObject queryStoreType(String cmId) {
        String url = String.format("%s/app/cm/apaas/customer/detail.do?cmId=%s", appsvrDomainUrl, cmId);
        try {
            String response = AgentBizDataSupport.getWithAuthHeaders(url);
            JSONObject data = JSONObject.parseObject(response).getJSONObject("data");
            if (data != null) {
                JSONObject customer = data.getJSONObject("basCmCustomer");
                return customer;
            }
        } catch (Exception e) {
            log.error("查询门店信息异常", e);
        }
        return null;
    }

}