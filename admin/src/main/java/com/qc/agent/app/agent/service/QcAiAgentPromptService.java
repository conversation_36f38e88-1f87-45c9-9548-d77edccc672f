package com.qc.agent.app.agent.service;

import com.alibaba.fastjson.JSONObject;
import com.qc.agent.app.agent.model.dto.AgentPromptSaveDTO;
import com.qc.agent.app.agent.model.dto.PromptOptimizedDTO;
import com.qc.agent.app.agent.model.po.QcAiAgentPrompt;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.List;

public interface QcAiAgentPromptService {

    void savePrompt(AgentPromptSaveDTO agentPromptSaveDTO);

    void deletePrompt(Long id, Long userId, String userName);

    QcAiAgentPrompt getPromptById(Long id);

    List<QcAiAgentPrompt> getAllPrompts();

    List<QcAiAgentPrompt> queryPrompts(String recommend, String mine);

    List<QcAiAgentPrompt> searchPrompts(JSONObject jsonObject);

    SseEmitter optimizedPrompt(PromptOptimizedDTO dto);
}
