package com.qc.agent.app.workflow.inner.mapper;

import com.qc.agent.app.workflow.inner.pojo.QcUploadFile;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface QcUploadFileMapper {

    QcUploadFile selectById(Long id);

    List<QcUploadFile> selectAll();

    int insert(QcUploadFile file);

    int batchInsert(@Param("list") List<QcUploadFile> fileList);

    int update(QcUploadFile file);

    int deleteById(Long id);

    void deleteByAgentId(@Param("agentId") Long agentId, @Param("status") String status);
}
