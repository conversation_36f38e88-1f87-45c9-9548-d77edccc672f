package com.qc.agent.app.agent.controller;

import com.qc.agent.app.agent.model.query.QcAiGoodsAssistantQuery;
import com.qc.agent.app.agent.model.query.QcAiGoodsAssistantRecommendQuery;
import com.qc.agent.app.agent.service.GoodsRecommendService;
import com.qc.agent.common.core.Message;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/ai-agent/assistant/goods")
public class GoodsAssistantController {
    @Resource
    private GoodsRecommendService goodsRecommendService;

    @PostMapping("/recommendQuestions")
    public Message getRecommendQuestions(@RequestBody QcAiGoodsAssistantRecommendQuery query) {
        return Message.of().ok().data(goodsRecommendService.generateRecommendQuestions(query));
    }

    @PostMapping("/intent")
    public Message intent(@RequestBody QcAiGoodsAssistantQuery query) {
        return Message.of().ok().data(goodsRecommendService.intent(query));
    }
}