package com.qc.agent.app.outside_interface.pojo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025-03-11
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Data
public class QcAiAgentConversationDO {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 状态，默认值为'1'
     */
    private String status;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;


    /**
     * 回答时间
     */
    private LocalDateTime answerTime;

    /**
     * 对话ID
     */
    private Long sessionId;

    /**
     * 大模型ID
     */
    private Long modelId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 智能体ID
     */
    private Long agentId;
    /**
     * 智能体名称
     */
    private String agentName;
    /**
     * 创建方式（0：自定义智能体，1：内置智能体）
     */
    private String agentType;


    /**
     * 问题
     */
    private String question;

    /**
     * 问题Token数
     */
    private BigDecimal questionToken;

    /**
     * 回答
     */
    private String answer;

    /**
     * 回答Token数
     */
    private BigDecimal answerToken;

    /**
     * 会话时长，单位：秒
     */
    private BigDecimal conversationTime;

    /**
     * 会话状态：-1：对话失败，0：对话中，1：对话成功
     */
    private String conversationStatus;
    /**
     * 1：web端，2：客户端
     */
    private String source;
}
