package com.qc.agent.app.agent.service.impl;

import com.alibaba.fastjson.JSON;
import com.qc.agent.app.agent.mapper.QcAiAgentModelMapper;
import com.qc.agent.app.agent.model.dto.LLAKeyProperties;
import com.qc.agent.app.agent.model.po.QcAiAgentModel;
import com.qc.agent.app.workflow.WorkflowLLAClient;
import com.qc.agent.jdbc.datasource.connection.JdbcConnectionBuilder;
import com.qc.agent.lla.model.LLAConfig;
import com.qc.agent.lla.model.LLAProvider;
import com.qc.agent.lla.model.LLARequest;
import com.qc.agent.lla.model.LLAResponse;
import com.qc.agent.platform.datasource.model.DatasourceConfig;
import com.qc.agent.platform.pojo.QcProductInfo;
import com.qc.agent.platform.user.service.AppServerUserService;
import com.qc.agent.platform.util.UUIDUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.qc.agent.app.agent.service.impl.QcAiAgentConversationServiceImpl.convertListToString;
import static com.qc.agent.app.agent.service.impl.QcAiAgentConversationServiceImpl.extractFirstThreeQuestions;

@Slf4j
@Service
public class LeadingQuestionService {
    @Resource
    private AppServerUserService appServerUserService;
    @Resource
    private LLAKeyProperties llaKeyProperties;

    public static final String CONDITION_PROMPT = "提出与产品紧密相关的问题，可以引发进一步的讨论。"
            + "问题应围绕产品卖点展开，突出其相较竞品的优势。\n"
            + "站在销售代表的角度思考，提出有助于将产品卖给客户的问题。\n"
            + "每句话只包含一个问题，问题中有产品名称。\n"
            + "推荐你有能力回答的问题。"
            + "你的回答中只包含问题。\n";

    // 复用产品查询逻辑
    public List<String> queryProductInfo() {
        return appServerUserService.getProductInfo().stream()
                .map(product -> StringUtils.isEmpty(product.getShortName()) ? product.getName() : product.getShortName())
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public String generateQuestions(List<String> products) {
        String productList = String.join("，", products);
        LLARequest request = LLARequest.builder()
                .id(Objects.toString(UUIDUtils.getUUID2Long()))
                .content("请基于【" + productList + "】" + CONDITION_PROMPT)
                .build();

        LLAConfig config = LLAConfig.builder()
                .apiKey(llaKeyProperties.getKimiApikey())
                .stream(true)
                .provider(LLAProvider.getEnumByValue("Kimi"))
                .model("moonshot-v1-auto")
                .build();

        config.setModelTemperature(new BigDecimal("0.5"));
        config.setModelTopP(new BigDecimal("0.5"));
        config.setModelMaxTokens(new BigDecimal("1000"));

        LLAResponse response = WorkflowLLAClient.send(config, request, false);
        log.info("调用KiMI生成默认问题 response:{}", JSON.toJSONString(response));
        List<String> list = extractFirstTwoQuestions(response.getContent());
        return convertListToString(list);
    }


    public static List<String> extractFirstTwoQuestions(String input) {
        List<String> result = new ArrayList<>();
        String[] questionArray = input.split("\\d+\\.\\s");

        for (int i = 1; i <= 2 && i < questionArray.length; i++) {
            // 只保留问题内容，去除序号和多余换行
            String questionContent = questionArray[i].trim().replaceAll("\n+", "");
            result.add(questionContent);
        }
        return result;
    }

    public void saveQuestions(DatasourceConfig config, String questions) {
        try (Connection conn =
                     JdbcConnectionBuilder.getConnection(config.toJdbcConnectionProperty());
             Statement stmt = conn.createStatement()) {
            stmt.executeUpdate("UPDATE qc_ai_agent SET leading_question='" +
                    questions + "' WHERE id = 6");
        } catch (SQLException e) {
            throw new RuntimeException("更新问题失败", e);
        }
    }

    public boolean checkAgentLeadingQuestions(DatasourceConfig config, Long productTalkAgentId) {
        try (Connection conn = JdbcConnectionBuilder.getConnection(config.toJdbcConnectionProperty());
             Statement stmt = conn.createStatement()) {
            ResultSet resultSet = stmt
                    .executeQuery("select leading_question from qc_ai_agent where id = " + productTalkAgentId);
            // sql查询结果leading_question为空返回false,不为空返回true,先把日志打印出来

            if (resultSet.next()) {
                String leadingQuestion = resultSet.getString("leading_question");
                log.info("租户{}商品话语助手内置问题:{}", config.getId(), leadingQuestion);
                //不为空且不等于[]返回true
                return !StringUtils.isEmpty(leadingQuestion) && !"[]".equals(leadingQuestion);
            }
            return false;
        } catch (SQLException e) {
            throw new RuntimeException("查询问题失败", e);
        }
    }
}