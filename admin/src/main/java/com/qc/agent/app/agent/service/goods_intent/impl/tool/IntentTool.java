package com.qc.agent.app.agent.service.goods_intent.impl.tool;

import com.alibaba.fastjson.JSONObject;
import com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.service.tool.CustomerTool;
import com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.service.tool.CustomerTypeTool;
import com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.service.tool.ProductTool;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Predicate;

/**
 * Utility service to enrich intent entities with detailed information by searching
 * against product, customer, and customer type databases. This class enhances a
 * base entity object by resolving names to canonical IDs and data.
 *
 * <AUTHOR>
 * @date 2025-06-27
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IntentTool {

    // --- Injected Dependencies ---
    private final ProductTool productTool;
    private final CustomerTool customerTool;
    private final CustomerTypeTool customerTypeTool;

    // --- JSON Key Constants ---
    private static final String PRODUCT_ID_KEY = "productId";
    private static final String PRODUCT_NAME_KEY = "productName";
    private static final String BRAND_ID_KEY = "brandId";
    private static final String BRAND_NAME_KEY = "brand";
    private static final String CUSTOMER_ID_KEY = "cmId";
    private static final String CUSTOMER_NAME_KEY = "customer";
    private static final String SALES_CHANNEL_ID_KEY = "salesChannelId";
    private static final String SALES_CHANNEL_NAME_KEY = "salesChannel";


    /**
     * Enriches entities with product and brand information based on a product name.
     * Increments the counter if search results contain only one distinct product name.
     *
     * @param entities           The JSON object containing entities to enrich.
     * @param exactMatchCounter  A counter for "exact" matches.
     * @param logPrefix          A prefix for log messages for traceability.
     */
    public void enrichWithProductInfo(JSONObject entities, AtomicInteger exactMatchCounter, String logPrefix) {
        log.debug("{} Starting product info enrichment.", logPrefix);

        Optional<String> productNameOpt = Optional.ofNullable(entities.getString(PRODUCT_NAME_KEY)).filter(name -> !name.isBlank());

        productNameOpt.ifPresentOrElse(name -> {
            log.info("{} Entity key '{}' has value '{}'. Searching for product matches.", logPrefix, PRODUCT_NAME_KEY, name);
            productTool.findProductList(name, logPrefix)
                    .filter(list -> !list.isEmpty())
                    .ifPresentOrElse(products -> {
                        log.info("{} Found {} potential product(s) for name '{}'.", logPrefix, products.size(), name);

                        // An "exact match" is defined as all found products sharing the same canonical name.
                        if (products.stream().map(p -> p.getString("name")).distinct().count() == 1) {
                            exactMatchCounter.incrementAndGet();
                            log.info("{} Registered an exact match for product (all results have the same distinct name).", logPrefix);
                        }

                        // Find the most similar product to enrich the entities.
                        productTool.findMostSimilarProduct(name, products, logPrefix)
                                .ifPresent(product -> enrichWithProduct(entities, product, logPrefix));

                    }, () -> log.warn("{} No products found for name '{}'.", logPrefix, name));

        }, () -> log.debug("{} No '{}' key in entities or value is blank; skipping product enrichment.", logPrefix, PRODUCT_NAME_KEY));

        // Final check: if no product was identified, clear related fields for consistency.
        if (entities.get(PRODUCT_ID_KEY) == null) {
            log.warn("{} Product could not be conclusively identified. Clearing product-related fields.", logPrefix);
            entities.put(PRODUCT_NAME_KEY, "");
            entities.put(BRAND_NAME_KEY, "");
        }
        log.debug("{} Finished product info enrichment.", logPrefix);
    }

    /**
     * Enriches entities with customer ID (cmId) based on a customer name.
     * Increments the counter if exactly one customer is found.
     *
     * @param entities           The JSON object containing entities to enrich.
     * @param exactMatchCounter  A counter for "exact" matches.
     * @param logPrefix          A prefix for log messages for traceability.
     * @param customerNameKey    The key in the entities object that holds the customer name to search for.
     */
    public void enrichWithCustomerInfo(JSONObject entities, AtomicInteger exactMatchCounter, String logPrefix, String customerNameKey) {
        // An "exact match" is defined as finding exactly one result.
        Predicate<List<JSONObject>> exactMatchPredicate = list -> list.size() == 1;
        enrichWithCustomerInfoInternal(entities, exactMatchCounter, logPrefix, customerNameKey, "standard", exactMatchPredicate);
    }

    /**
     * Enriches entities with customer ID (cmId) for the "SalesInto" use case based on a customer name.
     * Increments the counter if any potential customer is found (i.e., the search result is not empty).
     *
     * @param entities           The JSON object containing entities to enrich.
     * @param exactMatchCounter  A counter for "exact" matches.
     * @param logPrefix          A prefix for log messages for traceability.
     * @param customerNameKey    The key in the entities object that holds the customer name to search for.
     */
    public void enrichWithCustomerInfoForSalesInto(JSONObject entities, AtomicInteger exactMatchCounter, String logPrefix, String customerNameKey) {
        // "SalesInto" match is defined as finding any result (a non-empty list).
        Predicate<List<JSONObject>> salesIntoMatchPredicate = list -> !list.isEmpty();
        enrichWithCustomerInfoInternal(entities, exactMatchCounter, logPrefix, customerNameKey, "SalesInto", salesIntoMatchPredicate);
    }

    /**
     * Enriches entities with customer name based on a customer ID (cmId).
     *
     * @param entities  The JSON object containing entities to enrich.
     * @param logPrefix A prefix for log messages for traceability.
     * @param cmIdKey   The key in the entities object that holds the customer ID.
     */
    public void enrichWithCustomerInfoById(JSONObject entities, String logPrefix, String cmIdKey) {
        log.debug("{} Starting customer info enrichment by ID using key '{}'.", logPrefix, cmIdKey);

        Optional<String> cmIdOpt = Optional.ofNullable(entities.getString(cmIdKey)).filter(id -> !id.isBlank());

        cmIdOpt.ifPresentOrElse(cmId -> {
            log.info("{} Entity key '{}' has value '{}'. Searching for customer by ID.", logPrefix, cmIdKey, cmId);
            customerTool.findCustomerById(cmId, logPrefix)
                    .ifPresentOrElse(
                            customer -> enrichWithCustomer(entities, customer, logPrefix),
                            () -> log.warn("{} No customer found for ID '{}'.", logPrefix, cmId)
                    );
        }, () -> log.debug("{} No '{}' key in entities or value is blank; skipping customer enrichment by ID.", logPrefix, cmIdKey));

        if (entities.get(CUSTOMER_NAME_KEY) == null) {
            log.warn("{} Customer name could not be identified from ID. Clearing '{}' field.", logPrefix, CUSTOMER_NAME_KEY);
            entities.put(CUSTOMER_NAME_KEY, "");
        }
        log.debug("{} Finished customer info enrichment by ID.", logPrefix);
    }


    /**
     * Enriches entities with customer type information based on a name.
     * It first attempts an exact name match. If successful, the counter is incremented.
     * If not, it falls back to a similarity search against all available customer types.
     *
     * @param entities          The JSON object containing entities to enrich.
     * @param exactMatchCounter A counter for "exact" matches.
     * @param logPrefix         A prefix for log messages for traceability.
     * @param typeNameKey       The key in the entities object that holds the customer type name.
     */
    public void enrichWithCustomerTypeInfo(JSONObject entities, AtomicInteger exactMatchCounter, String logPrefix, String typeNameKey) {
        log.debug("{} Starting customer type info enrichment for key '{}'.", logPrefix, typeNameKey);

        Optional<String> nameOpt = Optional.ofNullable(entities.getString(typeNameKey)).filter(name -> !name.isBlank());

        nameOpt.ifPresentOrElse(name -> {
            log.info("{} Entity key '{}' has value '{}'. Searching for customer type matches.", logPrefix, typeNameKey, name);

            // First, try for an efficient exact match.
            Optional<JSONObject> exactMatchOpt = customerTypeTool.findExactCustomerTypeByName(name, logPrefix);

            if (exactMatchOpt.isPresent()) {
                log.info("{} Found a direct exact match for customer type '{}'.", logPrefix, name);
                exactMatchCounter.incrementAndGet();
                enrichWithType(entities, exactMatchOpt.get(), logPrefix, "Exact match");
            } else {
                // If no exact match, fall back to similarity search.
                log.info("{} No exact match for '{}'. Fetching all types for a similarity search.", logPrefix, name);
                customerTypeTool.fetchAllCustomerTypes(logPrefix)
                        .filter(list -> !list.isEmpty())
                        .flatMap(allTypes -> customerTypeTool.findMostSimilarCustomerTypeByName(name, allTypes, logPrefix))
                        .ifPresentOrElse(
                                similarType -> enrichWithType(entities, similarType, logPrefix, "Most similar match"),
                                () -> log.warn("{} Could not find any similar customer type for '{}'.", logPrefix, name)
                        );
            }
        }, () -> log.debug("{} No '{}' key in entities or value is blank; skipping customer type enrichment.", logPrefix, typeNameKey));

        if (entities.get(SALES_CHANNEL_ID_KEY) == null) {
            log.warn("{} Customer type could not be identified. Clearing '{}' field.", logPrefix, SALES_CHANNEL_NAME_KEY);
            entities.put(SALES_CHANNEL_NAME_KEY, "");
        }
        log.debug("{} Finished customer type info enrichment for key '{}'.", logPrefix, typeNameKey);
    }

    // --- Internal Helper Methods ---

    /**
     * Internal generic helper to enrich entities with customer information based on a name search.
     *
     * @param isExactMatch Predicate to define the condition for an "exact match" based on the use case.
     */
    private void enrichWithCustomerInfoInternal(JSONObject entities, AtomicInteger exactMatchCounter, String logPrefix,
                                                String customerNameKey, String matchType, Predicate<List<JSONObject>> isExactMatch) {
        log.debug("{} Starting customer info enrichment for key '{}' (Type: {}).", logPrefix, customerNameKey, matchType);

        Optional<String> nameOpt = Optional.ofNullable(entities.getString(customerNameKey)).filter(name -> !name.isBlank());

        nameOpt.ifPresentOrElse(name -> {
            log.info("{} Entity key '{}' has value '{}'. Searching for customer matches.", logPrefix, customerNameKey, name);
            customerTool.findCustomerList(name, logPrefix)
                    .filter(list -> !list.isEmpty())
                    .ifPresentOrElse(customers -> {
                        log.info("{} Found {} potential customer(s) for name '{}'.", logPrefix, customers.size(), name);

                        if (isExactMatch.test(customers)) {
                            exactMatchCounter.incrementAndGet();
                            log.info("{} Registered an exact match for customer ({} rule).", logPrefix, matchType);
                        }

                        customerTool.findMostSimilarCustomer(name, customers, logPrefix)
                                .ifPresent(customer -> enrichWithCustomer(entities, customer, logPrefix));

                    }, () -> log.warn("{} No customers found for name '{}'.", logPrefix, name));

        }, () -> log.debug("{} No '{}' key in entities or value is blank; skipping customer enrichment.", logPrefix, customerNameKey));

        if (entities.get(CUSTOMER_ID_KEY) == null) {
            log.warn("{} Customer could not be identified. Clearing '{}' field.", logPrefix, CUSTOMER_NAME_KEY);
            entities.put(CUSTOMER_NAME_KEY, "");
        }
        log.debug("{} Finished customer info enrichment for key '{}'.", logPrefix, customerNameKey);
    }

    /**
     * Helper method to populate the entity JSONObject with canonical product data.
     */
    private void enrichWithProduct(JSONObject entities, JSONObject product, String logPrefix) {
        String productId = product.getString("id");
        String productName = product.getString("name");
        String brandId = product.getString("brandId");
        String brandName = product.getString("brandName");

        entities.put(PRODUCT_ID_KEY, productId);
        entities.put(PRODUCT_NAME_KEY, productName); // Overwrite with canonical name
        entities.put(BRAND_ID_KEY, brandId);
        entities.put(BRAND_NAME_KEY, brandName);

        log.info("{} Enriched entities with ProductID: '{}', CanonicalProductName: '{}', BrandID: '{}', BrandName: '{}'.",
                logPrefix, productId, productName, brandId, brandName);
    }

    /**
     * Helper method to populate the entity JSONObject with canonical customer data.
     */
    private void enrichWithCustomer(JSONObject entities, JSONObject customer, String logPrefix) {
        String customerId = customer.getString("id");
        String customerName = customer.getString("name");

        entities.put(CUSTOMER_ID_KEY, customerId);
        entities.put(CUSTOMER_NAME_KEY, customerName); // Overwrite with canonical name

        log.info("{} Enriched entities with CustomerID (cmId): '{}', CanonicalCustomerName: '{}'.",
                logPrefix, customerId, customerName);
    }

    /**
     * Helper method to populate the entity JSONObject with canonical customer type data.
     */
    private void enrichWithType(JSONObject entities, JSONObject customerType, String logPrefix, String matchType) {
        String id = customerType.getString("id");
        String name = customerType.getString("name");

        entities.put(SALES_CHANNEL_ID_KEY, id);
        entities.put(SALES_CHANNEL_NAME_KEY, name); // Overwrite with canonical name

        log.info("{} [{}] Enriched entities with SalesChannelID: '{}', SalesChannelName: '{}'.",
                logPrefix, matchType, id, name);
    }
}