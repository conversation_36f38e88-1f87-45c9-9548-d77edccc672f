package com.qc.agent.app.knowledge.service.impl;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.qc.agent.app.knowledge.mapper.KnowledgeQuestionAnswerSettingMapper;
import com.qc.agent.app.knowledge.model.KnowledgeQuestionAnswerSettingInfo;
import com.qc.agent.app.knowledge.model.KnowledgeQuestionAnswerSettingParams;
import com.qc.agent.app.knowledge.service.KnowledgeQuestionAnswerSettingService;
import com.qc.agent.common.util.UUIDUtils;
import com.qc.agent.platform.config.FileProperty;
import com.qc.agent.platform.register.UserManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @className KnowledgeQuestionAnswerSettingServiceImpl
 * @description TODO
 * @date 2024/2/6 16:17
 */
@Service
public class KnowledgeQuestionAnswerSettingServiceImpl implements KnowledgeQuestionAnswerSettingService {

    @Autowired
    private KnowledgeQuestionAnswerSettingMapper knowledgeQuestionAnswerSettingMapper;
    @Resource
    private FileProperty fileProperty;

    @Override
    public KnowledgeQuestionAnswerSettingInfo getInfo(KnowledgeQuestionAnswerSettingParams params) {
        List<KnowledgeQuestionAnswerSettingInfo> list = knowledgeQuestionAnswerSettingMapper.queryList(params);
        if (CollectionUtils.isNotEmpty(list)) {
            KnowledgeQuestionAnswerSettingInfo settingInfo = list.get(0);
            if(StringUtils.isNotEmpty(settingInfo.getFunctionLogo())){
                settingInfo.setFunctionLogo(fileProperty.getUrl() + "/ai-agent/" + UserManager.getTenantUser().getTenantId() + "/" + settingInfo.getFunctionLogo());
            }
            return list.get(0);
        }
        return null;
    }

    @Override
    public int save(KnowledgeQuestionAnswerSettingParams params) {
        Long id = UUIDUtils.getUUID2Long();
        params.setId(id);
        params.setCreateTime(new Date());
        params.setModifyTime(new Date());
        params.setCreateUserId(UserManager.getTenantUser().getUserId());
        params.setCreateUserName(UserManager.getTenantUser().getUserName());
        params.setModifyUserId(UserManager.getTenantUser().getUserId());
        params.setModifyUserName(UserManager.getTenantUser().getUserName());
        return knowledgeQuestionAnswerSettingMapper.insert(params);

    }

    @Override
    public int update(KnowledgeQuestionAnswerSettingParams params) {
        params.setCreateTime(new Date());
        params.setModifyTime(new Date());
        params.setModifyUserId(UserManager.getTenantUser().getUserId());
        params.setModifyUserName(UserManager.getTenantUser().getUserName());
        return knowledgeQuestionAnswerSettingMapper.update(params);
    }

    @Override
    public List<Map<String, Object>> findAiVendors(Map<String, Object> params) {
        List<Map<String, Object>> data = Lists.newLinkedList();
        data.add(ImmutableMap.of("id", "1","name", "腾讯混元模型"));
        data.add(ImmutableMap.of("id", "2","name", "百度文心一言"));
        data.add(ImmutableMap.of("id", "3","name", "阿里通义千问"));
        data.add(ImmutableMap.of("id", "4","name", "智谱AI"));
        data.add(ImmutableMap.of("id", "5","name", "扣子"));
        data.add(ImmutableMap.of("id", "6","name", "OpenAi"));
        data.add(ImmutableMap.of("id", "7","name", "Kimi"));
        return data;
    }
}
