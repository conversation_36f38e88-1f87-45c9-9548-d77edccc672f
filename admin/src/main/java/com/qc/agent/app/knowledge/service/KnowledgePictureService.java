package com.qc.agent.app.knowledge.service;

import com.qc.agent.app.knowledge.model.query.KnowledgePictureQuery;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-07-11
 */
public interface KnowledgePictureService {
    /**
     * 导入图片
     *
     * @param query
     */
    void importPicture(KnowledgePictureQuery query);

    /**
     * 详情
     * 
     * @param query
     */
    Map<String,Object> detail(KnowledgePictureQuery query);

    /**
     * 批量更新
     * @param query
     */
    void batchInsert(KnowledgePictureQuery query);
}
