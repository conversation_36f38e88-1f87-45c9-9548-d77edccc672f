package com.qc.agent.app.outside_interface.controller;

import com.qc.agent.app.outside_interface.pojo.query.QcAiAgentExtractDataQuery;
import com.qc.agent.app.outside_interface.service.QcAiAgentExtractDataService;
import com.qc.agent.common.core.Message;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025-03-17
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Slf4j
@RestController
@RequestMapping("/ai-agent/api/extract_data/")
public class QcAiAgentExtractDataController {

    @Resource
    private QcAiAgentExtractDataService qcAiAgentAdminService;

    @PostMapping("/master")
    public Message master(@RequestBody QcAiAgentExtractDataQuery query) {
        log.info("agent平台拉取数据-调用master接口");
        return Message.of().ok().data(qcAiAgentAdminService.master(query));
    }

    @PostMapping("/tenant")
    public Message tenant(@RequestBody QcAiAgentExtractDataQuery query) {
        log.info("agent平台拉取数据-调用tenant接口");
        if (query.getTenantId() == null || query.getStartDate() == null || query.getEndDate() == null) {
            return Message.of().error("参数错误");
        }
        return Message.of().ok().data(qcAiAgentAdminService.tenant(query));
    }
}
