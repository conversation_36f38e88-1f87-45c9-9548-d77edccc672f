package com.qc.agent.app.question.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.StringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.qc.agent.app.knowledge.mapper.KnowledgeFileMapper;
import com.qc.agent.app.knowledge.mapper.KnowledgeInfoMapper;
import com.qc.agent.app.knowledge.mapper.KnowledgeQuestionAnswerSettingMapper;
import com.qc.agent.app.knowledge.model.KnowledgeQuestionAnswerSettingInfo;
import com.qc.agent.app.knowledge.model.KnowledgeQuestionAnswerSettingParams;
import com.qc.agent.app.knowledge.model.QcKnowledgeFile;
import com.qc.agent.app.knowledge.model.QcKnowledgeInfo;
import com.qc.agent.app.question.impl.SseCompleteCallbackImpl;
import com.qc.agent.app.question.mapper.QcKnowledgeAnswerSessionMapper;
import com.qc.agent.app.question.mapper.QcKnowledgeQuestionMapper;
import com.qc.agent.app.question.pojo.*;
import com.qc.agent.app.question.service.QcKnowledgeService;
import com.qc.agent.common.core.RequestHolder;
import com.qc.agent.common.core.TenantUser;
import com.qc.agent.common.util.UUIDUtils;
import com.qc.agent.platform.register.UserManager;
import com.qc.agent.platform.sse.AnswerAIClient;
import com.qc.agent.platform.sse.AnswerAISubscriber;
import com.qc.agent.platform.sse.SseEmitterClient;
import com.qc.agent.platform.sse.SseRequest;
import com.qc.agent.platform.sse.dto.AnswerAIConfig;
import com.qc.agent.vectordb.llm.tencentHunyuan;
import com.qc.agent.vectordb.pojo.ChatInput;
import com.qc.agent.vectordb.pojo.SearchContent;
import com.qc.agent.vectordb.pojo.VectorDbResponse;
import com.qc.agent.vectordb.tencentdb.tencentDbClient;
import com.tencentcloudapi.common.SSEResponseModel;
import com.tencentcloudapi.hunyuan.v20230901.models.ChatStdResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * @description：TODO
 * @author： sc
 * @create： 2024/1/23 9:52
 */
@Component
@Slf4j
public class QcKnowledgeServiceImpl implements QcKnowledgeService {

    @Resource
    private tencentDbClient client;

    @Resource
    private QcKnowledgeQuestionMapper qcKnowledgeQuestionMapper;

    @Resource
    private KnowledgeInfoMapper knowledgeInfoMapper;

    @Resource
    private KnowledgeFileMapper knowledgeFileMapper;

    @Resource
    private tencentHunyuan tencentHunyuan;

    @Resource
    private QcKnowledgeAnswerSessionMapper qcKnowledgeAnswerSessionMapper;

    @Resource
    private SseCompleteCallbackImpl sseCompleteCallback;

    @Resource
    private KnowledgeQuestionAnswerSettingMapper knowledgeQuestionAnswerSettingMapper;

    private final ExecutorService chatThreadPool = Executors.newFixedThreadPool(20);

    private static final Map<String, AnswerAISubscriber> CLIENT_CACHE = Maps.newHashMap();

    @Override
    public List<SearchContent> search(QcKnowledgeQuestion question) {
        String databaseName = "db-" + UserManager.getTenantUser().getTenantId(); //企业数据库
        String collectionName = "collectionVie-" + question.getCollectionId(); //知识库
        String qaDatabaseName = "QA-database-" + UserManager.getTenantUser().getTenantId();
        String qaCollectionName = "QA-collection-" + question.getCollectionId();
        QcKnowledgeInfo qcKnowledgeInfo = knowledgeInfoMapper.selectQcKnowledgeInfoById(question.getCollectionId());
        List<SearchContent> data = Lists.newLinkedList();
        if (qcKnowledgeInfo != null) {
            client.initClient();
            List<QcKnowledgeFile> qcKnowledgeFiles = knowledgeFileMapper.queryFileNameByDatabaseId(question.getCollectionId());
            double scoreThresh = 0.5;
            double qaScoreThresh = 0.9;
      /*      KnowledgeQuestionAnswerSettingInfo settingInfo = queryQuestionAnswerSetting();
            if (settingInfo != null && StringUtils.isNotEmpty(settingInfo.getThreshold())) {
                scoreThresh = Double.parseDouble(settingInfo.getThreshold());
                qaScoreThresh = Double.parseDouble(settingInfo.getQaThreshold());
            }*/
            if (CollectionUtils.isNotEmpty(qcKnowledgeFiles)) {
                for (QcKnowledgeFile qcKnowledgeFile : qcKnowledgeFiles) {
                    List<SearchContent> contents = Lists.newArrayList();
                    if ("RAW".equals(qcKnowledgeFile.getDataImpType())) {
                        contents = searchRawContent(qcKnowledgeFile.getFileUrl(), databaseName, collectionName, question.getQuestionContent(), scoreThresh);
                    } else if ("QA".equals(qcKnowledgeFile.getDataImpType())
                            || "PICTURE".equals(qcKnowledgeFile.getDataImpType())) {
                        contents = searchQaContent(qaCollectionName, qaDatabaseName, question.getQuestionContent(), qaScoreThresh);
                    }
                    if (CollectionUtils.isNotEmpty(contents)) {
                        data.addAll(contents);
                    }
                }
            }
        }
        return data;
    }

    private List<SearchContent> searchQaContent(String qaCollectionName, String databaseName, String question, double scoreThresh) {
        VectorDbResponse response = client.searchFileContentV2(qaCollectionName, databaseName, question, 10, scoreThresh);
        if (response != null && CollectionUtils.isNotEmpty((response.getData()))) {
            List<SearchContent> searchContents = response.getData();
            return searchContents;
        }
        return Lists.newArrayList();
    }

    private List<SearchContent> searchRawContent(String fileName, String databaseName, String collectionName, String question, double scoreThresh) {
        fileName = extractFileName(fileName);
        VectorDbResponse response = client.searchFileContent(databaseName, collectionName, fileName, question, 10, scoreThresh);
        if (response != null && CollectionUtils.isNotEmpty((response.getData()))) {
            List<SearchContent> searchContents = response.getData();
            return searchContents;
        }
        return Lists.newArrayList();
    }

    private String extractFileName(String path) {
        int lastSlashIndex = path.lastIndexOf('/');
        if (lastSlashIndex == -1) {
            return path; // 如果路径中没有斜杠，则返回整个路径作为文件名
        }
        return path.substring(lastSlashIndex + 1); // 返回从斜杠后面的部分作为文件名
    }

    @Override
    public List<QcKnowledgeQuestion> historyTest(QcKnowledgeQuestion question) {
        List<QcKnowledgeQuestion> qcKnowledgeHistoryQuestions = qcKnowledgeQuestionMapper.selectByDatabaseId(question.getDatabaseId());
        return qcKnowledgeHistoryQuestions;
    }

    @Override
    public void chat(SseEmitter emitter, String sessionId, String question) {
        emitter.onError(t -> {
            log.error("sseEmitter异常", t);
            emitter.complete();
        });

        emitter.onCompletion(() -> {
            log.info("对话完成");
        });

        emitter.onTimeout(() -> {
            log.error("sseEmitter连接超时");
        });

        Long id = insertQcKnowledgeAnswerHis(sessionId, question, null, null);

        TenantUser tenantUser = UserManager.getTenantUser();

        chatThreadPool.execute(() -> {
            try {
                ChatStdResponse sses = doChat(question);
                if (sses != null) {
                    StringBuffer answerSb = new StringBuffer();
                    for (SSEResponseModel.SSE ss : sses) {
                        AiAnswerMessage answer = parseAnswer(ss);
                        if (answer != null) {
                            emitter.send(JSONObject.toJSONString(answer), MediaType.TEXT_EVENT_STREAM);
                            if (answer.finished() && !answerSb.isEmpty()) {
                                RequestHolder.setThreadLocalUser(tenantUser);
                                qcKnowledgeQuestionMapper.updateAnswer(id, answerSb.toString());
                            } else {
                                answerSb.append(answer.getContent());
                                Thread.sleep(80L);
                            }
                        }
                    }
                }
            } catch (Exception e) {
                log.error("发送消息异常", e);
            } finally {
                emitter.complete();
            }
        });
    }

    private AiAnswerMessage parseAnswer(SSEResponseModel.SSE ss) {
        if (StringUtils.isNotEmpty(ss.Data)) {
            JSONArray choices = JSONObject.parseObject(ss.Data).getJSONArray("Choices");
            if (CollectionUtils.isNotEmpty(choices)) {
                JSONObject o = (JSONObject) choices.get(0);
                if ("stop".equals(o.getString("FinishReason"))) {
                    return AiAnswerMessage.builder().content(AiAnswerMessage.END_FLAG).build();
                }
                String content = o.getJSONObject("Delta").getString("Content");
                return AiAnswerMessage.builder().content(content).build();
            }
        }
        return null;
    }

    @Override
    public QcKnowledgeAnswerSession saveAnswerSession(QcKnowledgeAnswerSession qcKnowledgeAnswerSession) {
        Long id = UUIDUtils.getUUID2Long();
        qcKnowledgeAnswerSession.setId(id);
        qcKnowledgeAnswerSession.setStatus("1");
        qcKnowledgeAnswerSession.setCreateUserId(UserManager.getTenantUser().getUserId());
        qcKnowledgeAnswerSession.setCreateTime(new Date());
        qcKnowledgeAnswerSessionMapper.insert(qcKnowledgeAnswerSession);
        KnowledgeQuestionAnswerSettingInfo settingInfo = queryQuestionAnswerSetting();
        if (settingInfo != null) {
            insertQcKnowledgeAnswerHis(id.toString(), null, settingInfo.getPromptWord(), null);
        }
        return qcKnowledgeAnswerSession;
    }

    private KnowledgeQuestionAnswerSettingInfo queryQuestionAnswerSetting() {
        List<KnowledgeQuestionAnswerSettingInfo> list = knowledgeQuestionAnswerSettingMapper.queryList(new KnowledgeQuestionAnswerSettingParams());
        if (CollectionUtils.isNotEmpty(list)) {
            KnowledgeQuestionAnswerSettingInfo info = list.get(0);
            return info;
        } else {
            return null;
        }
    }

    @Override
    public void deleteAnswerSession(QcKnowledgeAnswerSession qcKnowledgeAnswerSession) {
        qcKnowledgeAnswerSessionMapper.delete(qcKnowledgeAnswerSession);
    }

    @Override
    public List<AnswerSessionView> listAnswerSession(QcKnowledgeAnswerSession qcKnowledgeAnswerSession) {
        qcKnowledgeAnswerSession.setCreateUserId(UserManager.getTenantUser().getUserId());
        qcKnowledgeAnswerSession.setAdminUser(UserManager.getTenantUser().isAdmin() ? "1" : "0");
        List<AnswerSessionView> data = qcKnowledgeAnswerSessionMapper.listAnswerSession(qcKnowledgeAnswerSession);
        return data;
    }

    @Override
    public List<QcKnowledgeQuestion> listAnswerHistory(QcKnowledgeQuestion qcKnowledgeQuestion) {
        return qcKnowledgeQuestionMapper.queryAnswerHistory(qcKnowledgeQuestion);
    }

    @Override
    public SseEmitter streamChat(StreamChatRequest request) {
        Long id;
        if (StringUtil.isEmpty(request.getQuestionId())) {
            // 根据msgId获取问答记录
            QcKnowledgeQuestion qcKnowledgeQuestion = qcKnowledgeQuestionMapper.selectByMsgId(request.getMsgId());
            if (qcKnowledgeQuestion != null) {
                id = clearQuestionAnswer(request.getQuestionId(), request.getMsgId());
            } else {
                id = insertQcKnowledgeAnswerHis(request.getSessionId(), request.getQuestion(), request.getMsgId());
            }
        } else {
            id = clearQuestionAnswer(request.getQuestionId(), request.getMsgId());
        }
        AnswerAISubscriber subscribe = AnswerAISubscriber.builder(SseEmitterClient.newBuilder());
        CLIENT_CACHE.put(request.getMsgId(), subscribe);
        KnowledgeQuestionAnswerSettingInfo setting = queryQuestionAnswerSetting();
        return AnswerAIClient
                .builder()
                .subscribe(subscribe)
                .addCallback(sseCompleteCallback)
                .execute(SseRequest.builder()
                        .clientId(request.getSessionId())
                        .question(request.getQuestion())
                        .questionId(id)
                        .msgId(request.getMsgId())
                        .sessionId(request.getSessionId())
                        .answerSearchRange(setting.getAnswerSearchRange())
                        .prompt(setting.getPrompt())
                        .showReference(setting.getShowReference())
                        .errorAnswer(setting.getErrorAnswer())
                        .config(Optional.ofNullable(setting)
                                .map(s -> AnswerAIConfig.builder()
                                        .defaultAnswer(s.getDefaultAnswer())
                                        .searchCount(s.getEachSearchCount())
                                        .similarityRatio(Double.valueOf(s.getThreshold()))
                                        .qaSimilarityRatio(Double.valueOf(s.getQaThreshold()))
                                        .vendor(s.toAIVendor()).build()).orElse(null))
                        .sleepTime(50L).build());
    }

    @Override
    public List<QcKnowledgeQuestion> listAnswerContext(QcKnowledgeQuestion qcKnowledgeQuestion) {
        List<QcKnowledgeQuestion> data = qcKnowledgeQuestionMapper.queryAnswerContext(qcKnowledgeQuestion);
        return data;
    }

    @Override
    public List<SearchContent> listAnswerReference(QcKnowledgeQuestion qcKnowledgeQuestion) {
        QcKnowledgeQuestion qcKnowledgeQuestion1 = qcKnowledgeQuestionMapper.selectByPrimaryKey(qcKnowledgeQuestion.getId());
        String referenceContent = qcKnowledgeQuestion1.getReferenceContent();
        List<SearchContent> searchContents = JSONObject.parseObject(referenceContent, new TypeReference<List<SearchContent>>() {
        });
//        if (CollectionUtils.isNotEmpty(searchContents)) {
//            for (SearchContent searchContent : searchContents) {
//                searchContent.setDocumentName(knowledgeFileMapper.queryFileNameByOSSUrl(String.format("knowledgeBase/%s", searchContent.getDocumentName())));
//            }
//        }
        return searchContents;
    }

    @Override
    public void deleteQuestion(QcKnowledgeQuestion qcKnowledgeQuestion) {
        qcKnowledgeQuestionMapper.deleteQuestion(qcKnowledgeQuestion);
    }

    @Override
    public void deleteAnswer(QcKnowledgeQuestion qcKnowledgeQuestion) {
        qcKnowledgeQuestionMapper.deleteAnswer(qcKnowledgeQuestion);
    }

    @Override
    public void feedback(FeedBackRequest request) {
        qcKnowledgeQuestionMapper.updateFeedbackInfo(request);
    }

    @Override
    public Map<String, Object> queryQuestionSummary(QcKnowledgeQuestion qcKnowledgeQuestion) {
        return null;
    }

    @Override
    public void stopStreamChat(StreamChatRequest request) {
        AnswerAISubscriber answerAISubscriber = CLIENT_CACHE.get(request.getMsgId());
        if (answerAISubscriber != null) {
            answerAISubscriber.stop();
            qcKnowledgeQuestionMapper.stopQuestion(request.getAnswer(), request.getMsgId());
            CLIENT_CACHE.remove(request.getMsgId());
        }
    }

    @Override
    public void clearCache(String msgId) {
        CLIENT_CACHE.remove(msgId);
    }

    private Long clearQuestionAnswer(String questionId, String msgId) {
        if (StringUtils.isNotEmpty(questionId)) {
            qcKnowledgeQuestionMapper.updateAnswer(Long.valueOf(questionId), null);
            return Long.valueOf(questionId);
        } else {
            QcKnowledgeQuestion qcKnowledgeQuestion = qcKnowledgeQuestionMapper.selectByMsgId(msgId);
            if (qcKnowledgeQuestion != null) {
                qcKnowledgeQuestionMapper.updateAnswerByMsgId(msgId, null);
                return qcKnowledgeQuestion.getId();
            } else {
                return null;
            }
        }
    }

    private ChatStdResponse doChat(String question) {
        ChatInput input = new ChatInput();
        input.setQuestion(question);
        tencentHunyuan.initClient();
        ChatStdResponse chat = tencentHunyuan.chat(input);
        return chat;
    }

    private Long insertQcKnowledgeAnswerHis(String sessionId, String question, String msgId) {
        return insertQcKnowledgeAnswerHis(sessionId, question, null, msgId);
    }

    private Long insertQcKnowledgeAnswerHis(String sessionId, String question, String answer, String msgId) {
        QcKnowledgeQuestion record = new QcKnowledgeQuestion();
        Long id = UUIDUtils.getUUID2Long();
        record.setId(id);
        record.setCreateTime(new Date());
        record.setCreateUserId(UserManager.getTenantUser().getUserId());
        record.setQuestionContent(question);
        record.setAnswerContent(answer);
        record.setSessionId(Long.valueOf(sessionId));
        record.setQuestionStatus("1");
        record.setMsgId(msgId);
        qcKnowledgeQuestionMapper.insert(record);
        return id;
    }

}
