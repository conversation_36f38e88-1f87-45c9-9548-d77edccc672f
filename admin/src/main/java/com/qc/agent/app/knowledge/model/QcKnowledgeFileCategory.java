package com.qc.agent.app.knowledge.model;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class QcKnowledgeFileCategory {

    /** 分类ID */
    private String id;

    /** 分类名称 */
    private String categoryName;

    /** 分类描述 */
    private String description;

    /** 创建时间 */
    private LocalDateTime createTime;

    /** 修改时间 */
    private LocalDateTime modifyTime;

    /**
     * 0-正常 1-删除
     */
    private String status;

    private String collectionId;
}