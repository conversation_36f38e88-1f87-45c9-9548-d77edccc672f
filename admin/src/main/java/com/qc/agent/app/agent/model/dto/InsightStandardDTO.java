package com.qc.agent.app.agent.model.dto;

import java.io.Serializable;

import lombok.Data;

/**
 * 衡量标准数据传输对象（简化版，只包含必要的配置字段）
 *
 * <AUTHOR>
 */
@Data
public class InsightStandardDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 标准ID，用于标识操作类型：
     * - null: 新增
     * - 有值: 修改
     */
    private Long id;

    /**
     * 标准名称
     */
    private String standardName;

    /**
     * 标准定义
     */
    private String standardDefinition;

    /**
     * 排序顺序
     */
    private Integer sortOrder;
}