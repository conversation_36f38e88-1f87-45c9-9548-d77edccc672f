package com.qc.agent.app.workflow.inner.impl.tools;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.qc.agent.app.agent.mapper.QcAiAgentConversationMapper;
import com.qc.agent.app.agent.model.po.QcAiAgent;
import com.qc.agent.app.agent.model.po.QcAiAgentConversation;
import com.qc.agent.app.agent.model.po.QcAiAgentModel;
import com.qc.agent.app.workflow.WorkflowLLAClient;
import com.qc.agent.common.core.Message;
import com.qc.agent.common.core.TenantUser;
import com.qc.agent.lla.model.*;
import com.qc.agent.platform.register.UserManager;
import com.qc.agent.platform.sse.SseClient;
import com.qc.agent.platform.sse.SseMessage;
import com.qc.agent.platform.sse.dto.MessageType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * Utility class for LLM-related operations within workflows.
 * Optimized for Java 17.
 *
 * <AUTHOR>
 * @date 2025-06-23
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public final class LLMTools {

    private final QcAiAgentConversationMapper qcAiAgentConversationMapper;

    // A single, reusable, thread-safe ObjectMapper instance is more performant.
    private static final ObjectMapper JSON_MAPPER = new ObjectMapper();

    // Using .formatted() is modern; the text block is already good.
    private static final String COMBINATORIAL_QUESTION_PROMPT = """
            理解用户连续提出的多个问题，请结合最后一轮问答的语义，直接输出用户此刻真正想问的完整问题，仅输出问题本身，不要添加任何说明或前缀。
            ## 之前的问题和答案
            %s
            ## 本次问题
            %s
            """;

    private static String getTenantIdSafe() {
        return Optional.ofNullable(UserManager.getTenantUser())
                .map(TenantUser::getTenantId)
                .map(String::valueOf)
                .orElse("unknown");
    }

    public static String getLogPrefix(LLARequest request, QcAiAgent agent) {
        return String.format("[tenantId=%s, agentId=%d, requestId=%s]",
                getTenantIdSafe(), agent.getId(), request.getId());
    }

    public static String getLogPrefix(Long agentId, String requestId) {
        return String.format("[tenantId=%s, agentId=%d, requestId=%s]",
                getTenantIdSafe(), agentId, requestId);
    }

    public static LLAConfig initLLAConfig(QcAiAgentModel qcAiAgentModel) {
        // Use a single, chained builder call for conciseness and immutability.
        return LLAConfig.builder()
                .provider(LLAProvider.getEnumByValue(qcAiAgentModel.getVendor()))
                .model(qcAiAgentModel.getModel())
                .apiKey(qcAiAgentModel.getKey())
                .secretKey(qcAiAgentModel.getSecret())
                .stream(true)
                .modelTemperature(new BigDecimal("0.5"))
                .modelTopP(new BigDecimal("0.5"))
                .modelMaxTokens(new BigDecimal("1000"))
                .endpoint(qcAiAgentModel.getModelEndPoint())
                .build();
    }

    /**
     * Attempts to generate an improved question using conversation history and an LLM.
     * Returns an Optional containing the question, or empty if it fails.
     */
    public static Optional<String> generateCombinedQuestion(LLAConfig config, LLARequest request, String logPrefix) {
        return buildCombinedPrompt(request.getQas(), request.getContent(), logPrefix)
                .flatMap(prompt -> {
                    try {
                        String combinedQuestion = generateFromLlm(config, request, prompt, logPrefix);
                        if (StringUtils.isBlank(combinedQuestion)) {
                            log.warn("{} COMBINED_SETUP_LLM_EMPTY - LLM returned a blank question.", logPrefix);
                            return Optional.empty();
                        }
                        log.info("{} COMBINED_SEARCH_EXECUTE - Generated question: '{}'", logPrefix, StringUtils.truncate(combinedQuestion, 100));
                        return Optional.of(combinedQuestion);
                    } catch (Exception e) {
                        log.error("{} COMBINED_SETUP_ERROR - Error during LLM call for combined question.", logPrefix, e);
                        return Optional.empty();
                    }
                });
    }

    /**
     * Calls an external LLM to generate the combined question.
     */
    private static String generateFromLlm(LLAConfig config, LLARequest originalRequest, String prompt, String logPrefix) {
        var combinedLlaReq = LLARequest.builder()
                .id(originalRequest.getId())
                .clientId(originalRequest.getClientId())
                .content(prompt)
                .build();

        log.info("{} LLM_CALL_START - Sending combinatorial prompt to LLM.", logPrefix);
        return Optional.ofNullable(WorkflowLLAClient.send(config, combinedLlaReq, true))
                .map(LLAResponse::getContent)
                .map(String::trim)
                .orElse("");
    }

    private static Optional<String> buildCombinedPrompt(List<LLAQa> qas, String originalQuestion, String logPrefix) {
        if (CollectionUtils.isEmpty(qas)) {
            log.info("{} COMBINED_SETUP_SKIP - No previous QAs available to build a combined prompt.", logPrefix);
            return Optional.empty();
        }

        // Simplified logic: directly access the first element, no need for complex streams.
        LLAQa lastQa = qas.get(0);
        String history = "【问题】：" + lastQa.getQuestion() + ", 【答案】：" + lastQa.getAnswer() + ";";

        String prompt = COMBINATORIAL_QUESTION_PROMPT.formatted(history, originalQuestion);
        log.debug("{} QA_COMBINE_PROMPT - Constructed prompt, length: {}, preview: '{}'",
                logPrefix, prompt.length(), StringUtils.truncate(prompt, 150));
        return Optional.of(prompt);
    }

    public static void directReturnMessage(SseClient client, LLARequest request, String logPrefix, String message) {
        // --- 配置项：每个字符之间的延迟（毫秒）---
        // 你可以根据需要调整这个值，50ms是一个比较舒适的打字机效果
        final long CHARACTER_DELAY_MS = 50;

        var clientId = Optional.ofNullable(request)
                .map(LLARequest::getClientId)
                .filter(StringUtils::isNotBlank);
        var conversationId = Optional.ofNullable(request)
                .map(LLARequest::getId)
                .orElse("N/A");

        if (client == null || clientId.isEmpty()) {
            log.warn("{} HANDLE_ERROR_SKIP - SseClient is null or ClientId is missing. Cannot send message. RequestId: {}",
                    logPrefix, conversationId);
            return;
        }

        String actualClientId = clientId.get();
        // 日志级别调整：这里是正常的消息发送流程，使用 info 或 debug 更合适
        log.info("{} STREAM_START - Starting to stream message. ClientId: {}, ConversationId: {}",
                logPrefix, actualClientId, conversationId);

        try {
            Map<String, Object> extData = Map.of(
                    "conversationId", conversationId,
                    "quoteCount", 0
            );

            String content = parseErrorMessage(message); // 假设这个方法是解析或格式化消息

            // --- 改动核心：使用 for 循环代替 Stream.forEach 来实现延迟 ---
            // 使用 codePoints().toArray() 来正确处理包含Unicode代理对的字符（如emoji）
            int[] codePoints = content.codePoints().toArray();
            for (int codePoint : codePoints) {
                // 1. 发送单个字符
                client.send(
                        actualClientId,
                        Message.of().ok().data(SseMessage.builder()
                                .message(new String(Character.toChars(codePoint))) // 将码点转回字符串
                                .messageType(MessageType.TEXT)
                                .extData(extData)
                                .isEnd(false)
                                .build())
                );

                // 2. 线程休眠，制造延迟效果
                Thread.sleep(CHARACTER_DELAY_MS);
            }

            // --- 所有字符发送完毕后，发送结束标志 ---
            client.send(actualClientId, Message.of().ok().data(SseMessage.builder()
                    .messageType(MessageType.TEXT)
                    .extData(extData)
                    .isEnd(true)
                    .build()));

            client.complete(actualClientId);
            log.info("{} STREAM_SUCCESS - Message streamed and client completed. ClientId: {}, ConversationId: {}",
                    logPrefix, actualClientId, conversationId);

        } catch (InterruptedException e) {
            // 当线程在 sleep 时被中断，是一种正常的退出方式
            log.warn("{} STREAM_INTERRUPTED - Stream was interrupted. ClientId: {}, ConversationId: {}",
                    logPrefix, actualClientId, conversationId);
            // 恢复中断状态，这是处理InterruptedException的良好实践
            Thread.currentThread().interrupt();
            // 确保在这种情况下也尝试关闭客户端
            client.complete(actualClientId);
        } catch (Exception e) {
            log.error("{} STREAM_FATAL - Exception during streaming message. ClientId: {}, ConversationId: {}",
                    logPrefix, actualClientId, conversationId, e);
            // 出现其他异常时，也确保关闭客户端
            client.complete(actualClientId);
        }
    }

    private static String parseErrorMessage(String jsonMessage) {
        if (StringUtils.isBlank(jsonMessage)) {
            return "An unspecified error occurred."; // Provide a default error message.
        }
        try {
            JsonNode root = JSON_MAPPER.readTree(jsonMessage);
            return root.path("error").path("message").asText(jsonMessage);
        } catch (JsonProcessingException e) {
            // If it's not valid JSON, return the original message.
            return jsonMessage;
        }
    }

    // A record to cleanly pass parameters to the private update method.
    private record ConversationUpdateParams(
            Long conversationId,
            String question, // Can be null
            String content,
            int questionTokens,
            int answerTokens,
            BigDecimal duration,
            String status,
            LocalDateTime createTime
    ) {}

    public void updateConversationDuration(QcAiAgentConversation conversation, String content, String logPrefix) {
        updateConversationInDatabase(conversation, null, content, logPrefix);
    }

    public void updateConversationDuration(QcAiAgentConversation conversation, String question, String content, String logPrefix) {
        updateConversationInDatabase(conversation, question, content, logPrefix);
    }

    private void updateConversationInDatabase(QcAiAgentConversation conversation, String question, String content, String logPrefix) {
        var now = LocalDateTime.now();
        var duration = Duration.between(conversation.getCreateTime(), now);
        var durationSeconds = BigDecimal.valueOf(duration.toNanos(), 9) // More direct conversion
                .setScale(3, RoundingMode.HALF_UP);

        int questionTokens = Objects.requireNonNullElse(conversation.getQuestionToken(), BigDecimal.ZERO).intValue();
        int answerTokens = Objects.requireNonNullElse(conversation.getAnswerToken(), BigDecimal.ZERO).intValue();
        String status = QcAiAgentConversation.CONVERSATION_SUCCESS;

        var params = new ConversationUpdateParams(
                conversation.getId(),
                question,
                content,
                questionTokens,
                answerTokens,
                durationSeconds,
                status,
                conversation.getCreateTime()
        );

        log.info("{} UPDATE_CONV_DURATION_CALCULATED - Calculated duration: {}s for ConversationId: {}",
                logPrefix, durationSeconds, params.conversationId());

        try {
            log.debug("{} UPDATE_CONV_DURATION_ATTEMPT - Attempting to update DB for ConversationId: {}. Parameters: {}",
                    logPrefix, params.conversationId(), params);

            // Note: The specific mapper methods must be adapted to accept the 'params' record or individual fields from it.
            // If you cannot change the mapper interface, the alternative is an if/else block here.


            // Alternative if mapper methods cannot be changed:
            int updatedRowCount;
            if (question != null) {
                updatedRowCount = qcAiAgentConversationMapper.updatePost(
                    params.conversationId(), params.question(), params.content(), params.questionTokens(),
                    params.answerTokens(), params.duration(), params.status(), params.createTime()
                );
            } else {
                 updatedRowCount = qcAiAgentConversationMapper.updateStreamChatDone(
                    params.conversationId(), params.content(), params.questionTokens(),
                    params.answerTokens(), params.duration(), params.status(), params.createTime()
                );
            }

            if (updatedRowCount > 0) {
                 log.info("{} UPDATE_CONV_DURATION_SUCCESS - ConversationId: {} updated. Duration: {}s.",
                        logPrefix, params.conversationId(), params.duration());
            } else {
                log.warn("{} UPDATE_CONV_DURATION_NO_ROWS_AFFECTED - ConversationId: {} update had no effect.",
                        logPrefix, params.conversationId());
            }


        } catch (DataAccessException e) {
            log.error("{} UPDATE_CONV_DURATION_DB_ERROR - Database error updating ConversationId: {}.",
                    logPrefix, params.conversationId(), e);
        } catch (Exception e) {
            log.error("{} UPDATE_CONV_DURATION_UNEXPECTED_ERROR - Unexpected error updating ConversationId: {}.",
                    logPrefix, params.conversationId(), e);
        }
    }
}