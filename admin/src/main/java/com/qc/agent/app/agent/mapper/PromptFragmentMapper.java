package com.qc.agent.app.agent.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.qc.agent.app.agent.model.entity.PromptFragment;

/**
 * 提示词片段Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PromptFragmentMapper {

    /**
     * 根据ID查询
     */
    PromptFragment selectById(@Param("id") Long id);

    PromptFragment selectByKey(@Param("key") String key,@Param("mainId") Long id);

    /**
     * 根据主提示词ID查询并按排序号排序
     */
    List<PromptFragment> selectByMainPromptIdOrderBySort(@Param("mainPromptId") Long mainPromptId);

    /**
     * 插入
     */
    int insert(PromptFragment promptFragment);

    /**
     * 批量插入
     */
    int batchInsert(@Param("fragments") List<PromptFragment> fragments);

    /**
     * 更新
     */
    int updateById(PromptFragment promptFragment);

    /**
     * 根据ID删除
     */
    int deleteById(@Param("id") Long id);

    /**
     * 根据主提示词ID删除
     */
    int deleteByMainPromptId(@Param("mainPromptId") Long mainPromptId);

}