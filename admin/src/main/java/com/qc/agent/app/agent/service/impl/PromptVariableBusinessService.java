package com.qc.agent.app.agent.service.impl;

import com.qc.agent.app.agent.model.entity.CustomerVariableData;
import com.qc.agent.app.agent.model.entity.PromptVariableRecord;
import com.qc.agent.app.agent.service.CustomerVariableDataService;
import com.qc.agent.app.agent.service.PromptService;
import com.qc.agent.app.agent.service.PromptVariableRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 提示词变量业务服务类
 * 整合提示词变量记录和客户变量数据的业务逻辑
 */
@Slf4j
@Service
public class PromptVariableBusinessService {

    @Resource
    private PromptVariableRecordService promptVariableRecordService;

    @Resource
    private CustomerVariableDataService customerVariableDataService;

    @Resource
    private PromptService promptService;

    /**
     * 保存客户的变量数据
     * 
     * @param configId 配置ID
     * @param dimensionCode 维度代码
     * @param promptType 提示词类型
     * @param customerId 客户ID
     * @param conversationId 对话ID
     * @param variableDataMap 变量数据映射 (variableKey -> variableValue)
     * @return 是否保存成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean saveCustomerVariableData(Long configId, String dimensionCode, String promptType,
                                           Long customerId, Long conversationId, 
                                           Map<String, String> variableDataMap) {
        if (configId == null || dimensionCode == null || promptType == null || 
            customerId == null || conversationId == null || CollectionUtils.isEmpty(variableDataMap)) {
            return false;
        }

        // 获取变量记录配置
        List<PromptVariableRecord> variableRecords = promptVariableRecordService
                .getVariableRecordsByConfigIdAndDimensionCodeAndPromptType(configId, dimensionCode, promptType);

        if (CollectionUtils.isEmpty(variableRecords)) {
            log.warn("未找到变量记录配置: configId={}, dimensionCode={}, promptType={}", 
                    configId, dimensionCode, promptType);
            return false;
        }

        // 构建客户变量数据
        List<CustomerVariableData> customerVariableDataList = new ArrayList<>();
        for (PromptVariableRecord record : variableRecords) {
            String variableValue = variableDataMap.get(record.getVariableKey());
            if (variableValue != null) {
                CustomerVariableData data = new CustomerVariableData();
                data.setCustomerId(customerId);
                data.setConversationId(conversationId);
                data.setPromptVariableRecordId(record.getId());
                data.setVariableValue(variableValue);
                data.setStatus("1");
                data.setCreateTime(LocalDateTime.now());
                customerVariableDataList.add(data);
            }
        }

        if (CollectionUtils.isEmpty(customerVariableDataList)) {
            log.warn("没有有效的变量数据需要保存");
            return false;
        }

        // 先删除现有数据，再保存新数据
        customerVariableDataService.deleteVariableDataByCustomerIdAndConversationId(customerId, conversationId);
        return customerVariableDataService.batchSaveCustomerVariableData(customerVariableDataList);
    }

    /**
     * 获取替换后的提示词内容
     * 
     * @param configId 配置ID
     * @param dimensionCode 维度代码
     * @param promptType 提示词类型
     * @param originalPrompt 原始提示词内容
     * @param customerId 客户ID
     * @param conversationId 对话ID
     * @return 替换后的提示词内容
     */
    public String getReplacedPromptContent(Long configId, String dimensionCode, String promptType,
                                         String originalPrompt, Long customerId, Long conversationId) {
        return promptService.replacePromptVariables(configId, dimensionCode, promptType, 
                originalPrompt, customerId, conversationId);
    }

    /**
     * 获取客户的变量数据映射
     * 
     * @param customerId 客户ID
     * @param conversationId 对话ID
     * @return 变量数据映射 (variableKey -> variableValue)
     */
    public Map<String, String> getCustomerVariableDataMap(Long customerId, Long conversationId) {
        List<CustomerVariableData> dataList = customerVariableDataService
                .getVariableDataByCustomerIdAndConversationId(customerId, conversationId);

        Map<String, String> resultMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(dataList)) {
            // 需要通过promptVariableRecordId获取variableKey
            // 这里简化处理，实际使用时需要关联查询
            for (CustomerVariableData data : dataList) {
                // TODO: 通过data.getPromptVariableRecordId()获取对应的variableKey
                // resultMap.put(variableKey, data.getVariableValue());
            }
        }

        return resultMap;
    }

    /**
     * 删除客户的变量数据
     * 
     * @param customerId 客户ID
     * @param conversationId 对话ID
     * @return 是否删除成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteCustomerVariableData(Long customerId, Long conversationId) {
        return customerVariableDataService.deleteVariableDataByCustomerIdAndConversationId(customerId, conversationId);
    }
}
