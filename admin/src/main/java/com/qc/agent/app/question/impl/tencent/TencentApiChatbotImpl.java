package com.qc.agent.app.question.impl.tencent;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.qc.agent.app.question.impl.AbstrctAnswerAIRequest;
import com.qc.agent.platform.sse.*;
import com.qc.agent.platform.sse.dto.AIVendors;
import com.qc.agent.vectordb.llm.tencentHunyuan;
import com.qc.agent.vectordb.pojo.ChatInput;
import com.tencentcloudapi.common.SSEResponseModel;
import com.tencentcloudapi.hunyuan.v20230901.models.ChatStdResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import javax.annotation.Resource;

/**
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class TencentApiChatbotImpl extends AbstrctAnswerAIRequest
{
    @Resource
    private tencentHunyuan hunyuanClient;

    @Override
    public AIVendors determineAIVendors()
    {
        return AIVendors.TENCENT;
    }
    @Override
    public SseMessageConverter determineMessageConverter()
    {
        return new TencentApiMessageConverter();
    }

    @Override
    protected SSEResponseModel.SSE buildAnswer(String defaultAnswer)
    {
        SSEResponseModel.SSE sse = new SSEResponseModel.SSE();
        if (StringUtils.isNotEmpty(defaultAnswer)) {
            sse.Data = createTencentAnswer(defaultAnswer, false);
        }else{
            sse.Data = createTencentAnswer(defaultAnswer, true);
        }
        return sse;
    }
    @Override
    protected void doAnswer(SseRequest request, AnswerAISubscriber subscriber)
    {
        log.info("做文腾讯混元大模型问答");
        hunyuanClient.initClient();
        ChatInput input = new ChatInput();
        input.setQuestion(request.getQuestion());
        input.setSearchContents(request.getCallWords());
        input.setHistoryChatQA(request.getHistories());
        ChatStdResponse resp = hunyuanClient.chat(input);
        Flux<SSEResponseModel.SSE> responseFlux = Flux.fromIterable(resp);
        responseFlux.subscribe(subscriber);
    }


    private String createTencentAnswer(String defaultAnswer, boolean stop){
        JSONArray Choices = new JSONArray();
        JSONObject Choice = new JSONObject();
        Choice.put("FinishReason", stop?"stop":"");
        JSONObject Delta = new JSONObject();
        Delta.put("Content", defaultAnswer);
        Choice.put("Delta", Delta);
        Choices.add(Choice);
        JSONObject data = new JSONObject();
        data.put("Choices", Choices);
        return data.toJSONString();
    }

}
