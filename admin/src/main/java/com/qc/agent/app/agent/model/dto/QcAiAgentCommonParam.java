package com.qc.agent.app.agent.model.dto;


import com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.model.dto.BusinessIntent;
import com.qc.agent.app.workflow.inner.impl.customer_insight_assistant.model.CustomerInsightBusinessIntent;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025-05-27
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QcAiAgentCommonParam {
    /**
     * 商品识别意图
     */
    private BusinessIntent goodsIntent;

    /**
     * 客户洞察意图
     */
    private CustomerInsightBusinessIntent insightIntent;
}
