package com.qc.agent.app.file_process.service;

import cn.hutool.core.io.FileUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.qc.agent.app.agent.mapper.QcAiAgentModelMapper;
import com.qc.agent.app.agent.model.po.QcAiAgentModel;
import com.qc.agent.app.agent.util.JsonUtils;
import com.qc.agent.app.workflow.WorkflowLLAClient;
import com.qc.agent.app.workflow.inner.pojo.QcUploadFile;
import com.qc.agent.lla.model.LLAConfig;
import com.qc.agent.lla.model.LLAProvider;
import com.qc.agent.utils.FileUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.dromara.x.file.storage.core.FileInfo;
import org.dromara.x.file.storage.core.FileStorageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.InputStream;
import java.util.Map;

@Slf4j
@Service
public class FileProcessingService {

    @Autowired
    private FileStorageService fileStorageService;
    @Resource
    private QcAiAgentModelMapper qcAiAgentModelMapper;

    private static final String PARSED_FILE_PATH_PREFIX = "parsed/";
    private static final String PARSED_FILE_SUFFIX = ".json";

    private static String BASE_PATH;
    private static String DOMAIN;
    private static String PLATFORM;

    @Autowired
    public void setOssConfig(
            @Value("${dromara.x-file-storage.aliyun-oss[0].base-path}") String bp,
            @Value("${dromara.x-file-storage.aliyun-oss[0].domain}") String dn,
            @Value("${dromara.x-file-storage.aliyun-oss[0].platform}") String pt
    ) {
        BASE_PATH = bp;
        DOMAIN = dn;
        PLATFORM = pt;
    }

    public String getOrParseFileContent(QcUploadFile file, String logPrefix) {
        String uri = file.getBasePath() + file.getPath() + file.getName();
        String parsedOssPath = generateParsedFilePath(uri);
        log.info("{} 原始文件路径: {}, 预期解析路径: {}", logPrefix, uri, parsedOssPath);

        return tryGetCachedContent(parsedOssPath, logPrefix)
                .orElseGet(() ->
                        parseAndCacheContent(uri, file.getExt(), parsedOssPath, logPrefix)
                );
    }

    private java.util.Optional<String> tryGetCachedContent(String parsedPath, String logPrefix) {
        String fullPath = BASE_PATH + parsedPath;
        FileInfo info = new FileInfo().setPlatform(PLATFORM).setFilename(fullPath);

        if (!fileStorageService.exists(info)) {
            return java.util.Optional.empty();
        }

        log.info("{}缓存命中: {}", logPrefix, parsedPath);
        FileInfo fileInfo = fileStorageService.getFileInfoByUrl(DOMAIN + fullPath);

        if (fileInfo == null) {
            throw new RuntimeException("找不到缓存文件: " + parsedPath);
        }

        byte[] contentBytes = fileStorageService.download(fileInfo).bytes();
        try {
            Map<?, ?> map = new ObjectMapper().readValue(contentBytes, Map.class);
            return java.util.Optional.ofNullable((String) map.get("content"));
        } catch (Exception e) {
            log.error("{} 读取缓存解析内容失败", logPrefix, e);
            throw new RuntimeException("缓存文件解析失败", e);
        }
    }

    private LLAConfig initLLAConfig() {
        QcAiAgentModel qcAiAgentModel = qcAiAgentModelMapper.selectById(93L);
        return LLAConfig.builder()
                .apiKey(qcAiAgentModel.getKey())
                .secretKey(qcAiAgentModel.getSecret())
                .stream(false)
                .provider(LLAProvider.getEnumByValue(qcAiAgentModel.getVendor()))
                .model(qcAiAgentModel.getModel())
                .build();
    }

    private String parseAndCacheContent(String uri, String ext, String parsedPath, String logPrefix) {

        verifyFileExists(uri, logPrefix);
        LLAConfig config = initLLAConfig();
        File tempFile = downloadToTemp(uri, ext, logPrefix);
        String parsedContent = callKimiParse(tempFile, config, logPrefix);

        Map<String, Object> parsedMap = readJson(parsedContent, logPrefix);
        String id = (String) parsedMap.get("id");

        String finalContent = WorkflowLLAClient.getFileContent(id, config);
        uploadParsedContent(parsedPath, finalContent, logPrefix);

        return extractContent(finalContent, logPrefix);
    }

    private void verifyFileExists(String uri, String logPrefix) {
        FileInfo originalInfo = new FileInfo().setPlatform(PLATFORM).setFilename(uri);
        if (!fileStorageService.exists(originalInfo)) {
            log.error("{} 原始文件不存在于OSS: {}", logPrefix, uri);
            throw new RuntimeException("原始文件不存在: " + uri);
        }
    }

    private File downloadToTemp(String uri, String ext, String logPrefix) {
        log.info("{} 开始下载原始文件: {}", logPrefix, uri);
        File tempFile = FileUtil.createTempFile("." + ext, true);
        fileStorageService.download(new FileInfo().setPlatform(PLATFORM).setFilename(uri)).file(tempFile);
        return tempFile;
    }

    private String callKimiParse(File file, LLAConfig config, String logPrefix) {
        log.info("{} 发送文件至 Kimi API 进行解析: {}", logPrefix, file.getName());

        String result = WorkflowLLAClient.parseUploadFile(file, config);
        if (result == null) {
            log.error("{}Kimi API 返回为空", logPrefix);
            throw new RuntimeException("Kimi 解析失败");
        }
        log.info("{}Kimi API 解析成功，内容长度: {}", logPrefix, result.length());
        return result;
    }

    private void uploadParsedContent(String parsedPath, String content, String logPrefix) {
        try (InputStream is = JsonUtils.jsonToInputStream(content)) {
            fileStorageService.of(is)
                    .setPlatform(PLATFORM)
                    .setPath(FilenameUtils.getPath(parsedPath))
                    .setOriginalFilename(FilenameUtils.getName(parsedPath))
                    .setSaveFilename(FilenameUtils.getName(parsedPath))
                    .setContentType("application/json")
                    .upload();
            log.info("{} 解析文件缓存上传成功: {}", logPrefix, parsedPath);
        } catch (Exception e) {
            log.error("{}上传解析缓存失败", logPrefix, e);
            throw new RuntimeException("上传缓存失败", e);
        }
    }

    private String extractContent(String json, String logPrefix) {
        try {
            Map<?, ?> map = new ObjectMapper().readValue(json, Map.class);
            return (String) map.get("content");
        } catch (Exception e) {
            log.error("{} 解析内容字段失败", logPrefix, e);
            throw new RuntimeException("字段提取失败", e);
        }
    }

    private Map<String, Object> readJson(String json, String logPrefix) {
        try {
            return new ObjectMapper().readValue(json, Map.class);
        } catch (Exception e) {
            log.error("{}JSON解析失败", logPrefix, e);
            throw new RuntimeException("Kimi 返回内容解析失败", e);
        }
    }

    private String generateParsedFilePath(String originalPath) {
        String filename = FilenameUtils.getName(originalPath);
        return PARSED_FILE_PATH_PREFIX + FileUtils.generateHashId(filename) + PARSED_FILE_SUFFIX;
    }
}
