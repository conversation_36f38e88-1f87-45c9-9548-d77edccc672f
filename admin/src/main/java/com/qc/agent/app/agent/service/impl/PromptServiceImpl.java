package com.qc.agent.app.agent.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSONObject;
import com.qc.agent.app.agent.mapper.DimensionPromptValueMapper;
import com.qc.agent.app.agent.model.entity.DimensionPromptValue;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.qc.agent.app.agent.mapper.MainPromptMapper;
import com.qc.agent.app.agent.mapper.PromptFragmentMapper;
import com.qc.agent.app.agent.model.dto.MainPromptDTO;
import com.qc.agent.app.agent.model.dto.PromptFragmentDTO;
import com.qc.agent.app.agent.model.entity.MainPrompt;
import com.qc.agent.app.agent.model.entity.PromptFragment;
import com.qc.agent.app.agent.service.PromptService;

import lombok.extern.slf4j.Slf4j;

/**
 * 提示词服务实现类
 */
@Slf4j
@Service
public class PromptServiceImpl implements PromptService {

    @Resource
    private MainPromptMapper mainPromptMapper;

    @Resource
    private PromptFragmentMapper promptFragmentMapper;

    @Resource
    private DimensionPromptValueMapper dimensionPromptValueMapper;

    @Override
    public MainPromptDTO getMainPromptValueByDimensionCodeAndType(Long configId, String dimensionCode, String promptType) {
        MainPromptDTO mainPromptDTO = getMainPromptByDimensionCodeAndType(dimensionCode, promptType);
        if (mainPromptDTO != null && !CollectionUtils.isEmpty(mainPromptDTO.getFragments())) {
            for (PromptFragmentDTO fragmentDTO : mainPromptDTO.getFragments()) {
                DimensionPromptValue dimensionPromptValue = dimensionPromptValueMapper.selectByConfigIdAndPromptFragmentId(configId, fragmentDTO.getId());
                fragmentDTO.setFragmentValue(Optional.ofNullable(dimensionPromptValue).map(DimensionPromptValue::getFragmentValue).orElse(""));
            }
        }
        return mainPromptDTO;
    }

    @Override
    public MainPromptDTO getMainPromptByDimensionCodeAndType(String dimensionCode, String promptType) {
        MainPrompt mainPrompt = mainPromptMapper.selectByDimensionCodeAndType(dimensionCode, promptType);
        if (mainPrompt == null) {
            return null;
        }

        MainPromptDTO mainPromptDTO = new MainPromptDTO();
        BeanUtils.copyProperties(mainPrompt, mainPromptDTO);

        // 获取片段
        List<PromptFragment> fragments = promptFragmentMapper.selectByMainPromptIdOrderBySort(mainPrompt.getId());
        if (!CollectionUtils.isEmpty(fragments)) {
            List<PromptFragmentDTO> fragmentDTOs = new ArrayList<>();
            for (PromptFragment fragment : fragments) {
                PromptFragmentDTO fragmentDTO = new PromptFragmentDTO();
                BeanUtils.copyProperties(fragment, fragmentDTO);
                fragmentDTOs.add(fragmentDTO);
            }
            mainPromptDTO.setFragments(fragmentDTOs);
        }

        return mainPromptDTO;
    }

    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveFragmentPrompt(JSONObject jsonObject, Long configId, String dimensionCode, String promptType) {
        List<DimensionPromptValue> promptValues = new ArrayList<>();
        MainPrompt dimensionMainPrompt = mainPromptMapper.selectByDimensionCodeAndType(dimensionCode, promptType);
        jsonObject.forEach((key, value) -> {
            DimensionPromptValue promptValue = new DimensionPromptValue();
            promptValue.setConfigId(configId);

            promptValue.setPromptFragmentId(promptFragmentMapper.selectByKey(key, dimensionMainPrompt.getId()).getId());
            promptValue.setFragmentValue((String) value);
            promptValue.setStatus("1");
            promptValues.add(promptValue);
        });
        if (!promptValues.isEmpty()) {
            dimensionPromptValueMapper.batchInsert(promptValues);
        }
        return true;
    }
}