package com.qc.agent.app.agent.service.impl;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSONObject;
import com.qc.agent.app.agent.mapper.DimensionPromptValueMapper;
import com.qc.agent.app.agent.mapper.PromptVariableRecordMapper;
import com.qc.agent.app.agent.model.entity.DimensionPromptValue;
import com.qc.agent.app.agent.model.entity.PromptVariableRecord;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.qc.agent.app.agent.mapper.MainPromptMapper;
import com.qc.agent.app.agent.mapper.PromptFragmentMapper;
import com.qc.agent.app.agent.model.dto.MainPromptDTO;
import com.qc.agent.app.agent.model.dto.PromptFragmentDTO;
import com.qc.agent.app.agent.model.entity.MainPrompt;
import com.qc.agent.app.agent.model.entity.PromptFragment;
import com.qc.agent.app.agent.service.PromptService;

import lombok.extern.slf4j.Slf4j;

/**
 * 提示词服务实现类
 */
@Slf4j
@Service
public class PromptServiceImpl implements PromptService {

    @Resource
    private MainPromptMapper mainPromptMapper;

    @Resource
    private PromptFragmentMapper promptFragmentMapper;

    @Resource
    private DimensionPromptValueMapper dimensionPromptValueMapper;

    @Resource
    private PromptVariableRecordMapper promptVariableRecordMapper;

    @Override
    public MainPromptDTO getMainPromptValueByDimensionCodeAndType(Long configId, String dimensionCode, String promptType) {
        MainPromptDTO mainPromptDTO = getMainPromptByDimensionCodeAndType(dimensionCode, promptType);
        if (mainPromptDTO != null && !CollectionUtils.isEmpty(mainPromptDTO.getFragments())) {
            for (PromptFragmentDTO fragmentDTO : mainPromptDTO.getFragments()) {
                DimensionPromptValue dimensionPromptValue = dimensionPromptValueMapper.selectByConfigIdAndPromptFragmentId(configId, fragmentDTO.getId());
                fragmentDTO.setFragmentValue(Optional.ofNullable(dimensionPromptValue).map(DimensionPromptValue::getFragmentValue).orElse(""));
            }
        }
        return mainPromptDTO;
    }

    @Override
    public MainPromptDTO getMainPromptByDimensionCodeAndType(String dimensionCode, String promptType) {
        MainPrompt mainPrompt = mainPromptMapper.selectByDimensionCodeAndType(dimensionCode, promptType);
        if (mainPrompt == null) {
            return null;
        }

        MainPromptDTO mainPromptDTO = new MainPromptDTO();
        BeanUtils.copyProperties(mainPrompt, mainPromptDTO);

        // 获取片段
        List<PromptFragment> fragments = promptFragmentMapper.selectByMainPromptIdOrderBySort(mainPrompt.getId());
        if (!CollectionUtils.isEmpty(fragments)) {
            List<PromptFragmentDTO> fragmentDTOs = new ArrayList<>();
            for (PromptFragment fragment : fragments) {
                PromptFragmentDTO fragmentDTO = new PromptFragmentDTO();
                BeanUtils.copyProperties(fragment, fragmentDTO);
                fragmentDTOs.add(fragmentDTO);
            }
            mainPromptDTO.setFragments(fragmentDTOs);
        }

        return mainPromptDTO;
    }

    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveFragmentPrompt(JSONObject jsonObject, Long configId, String dimensionCode, String promptType) {
        List<DimensionPromptValue> promptValues = new ArrayList<>();
        List<PromptVariableRecord> variableRecords = new ArrayList<>();

        // 先删除现有的提示词片段值和变量记录
        dimensionPromptValueMapper.deleteByConfigId(configId);
        promptVariableRecordMapper.deleteByConfigId(configId);

        MainPrompt dimensionMainPrompt = mainPromptMapper.selectByDimensionCodeAndType(dimensionCode, promptType);

        jsonObject.forEach((key, value) -> {
            DimensionPromptValue promptValue = new DimensionPromptValue();
            promptValue.setConfigId(configId);

            PromptFragment fragment = promptFragmentMapper.selectByKey(key, dimensionMainPrompt.getId());
            promptValue.setPromptFragmentId(fragment.getId());
            promptValue.setFragmentValue((String) value);
            promptValue.setStatus("1");
            promptValues.add(promptValue);

            // 解析提示词中的变量并保存到变量记录表
            List<PromptVariableRecord> fragmentVariables = parseAndCreateVariableRecords(
                configId, dimensionCode, promptType, fragment.getId(), (String) value);
            variableRecords.addAll(fragmentVariables);
        });

        if (!promptValues.isEmpty()) {
            dimensionPromptValueMapper.batchInsert(promptValues);
        }

        if (!variableRecords.isEmpty()) {
            promptVariableRecordMapper.batchInsert(variableRecords);
        }

        return true;
    }

    /**
     * 解析提示词内容中的变量并创建变量记录
     *
     * @param configId 配置ID
     * @param dimensionCode 维度代码
     * @param promptType 提示词类型
     * @param fragmentId 片段ID
     * @param fragmentValue 片段值
     * @return 变量记录列表
     */
    private List<PromptVariableRecord> parseAndCreateVariableRecords(Long configId, String dimensionCode,
            String promptType, Long fragmentId, String fragmentValue) {
        List<PromptVariableRecord> records = new ArrayList<>();

        // 使用正则表达式匹配 $变量名$ 格式的占位符
        Pattern pattern = Pattern.compile("\\$([^$]+)\\$");
        Matcher matcher = pattern.matcher(fragmentValue);

        int order = 1;
        while (matcher.find()) {
            String variableKey = matcher.group(1);

            PromptVariableRecord record = new PromptVariableRecord();
            record.setConfigId(configId);
            record.setDimensionCode(dimensionCode);
            record.setPromptType(promptType);
            record.setPromptFragmentId(fragmentId);
            record.setVariableKey(variableKey);
            record.setVariableOrder(order++);
            record.setStatus("1");
            record.setCreateTime(LocalDateTime.now());

            records.add(record);
        }

        return records;
    }

    /**
     * 替换提示词中的变量为具体值
     *
     * @param configId 配置ID
     * @param dimensionCode 维度代码
     * @param promptType 提示词类型
     * @param originalPrompt 原始提示词内容
     * @return 替换后的提示词内容
     */
    public String replacePromptVariables(Long configId, String dimensionCode, String promptType, String originalPrompt) {
        // 获取该配置下的所有变量记录
        List<PromptVariableRecord> variableRecords = promptVariableRecordMapper
                .selectByConfigIdAndDimensionCodeAndPromptType(configId, dimensionCode, promptType);

        if (CollectionUtils.isEmpty(variableRecords)) {
            return originalPrompt;
        }

        String result = originalPrompt;

        // 按照变量顺序替换
        for (PromptVariableRecord record : variableRecords) {
            if (record.getVariableValue() != null) {
                String placeholder = "$" + record.getVariableKey() + "$";
                result = result.replaceFirst(Pattern.quote(placeholder), record.getVariableValue());
            }
        }

        return result;
    }
}