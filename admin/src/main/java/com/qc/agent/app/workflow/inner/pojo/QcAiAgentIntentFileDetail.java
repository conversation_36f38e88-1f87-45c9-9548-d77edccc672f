package com.qc.agent.app.workflow.inner.pojo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025-06-09
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Data
public class QcAiAgentIntentFileDetail {

    /** 主键ID */
    private Long id;

    /** 状态，默认'1' */
    private String status;

    /** 创建人ID */
    private Long creatorId;

    /** 创建人名称 */
    private String creatorName;

    /** 创建时间 */
    private LocalDateTime createTime;

    /** 修改人ID */
    private Long modifyierId;

    /** 修改人名称 */
    private String modifyierName;

    /** 修改时间 */
    private LocalDateTime modifyTime;

    /** 智能体ID，对应 qc_ai_agent 的 id */
    private Long agentId;

    /** 意图ID，对应 qc_ai_agent_intent_recognition 的 id */
    private Long intentId;

    /** 文件ID，对应 qc_upload_file 的 id */
    private Long fileId;
}
