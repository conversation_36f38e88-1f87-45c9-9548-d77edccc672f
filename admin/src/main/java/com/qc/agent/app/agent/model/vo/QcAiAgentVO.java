package com.qc.agent.app.agent.model.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class QcAiAgentVO {

    /**
     * 智能体id
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long agentId;

    /**
     * 创建人id
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改人id
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long modifyierId;

    /**
     * 修改人名称
     */
    private String modifyierName;

    /**
     * 修改时间
     */
    private LocalDateTime modifyTime;

    /**
     * 智能体名称
     */
    private String agentName;

    /**
     * 1：内置智能体，0：自定义智能体
     */
    private String internalFlag;

    /**
     * 智能体logo域名
     */
    private String agentLogoDomain;

    /**
     * 智能体logo
     */
    private String agentLogo;

    /**
     * 智能体描述
     */
    private String description;

    /**
     * 开场白
     */
    private String introduction;

    /**
     * 引导问题
     */
    private String leadingQuestion;

    /**
     * 是否发布，1：已发布，0：未发布
     */
    private String publishFlag;

    /**
     * 发布时间
     */
    private LocalDateTime publishTime;

    /**
     * 发布用户id
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long publishUserId;

    /**
     * 发布用户名称
     */
    private String publishUserName;

    /**
     * 1：启用，0：停用
     */
    private String enable;

    private String dataRange;
    /**
     * h5页面地址
     */
    private String h5Url;

}
