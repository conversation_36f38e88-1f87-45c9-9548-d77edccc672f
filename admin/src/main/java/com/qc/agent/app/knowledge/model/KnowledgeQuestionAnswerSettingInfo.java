package com.qc.agent.app.knowledge.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.qc.agent.platform.sse.dto.AIVendors;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @className KnowledgeQuestionAnswerSettingParams
 * @description TODO
 * @date 2024/2/6 16:11
 */
@Data
public class KnowledgeQuestionAnswerSettingInfo {
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;

    private int eachSearchCount;

    @JSONField(serializeUsing = ToStringSerializer.class)
    private int engineType;

    private String engineTypeName;

    private String threshold;

    private String promptWord;

    private String defaultAnswer;


    private Date modifyTime;


    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long createUserId;


    private String createUserName;

    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long modifyUserId;


    private String modifyUserName;

    private Date createTime;

    private String functionLogo;

    private String answerSearchRange;

    private String prompt;

    private String showReference;

    private String errorAnswer;

    private String qaThreshold;

    public AIVendors toAIVendor() {
        switch (engineType) {
            case 2:
                return AIVendors.BAIDU;
            case 3:
                return AIVendors.ALI;
            case 4:
                return AIVendors.ZHIPU;
            case 5:
                return AIVendors.COZE;
            case 6:
                return AIVendors.OPENAI;
            case 7:
                return AIVendors.KIMI;
            case 8:
                return AIVendors.DEEPSEEK;
            default:
                return AIVendors.TENCENT;
        }
    }


}
