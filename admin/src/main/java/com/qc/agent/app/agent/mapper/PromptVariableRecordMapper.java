package com.qc.agent.app.agent.mapper;

import com.qc.agent.app.agent.model.entity.PromptVariableRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 提示词变量记录Mapper接口
 */
public interface PromptVariableRecordMapper {

    /**
     * 根据配置ID查询变量记录列表
     *
     * @param configId 配置ID
     * @return 变量记录列表
     */
    List<PromptVariableRecord> selectByConfigId(@Param("configId") Long configId);

    /**
     * 根据配置ID、维度代码、提示词类型查询变量记录列表
     *
     * @param configId 配置ID
     * @param dimensionCode 维度代码
     * @param promptType 提示词类型
     * @return 变量记录列表
     */
    List<PromptVariableRecord> selectByConfigIdAndDimensionCodeAndPromptType(
            @Param("configId") Long configId,
            @Param("dimensionCode") String dimensionCode,
            @Param("promptType") String promptType);

    /**
     * 根据提示词片段ID查询变量记录列表
     *
     * @param promptFragmentId 提示词片段ID
     * @return 变量记录列表
     */
    List<PromptVariableRecord> selectByPromptFragmentId(@Param("promptFragmentId") Long promptFragmentId);

    /**
     * 插入变量记录
     *
     * @param promptVariableRecord 变量记录
     * @return 影响行数
     */
    int insert(PromptVariableRecord promptVariableRecord);

    /**
     * 批量插入变量记录
     *
     * @param promptVariableRecords 变量记录列表
     * @return 影响行数
     */
    int batchInsert(@Param("promptVariableRecords") List<PromptVariableRecord> promptVariableRecords);

    /**
     * 根据ID更新变量记录
     *
     * @param promptVariableRecord 变量记录
     * @return 影响行数
     */
    int updateById(PromptVariableRecord promptVariableRecord);

    /**
     * 根据配置ID删除变量记录
     *
     * @param configId 配置ID
     * @return 影响行数
     */
    int deleteByConfigId(@Param("configId") Long configId);


    /**
     * 根据提示词片段ID删除变量记录
     *
     * @param promptFragmentId 提示词片段ID
     * @return 影响行数
     */
    int deleteByPromptFragmentId(@Param("promptFragmentId") Long promptFragmentId);

    /**
     * 根据配置ID列表批量删除变量记录
     *
     * @param configIds 配置ID列表
     * @return 影响行数
     */
    int deleteByConfigIds(@Param("configIds") List<Long> configIds);
}
