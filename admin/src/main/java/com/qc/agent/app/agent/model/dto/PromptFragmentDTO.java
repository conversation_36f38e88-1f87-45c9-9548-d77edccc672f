package com.qc.agent.app.agent.model.dto;

import lombok.Data;

/**
 * 提示词片段DTO
 *
 * <AUTHOR>
 */
@Data
public class PromptFragmentDTO {

    /**
     * 片段ID
     */
    private Long id;

    /**
     * 关联主提示词ID
     */
    private Long mainPromptId;

    /**
     * 片段键名
     */
    private String fragmentKey;

    private String fragmentName;

    /**
     * 用户填写的个性化值（来自qc_ai_dimension_prompt_value表）
     */
    private String fragmentValue;

    /**
     * 排序号
     */
    private Integer sortOrder;
}