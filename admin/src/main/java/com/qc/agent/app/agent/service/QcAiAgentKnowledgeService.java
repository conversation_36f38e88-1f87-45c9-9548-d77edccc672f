package com.qc.agent.app.agent.service;

import com.qc.agent.app.agent.model.query.QcAiAgentKnowledgeQuery;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-03-26
 */
public interface QcAiAgentKnowledgeService {
    /**
     * 知识库授权交集
     *
     * @param query
     * @return
     */
    Map<String, Object> authorityDetail(QcAiAgentKnowledgeQuery query);

    Map<String, Object> quoteDetail(QcAiAgentKnowledgeQuery query);
}
