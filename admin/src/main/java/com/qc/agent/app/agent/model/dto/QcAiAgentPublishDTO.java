package com.qc.agent.app.agent.model.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-24
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Data
public class QcAiAgentPublishDTO {
    private Long id;

    private Long userId;

    private String userName;

    /**
     * 授权范围  用户信息
     */
    private List<QcAiAgentUserDTO> userList;
    /**
     * 授权范围 部门信息
     */
    private List<QcAiAgentDeptDTO> deptList;

    /**
     * agent日志中是否展示完整对话内容 1展示 0不展示
     */
    private String showChatLogContentType;

}
