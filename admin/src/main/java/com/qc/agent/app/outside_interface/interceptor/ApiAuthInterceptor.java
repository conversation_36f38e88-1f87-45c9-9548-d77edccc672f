package com.qc.agent.app.outside_interface.interceptor;

import org.apache.commons.codec.digest.HmacAlgorithms;
import org.apache.commons.codec.digest.HmacUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2025-03-17
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Component
public class ApiAuthInterceptor implements HandlerInterceptor {
    private static final long MAX_TIME_DIFF = 5 * 60 * 1000; // 5分钟有效期

    private String API_KEY = ""; // 需与调用方一致

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 获取签名头
        String signature = request.getHeader("X-Signature");
        String timestampStr = request.getHeader("X-Timestamp");

        // 基本校验
        if (StringUtils.isEmpty(signature) || StringUtils.isEmpty(timestampStr)) {
            response.sendError(HttpStatus.UNAUTHORIZED.value(), "缺少认证头信息");
            return false;
        }

        // 时间有效性校验
        long timestamp = Long.parseLong(timestampStr);
        if (Math.abs(System.currentTimeMillis() - timestamp) > MAX_TIME_DIFF) {
            response.sendError(HttpStatus.UNAUTHORIZED.value(), "请求已过期");
            return false;
        }

        // 重构待签名字符串
        String requestUri = request.getRequestURI();
        String dataToSign = requestUri + timestamp;

        // 验证签名
        String expectedSignature = new HmacUtils(HmacAlgorithms.HMAC_SHA_256, API_KEY).hmacHex(dataToSign);
        if (!signature.equals(expectedSignature)) {
            response.sendError(HttpStatus.UNAUTHORIZED.value(), "签名验证失败");
            return false;
        }

        return true;
    }
}

