package com.qc.agent.app.workflow.inner.impl.customer_insight_assistant.model;

import com.alibaba.fastjson.JSONObject;
import com.qc.agent.app.workflow.inner.impl.customer_insight_assistant.model.enums.CustomerInsightIntent;
import com.qc.agent.lla.model.LLAUsage;

/**
 * 客户洞察业务意图模型
 * 
 * <AUTHOR>
 * @since 1.0.0
 * @version 1.0.0
 */
public record CustomerInsightBusinessIntent(
    CustomerInsightIntent intent,
    JSONObject entities,
    LLAUsage usage
) {
    
    /**
     * 创建业务意图实例
     * 
     * @param intent 意图类型
     * @param entities 实体信息
     * @param usage 用途
     * @return 业务意图实例
     */
    public static CustomerInsightBusinessIntent of(CustomerInsightIntent intent, JSONObject entities, LLAUsage usage) {
        return new CustomerInsightBusinessIntent(intent, entities, usage);
    }
    
    /**
     * 创建业务意图实例（默认用途）
     * 
     * @param intent 意图类型
     * @param entities 实体信息
     * @return 业务意图实例
     */
    public static CustomerInsightBusinessIntent of(CustomerInsightIntent intent, JSONObject entities) {
        return new CustomerInsightBusinessIntent(intent, entities, new LLAUsage());
    }
} 