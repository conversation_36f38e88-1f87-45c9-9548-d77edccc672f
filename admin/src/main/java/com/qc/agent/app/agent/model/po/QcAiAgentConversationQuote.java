package com.qc.agent.app.agent.model.po;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025-04-01
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Data
public class QcAiAgentConversationQuote {
    /**
     * 主键ID
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;

    /**
     * 状态（默认值为'1'）
     */
    private String status;

    /**
     * 创建人ID
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long creatorId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 修改人ID
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long modifierId;
    /**
     * 对话id
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long conversationId;

    /**
     * 修改时间
     */
    private LocalDateTime modifyTime;

    /**
     * 文件名称
     */
    private String docName;

    /**
     * 知识库名称
     */
    private String knowledgeName;

    /**
     * 分数
     */
    @JSONField(format = "0.0000")
    private BigDecimal score;

    public void setScore(Double score) {
        this.score = new BigDecimal(score + "").setScale(4, RoundingMode.HALF_UP);
    }

    /**
     * 引用内容
     */
    private String content;
}
