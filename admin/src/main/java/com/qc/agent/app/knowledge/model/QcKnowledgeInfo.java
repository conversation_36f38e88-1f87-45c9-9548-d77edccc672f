package com.qc.agent.app.knowledge.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @className QcKnowledgeInfo
 * @description TODO
 * @date 2024/1/22 11:27
 */
@Data
public class QcKnowledgeInfo {

    /** $column.columnComment */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;


    private String status;

    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long creatorId;

    private String creatorName;

    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long modifyierId;

    private String modifyierName;

    private Date createTime;
    private Date modifyTime;


    private String indexModel;

    private Long maxTokens;


    private String knowledgeFace;
    private String knowledgeFaceFullPath;


    private String knowledgeName;


    private String knowledgeDes;

    private String enable;

    private String publishFlag;

    private LocalDateTime publishTime;

    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long publishUserId;

    private String publishUserName;

    private List<KnowledgeRange> user;

    private List<KnowledgeRange> dept;

    public String getKnowledgeFaceFullPath() {
        return knowledgeFaceFullPath;
    }

    public void setKnowledgeFaceFullPath(String knowledgeFaceFullPath) {
        this.knowledgeFaceFullPath = knowledgeFaceFullPath;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Long getModifyierId() {
        return modifyierId;
    }

    public void setModifyierId(Long modifyierId) {
        this.modifyierId = modifyierId;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getIndexModel() {
        return indexModel;
    }

    public void setIndexModel(String indexModel) {
        this.indexModel = indexModel;
    }

    public Long getMaxTokens() {
        return maxTokens;
    }

    public void setMaxTokens(Long maxTokens) {
        this.maxTokens = maxTokens;
    }

    public String getKnowledgeFace() {
        return knowledgeFace;
    }

    public void setKnowledgeFace(String knowledgeFace) {
        this.knowledgeFace = knowledgeFace;
    }

    public String getKnowledgeName() {
        return knowledgeName;
    }

    public void setKnowledgeName(String knowledgeName) {
        this.knowledgeName = knowledgeName;
    }

    public String getKnowledgeDes() {
        return knowledgeDes;
    }

    public void setKnowledgeDes(String knowledgeDes) {
        this.knowledgeDes = knowledgeDes;
    }

    public String getEnable() {
        return enable;
    }

    public void setEnable(String enable) {
        this.enable = enable;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public String getModifyierName() {
        return modifyierName;
    }

    public void setModifyierName(String modifyierName) {
        this.modifyierName = modifyierName;
    }

    public String getPublishFlag() {
        return publishFlag;
    }

    public void setPublishFlag(String publishFlag) {
        this.publishFlag = publishFlag;
    }

    public LocalDateTime getPublishTime() {
        return publishTime;
    }

    public void setPublishTime(LocalDateTime publishTime) {
        this.publishTime = publishTime;
    }

    public Long getPublishUserId() {
        return publishUserId;
    }

    public void setPublishUserId(Long publishUserId) {
        this.publishUserId = publishUserId;
    }

    public String getPublishUserName() {
        return publishUserName;
    }

    public void setPublishUserName(String publishUserName) {
        this.publishUserName = publishUserName;
    }

    public List<KnowledgeRange> getUser() {
        return user;
    }

    public void setUser(List<KnowledgeRange> user) {
        this.user = user;
    }

    public List<KnowledgeRange> getDept() {
        return dept;
    }

    public void setDept(List<KnowledgeRange> dept) {
        this.dept = dept;
    }
}
