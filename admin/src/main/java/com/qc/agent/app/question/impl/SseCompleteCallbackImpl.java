package com.qc.agent.app.question.impl;

import com.qc.agent.app.question.mapper.QcKnowledgeQuestionMapper;
import com.qc.agent.platform.sse.SseCompleteCallback;
import com.qc.agent.platform.sse.SseRequest;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description：TODO
 * @author： sc
 * @create： 2024/2/18 14:42
 */
@Component
public class SseCompleteCallbackImpl implements SseCompleteCallback {

    @Resource
    private QcKnowledgeQuestionMapper qcKnowledgeQuestionMapper;

    @Resource
    private com.qc.agent.vectordb.tencentdb.tencentDbClient tencentDbClient;

    @Override
    public void onComplete(SseRequest request, List<String> messages) {
        if(!request.isStopped() && request.getQuestionId() != null && CollectionUtils.isNotEmpty(messages)){
            String answer = StringUtils.join(messages, "");
            qcKnowledgeQuestionMapper.updateAnswer(request.getQuestionId(), answer);
            if(request.getSimilarQuestionId() == null){
//                String databaseName = "qa-db-" + UserManager.getTenantUser().getTenantId();
//                String collectionName = "qa-collectionVie-" + UserManager.getTenantUser().getTenantId();
//                tencentDbClient.insertQuestion(databaseName, collectionName, request.getQuestion(), answer, String.valueOf(request.getQuestionId()), System.currentTimeMillis());
            }
        }
    }
}
