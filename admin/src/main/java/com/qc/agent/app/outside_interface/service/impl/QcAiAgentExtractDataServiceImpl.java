package com.qc.agent.app.outside_interface.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.qc.agent.app.outside_interface.mapper.QcAiAgentExtractDataMapper;
import com.qc.agent.app.outside_interface.pojo.QcAiAgentConversationDO;
import com.qc.agent.app.outside_interface.pojo.query.QcAiAgentExtractDataQuery;
import com.qc.agent.app.outside_interface.service.QcAiAgentExtractDataService;
import com.qc.agent.common.config.datasource.annotation.AnswerAI;
import com.qc.agent.common.core.RequestHolder;
import com.qc.agent.common.core.TenantUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-03-17
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Slf4j
@Service
public class QcAiAgentExtractDataServiceImpl implements QcAiAgentExtractDataService {

    @Resource
    private QcAiAgentExtractDataMapper qcAiAgentExtractDataMapper;

    @AnswerAI
    @Override
    public Map<String, Object> master(QcAiAgentExtractDataQuery query) {
        log.debug("agent平台收取数据-调用master接口");
        Map<String, Object> resultMap = new HashMap<>(1 << 4);
        List<Long> tenantIds = qcAiAgentExtractDataMapper.selectTenantIds(query);
        resultMap.put("tenantIds", tenantIds);
        return resultMap;
    }

    @Override
    public Map<String, Object> tenant(QcAiAgentExtractDataQuery query) {
        log.debug("agent平台收取数据-调用tenant接口,{}", JSONObject.toJSONString(query));
        Map<String, Object> resultMap = new HashMap<>(1 << 4);
        List<QcAiAgentConversationDO> list = new ArrayList<>();
        try {
            TenantUser tenantUser = new TenantUser();
            tenantUser.setTenantId(query.getTenantId());
            RequestHolder.setThreadLocalUser(tenantUser);
            if (query.getTenantId() != null) {
                list = qcAiAgentExtractDataMapper.selectConversationRecordList(query);
            }
        } finally {
            RequestHolder.clear();
        }
        resultMap.put("rows", list);
        return resultMap;
    }
}
