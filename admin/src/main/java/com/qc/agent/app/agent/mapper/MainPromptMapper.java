package com.qc.agent.app.agent.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Param;

import com.qc.agent.app.agent.model.entity.MainPrompt;

/**
 * 主提示词Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MainPromptMapper {

    /**
     * 根据ID查询
     */
    MainPrompt selectById(@Param("id") Long id);

    /**
     * 根据维度编码和类型查询
     */
    MainPrompt selectByDimensionCodeAndType(@Param("dimensionCode") String dimensionCode, @Param("promptType") String promptType);

    /**
     * 插入
     */
    int insert(MainPrompt mainPrompt);

    /**
     * 更新
     */
    int updateById(MainPrompt mainPrompt);

    /**
     * 根据ID删除
     */
    int deleteById(@Param("id") Long id);

    /**
     * 根据维度编码和类型删除
     */
    int deleteByDimensionCode(@Param("dimensionCode") String dimensionCode, @Param("promptType") String promptType);
}