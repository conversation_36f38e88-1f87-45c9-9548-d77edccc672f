package com.qc.agent.app.knowledge.mapper;

import com.qc.agent.app.knowledge.model.ListExcelFileQAParams;
import com.qc.agent.app.knowledge.model.QcKnowledgeExcelData;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface QcKnowledgeExcelDataMapper {
    int deleteByPrimaryKey(Long id);

    int insert(QcKnowledgeExcelData record);

    int insertWithMaxSequ(QcKnowledgeExcelData record);

    int insertSelective(QcKnowledgeExcelData record);

    int batchInsert(@Param("records") List<QcKnowledgeExcelData> records);

    QcKnowledgeExcelData selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(QcKnowledgeExcelData record);

    int updateByPrimaryKey(QcKnowledgeExcelData record);

    int countQAByFileId(ListExcelFileQAParams listExcelFileQAParams);

    List<QcKnowledgeExcelData> listQAByFileId(ListExcelFileQAParams listExcelFileQAParams);

    int deleteById(@Param("id") Long id);

    List<QcKnowledgeExcelData> findByFileId(@Param("fileId") Long fileId);

    void deleteByFileId(Long id);
}
