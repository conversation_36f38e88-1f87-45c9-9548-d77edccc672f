package com.qc.agent.app.knowledge.mapper;

import com.qc.agent.app.knowledge.model.KnowledgeQuestionAnswerSettingInfo;
import com.qc.agent.app.knowledge.model.KnowledgeQuestionAnswerSettingParams;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @className KnowledgeQuestionAnswerSettingMapper
 * @description TODO
 * @date 2024/2/6 16:18
 */
@Component
public interface KnowledgeQuestionAnswerSettingMapper {
    List<KnowledgeQuestionAnswerSettingInfo> queryList(KnowledgeQuestionAnswerSettingParams params);
    int insert(KnowledgeQuestionAnswerSettingParams params);
    int update(KnowledgeQuestionAnswerSettingParams params);
}
