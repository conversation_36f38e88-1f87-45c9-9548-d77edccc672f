package com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.mapper;

import com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.model.domain.QcAiAgentRecommendQuestions;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface QcAiAgentRecommendQuestionsMapper {
    /**
     * 插入推荐问题
     *
     * @param list
     */
    void batchInsert(List<QcAiAgentRecommendQuestions> list);

    /**
     * 查询推荐问题
     *
     * @param id
     * @return
     */
    List<String> selectQuestions(Long id);
}
