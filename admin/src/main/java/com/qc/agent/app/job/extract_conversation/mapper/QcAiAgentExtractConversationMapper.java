package com.qc.agent.app.job.extract_conversation.mapper;

import com.qc.agent.app.job.extract_conversation.pojo.QcAiAgentConversationDO;
import com.qc.agent.app.job.extract_conversation.pojo.query.QcAiAgentMasterQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-07
 */
public interface QcAiAgentExtractConversationMapper {
    /**
     * 查询当天的会话记录
     *
     * @param currentDate
     * @param tenantId
     * @return
     */
    List<QcAiAgentConversationDO> selectList(@Param("currentDate") String currentDate,@Param("tenantId") Long tenantId);

    /**
     * 查询租户id
     *
     * @return
     */
    List<Long> selectMasterTenantIdList(QcAiAgentMasterQuery query);
}
