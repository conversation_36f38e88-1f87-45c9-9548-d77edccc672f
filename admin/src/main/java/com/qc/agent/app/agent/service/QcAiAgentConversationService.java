package com.qc.agent.app.agent.service;

import com.alibaba.fastjson.JSONObject;
import com.qc.agent.app.agent.model.dto.*;
import com.qc.agent.app.agent.model.po.QcAiAgentConversationParams;
import com.qc.agent.app.agent.model.query.QcAiAgentChatQuery;
import com.qc.agent.app.agent.model.vo.ConversationVO;
import com.qc.agent.common.core.Message;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface QcAiAgentConversationService {
    List<ConversationVO> queryAgentHistoryConversation(Long agentId, Integer page, Integer rows);

    SseEmitter conversation(QcAiAgentChatDTO qcAiAgentChatDTO);

    JSONObject queryHistoryConversation(QcAiAgentConversationParams model);

    SseEmitter testConversation(QcAiAgentTestDTO qcAiAgentTestDTO);

    /**
     * 默认展示3个内置的问题，调用KIMI模型默认生成三个问题
     * 提示词为:-问题应该与【取该AGENT的描述和名称】紧密相关，可以引发进一步的讨论。
     * 每句话只包含一个问题。
     * 推荐你有能力回答的问题。
     *
     * @param query
     * @return
     */
    String generateDefaultQuestion(QcAiAgentChatQuery query);

    Message evaluateConversation(ConversationEvaluationDTO evaluationDTO);

    List<CommonFeedbackDTO> getCommonFeedback(String feedbackType);

    void stopChat(ChatStopDTO chatStopDTO);

    void exportHistoryConversation(QcAiAgentConversationParams model, HttpServletResponse response);

    Message saleAnalysisChat(QcAiAgentChatDTO qcAiAgentChatDTO);

    Message recommendPrompt(QcAiAgentRecommendDTO recommendationDTO);
}
