package com.qc.agent.app.workflow;

import com.qc.agent.lla.LLAClient;
import com.qc.agent.lla.adapter.LLAAdapter;
import com.qc.agent.lla.adapter.LLAAdapterBuilder;
import com.qc.agent.lla.model.LLAConfig;
import com.qc.agent.lla.model.LLARequest;
import com.qc.agent.lla.model.LLAResponse;
import com.qc.agent.platform.sse.SseClient;
import lombok.NonNull;

import java.io.File;

/**
 * <AUTHOR>
 * @date 2025/3/12 10:05:04
 */
public class WorkflowLLAClient {
    /**
     * 发送异步请求
     *
     * @param config  模型配置
     * @param request 请求参数
     */
    public static void sendAsync(LLAConfig config, LLARequest request, SseClient client) {
        config.setStream(true);
        LLAClient.sendAsync(config, request, WorkflowSubscriber.of(client).request(request));
    }

    /**
     * 发送同步请求
     *
     * @param config  模型配置
     * @param request 请求参数
     * @return 响应结果
     */
    public static LLAResponse send(LLAConfig config, LLARequest request) {
        return send(config, request, true);
    }

    /**
     * 发送同步请求
     *
     * @param config   模型配置
     * @param request  请求参数
     * @param doInvoke 是否回调
     * @return 响应结果
     */
    public static LLAResponse send(LLAConfig config, LLARequest request, boolean doInvoke) {
        config.setStream(false);
        LLAResponse response = LLAClient.send(config, request);
        // 添加监听器调用
        if (doInvoke) {
            ListenerInvoker.invoke(response, request);
        }
        return response;
    }


    /**
     * 发送异步请求
     *
     * @param config  模型配置
     */
    public static String parseUploadFile(@NonNull File file, LLAConfig config) {
        return LLAClient.parseUploadFile(file, config);
    }

    /**
     * 获取文件内容
     *
     * @param fileId
     * @param config
     * @return
     */
    public static String getFileContent(@NonNull String fileId, LLAConfig config) {
        LLAAdapter adapter = LLAAdapterBuilder.getAdapter(config.getProvider());
        return adapter.getFileContent(fileId);
    }
}
