package com.qc.agent.app.job.extract_conversation.impl;

import com.qc.agent.app.agent.model.query.ExtractConversationQuery;
import com.qc.agent.app.agent.util.JsonUtils;
import com.qc.agent.app.job.extract_conversation.config.OssCustomUtils;
import com.qc.agent.app.job.extract_conversation.mapper.QcAiAgentExtractConversationMapper;
import com.qc.agent.app.job.extract_conversation.pojo.QcAiAgentConversationDO;
import com.qc.agent.app.job.extract_conversation.pojo.query.QcAiAgentMasterQuery;
import com.qc.agent.common.core.RequestHolder;
import com.qc.agent.common.core.TenantUser;
import com.qc.agent.platform.register.UserManager;
import lombok.extern.slf4j.Slf4j;
import org.dromara.x.file.storage.core.FileStorageService;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.InputStream;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-21
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Slf4j
@Component
public class AiAgentExtractConversationJob {

    @Resource
    private QcAiAgentExtractConversationMapper qcAiAgentExtractConversationMapper;
    @Resource
    private FileStorageService fileStorageService;

    public void uploadToOSS(InputStream fileStream, String originalFilename, String date, Long tenantId) {
        String ossDirectoryPath = "agent-conversation/" + date + "/" + tenantId + "/";

        OssCustomUtils.deleteFilesInDirectory(ossDirectoryPath);
        String url = fileStorageService.of(fileStream)
                .setOriginalFilename(originalFilename)
                .setPlatform("aliyun-oss-1")
                .setPath(ossDirectoryPath)
                .setContentType("application/json")
                .upload()
                .getUrl();
        log.info("抽取对话记录上传到oss,url:{}", url);
    }


    @Scheduled(cron = "${extract.conversation.task.cron.expression}")
    public void execute() {
        ExtractConversationQuery query = new ExtractConversationQuery();
        query.setStartDate(LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        query.setEndDate(LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        extractConversationDataToOss(query);
    }

    public void extractConversationDataToOss(ExtractConversationQuery query) {
        log.info("抽取对话记录上传到oss");
        QcAiAgentMasterQuery pageQuery = new QcAiAgentMasterQuery();
        pageQuery.setPage(1);
        pageQuery.setRows(5000);
        while (true) {
            try {
                TenantUser tenantUser = new TenantUser();
                RequestHolder.setThreadLocalUser(tenantUser);
                List<Long> tenantIdList = qcAiAgentExtractConversationMapper.selectMasterTenantIdList(pageQuery);
                if (tenantIdList.isEmpty()) {
                    return;
                }
                for (Long tenantId : tenantIdList) {
                    queryTenantConversationDataToOss(tenantId, query);
                }
                pageQuery.setPage(pageQuery.getPage() + 1);
            } finally {
                RequestHolder.clear();
            }
        }
    }

    public static List<String> getDateRange(String startDateStr, String endDateStr) {
        List<String> dateList = new ArrayList<>();

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate startDate = LocalDate.parse(startDateStr, formatter);
        LocalDate endDate = LocalDate.parse(endDateStr, formatter);

        // 确保开始日期不大于结束日期
        if (startDate.isAfter(endDate)) {
            throw new IllegalArgumentException("开始日期不能晚于结束日期");
        }

        LocalDate currentDate = startDate;
        while (!currentDate.isAfter(endDate)) {
            dateList.add(currentDate.format(formatter));
            currentDate = currentDate.plusDays(1);
        }

        return dateList;
    }

    public void queryTenantConversationDataToOss(Long tenantId, ExtractConversationQuery query) {
        try {
            TenantUser tenantUser = new TenantUser();
            tenantUser.setTenantId(tenantId);
            RequestHolder.setThreadLocalUser(tenantUser);
            List<String> dateRange = getDateRange(query.getStartDate(), query.getEndDate());
            for (String date : dateRange) {
                List<QcAiAgentConversationDO> list = qcAiAgentExtractConversationMapper.selectList(date, tenantId);
                if (!list.isEmpty()) {
                    // 2. 转换为 JSON 字符串 [[5]]
                    InputStream inputStream = JsonUtils.jsonToInputStream(JsonUtils.listToJson(list));
                    // 3. 生成文件名（建议包含时间戳）
                    String filename = String.format("agent_conversation_%d_%s.json",
                            UserManager.getTenantUser().getTenantId(),
                            date);
                    uploadToOSS(inputStream, filename, date, tenantId);
                }
            }
        } catch (Exception e) {
            log.error("抽取对话记录上传到oss异常,tenantId:{}", tenantId, e);
        } finally {
            RequestHolder.clear();
        }
    }

}

