package com.qc.agent.app.agent.model.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 数据源参数分组VO
 */
@Data
public class SourceParamGroupVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 分组编码
     */
    private String groupCode;

    /**
     * 分组名称，前端展示的选项名称
     */
    private String groupName;

    /**
     * 分组枚举值列表
     */
    private List<EnumValueVO> enumValues;

    /**
     * 该分组下的参数列表
     */
    private List<SourceParamVO> params;

    /**
     * 排序顺序
     */
    private Integer displayOrder;

    /**
     * 枚举值VO
     */
    @Data
    public static class EnumValueVO implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 枚举编码
         */
        private String code;

        /**
         * 枚举名称
         */
        private String name;
    }
}
