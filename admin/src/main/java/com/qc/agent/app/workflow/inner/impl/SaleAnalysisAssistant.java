package com.qc.agent.app.workflow.inner.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.qc.agent.app.agent.model.po.QcAiAgent;
import com.qc.agent.app.agent.support.AgentBizDataSupport;
import com.qc.agent.app.workflow.WorkflowLLAClient;
import com.qc.agent.app.workflow.dataUtils;
import com.qc.agent.app.workflow.inner.AgentType;
import com.qc.agent.common.core.Message;
import com.qc.agent.lla.exception.LLAInvokerRequestException;
import com.qc.agent.lla.model.LLAConfig;
import com.qc.agent.lla.model.LLARequest;
import com.qc.agent.lla.model.LLAResponse;
import com.qc.agent.platform.sse.SseClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.tika.utils.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Slf4j
@Component
public class SaleAnalysisAssistant extends AbstractIAgentInvoker {

    @Override
    public AgentType determineAgent() {
        return AgentType.SALES_ANALYSIS;
    }

    public static String REWRITE_PROMPT = "你是一个专业的销售分析助手，能够精准地将用户的问题进行分类，理解用户问题的意图含义；你的任务是根据用户的上一次会话内容对用户当前的问题进行改写，使得用户的问题符合上一次的主题意图；\n" +
            "首先你应该对用户的意图进行判断分类，如果用户问题的意图明确且不属于销售分析问题，比如天气怎么样、今天去哪玩，推荐一个美食等问题，你要直接返回原问题；\n" +
            "用户的问题意图不明确时，你需要结合上一次会话内容去判断用户是否省略了部分内容；如果用户省略了部分内容，你需要结合上一次会话内容对用户的问题进行改写并返回改写后的内容，否则你要直接返回原问题。\n" +
            "\n" +
            "##【改写原则】\n" +
            "1、精确分析当前问题的意图和问题类型，禁止改写意图明确的非销售分析问题；\n" +
            "2、对于意图不明确的问题，结合上一次会话进行改写，改写后的意图要和上一次会话意图一致；\n" +
            "3、改写后的问题要简洁意图明确；\n" +
            "4、只需输出改写后的问题。\n" +
            "\n" +
            "##【改写示例】\n" +
            "1、昨天呢\n" +
            "\t上一次会话：今天销售额合计是多少。\n" +
            "\t分析过程：用户的问题是昨天呢，意图不明确，我需要结合上一次会话内容进行改写，上一次会话是在讨论今天销售额的合计，根据改写规则改写后的问题要符合上一次会话的意图，用户想问的是昨天销售额的合计。\n" +
            "\t回答：昨天销售合计是多少？\n" +
            "2、王一呢\n" +
            "\t上一次会话：张一昨天卖了多少货。\n" +
            "\t分析过程：用户的问题是王一呢，意图不明确，我需要结合上一次会话内容进行改写，上一次会话是昨天卖了多少货，用户的问题是王一呢，根据改写规则改写后的问题要符合上一次会话的意图，用户想问的是王一昨天卖了多少货。\n" +
            "\t回答：王一昨天昨天卖了多少货？\n" +
            "6、今天天气怎么样\n" +
            "\t上一次会话：张一昨天卖了多少货。\n" +
            "\t分析过程：用户的问题天气怎么样，这是一个天气问题，意图明确不属于销售分析问题，禁止改写。\n" +
            "\t回答：今天天气怎么样\n" +
            "##【上一次会话】\n" +
            "%s\n" +
            "##【问题】：\n";

    @Override
    public Message invoke(QcAiAgent qcAiAgent, LLAConfig config, LLARequest request, SseClient client) {
        Message msg = Message.of();
        LLARequest noHistoryRequest = LLARequest.builder().id(request.getId()).clientId(request.getClientId()).start(request.getStart()).build();
        try {
            // 获取问题条件
            String question = request.getContent();
            noHistoryRequest.setContent(qcAiAgent.getSplitSqlPrompt().formatted(request.getUserName(), request.getDeptName()) + dataUtils.getCurrentDate() + question);
            LLAResponse conditionResponse = WorkflowLLAClient.send(config, noHistoryRequest, false);
            log.info("销售分析智能体 条件分析模型返回:{}", JSON.toJSONString(conditionResponse));
            JSONObject condition = null;
            try {
                condition = JSONObject.parseObject(conditionResponse.getContent());
                log.info("销售分析智能体 首次条件分析模型返回:{}", JSON.toJSONString(condition));
            } catch (Exception e1) {
                log.info("首次条件分析模型返回失败: " + conditionResponse.getContent());
                // 条件生成失败，根据最近一条的历史会话对问题进行改写
                if (request.getQas().size() > 0) {
                    String historyContext = request.getQas().get(0).toString();
                    noHistoryRequest.setContent(REWRITE_PROMPT.formatted(historyContext) + question);
                    String rewriteQuestion = WorkflowLLAClient.send(config, noHistoryRequest, false).getContent();
                    log.info("条件重写后问题:{}", rewriteQuestion);
                    question = rewriteQuestion;
                    // 重新进行条件生成
                    noHistoryRequest.setContent(qcAiAgent.getSplitSqlPrompt() + dataUtils.getCurrentDate() + question);
                    conditionResponse = WorkflowLLAClient.send(config, noHistoryRequest, false);
                    try {
                        condition = JSONObject.parseObject(conditionResponse.getContent());
                        log.info("销售分析智能体 重写后条件分析模型返回:{}", JSON.toJSONString(condition));
                    } catch (Exception e2) {
                        log.info("条件重写后分析模型返回失败: " + conditionResponse.getContent());
                    }
                }
            }
            if (MapUtils.isEmpty(condition)) {
                String conditionNullMessage = "很高兴为您服务，我是您的销售分析助手，请提问销售分析相关的问题。";
                doErrorSend(conditionNullMessage, request, conditionResponse, client);
                return msg.error("大");
            }
            String data = AgentBizDataSupport.fetchBizData(qcAiAgent, condition);
            log.info("销售分析智能体接口返回数据:{}", data);
            if (Objects.equals(data, StringUtils.EMPTY) || data.length() < 10) {
                data = "null";
            }
            noHistoryRequest.setContent(String.format(qcAiAgent.getPrompt(), data) + dataUtils.getCurrentDate() + question);
            WorkflowLLAClient.sendAsync(config, noHistoryRequest, client);
        } catch (LLAInvokerRequestException e) {
            log.error("调用大模型问答异常", e);
            String errorMessage = "该AGENT使用的模型的KEY已失效，请联系创建者/管理员调整后重新发布后再使用。";
            doErrorSend(errorMessage, request, null, client, false);
        }
        return msg;
    }
}
