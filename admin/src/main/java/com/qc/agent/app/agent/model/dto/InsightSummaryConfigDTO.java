package com.qc.agent.app.agent.model.dto;

import java.io.Serializable;
import java.util.List;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

/**
 * 洞察总结配置数据传输对象
 *
 * <AUTHOR>
 */
@Data
public class InsightSummaryConfigDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 总结配置ID（已废弃，不需要传递，后端通过agentId自动处理）
     */
    private Long id;

    /**
     * 综合分析结构化提示词片段
     */
    private List<PromptFragmentDTO> comprehensivePromptFragments;

    private JSONObject comprehensivePrompt;

    /**
     * 建议结构化提示词片段
     */
    private List<PromptFragmentDTO> summaryAdvicePromptFragments;

    private JSONObject summaryAdvicePrompt;

    /**
     * 综合分析提示词片段值列表
     */
    private List<DimensionPromptValueDTO> comprehensivePromptValues;

    /**
     * 建议提示词片段值列表
     */
    private List<DimensionPromptValueDTO> summaryAdvicePromptValues;

    /**
     * 关联的衡量标准列表（支持增删改查）
     */
    private List<InsightStandardDTO> standards;
}