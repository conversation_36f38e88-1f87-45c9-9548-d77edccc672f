package com.qc.agent.app.agent.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@AllArgsConstructor
@Data
public class QcAiAgentChatDTO {

    private Long deptId;

    private String status = "1";
    /**
     * 对话id
     */
    private Long conversationId;

    private String deptName;

    private List<Long> deptIdList;

    /**
     * 创建人id
     */
    private Long userId;

    /**
     * 前端传入的唯一id
     */
    private String chatSessionId;

    /**
     * 创建人名称
     */
    private String userName;

    /**
     * 智能体id
     */
    private Long agentId;

    /**
     * 是否新对话
     */
    private String newConversationFlag;

    /**
     * 问题
     */
    private String question;

    /**
     * 来源：web悬浮窗：web-levitate ； web端：web ； app端：app
     */
    private String source;



    /**
     * 对话 ID
     */
    private Long sessionId;
    /**
     * 1：新对话
     */
    private String isNew;

    /**
     * 前端生成的对话id
     */
    private String clientId;

    /**
     * 地址信息
     */
    private String address;

    /**
     * 回答
     */
    private String answer;

    /**
     * 回答时间
     */
    private LocalDateTime answerTime;

    /**
     * 拜访助手的参数
     */
    private VisitAgentParamDTO visitAgentParam;

    /**
     * 客户名称列表
     */
    private List<String> cmNameList;

    /**
     * 测试会话 是否是测试会话 1：是
     */
    private String isTest;

    /**
     * 问题类型  1：拜访客户推荐 2：拜访数据查询 0：其他
     */
    private String questionType;

    /**
     * 测试对话和正式对话都有的公共参数
     */
    private QcAiAgentCommonParam commonParam;
}
