package com.qc.agent.app.workflow.inner.pojo;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-05-27
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Data
public class QcAiKnowledgeRelated {

    /**
     * 知识库id
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long collectionId;
    /**
     * 发布状态 1：已发布  0：已取消发布
     */
    private String publishFlag;
    /**
     * 1:正常 0：已删除
     */
    private String knowledgeStatus;
    /**
     * 知识库名称
     */
    private String name;
    /**
     * QA / RAW
     */
    private String dataImpType;

    /**
     * 分类id
     */
    List<QcAiKnowledgeFile> relateDocList = new ArrayList<>();

}
