package com.qc.agent.app.agent.service;

import com.alibaba.fastjson.JSONObject;
import com.qc.agent.app.agent.model.po.RecommendResult;
import com.qc.agent.app.agent.model.query.QcAiGoodsAssistantQuery;
import com.qc.agent.app.agent.model.query.QcAiGoodsAssistantRecommendQuery;

import java.util.Map;

public interface GoodsRecommendService {
    /**
     * 意图识别
     *
     * @param query
     * @return
     */
    Map<String, Object> intent(QcAiGoodsAssistantQuery query);

    /**
     * 生成推荐商品和问题
     * @param query
     * @return
     */
    JSONObject generateRecommendQuestions(QcAiGoodsAssistantRecommendQuery query);
}