package com.qc.agent.app.agent.model.entity;

import java.util.List;

import com.qc.agent.platform.pojo.BaseEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 数据项实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InsightDataItem extends BaseEntity {
    private Long id;
    private String itemCode;
    private String itemName;
    /**
     * 关联的查询业务--对应qc_ai_data_source.source_code
     */
    private String queryBusinessCode;
    private String dataTypeCode;
    private String placeholderName;
    private String description;
    private Integer sortOrder;

    // 树形结构支持字段
    private Long parentId;
    private List<InsightDataItem> children;

    /**
     * 是否被选中（用于前端展示）
     */
    private Boolean selected = false;

    /**
     * 判断是否为父级数据项
     * 
     * @return true表示是父级，false表示是子级
     */
    public boolean isParent() {
        return parentId == null;
    }

    /**
     * 获取数据项的层级深度
     * 
     * @return 层级深度，从0开始
     */
    public int getLevel() {
        if (itemCode == null) {
            return 0;
        }
        return (int) itemCode.chars().filter(ch -> ch == '.').count();
    }
}