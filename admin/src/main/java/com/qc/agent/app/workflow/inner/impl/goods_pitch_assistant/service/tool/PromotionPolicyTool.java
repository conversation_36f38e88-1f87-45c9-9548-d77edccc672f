package com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.service.tool;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.qc.agent.app.agent.support.AgentBizDataSupport;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Select;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class PromotionPolicyTool {

    @Value("${ai.agent.appsvr-domin-url}")
    private String appsvrDomainUrl;

    public String getPromotionPolicy(String productId, String cmId) {
        JSONObject params = new JSONObject();
        params.put("cm_id", cmId);
        JSONArray prodIds = new JSONArray();
        prodIds.add(new JSONObject().fluentPut("pd_id", productId));
        params.put("products", prodIds);

        String url = this.appsvrDomainUrl + "/app/bas_pd/client/v3/promotion/autoAssignPromotions.do";

        try {
            String response = AgentBizDataSupport.fetchBizData(url, params);
            if (StringUtils.isEmpty(response)) {
                return null;
            }
            JSONObject jsonObject = JSONArray.parseArray(response).getJSONObject(0);
            JSONObject promotions = jsonObject.getJSONObject("promotions");

            StringBuilder promotionBuilder = new StringBuilder();
            appendPromotionInfo(promotions, "full_discounts", promotionBuilder);
            appendPromotionInfo(promotions, "min_prices", promotionBuilder);
            appendPromotionInfo(promotions, "mz_promoitons", promotionBuilder);

            return promotionBuilder.toString();
        } catch (Exception e) {
            log.error("商品查询接口调用失败", e);
            return null;
        }
    }

    private static void appendPromotionInfo(JSONObject promotions, String promotionType, StringBuilder builder) {
        if (promotions.containsKey(promotionType)) {
            JSONArray promotionsArray = promotions.getJSONArray(promotionType);
            if (promotionsArray != null && !promotionsArray.isEmpty()) {
                for (int i = 0; i < promotionsArray.size(); i++) {
                    JSONObject promotion = promotionsArray.getJSONObject(i);
                    if (promotion != null && StringUtils.isNotEmpty(promotion.getString("title"))) {
                        if (!builder.isEmpty()) {
                            builder.append("\n\n");
                        }
                        builder.append(String.format("促销主题：%s\n促销说明：%s\n促销日期：%s~%s",
                                promotion.getString("title"),
                                promotion.getString("hint"),
                                promotion.getString("start_time"),
                                promotion.getString("end_time")));
                    }
                }
            }
        }
    }
}
