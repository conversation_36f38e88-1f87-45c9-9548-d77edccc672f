package com.qc.agent.app.knowledge.utils;

import cn.hutool.core.lang.Assert;
import com.google.common.collect.Lists;
import com.qc.agent.app.knowledge.model.ExcelHandlerParam;
import com.qc.agent.common.exception.BizException;
import com.qc.agent.common.util.SpringApplicationUtils;
import com.qc.agent.platform.file.dto.UploadRequest;
import com.qc.agent.platform.file.service.FileUploadService;
import com.qc.agent.storage.StorageClient;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.dromara.x.file.storage.core.FileInfo;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;

/**
 * excel处理器，处理excel的读写支持自定义业务操作
 *
 * <AUTHOR>
 * @date 2024/9/30 09:46:32
 *
 */
public class ExcelHandler
{



    /**
     * 上传文件，并读取文件内容，回调函数处理
     * @param request 上传文件请求
     * @param bizFunction 业务函数
     */
    public static void readExcelAndUpload(UploadRequest request, Function<ExcelHandlerParam,Void> bizFunction)
    {
        Assert.notNull(request,"request must not be null");

        assert request.getFiles().size() == 1 : "仅支持单文件上传";

        MultipartFile file = request.getFiles().get(0);

        // 检测文件类型
        checkFileType(file.getOriginalFilename());

        // 上传文件
        FileUploadService fileUploadService = SpringApplicationUtils.getBean(FileUploadService.class);

        List<FileInfo> infos = fileUploadService.upload(request);

        // 验证infos是否为空
        assert !infos.isEmpty() : "文件上传失败";

        // 读取文件
        List<Pair<String,List<String[]>>> result = readExcel(file);

        // lamda兼容callback为null 的情况
        if(bizFunction != null){
            bizFunction.apply(ExcelHandlerParam.builder().data(result).fileInfo(infos.get(0)).build());
        }

    }


    /**
     * 写入excel文件
     * @param data 数据
     * @param filePath 文件路径
     */
    public static FileInfo writeExcel(String[] header,List<String[]> data,String filePath){

        Assert.notNull(header,"data must not be null");
        Assert.notNull(filePath,"filePath must not be null");

        checkFileType(filePath);


        try(Workbook workbook = new XSSFWorkbook();)
        {
            List<List<String[]>> partitions = Lists.partition(data,50000);
            // 遍历数据
            for (int r=0;r<partitions.size();r++) {

                // 在工作簿中创建新的sheet
                Sheet sheet = workbook.createSheet(String.format("Sheet%s",r+1));

                List<String[]> rows = partitions.get(r);  // 获取sheet数据

                // 添加头
                if(header != null){
                    rows.add(0,header);
                }

                // 写入sheet数据
                for (int i = 0; i < rows.size(); i++) {
                    Row row = sheet.createRow(i);  // 创建行
                    String[] rowData = rows.get(i);

                    // 写入每一列数据
                    for (int j = 0; j < rowData.length; j++) {
                        Cell cell = row.createCell(j);  // 创建单元格
                        cell.setCellValue(rowData[j]);  // 写入单元格值
                    }
                }
            }
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            workbook.write(bos);

            return StorageClient.of().buildUploader(bos.toByteArray(),FilenameUtils.getName(filePath),FilenameUtils.getExtension(filePath),bos.size()).setPath(FilenameUtils.getPath(filePath)).setSaveFilename(FilenameUtils.getName(filePath)).upload();
        }catch(IOException e){
            throw new BizException("写Excel出错。");
        }

    }

    /**
     * 读取excel文件，返回sheet名称和数据
     * @param file 文件
     * @return sheet名称和数据
     */
    public static List<Pair<String,List<String[]>>> readExcel(MultipartFile file)
    {
        try(InputStream is=file.getInputStream())
        {
            return readExcel(is);
        }
        catch(IOException e)
        {
            throw new RuntimeException(e);
        }

    }

    public static List<Pair<String,List<String[]>>> readExcel(String filePath)
    {
        AtomicReference<List<Pair<String, List<String[]>>>> result = new AtomicReference<>(new ArrayList<>());
        StorageClient.of().buildDownloader(filePath).inputStream(is -> {
            result.set(readExcel(is));
        });
        return result.get();
    }

    public static List<Pair<String,List<String[]>>> readExcel(InputStream is)
    {
        List<Pair<String,List<String[]>>> result = Lists.newArrayList();
        try(Workbook wb = new XSSFWorkbook(is))
        {
            // 获取工作表的数量
            int numberOfSheets = wb.getNumberOfSheets();
            for (int i = 0; i < numberOfSheets; i++)
            {
                // 获取第 i 个工作表
                Sheet sheet = wb.getSheetAt(i);
                String sheetName = sheet.getSheetName();
                List<String[]> sheetData = new ArrayList<>();

                for (Row row : sheet) {
                    String[] rowData = new String[row.getLastCellNum()];
                    for(int j = 0; j < row.getLastCellNum(); j++)
                    {
                        String cellValue = getCellValue(row.getCell(j));
                        rowData[j] = cellValue;
                    }

                    sheetData.add(rowData);
                }
                result.add(Pair.of(sheetName, sheetData));


            }
        }
        catch(IOException e)
        {
            throw new RuntimeException(e);
        }
        return result;
    }


    /**
     * 保存excel文件
     * @param filePath 文件路径
     * @param bizFunction 业务函数
     */
    public static void saveExcel(String filePath,Function<Workbook,Void> bizFunction){

        // 检测文件类型
        checkFileType(filePath);

        // 下载文件
        StorageClient.of().buildDownloader(filePath).inputStream(is -> {
            try(Workbook workbook =new XSSFWorkbook(is);)
            {
                if(bizFunction == null)
                {
                    throw new BizException("bizFunction is null");
                }
                // 业务处理
                bizFunction.apply(workbook);

                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                workbook.write(bos);

                StorageClient.of().buildUploader(bos.toByteArray(),FilenameUtils.getName(filePath),FilenameUtils.getExtension(filePath),bos.size()).setPath(FilenameUtils.getPath(filePath)).setSaveFilename(FilenameUtils.getName(filePath)).upload();
            }
            catch(IOException e)
            {
                throw new RuntimeException(e);
            }

        });


    }

    /**
     * 添加一行数据
     * @param workbook excel 工作簿
     * @param sheetName sheet 名称
     * @param data 新行数据
     */
    public static void addRow(Workbook workbook, String sheetName, String[] data) {
        Sheet sheet = workbook.getSheet(sheetName);
        int lastRowNum = sheet.getLastRowNum(); // 获取最后一行的行号
        Row newRow = sheet.createRow(lastRowNum + 1); // 创建新的一行

        for (int i = 0; i < data.length; i++) {
            Cell cell = newRow.createCell(i);
            cell.setCellValue(data[i]); // 在新行中创建单元格并写入数据
        }
    }

    /**
     * 更新单元格
     * @param workbook excel 工作簿
     * @param sheetName sheet 名称
     * @param rowNumber 更新行号（行号从0开始）
     * @param cellNumber 更新列号（列号从0开始）
     * @param newValue 新值
     */
    public static void updateCell(Workbook workbook, String sheetName, int rowNumber, int cellNumber, String newValue) {
        Sheet sheet = workbook.getSheet(sheetName);
        if (sheet != null) {
            Row row = sheet.getRow(rowNumber);
            if (row == null) {
                row = sheet.createRow(rowNumber);
            }
            Cell cell = row.getCell(cellNumber);
            if (cell == null) {
                cell = row.createCell(cellNumber);
            }
            cell.setCellValue(newValue); // 更新单元格的值
        }
    }

    /**
     * 删除一行数据
     * @param workbook excel 工作簿
     * @param sheetName sheet 名称
     * @param rowNumber 删除行号（行号从0开始）
     */
    public static void deleteRow(Workbook workbook, String sheetName, int rowNumber) {
        Sheet sheet = workbook.getSheet(sheetName);
        int lastRowNum = sheet.getLastRowNum();

        // 如果删除的行不是最后一行，则需要移动下面的行
        if (rowNumber >= 0 && rowNumber < lastRowNum) {
            sheet.shiftRows(rowNumber + 1, lastRowNum, -1); // 将行上移一行
        }

        // 如果删除的是最后一行或者需要删除行内容
        Row rowToDelete = sheet.getRow(lastRowNum);
        if (rowToDelete != null) {
            sheet.removeRow(rowToDelete); // 删除最后一行（已经上移）
        }
    }


    /**
     * 获取单元格的值
     * @param cell 单元格
     * @return 单元格的值
     */
    private static String getCellValue(Cell cell) {
        if(cell == null){
            return "";
        }
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    return String.valueOf(cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            case BLANK:
            default:
                return "";
        }
    }

    private static void checkFileType(String fileName){
        FilenameUtils.getExtension(fileName);
        if(!List.of("xls", "xlsx").contains(FilenameUtils.getExtension(fileName)))
        {
            throw new BizException("文件格式不正确，只支持xls,xlsx。");

        }
    }

    public static void main(String[] args)
    {
        String path = "ai-agent/6928113655533238145/knowledgeBase/third_part_mbo.md";

        System.out.println(FilenameUtils.getPath(path));
    }
}
