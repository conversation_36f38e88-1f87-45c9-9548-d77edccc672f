package com.qc.agent.app.workflow.inner.mapper;

import com.qc.agent.app.workflow.inner.pojo.QcAiAgentIntentKnowledgeCategoryMapping;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-05-27
 */
public interface QcAiAgentIntentKnowledgeCategoryMappingMapper {
    QcAiAgentIntentKnowledgeCategoryMapping selectById(Long id);

    List<QcAiAgentIntentKnowledgeCategoryMapping> selectAll();

    int insert(QcAiAgentIntentKnowledgeCategoryMapping record);

    int update(QcAiAgentIntentKnowledgeCategoryMapping record);

    int deleteById(Long id);

    /**
     * 批量插入
     *
     * @param list
     */
    void batchInsert(@Param("list") List<QcAiAgentIntentKnowledgeCategoryMapping> list);

    void deleteByAgentId(@Param("agentId") Long agentId,@Param("status")String status);
}
