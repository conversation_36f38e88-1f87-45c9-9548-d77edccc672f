package com.qc.agent.app.workflow.inner.impl.customer_insight_assistant.service.processor.impl;

import com.alibaba.fastjson.JSONObject;
import com.qc.agent.app.workflow.inner.impl.customer_insight_assistant.model.CustomerInsightBusinessIntent;
import com.qc.agent.app.workflow.inner.impl.customer_insight_assistant.model.enums.CustomerInsightIntent;
import com.qc.agent.app.workflow.inner.impl.customer_insight_assistant.service.processor.CustomerInsightProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 默认客户洞察处理器实现
 * 
 * <AUTHOR>
 * @since 1.0.0
 * @version 1.0.0
 */
@Slf4j
@Component
public class DefaultCustomerInsightProcessor implements CustomerInsightProcessor {

    @Override
    public String generateAnswer(CustomerInsightBusinessIntent intent, String logPrefix) {
        log.info("{} 使用客户分析处理器生成回答", logPrefix);
        
        JSONObject entities = intent.entities();
        String customerName = entities.getString("customer");
        
        return generateCustomerAnalysisAnswer(customerName);
    }

    @Override
    public String getSupportedIntent() {
        return "CUSTOMER_ANALYSIS"; // 支持客户分析意图
    }

    /**
     * 生成客户分析回答
     */
    private String generateCustomerAnalysisAnswer(String customerName) {
        if (customerName != null && !customerName.isEmpty()) {
            return String.format("根据分析，%s的主要特征包括：\n" +
                    "1. 客户类型：大型连锁超市\n" +
                    "2. 经营规模：全国性连锁\n" +
                    "3. 目标客群：中高端消费者\n" +
                    "4. 经营特色：一站式购物体验\n" +
                    "5. 合作模式：长期战略合作伙伴", customerName);
        } else {
            return "请提供具体的客户名称，我可以为您分析该客户的主要特征。";
        }
    }


} 