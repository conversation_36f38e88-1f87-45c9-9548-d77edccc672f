package com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.processor.impl;

import com.alibaba.fastjson.JSONObject;
import com.qc.agent.app.agent.model.po.QcAiAgent;
import com.qc.agent.app.agent.model.po.QcAiAgentConversation;
import com.qc.agent.app.workflow.WorkflowLLAClient;
import com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.model.enums.GoodsIntent;
import com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.processor.IntentProcessor;
import com.qc.agent.app.workflow.inner.impl.tools.LLMTools;
import com.qc.agent.common.core.Message;
import com.qc.agent.lla.model.LLAConfig;
import com.qc.agent.lla.model.LLARequest;
import com.qc.agent.platform.sse.SseClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-07-01
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OtherProcessor implements IntentProcessor {

    @Override
    public Message process(LLARequest request, LLAConfig config, SseClient client, QcAiAgent agent, JSONObject entities) {
        String logPrefix = LLMTools.getLogPrefix(request, agent);
        log.info("{} Processing intent: {}", logPrefix, getSupportedIntent());
        request.setSystem(agent.getPrompt());
        request.setContent(request.getContent());
        WorkflowLLAClient.sendAsync(config, request, client);
        return Message.of().ok();
    }

    @Override
    public GoodsIntent getSupportedIntent() {
        return GoodsIntent.OTHER;
    }

    @Override
    public Message generateRecommend(LLARequest request, LLAConfig config, QcAiAgent agent, JSONObject entities, QcAiAgentConversation conversation) {
        var logPrefix = LLMTools.getLogPrefix(request, agent);
        log.info("{} Processing intent,other intent not generate recommend: {}", logPrefix, getSupportedIntent());
        return Message.of().data(List.of()).ok();
    }
}
