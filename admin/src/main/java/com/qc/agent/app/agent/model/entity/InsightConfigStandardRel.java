package com.qc.agent.app.agent.model.entity;

import java.io.Serializable;

import com.qc.agent.platform.pojo.BaseEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 配置标准关系表
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InsightConfigStandardRel extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 关系 ID，主键
     */
    private Long id;

    /**
     * 配置 ID（维度配置或总结配置）
     */
    private Long configId;

    /**
     * 配置类型（DIMENSION/SUMMARY）
     */
    private String configType;

    /**
     * 标准 ID
     */
    private Long standardId;

}