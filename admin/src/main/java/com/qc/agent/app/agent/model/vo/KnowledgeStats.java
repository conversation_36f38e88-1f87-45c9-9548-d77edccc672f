package com.qc.agent.app.agent.model.vo;

import com.qc.agent.app.agent.model.po.QcAiAgentAdminChart;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-07-04
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Data
@AllArgsConstructor
public class KnowledgeStats {
    private List<QcAiAgentAdminChart> chart;
    private long totalCount;
    private long dissatisfiedCount;
    private long outOfScopeCount;
    private String solvedRatio;
}
