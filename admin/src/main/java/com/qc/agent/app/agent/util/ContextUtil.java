package com.qc.agent.app.agent.util;

import com.qc.agent.common.core.RequestHolder;
import com.qc.agent.common.core.TenantUser;
import com.qc.agent.platform.register.UserManager;

/**
 * <AUTHOR>
 * @date 2025-06-18
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
public class ContextUtil {

    private ContextUtil() {
    }

    // 统一上下文管理
    public static Runnable wrapWithContext(Runnable task) {
        TenantUser tenantUser = UserManager.getTenantUser();
        String requestCookie = RequestHolder.getRequestCookie();
        String token = RequestHolder.getRequestToken();
        return () -> {
            try {
                RequestHolder.setThreadLocalUser(tenantUser);
                RequestHolder.setRequestCookie(requestCookie);
                RequestHolder.setRequestToken(token);
                task.run();
            } finally {
                RequestHolder.clear();
            }
        };
    }
}
