package com.qc.agent.app.file_process.pojo;

import com.qc.agent.lla.model.LLAMessage;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-06-16
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Data
@Accessors(chain = true) // 开启链式调用
public class QcAiParseFileResult {
    /**
     * 文件解析的内容
     */
    private String content;

    /**
     * 缓存id
     */
    private String contextId;

    /**
     * 缓存信息
     */
    private List<LLAMessage> cacheInfoList;
}
