package com.qc.agent.app.knowledge.service;

import com.qc.agent.app.knowledge.model.*;
import com.qc.agent.app.knowledge.model.query.KnowledgeQuery;
import com.qc.agent.common.core.Message;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @className KnowledgeInfoService
 * @description TODO
 * @date 2024/1/22 14:11
 */
public interface KnowledgeInfoService {
    /**
     * 【知识库详情查询】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    QcKnowledgeInfo selectQcKnowledgeInfoById(Long id);

    /**
     * 【知识库列表查询】
     *
     * @param qcKnowledgeInfoParams 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    List<QcKnowledgeInfoExt> selectQcKnowledgeInfoList(QcKnowledgeInfoParams qcKnowledgeInfoParams);

    /**
     * 新增【请填写功能名称】
     *
     * @param qcKnowledgeInfoParams 【请填写功能名称】
     * @return 结果
     */
    Long insertQcKnowledgeInfo(QcKnowledgeInfoParams qcKnowledgeInfoParams);

    /**
     * 修改【请填写功能名称】
     *
     * @param qcKnowledgeInfoParams 【请填写功能名称】
     * @return 结果
     */
    int updateQcKnowledgeInfo(QcKnowledgeInfoParams qcKnowledgeInfoParams);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键集合
     * @return 结果
     */
    int deleteQcKnowledgeInfoByIds(String ids);

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    Message deleteQcKnowledgeInfoById(Long id);

    /**
     * 获取问答统计、文件统计
     *
     * @param qcKnowledgeInfoParams
     * @return
     */
    Map<String, Object> queryQcKnowledgeSummary(QcKnowledgeInfoParams qcKnowledgeInfoParams);

    /**
     * 启用知识库
     *
     * @param id
     * @param userId
     * @param userName
     */
    void enable(Long id, Long userId, String userName);

    /**
     * 停用知识库
     *
     * @param id
     * @param userId
     * @param userName
     */
    void disable(Long id, Long userId, String userName);

    /**
     * 发布
     *
     * @param knowledgePublishParam
     */
    void publish(KnowledgePublishParam knowledgePublishParam);

    /**
     * 取消发布
     *
     * @param id
     * @param userId
     * @param userName
     */
    void deactivate(Long id, Long userId, String userName);

    Map<String,Object> quoteList(KnowledgeQuery query);

    List<QcKnowledgeFileCategory> queryCategorySetting(String collectionId);

    void modifyCategorySetting(QcKnowledgeCategoryWrapperParam qcKnowledgeCategoryWrapperParam);

    String checkCategorySetting(QcKnowledgeCategoryWrapperParam qcKnowledgeCategoryWrapperParam);
}
