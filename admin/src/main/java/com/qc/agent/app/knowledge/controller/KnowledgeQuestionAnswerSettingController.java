package com.qc.agent.app.knowledge.controller;

import com.qc.agent.app.knowledge.model.*;
import com.qc.agent.app.knowledge.service.KnowledgeQuestionAnswerSettingService;
import com.qc.agent.common.core.Message;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @className KnowledgeFileController
 * @description TODO
 * @date 2024/1/22 9:52
 */
@RequestMapping("/ai-agent/knowledge/qa/setting")
@RestController
public class KnowledgeQuestionAnswerSettingController {
    private final Log logger = LogFactory.getLog(KnowledgeQuestionAnswerSettingController.class);

    @Autowired
    private KnowledgeQuestionAnswerSettingService knowledgeQuestionAnswerSettingService;


    @RequestMapping("/addOrUpdate.do")
    public Message addOrUpdate(@RequestBody KnowledgeQuestionAnswerSettingParams params){

        //更新
        if (!Objects.isNull(params.getId()) && params.getId() > 0L) {
            knowledgeQuestionAnswerSettingService.update(params);
        } else {
            return Message.of().data(String.valueOf(knowledgeQuestionAnswerSettingService.save(params))).ok();
        }
        return Message.of().ok();
    }



    @RequestMapping("/getInfo.do")
    public Message getInfo(@RequestBody KnowledgeQuestionAnswerSettingParams params){
        KnowledgeQuestionAnswerSettingInfo data = knowledgeQuestionAnswerSettingService.getInfo(params);
        return Message.of().data(data).ok();
    }

    /**
     * 获取ai厂商下拉接口数据
     * @param params
     * @return
     */
    @RequestMapping("/findAiVendors.do")
    public Object findAiVendors(@RequestBody Map<String, Object> params){
        List<Map<String, Object>> data = knowledgeQuestionAnswerSettingService.findAiVendors(params);
        return Message.of().data(data).ok();
    }
}
