package com.qc.agent.app.agent.model.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class ConversationVO {

    /**
     * 主键 ID
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long conversationId;

    /**
     * 状态，默认值 '1'
     */
    private String status;

    /**
     * 创建人 ID
     */
    private Long creatorId;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 回答时间
     */
    private LocalDateTime answerTime;

    /**
     * 对话 ID
     */
    private Long sessionId;

    /**
     * 大模型 ID
     */
    private Long modelId;

    /**
     * 用户 ID
     */
    private Long userId;

    /**
     * 智能体 ID
     */
    private Long agentId;

    /**
     * 问题
     */
    private String question;

    /**
     * 问题 token 数
     */
    private BigDecimal questionToken;

    /**
     * 回答
     */
    private String answer;

    /**
     * 回答 token 数
     */
    private BigDecimal answerToken;

    /**
     * 会话时长（单位：秒）
     */
    private BigDecimal conversationTime;

    /**
     * 对话状态
     */
    private String conversationStatus;

    private BigDecimal tokens;

    /**
     * 前端传入的唯一id
     */
    private String chatSessionId;


    /**
     * 评价类型 0:不满意 1:满意 2:未反馈
     */
    @JSONField(serialzeFeatures = SerializerFeature.WriteMapNullValue)
    private String evaluationType;

    /**
     * 评价内容
     */
    @JSONField(serialzeFeatures = SerializerFeature.WriteMapNullValue)
    private String feedback;

    private String viewCount;

    private String referenceContent;

    private String intentName;
    /**
     * 引用数量
     */
    private Integer quoteCount;
}
