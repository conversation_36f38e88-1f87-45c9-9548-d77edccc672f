package com.qc.agent.app.knowledge.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @className QcKnowledgeFile
 * @description 文件接口入参
 * @date 2024/1/22 10:22
 */
@Data
public class QcKnowledgeFile {

    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;
    /**  文件名称*/
    private String fileName;

    /**  文件路径*/
    private String fileUrl;

    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long folderId;
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long collectionId;

    /**  文件分段长度*/
    private String segmentLength;



    private String fileComments;
    private String fileLogo;
    private String fileSize;

    /**
     * 0：删除，1：启用，2：停用
     */
    private String fileStatus;
    private String originName;
    private String fileType;
    private Long createUserId;
    private String createUserName;
    private Long modifyUserId;
    private String modifyUserName;
    private String viewCount;
    private String downCount;
    private Long databaseId;

    private Date createTime;
    @JSONField(serializeUsing = ToStringSerializer.class)
    private String categoryName;
    private String categoryId;
    private Date modifyTime;

    private String dataImpType;
}
