package com.qc.agent.app.question.impl.baidu;

import com.alibaba.fastjson.JSONObject;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

import com.qc.agent.app.question.impl.AbstrctAnswerAIRequest;
import com.qc.agent.common.util.UUIDUtils;
import com.qc.agent.platform.sse.AnswerAISubscriber;
import com.qc.agent.platform.sse.SseMessageConverter;
import com.qc.agent.platform.sse.SseRequest;
import com.qc.agent.platform.sse.dto.AIVendors;

import com.qc.agent.platform.sse.dto.AnswerAIMessage;
import com.qc.agent.vectordb.pojo.ChatQA;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.net.URLEncoder;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;


/**
 *
 * <AUTHOR>
 */

@Component
@Slf4j
public class BaiduChatbotImpl extends AbstrctAnswerAIRequest
{
    @Value("${ai.agent.baidu-apikey}")
    String securityCredentialKeyApiKey;

    @Value("${ai.agent.baidu-secretkey}")
    String securityCredentialKeySecretKey;
    static  final String PROVIDER_DOMAIN = "https://aip.baidubce.com/rpc/2.0/ai_custom";

    @Override
    public AIVendors determineAIVendors()
    {
        return AIVendors.BAIDU;
    }
    @Override
    public SseMessageConverter determineMessageConverter()
    {
        return new BaiduMessageStreamConverter();
    }

    @Override
    protected void doAnswer(SseRequest request, AnswerAISubscriber subscriber)
    {
        log.info("做文心一言问答, ApiKey:{}, SecretKey:{}", securityCredentialKeyApiKey, securityCredentialKeySecretKey);

        JSONObject requestBody = buildRequestBody(request);

        log.info("调用问答接口，request：{}", requestBody);

        HttpRequest httpRequest = HttpRequest.newBuilder()
                .header("Content-Type", "application/json")
                .uri(URI.create(URI.create(PROVIDER_DOMAIN) + "/v1/wenxinworkshop/chat/ernie-speed-128k" +
                        "?access_token=" + getAccessToken()))
                .POST(HttpRequest.BodyPublishers.ofString(requestBody.toJSONString()))
                .build();
        log.info("request url:{}", httpRequest.uri().getHost() + httpRequest.uri().getPath());
        try
        {
            request.setStartTime(System.currentTimeMillis());
            HttpClient.newBuilder().build().sendAsync(httpRequest, HttpResponse.BodyHandlers.fromLineSubscriber(subscriber)).get();
        }
        catch(Exception e)
        {
            throw new RuntimeException(e);
        }
    }

    @NotNull
    private JSONObject buildRequestBody(SseRequest request)
    {
        JSONObject param = new JSONObject();
        List<AnswerAIMessage> messages = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(request.getHistories())){
            for (ChatQA qa: request.getHistories()) {
                messages.add(AnswerAIMessage.builder().role("user").content(qa.getQuestion()).build());
                messages.add(AnswerAIMessage.builder().role("assistant").content(qa.getAnswer()).build());
            }
            messages.add(AnswerAIMessage.builder().role("user").content(request.getQuestion()).build());
            if (messages.size() > 30) {
                messages = messages.subList(messages.size() - 30, messages.size());
            }
            if(CollectionUtils.isNotEmpty(messages) && !"user".equals(messages.get(0).getRole())){
                messages.remove(0);
            }
        }else{
            messages.add(AnswerAIMessage.builder().role("user").content(request.getQuestion()).build());
        }
        param.put("messages",messages);
        param.put("system", request.getPrompt());
        param.put("stream", true);
        param.put("disable_search",false);
        param.put("enable_citation",false);
        return param;
    }

    private String getAccessToken() {
        HttpRequest request = buildFetchTokenRequest(securityCredentialKeyApiKey, securityCredentialKeySecretKey);

        try {
            HttpClient httpClient = HttpClient.newBuilder()
                    .connectTimeout(Duration.ofMinutes(5))
                    .build();
            HttpResponse<String> response= httpClient.send(request, HttpResponse.BodyHandlers.ofString());
            JsonObject jsonResponse = JsonParser.parseString(response.body()).getAsJsonObject();
            String accessToken = "access_token";
            return jsonResponse.has(accessToken) ? jsonResponse.get(accessToken).getAsString() : null;
        } catch (Exception e) {
            log.error("Failed to fetch token, the error is: ", e);
            return null;
        }
    }


    private HttpRequest buildFetchTokenRequest(String apiKey, String secretKey) {
        String url = "https://aip.baidubce.com/oauth/2.0/token";
        String params = "grant_type=client_credentials"
                + "&client_id=" + URLEncoder.encode(apiKey, StandardCharsets.UTF_8)
                + "&client_secret=" + URLEncoder.encode(secretKey, StandardCharsets.UTF_8);
        return HttpRequest.newBuilder()
                .uri(URI.create(url + "?" + params))
                .header("Content-Type", "application/json")
                .GET()
                .build();
    }




    public String buildAnswer(String defaultAnswer){
        JSONObject data = new JSONObject();
        data.put("id", UUIDUtils.getUUID2Long());
        data.put("result",defaultAnswer);
        data.put("is_end", StringUtils.isNotEmpty(defaultAnswer) ? "false" : "true");
        return String.format("data: %s", data.toJSONString());
    }
}
