package com.qc.agent.app.agent.model.query;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-26
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Data
public class QcAiAgentQuery {
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 部门id
     */
    private Long deptId;
    /**
     * agent名称
     */
    private String name;

    private Boolean viewAll;

    private List<Long> deptIdList;
}
