package com.qc.agent.app.agent.service.goods_intent;

import com.qc.agent.app.agent.model.query.QcAiGoodsAssistantQuery;
import com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.model.dto.BusinessIntent;
import com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.model.enums.GoodsIntent;

/**
 * <AUTHOR>
 * @date 2025-06-26
 */
public interface GoodsIntentHandler {
    /**
     * 意图
     *
     * @return
     */
    GoodsIntent getSupportedIntent();

    /**
     * 额外的意图处理
     *
     * @param intent
     * @param query
     * @param logPrefix
     * @return
     */
    BusinessIntent handle(BusinessIntent intent, QcAiGoodsAssistantQuery query, String logPrefix);
}
