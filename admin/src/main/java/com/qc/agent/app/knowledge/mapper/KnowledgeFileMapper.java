package com.qc.agent.app.knowledge.mapper;

import com.qc.agent.app.knowledge.model.QcKnowledgeFile;
import com.qc.agent.app.knowledge.model.QcKnowledgeFileListParams;
import com.qc.agent.app.knowledge.model.QcKnowledgeFileParams;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @className KnowledgeInfoMapper
 * @description TODO
 * @date 2024/1/22 14:14
 */
@Component
public interface KnowledgeFileMapper {
    int insertQcKnowledgeFile(QcKnowledgeFileParams qcKnowledgeFileParams);
    int batchInsert(@Param("list") List<QcKnowledgeFileParams> list);

    int countFile(QcKnowledgeFileListParams qcKnowledgeFileParams);

    List<QcKnowledgeFile> queryFileList(QcKnowledgeFileListParams qcKnowledgeFileParams);

    QcKnowledgeFile queryById(@Param("id") Long id);

    int updateFileStatus(QcKnowledgeFileParams qcKnowledgeFileParams);

    List<QcKnowledgeFile> queryFileNameByDatabaseId(Long id);

    String queryFileNameByOSSUrl(@Param("ossUrl") String ossUrl);

    int countByFileNameAndCollectionId(@Param("fileName") String fileName,@Param("collectionId") Long collectionId);

    int deleteByFileName(@Param("fileName") String fileName,@Param("collectionId") Long collectionId);

    List<QcKnowledgeFile> findAllFiles();

    QcKnowledgeFile queryByNameAndCollectionId(String fileName, Long collectionId);

    void updateViewCount(@Param("id") Long id, @Param("count") int count);

    int updateFile(QcKnowledgeFileParams qcKnowledgeFileParams);
}
