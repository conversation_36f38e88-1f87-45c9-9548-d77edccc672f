package com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.processor.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.qc.agent.app.agent.mapper.QcAiAgentConversationMapper;
import com.qc.agent.app.agent.model.po.QcAiAgent;
import com.qc.agent.app.agent.model.po.QcAiAgentConversation;
import com.qc.agent.app.agent.support.AgentBizDataSupport;
import com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.model.enums.GoodsIntent;
import com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.processor.IntentProcessor;
import com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.service.tool.ComparisonGenerator;
import com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.service.tool.ProductTool;
import com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.service.tool.PromotionPolicyTool;
import com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.service.tool.RecommendQuestionTool;
import com.qc.agent.app.workflow.inner.impl.tools.LLMTools;
import com.qc.agent.common.core.Message;
import com.qc.agent.lla.model.LLAConfig;
import com.qc.agent.lla.model.LLARequest;
import com.qc.agent.platform.register.UserManager;
import com.qc.agent.platform.sse.SseClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

@Slf4j
@Component
@RequiredArgsConstructor
public class SalesIntoProcessor implements IntentProcessor {

    private final ComparisonGenerator comparisonGenerator;
    private final ProductTool productTool;
    private final RecommendQuestionTool recommendQuestionTool;
    private final PromotionPolicyTool promotionPolicyTool;
    @Resource
    private QcAiAgentConversationMapper qcAiAgentConversationMapper;
    private final LLMTools llMTools;
    @Value("${ai.agent.appsvr-domin-url}")
    private String appsvrDomainUrl;

    @Override
    public GoodsIntent getSupportedIntent() {
        return GoodsIntent.SALES_INTO;
    }


    private static String getLogPrefix(LLARequest request, QcAiAgent agent) {
        return String.format("[tenantId=%s, agentId=%s, requestId=%s]",
                UserManager.getTenantUser().getTenantId(), agent.getId(), request.getId());
    }

    @Override
    public Message generateRecommend(LLARequest request, LLAConfig config, QcAiAgent agent, JSONObject entities, QcAiAgentConversation conversation) {
        final var logPrefix = getLogPrefix(request, agent);
        log.info("{} Processing intent, generate recommend: {}", logPrefix, getSupportedIntent());

        // 2. 使用 Optional 的链式调用，替代 if-else 结构
        return productTool.extractProductInfo(entities, logPrefix)
                .map(productInfo -> {
                    // 3. 将推荐列表的构建逻辑放在 map 操作中（成功提取到 productInfo 的情况）
                    var recommendList = comparisonGenerator.generateRecommendQuestions(request, config, productInfo, logPrefix, 3);
                    recommendQuestionTool.insertRecommendQuestions(conversation, recommendList, logPrefix);
                    return Message.of().data(recommendList).ok();
                })
                // 6. 如果 Optional 为空，执行 orElseGet 中的逻辑
                .orElseGet(() -> {
                    log.warn("{} Product information is missing in entities. Cannot generate recommendations.", logPrefix);
                    return Message.of().ok();
                });
    }

    @Override
    public Message process(LLARequest request, LLAConfig config, SseClient client, QcAiAgent agentModel, JSONObject entities) {
        String logPrefix = String.format("[tenantId=%s, agentId=%s,requestId=%s]",
                UserManager.getTenantUser().getTenantId(), agentModel.getId(), request.getId());
        log.info("{} 开始处理SALES_INTO意图", logPrefix);
        QcAiAgentConversation conversation = qcAiAgentConversationMapper.selectById(Long.parseLong(request.getId()));
        String question = request.getContent();
        if (Objects.equals(question, "确定")) {
            question = "基于商品：" + entities.get("productName") + " 品牌：" + entities.get("brand") + " 渠道：" + entities.get("salesChannel") + " 连锁门店推荐商品卖进话术";
        }
        qcAiAgentConversationMapper.updateQuestion(request.getId(), question);
        try {
            String cmId = entities.getString("cmId");
            String productId = entities.getString("productId");
            String productName = entities.getString("productName");
            String brandName = entities.getString("brand");
            String salesChannel = entities.getString("salesChannel");
            String activity = "";

            //获取促销政策
            if (StringUtils.isNotEmpty(cmId)) {
                activity = promotionPolicyTool.getPromotionPolicy(productId, cmId);
            }
            if (StringUtils.isEmpty(salesChannel)) {
                salesChannel = queryStoreType(cmId);
            }

            comparisonGenerator.generateSalesIntoPitchWithPromotion(productName, brandName, salesChannel, activity, request, config, client, logPrefix);
        } catch (Exception e) {
            log.error("{} 处理SALES_INTO意图时发生异常: {}", logPrefix, e.getMessage(), e);
            sendMessageToClient(client, request, "服务器忙，请稍后再试", logPrefix);
            llMTools.updateConversationDuration(conversation, "服务器忙，请稍后再试", logPrefix);
        }
        return Message.of().ok();
    }

    private void sendMessageToClient(SseClient client, LLARequest request, String content, String logPrefix) {
        log.info("{} Sending direct message to client: '{}'", logPrefix, content);
        // Assuming LLMTools is a utility class for this purpose. If not, the logic can be inlined.
        com.qc.agent.app.workflow.inner.impl.tools.LLMTools.directReturnMessage(client, request, logPrefix, content);
    }

    private String queryStoreType(String cmId) {
        String url = String.format("%s/app/cm/apaas/customer/detail.do?cmId=%s", appsvrDomainUrl, cmId);
        try {
            String response = AgentBizDataSupport.getWithAuthHeaders(url);
            JSONObject data = JSONObject.parseObject(response).getJSONObject("data");
            if (data != null) {
                JSONObject customer = data.getJSONObject("basCmCustomer");
                return (customer != null) ? customer.getString("typeName") : null;
            }
        } catch (Exception e) {
            log.error("查询门店信息异常", e);
        }
        return null;
    }

}
