package com.qc.agent.app.agent.mapper;

import com.qc.agent.app.agent.model.dto.QcAiAgentExtConfig;
import com.qc.agent.app.agent.model.po.QcAiAgent;
import com.qc.agent.app.agent.model.po.QcAiAgentAuthorityDistributeDetail;
import com.qc.agent.app.agent.model.query.QcAiAgentBaseQuery;
import com.qc.agent.app.agent.model.query.QcAiAgentQuery;
import com.qc.agent.app.agent.model.vo.QcAiAgentDetailVO;
import com.qc.agent.app.agent.model.vo.QcAiAgentVO;
import com.qc.agent.app.agent.model.vo.RecentUseQcAiAgentVO;
import com.qc.agent.app.workflow.inner.pojo.QcAiAgentIntentRecognition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface QcAiAgentMapper {

    QcAiAgent selectById(@Param("id") Long id);

    void insert(QcAiAgent agent);

    void update(QcAiAgent qcAiAgent);

    List<QcAiAgentVO> queryCustomAgents(@Param("userId") Long userId, @Param("agentName") String agentName,
                                        @Param("publishFlag") String publishFlag, @Param("viewAll") Boolean viewAll);

    void publish(@Param("id") Long id, @Param("userId") Long userId, @Param("userName") String userName, @Param("showChatLogContentType") String showChatLogContentType);

    void deactivate(@Param("id") Long id, @Param("userId") Long userId, @Param("userName") String userName);

    QcAiAgentDetailVO queryAgentDetail(@Param("id") Long id);

    void enableAgent(@Param("id") Long id, @Param("userId") Long userId, @Param("userName") String userName);

    void disableAgent(@Param("id") Long id, @Param("userId") Long userId, @Param("userName") String userName);

    void deleteAgent(@Param("id") Long id, @Param("userId") Long userId, @Param("userName") String userName);

    void updateBaseInfo(QcAiAgent qcAiAgent);

    List<QcAiAgentVO> seekAgents(QcAiAgentQuery query);

    List<QcAiAgentVO> queryInternalAgents(QcAiAgentBaseQuery query);

    QcAiAgentVO queryInternalAgentById(@Param("id") Long id);

    List<RecentUseQcAiAgentVO> queryRecentAgents(QcAiAgentQuery query);

    List<QcAiAgentVO> queryAllAgents(QcAiAgentQuery query);

    List<QcAiAgentAuthorityDistributeDetail> selectAuthorityDistributeDetailList(@Param("id") Long id);

    /**
     * 查询agent关联的意图设置
     *
     * @param id
     * @return
     */
    List<QcAiAgentIntentRecognition> selectIntentList(Long id);

    void updateExtConfigValue(@Param("agentId") Long agentId, @Param("configKey") String configKey, @Param("configValue") String configValue, @Param("now") LocalDateTime now);

    List<QcAiAgentExtConfig> selectExtConfigByAgentId(@Param("id") Long id);

    String selectSalesIntoRecommendInterface();
}
