package com.qc.agent.app.agent.mapper;

import com.qc.agent.app.outside_interface.pojo.QcAiAgentConversationDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-11
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
public interface StatisticsQcAiAgentAdminMapper {

    void batchInsert(@Param("list")List<QcAiAgentConversationDO> list);

    /**
     * 通过当前时间删除数据
     */
    void deleteByCurrentDate();
}
