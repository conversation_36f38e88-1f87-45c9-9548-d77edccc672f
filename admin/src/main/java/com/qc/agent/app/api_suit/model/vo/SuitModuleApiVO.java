package com.qc.agent.app.api_suit.model.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class SuitModuleApiVO {

    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long apiId;
    private String apiName;
    private String apiDescribe;
    private BigDecimal apiSequ;
    private String apiUrl;
    private String apiDocumentUrl;
    private LocalDateTime apiCreateTime;

    private String apiCreatorName;

    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long apiCreatorId;

    private LocalDateTime apiModifyTime;

    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long apiModifyierId;

    private String apiModifyierName;

    private String requestMethod;
}
