package com.qc.agent.app.knowledge.service.impl;

import com.qc.agent.app.knowledge.mapper.KnowledgeFolderMapper;
import com.qc.agent.app.knowledge.model.QcKnowledgeFolder;
import com.qc.agent.app.knowledge.model.QcKnowledgeFolderParams;
import com.qc.agent.app.knowledge.service.KnowledgeFolderService;
import com.qc.agent.common.util.UUIDUtils;
import com.qc.agent.platform.register.UserManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @className KnowledgeFolderServiceImpl
 * @description TODO
 * @date 2024/1/22 17:35
 */
@Component
public class KnowledgeFolderServiceImpl implements KnowledgeFolderService {

    @Autowired
    private KnowledgeFolderMapper knowledgeFolderMapper;

    @Override
    public List<QcKnowledgeFolder> selectDeepListByParentId(Long id) {
        return knowledgeFolderMapper.selectDeepListByParentId(id);
    }

    @Override
    public List<QcKnowledgeFolder> selectListByParentId(Long id) {
        return knowledgeFolderMapper.selectListByParentId(id);
    }

    @Override
    public int insertQcKnowledgeFolder(QcKnowledgeFolderParams qcKnowledgeFolderParams) {
        Long id = UUIDUtils.getUUID2Long();
        qcKnowledgeFolderParams.setId(id);
        qcKnowledgeFolderParams.setCreateTime(new Date());
        qcKnowledgeFolderParams.setModifyTime(new Date());
        qcKnowledgeFolderParams.setCreateUserId(UserManager.getTenantUser().getUserId());
        qcKnowledgeFolderParams.setModifyUserId(UserManager.getTenantUser().getUserId());
        return knowledgeFolderMapper.insertQcKnowledgeFolder(qcKnowledgeFolderParams);
    }

    @Override
    public int updateQcKnowledgeFolder(QcKnowledgeFolderParams qcKnowledgeFolderParams) {
        qcKnowledgeFolderParams.setModifyTime(new Date());
        qcKnowledgeFolderParams.setModifyUserId(UserManager.getTenantUser().getUserId());
        return knowledgeFolderMapper.updateQcKnowledgeFolder(qcKnowledgeFolderParams);
    }

    @Override
    public int deleteQcKnowledgeFolderById(Long id) {
        return 0;
    }

    @Override
    public int deleteQcKnowledgeFolderByIds(String[] ids) {
        return knowledgeFolderMapper.deleteQcKnowledgeFolderByIds(ids);
    }
}
