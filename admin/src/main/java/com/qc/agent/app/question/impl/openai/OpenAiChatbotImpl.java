package com.qc.agent.app.question.impl.openai;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.net.HttpHeaders;
import com.qc.agent.app.question.impl.AbstrctAnswerAIRequest;
import com.qc.agent.common.util.UUIDUtils;
import com.qc.agent.platform.sse.AnswerAISubscriber;
import com.qc.agent.platform.sse.SseMessageConverter;
import com.qc.agent.platform.sse.SseRequest;
import com.qc.agent.platform.sse.dto.AIVendors;
import com.qc.agent.platform.sse.dto.AnswerAIMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.util.List;

/**
 * @description：TODO
 * @author： sc
 * @create： 2024/6/13 19:42
 */
@Slf4j
@Component
public class OpenAiChatbotImpl extends AbstrctAnswerAIRequest {

    @Value("${ai.agent.openai-apikey}")
    String openapiKey;

    static final String PROVIDER_DOMAIN = "https://api.chatanywhere.tech/v1";

    @Override
    protected Object buildAnswer(String defaultAnswer) {
        String content;
        if(StringUtils.isNotEmpty(defaultAnswer)){
            JSONObject data = new JSONObject();
            JSONArray choices = new JSONArray();
            JSONObject choice = new JSONObject();
            JSONObject delta = new JSONObject();
            delta.put("content",defaultAnswer);
            choice.put("delta",delta);
            choices.add(choice);
            data.put("id", UUIDUtils.getUUID2Long());
            data.put("choices",choices);
            content = data.toJSONString();
        }else{
            content = "[DONE]";
        }
        return String.format("data: %s", content);
    }

    @Override
    protected void doAnswer(SseRequest request, AnswerAISubscriber subscriber) {
        log.info("做openai问答,openai api key:{}", openapiKey);

        JSONObject requestBody = buildRequestBody(request);

        log.info("调用问答接口，request：{}", requestBody);

        HttpRequest httpRequest = HttpRequest.newBuilder()
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + openapiKey)
                .header("Content-Type", "application/json")
                .uri(URI.create(URI.create(PROVIDER_DOMAIN) + "/chat/completions"))
                .POST(HttpRequest.BodyPublishers.ofString(requestBody.toJSONString()))
                .build();
        try
        {
            request.setStartTime(System.currentTimeMillis());
            HttpClient.newBuilder().build().sendAsync(httpRequest, HttpResponse.BodyHandlers.fromLineSubscriber(subscriber)).get();
        }
        catch(Exception e)
        {
            throw new RuntimeException(e);
        }
    }

    @Override
    public AIVendors determineAIVendors() {
        return AIVendors.OPENAI;
    }

    @Override
    public SseMessageConverter determineMessageConverter() {
        return new OpenAiMessageConverter();
    }

    private JSONObject buildRequestBody(SseRequest request) {
        JSONObject param = new JSONObject();
        List<AnswerAIMessage> list = buildMessage(request);
        AnswerAIMessage answerAIMessage = list.get(list.size() - 1);
        String content = answerAIMessage.getContent();
        if(StringUtils.isNotEmpty(content) && content.length() > 2048){
            answerAIMessage.setContent(content.substring(content.length() - 2048));
        }
        param.put("messages", list);
        param.put("stream",true);
        param.put("model","gpt-3.5-turbo");
        return param;
    }
}
