package com.qc.agent.app.agent.controller;

import com.qc.agent.app.agent.model.po.CredentialRequest;
import com.qc.agent.app.agent.model.po.QcAiAgentModel;
import com.qc.agent.app.agent.service.QcAiAgentModelService;
import com.qc.agent.common.core.Message;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@Slf4j
@RequestMapping("/ai-agent/agent-model")
public class QcAiAgentModelController {

    @Resource
    private QcAiAgentModelService qcAiAgentModelService;

    @PostMapping("/add.do")
    public int add(@RequestBody QcAiAgentModel model) {
        return qcAiAgentModelService.add(model);
    }

    @PostMapping("/privacyModelRemove.do")
    public Message privacyModelRemove(@RequestBody QcAiAgentModel model) {
        try {
            return qcAiAgentModelService.privacyModelRemove(model);
        } catch (Exception e) {
            log.error("模型移除失败", e);
            return Message.of().error("模型移除失败");
        }
    }

    @PostMapping("/remove.do")
    public Message remove(@RequestBody QcAiAgentModel model) {
        try {
            return qcAiAgentModelService.remove(model);
        } catch (Exception e) {
            log.error("模型移除失败", e);
            return Message.of().error("模型移除失败");
        }
    }

    @DeleteMapping("/delete/{id}")
    public int delete(@PathVariable Long id) {
        return qcAiAgentModelService.delete(id);
    }

    @PostMapping("/update.do")
    public Message update(@RequestBody QcAiAgentModel model) {
        try {
            return qcAiAgentModelService.update(model);
        } catch (Exception e) {
            log.error("更新失败", e);
            return Message.of().error("更新失败");
        }
    }

    @PostMapping("/updateCredentials.do")
    public Message updateModelCredentials(@RequestBody CredentialRequest request){
        return qcAiAgentModelService.updateModelCredentials(request);
    }


    @PostMapping("/addPrivacyDeployment.do")
    public Message addPrivacyDeployment(@RequestBody QcAiAgentModel model){
        return qcAiAgentModelService.addPrivacyDeployment(model);
    }

    @PostMapping("/list.do")
    public Message list() {
        try {
            return Message.of().data(qcAiAgentModelService.list()).ok();
        } catch (Exception e) {
            log.error("查询失败", e);
            return Message.of().error("查询失败");
        }
    }

    @PostMapping("/selectEnabledLLM.do")
    public Message selectEnabledLLM() {
        try {
            return Message.of().data(qcAiAgentModelService.selectEnabledLLM()).ok();
        } catch (Exception e) {
            log.error("查询失败", e);
            return Message.of().error("下拉查询失败");
        }
    }
}
