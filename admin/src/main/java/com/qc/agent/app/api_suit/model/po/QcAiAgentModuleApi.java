package com.qc.agent.app.api_suit.model.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QcAiAgentModuleApi {

    private Long id;
    private String status;
    private Long creatorId;
    private String creatorName;
    private LocalDateTime createTime;
    private Long modifyierId;
    private String modifyierName;
    private LocalDateTime modifyTime;

    private Long moduleId;
    private String name;
    private String describe;
    private BigDecimal sequ;

    private String url;

    private String documentUrl;

    private String requestMethod;

    private String headers;

    private String body;

    private String queryParam;

    private String response;

}
