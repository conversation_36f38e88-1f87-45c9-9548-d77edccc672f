package com.qc.agent.app.agent.model.query;

import com.qc.agent.app.agent.model.dto.QcAiAgentCommonParam;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025-06-26
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Data
public class QcAiGoodsAssistantQuery {

    /**
     *
     */
    private Long agentId;

    /**
     * 会话id
     */
    private Long sessionId;
    /**
     * 问题
     */
    private String question;

    /**
     * 是否新会话，1：是
     */
    private String isNew;

    private String cmId;

    /**
     * 前端传入的唯一id
     */
    private String chatSessionId;


    /**
     *  来源：web悬浮窗：web-levitate ； web端：web ； app端：app
     */
    private String source;

    /**
     * 上次的会话id
     */
    private Long conversationId;

    /**
     * 测试对话和正式对话都有的公共参数
     */
    private QcAiAgentCommonParam commonParam;

}
