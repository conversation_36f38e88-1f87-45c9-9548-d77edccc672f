package com.qc.agent.app.api_suit.model.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class SuitModuleVO {
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;

    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long suitId;
    private String name;
    private String describe;
    private BigDecimal sequ;
    private LocalDateTime createTime;

    private String creatorName;

    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long creatorId;

    private LocalDateTime modifyTime;

    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long modifyierId;

    private String modifyierName;

    private List<SuitModuleApiVO> apiList;
}
