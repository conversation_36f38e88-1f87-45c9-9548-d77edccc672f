package com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.processor.impl;

import com.alibaba.fastjson.JSONObject;
import com.qc.agent.app.agent.mapper.QcAiAgentConversationMapper;
import com.qc.agent.app.agent.model.po.QcAiAgent;
import com.qc.agent.app.agent.model.po.QcAiAgentConversation;
import com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.model.enums.GoodsIntent;
import com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.processor.IntentProcessor;
import com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.service.tool.*;
import com.qc.agent.app.workflow.inner.impl.tools.LLMTools;
import com.qc.agent.common.core.Message;
import com.qc.agent.lla.model.LLAConfig;
import com.qc.agent.lla.model.LLARequest;
import com.qc.agent.platform.sse.SseClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * Processor for handling the CUSTOMER_SCHEME intent.
 * This processor retrieves the specific price and scheme for a given product and customer.
 *
 * <AUTHOR>
 * @date 2025-06-26
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CustomerSchemeProcessor implements IntentProcessor {

    private final LLMTools llMTools;
    private final ProductTool productTool;
    private final CustomerTool customerTool;
    private final RecommendQuestionTool recommendQuestionTool;
    private final ComparisonGenerator comparisonGenerator;
    private final QcAiAgentConversationMapper qcAiAgentConversationMapper;
    private final PromotionPolicyTool promotionPolicyTool;

    @Override
    public GoodsIntent getSupportedIntent() {
        return GoodsIntent.CUSTOMER_SCHEME;
    }

    /**
     * Processes the request to find a customer-specific product scheme.
     * It validates input, finds the customer and product, and returns the pricing details.
     */
    @Override
    public Message process(LLARequest request, LLAConfig config, SseClient client, QcAiAgent agent, JSONObject entities) {
        final var logPrefix = LLMTools.getLogPrefix(request, agent);
        log.info("{} Processing intent: {}. Entities: {}", logPrefix, getSupportedIntent(), entities.toJSONString());

        // --- 3. Fetch conversation history ---
        final var conversation = qcAiAgentConversationMapper.selectById(Long.parseLong(request.getId()));

        final var productName = entities.getString("productName");
        final var customerName = entities.getString("customer");

        // --- 1. Validate required entities ---
        if (productName == null || customerName == null) {
            log.warn("{} Required entities 'productName' or 'customer' are missing.", logPrefix);
            final var errorMessage = "请同时提供产品名称和客户信息才能查询价格方案。";
            LLMTools.directReturnMessage(client, request, logPrefix, errorMessage);
            llMTools.updateConversationDuration(conversation, errorMessage, logPrefix);
            return Message.of().error(errorMessage); // Return a failure message
        }
        final String question = Objects.equals(request.getContent(), "确定") ?
                "基于客户：" + customerName + " 商品：" + productName + " 查询售卖价格策略" :
                request.getContent();

        // --- 2. Find customer ID if not provided ---
        // This block has a side-effect: it modifies the 'entities' JSONObject.
        if (entities.getString("cmId") == null) {
            log.info("{} 'cmId' not present, attempting to find a similar customer for '{}'.", logPrefix, customerName);
            customerTool.findMostSimilarCustomer(logPrefix, customerName)
                    .ifPresent(customerJson -> {
                        final var foundCmId = customerJson.getString("id");
                        log.info("{} Found similar customer with cmId: {}", logPrefix, foundCmId);
                        entities.put("cmId", foundCmId);
                    });
        }
        final var cmId = entities.getString("cmId");

        // --- 4. Find product and handle result ---
        final var productOptional = productTool.findProduct(productName, cmId, "1", logPrefix);

        productOptional.ifPresentOrElse(
                // Success path: Product found for the customer
                productJson -> {
                    log.info("{} [Handler:CustomerScheme] Product found: {}", logPrefix, productJson.toJSONString());
                    StringBuilder messageBuilder = new StringBuilder();
                    messageBuilder.append(String.format("%s在%s的售价为%s元",
                            productJson.getString("name"),
                            customerName,
                            productJson.getString("reportUnitPrice")));

                    if (productJson.getString("priceName") != null) {
                        messageBuilder.append(String.format("，正在执行【%s】价格方案，有效期：%s~%s",
                                productJson.getString("priceName"),
                                productJson.getString("priceStartDate"),
                                productJson.getString("priceEndDate")));
                    } else {
                        messageBuilder.append("，该门店本商品暂时没有价格方案。");
                    }

                    messageBuilder.append("\n\n");
                    String promotionPolicy = promotionPolicyTool.getPromotionPolicy(productJson.getString("id"), cmId);
                    if (promotionPolicy != null && !promotionPolicy.isEmpty()) {
                        messageBuilder.append("以下为当前随单促销活动内容：\n");
                        messageBuilder.append(promotionPolicy);
                    } else {
                        messageBuilder.append("暂无促销活动。");
                    }
                    String message = messageBuilder.toString();

                    LLMTools.directReturnMessage(client, request, logPrefix, message);
                    llMTools.updateConversationDuration(conversation, question, message, logPrefix);
                },
                // Failure path: Product not found
                () -> {
                    log.warn("{} Product '{}' price scheme not found for customer '{}' (cmId: {}).", logPrefix, productName, customerName, cmId);
                    final var failureMessage = "未能查询到该商品的价格信息，请检查产品和客户名称是否正确。";
                    LLMTools.directReturnMessage(client, request, logPrefix, failureMessage);
                    llMTools.updateConversationDuration(conversation, question, failureMessage, logPrefix);
                }
        );

        return Message.of().ok();
    }

    /**
     * Generates recommended follow-up questions based on the processed intent.
     * If product information was successfully extracted, it generates comparison questions.
     * Otherwise, it provides generic suggested questions.
     */
    @Override
    public Message generateRecommend(LLARequest request, LLAConfig config, QcAiAgent agent, JSONObject entities, QcAiAgentConversation conversation) {
        final var logPrefix = LLMTools.getLogPrefix(request, agent);
        log.info("{} Generating recommendations for intent: {}.", logPrefix, getSupportedIntent());

        return productTool.extractProductInfo(entities, logPrefix)
                .map(productInfo -> {
                    // Product info was successfully extracted, generate specific recommendations.
                    log.info("{} Product info extracted: {}. Generating comparison questions.", logPrefix, productInfo);
                    var recommendList = comparisonGenerator.generateRecommendQuestions(request, config, productInfo, logPrefix, 3);
                    log.info("{} Generated recommendations: {}", logPrefix, recommendList);
                    recommendQuestionTool.insertRecommendQuestions(conversation, recommendList, logPrefix);
                    return Message.of().data(recommendList).ok();
                })
                .orElseGet(() -> {
                    // Product info could not be extracted from entities.
                    log.warn("{} Product information is missing in entities. Cannot generate specific recommendations.", logPrefix);
                    // Returning an empty success message as per original logic.
                    // Could also fallback to generic questions here if desired.
                    return Message.of().ok();
                });
    }
}