package com.qc.agent.app.agent.mapper;

import com.qc.agent.app.agent.model.po.QcAiAgentAuthorityDistributeDetail;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-24
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
public interface QcAiAgentAuthorityDistributeDetailMapper {
    /**
     * 批量插入
     *
     * @param qcAiAgentAuthorityDistributeDetails
     */
    void batchInsert(@Param("list") List<QcAiAgentAuthorityDistributeDetail> qcAiAgentAuthorityDistributeDetails);

    /**
     * @param id
     */
    void deleteByAgentId(@Param("id") Long id);

    Long isUserAuthorizedForAgent(@Param("userId")Long userId, @Param("agentId") long agentId,@Param("deptIdList") List<Long> deptIds);
}
