package com.qc.agent.app.knowledge.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.qc.agent.app.agent.model.po.QcAiAgent;
import com.qc.agent.app.agent.model.po.QcAiAgentConversationQuote;
import com.qc.agent.app.knowledge.mapper.KnowledgeInfoMapper;
import com.qc.agent.app.knowledge.mapper.QcKnowledgeInfoRangeMapper;
import com.qc.agent.app.knowledge.model.*;
import com.qc.agent.app.knowledge.model.query.KnowledgeQuery;
import com.qc.agent.app.knowledge.service.KnowledgeInfoService;
import com.qc.agent.app.workflow.inner.mapper.QcAiAgentConversationQuoteMapper;
import com.qc.agent.common.core.Message;
import com.qc.agent.common.util.UUIDUtils;
import com.qc.agent.platform.config.FileProperty;
import com.qc.agent.platform.register.UserManager;
import com.qc.agent.vectordb.pojo.VectorDbResponse;
import com.qc.agent.vectordb.tencentdb.tencentDbClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @className KnowledgeInfoServiceImpl
 * @description TODO
 * @date 2024/1/22 14:11
 */
@Component
@Slf4j
public class KnowledgeInfoServiceImpl implements KnowledgeInfoService {

    @Autowired
    private KnowledgeInfoMapper knowledgeInfoMapper;

    @Resource
    private QcKnowledgeInfoRangeMapper qcKnowledgeInfoRangeMapper;

    @Resource
    private QcAiAgentConversationQuoteMapper qcAiAgentConversationQuoteMapper;

    @Autowired
    private FileProperty fileProperty;

    @Resource
    private tencentDbClient dbClient;

    @Override
    public QcKnowledgeInfo selectQcKnowledgeInfoById(Long id) {
        QcKnowledgeInfo qcKnowledgeInfo = knowledgeInfoMapper.selectQcKnowledgeInfoById(id);
        //处理图片
        if (StringUtils.isNotEmpty(qcKnowledgeInfo.getKnowledgeFace())) {
            qcKnowledgeInfo.setKnowledgeFaceFullPath(fileProperty.getUrl() + "/" + qcKnowledgeInfo.getKnowledgeFace());
        }
        qcKnowledgeInfo.setDept(qcKnowledgeInfoRangeMapper.getByKnowledgeId(id, "1"));
        qcKnowledgeInfo.setUser(qcKnowledgeInfoRangeMapper.getByKnowledgeId(id, "2"));

        return qcKnowledgeInfo;
    }

    @Override
    public List<QcKnowledgeInfoExt> selectQcKnowledgeInfoList(QcKnowledgeInfoParams qcKnowledgeInfoParams) {
        qcKnowledgeInfoParams.setCurrentUserId(UserManager.getTenantUser().getUserId());

        List<QcKnowledgeInfoExt> qcKnowledgeInfoList = Lists.newArrayList();
        if (StringUtils.isNotEmpty(qcKnowledgeInfoParams.getSearchFlag())) {
            switch (qcKnowledgeInfoParams.getSearchFlag()) {
                case QcKnowledgeInfoParams.SEARCH_MINE: {
                    qcKnowledgeInfoList = knowledgeInfoMapper.selectMyQcKnowledgeInfoList(qcKnowledgeInfoParams);
                    break;
                }
                case QcKnowledgeInfoParams.SEARCH_PUBLIC: {
                    qcKnowledgeInfoList = knowledgeInfoMapper.selectPublicQcKnowledgeInfoList(qcKnowledgeInfoParams);
                    break;
                }
                case QcKnowledgeInfoParams.SEARCH_MANAGE: {
                    qcKnowledgeInfoList = knowledgeInfoMapper.selectManageQcKnowledgeInfoList(qcKnowledgeInfoParams);
                    break;
                }
                case QcKnowledgeInfoParams.SEARCH_AGENT_SAVE: {
                    qcKnowledgeInfoList = knowledgeInfoMapper.selectAgentQcKnowledgeInfoList(qcKnowledgeInfoParams);
                    break;
                }
                default:
                    break;
            }
        }

        if (CollectionUtils.isNotEmpty(qcKnowledgeInfoList)) {
            //处理图片
            qcKnowledgeInfoList.forEach(qcKnowledgeInfo -> {
                if (StringUtils.isNotEmpty(qcKnowledgeInfo.getKnowledgeFace())) {
                    qcKnowledgeInfo.setKnowledgeFaceFullPath(fileProperty.getUrl() + "/" + qcKnowledgeInfo.getKnowledgeFace());
                }
            });
        }
        return qcKnowledgeInfoList;
    }

    @Override
    public Long insertQcKnowledgeInfo(QcKnowledgeInfoParams qcKnowledgeInfoParams) {
        Long id = UUIDUtils.getUUID2Long();
        qcKnowledgeInfoParams.setId(id);
        qcKnowledgeInfoParams.setCreateTime(new Date());
        qcKnowledgeInfoParams.setModifyTime(new Date());
        qcKnowledgeInfoParams.setCreatorId(UserManager.getTenantUser().getUserId());
        qcKnowledgeInfoParams.setCreatorName(qcKnowledgeInfoParams.getUserName());
        qcKnowledgeInfoParams.setModifyierId(UserManager.getTenantUser().getUserId());
        qcKnowledgeInfoParams.setModifyierName(qcKnowledgeInfoParams.getModifyierName());
        qcKnowledgeInfoParams.setEnable(QcKnowledgeInfoParams.DISABLE_STATUS);
        qcKnowledgeInfoParams.setPublishFlag("0");

        knowledgeInfoMapper.insertQcKnowledgeInfo(qcKnowledgeInfoParams);

        dbClient.initClient();
        // 创建知识库
        String databaseName = "db-" + UserManager.getTenantUser().getTenantId(); //企业数据库
        dbClient.createDatabase(databaseName);
        String collectionName = "collectionVie-" + id; //知识库
        dbClient.createCollection(databaseName, collectionName);

        // 创建qa知识库
        String qaDataBaseName = "QA-database-" + UserManager.getTenantUser().getTenantId();
        dbClient.createQuestionDatabase(qaDataBaseName);
        String qaCollectionName = "QA-collection-" + id;
        dbClient.createQACollection(qaDataBaseName, qaCollectionName);

        return id;
    }

    @Override
    public int updateQcKnowledgeInfo(QcKnowledgeInfoParams qcKnowledgeInfoParams) {
        qcKnowledgeInfoParams.setModifyTime(new Date());
        qcKnowledgeInfoParams.setModifyierId(UserManager.getTenantUser().getUserId());
        qcKnowledgeInfoParams.setModifyierName(qcKnowledgeInfoParams.getUserName());
        return knowledgeInfoMapper.updateQcKnowledgeInfo(qcKnowledgeInfoParams);
    }

    private void saveKnowledgeRange(Long knowledgeId, List<KnowledgeRange> dept, List<KnowledgeRange> user, Long userId, String userName) {
        qcKnowledgeInfoRangeMapper.deleteByKnowledgeId(knowledgeId);
        List<QcKnowledgeInfoRange> deptRange = Optional.ofNullable(dept)
                .orElse(Lists.newArrayList())
                .stream()
                .map(item -> initQcKnowledgeInfoRange(item, "1", userId, userName, knowledgeId))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deptRange)) {
            qcKnowledgeInfoRangeMapper.insertBatch(deptRange);
        }

        List<QcKnowledgeInfoRange> userRange = Optional.ofNullable(user)
                .orElse(Lists.newArrayList())
                .stream()
                .map(item -> initQcKnowledgeInfoRange(item, "2", userId, userName, knowledgeId))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(userRange)) {
            qcKnowledgeInfoRangeMapper.insertBatch(userRange);
        }
    }

    private QcKnowledgeInfoRange initQcKnowledgeInfoRange(KnowledgeRange range, String rangeType, Long userId, String userName, Long knowledgeId) {
        return QcKnowledgeInfoRange.builder()
                .id(UUIDUtils.getUUID2Long())
                .status("1")
                .createTime(LocalDateTime.now())
                .creatorId(userId)
                .creatorName(userName)
                .rangeType(rangeType)
                .rangeId(range.getId())
                .rangeName(range.getName())
                .knowledgeId(knowledgeId)
                .build();
    }

    @Override
    public int deleteQcKnowledgeInfoByIds(String ids) {
        return knowledgeInfoMapper.deleteQcKnowledgeInfoByIds(ids);
    }

    @Override
    public Message deleteQcKnowledgeInfoById(Long id) {
        //在删除知识库之前，先查询有没有关联agent，如果关联agent则不允许删除
        List<QcAiAgent> agents = knowledgeInfoMapper.selectAgentsByKnowledgeId(id);
        if (CollectionUtils.isNotEmpty(agents)) {
            String agentNames = agents.stream()
                    .map(QcAiAgent::getName)
                    .collect(Collectors.joining(","));
            return Message.of().error("知识库已被以下智能体引用，无法删除：" + agentNames);
        }
        int i = knowledgeInfoMapper.deleteQcKnowledgeInfoById(id);
        dbClient.initClient();
        String databaseName = "db-" + UserManager.getTenantUser().getTenantId(); //企业数据库
        String collectionName = "collectionVie-" + id; //知识库
        log.info("dbClient.dropCollection, databaseName:{}, collectionName:{}", databaseName, collectionName);
        VectorDbResponse response = dbClient.dropCollection(databaseName, collectionName);
        log.info("dbClient.dropCollection response:{}", response);
        return Message.of().ok();
    }

    @Override
    public Map<String, Object> queryQcKnowledgeSummary(QcKnowledgeInfoParams qcKnowledgeInfoParams) {
        Map<String, Object> data = Maps.newHashMap();

        Long questionTotal = knowledgeInfoMapper.summaryQcKnowledgeQuestion(qcKnowledgeInfoParams);

        qcKnowledgeInfoParams.setDataImpType("QA");
        Long qaFileTotal = knowledgeInfoMapper.summaryQcKnowledgeFile(qcKnowledgeInfoParams);

        qcKnowledgeInfoParams.setDataImpType("RAW");
        Long rawFileTotal = knowledgeInfoMapper.summaryQcKnowledgeFile(qcKnowledgeInfoParams);

        data.put("questionTotal", questionTotal);

        data.put("qaFileTotal", qaFileTotal);
        data.put("rawFileTotal", rawFileTotal);

        return data;
    }

    @Override
    public void enable(Long id, Long userId, String userName) {
        knowledgeInfoMapper.enableKnowledgeInfo(id, userId, userName);
    }

    @Override
    public void disable(Long id, Long userId, String userName) {
        knowledgeInfoMapper.disableKnowledgeInfo(id, userId, userName);
    }

    @Override
    public void publish(KnowledgePublishParam knowledgePublishParam) {
        knowledgeInfoMapper.publishKnowledgeInfo(knowledgePublishParam.getId(), knowledgePublishParam.getUserId(), knowledgePublishParam.getUserName());
        saveKnowledgeRange(knowledgePublishParam.getId(), knowledgePublishParam.getDept(), knowledgePublishParam.getUser(), knowledgePublishParam.getUserId(), knowledgePublishParam.getUserName());
    }

    @Override
    public void deactivate(Long id, Long userId, String userName) {
        knowledgeInfoMapper.deactivateKnowledgeInfo(id, userId, userName);
    }

    @Override
    public Map<String, Object> quoteList(KnowledgeQuery query) {
        List<QcAiAgentConversationQuote> list = qcAiAgentConversationQuoteMapper.selectListByConverstaionId(query);
        return Map.of("rows", list);
    }

    @Override
    public List<QcKnowledgeFileCategory> queryCategorySetting(String collectionId) {
        return knowledgeInfoMapper.queryCategorySetting(collectionId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void modifyCategorySetting(QcKnowledgeCategoryWrapperParam qcKnowledgeCategoryWrapperParam) {
        if (CollectionUtils.isEmpty(qcKnowledgeCategoryWrapperParam.getCategories())) {
            return;
        }

        for (QcKnowledgeCategoryActionParam item : qcKnowledgeCategoryWrapperParam.getCategories()) {
            switch (item.getAction()) {
                case "add":
                    knowledgeInfoMapper.insertQcKnowledgeCategory(item.getCategoryName(), qcKnowledgeCategoryWrapperParam.getCollectionId(), UUIDUtils.getUUID2Long());
                    break;

                case "update":
                    if (item.getId() != null && StringUtils.isNotBlank(item.getCategoryName())) {
                        QcKnowledgeFileCategory update = new QcKnowledgeFileCategory();
                        update.setId(item.getId());
                        update.setCategoryName(item.getCategoryName());
                        update.setCollectionId(qcKnowledgeCategoryWrapperParam.getCollectionId());
                        knowledgeInfoMapper.updateQcKnowledgeCategory(update);
                    }
                    break;

                case "delete":
                    if (item.getId() != null) {
                        knowledgeInfoMapper.deleteQcKnowledgeCategoryById(item.getId(), qcKnowledgeCategoryWrapperParam.getCollectionId());
                        knowledgeInfoMapper.updateQcKnowledgeFileById(item.getId(), qcKnowledgeCategoryWrapperParam.getCollectionId());
                        deleteCategory2DB(qcKnowledgeCategoryWrapperParam, item);
                    }
                    break;

                default:
                    log.warn("未知分类操作类型：{}", item.getAction());
            }
        }
    }

    private void deleteCategory2DB(QcKnowledgeCategoryWrapperParam qcKnowledgeCategoryWrapperParam, QcKnowledgeCategoryActionParam item) {
        Map<String, Object> params = new HashMap<>();
        params.put("categoryId", "-1");
        dbClient.initClient();
        List<Long> idList = new ArrayList<>();
        idList.add(Long.valueOf(item.getId()));

        List<QcKnowledgeFile> knowledgeFiles = knowledgeInfoMapper.selectQcKnowledgeFileByCategoryIds(idList, qcKnowledgeCategoryWrapperParam.getCollectionId());
        knowledgeFiles.stream().filter(file -> "QA".equals(file.getDataImpType())).forEach(knowledgeFile -> {
                    dbClient.updateQAField(UserManager.getTenantUser().getTenantId(), Long.valueOf(qcKnowledgeCategoryWrapperParam.getCollectionId()), knowledgeFile.getFileName(), params);
        });
    }


    @Override
    public String checkCategorySetting(QcKnowledgeCategoryWrapperParam qcKnowledgeCategoryWrapperParam) {
        if (CollectionUtils.isEmpty(qcKnowledgeCategoryWrapperParam.getCategories())) {
            return "";
        }
        //判断item.getAction()为delete的数据是否在知识库文件中被使用
        List<Long> ids = qcKnowledgeCategoryWrapperParam.getCategories().stream()
                .filter(item -> "delete".equals(item.getAction()) && item.getId() != null)
                .map(item -> Long.valueOf(item.getId()))
                .collect(Collectors.toList());
        //id为要删除的id，如果id被使用了，则返回提示信息
        if (CollectionUtils.isNotEmpty(ids)) {
            List<QcKnowledgeFile> usedCategories = knowledgeInfoMapper.selectQcKnowledgeFileByCategoryIds(ids, qcKnowledgeCategoryWrapperParam.getCollectionId());
            if (CollectionUtils.isNotEmpty(usedCategories)) {

                String usedCategoryNames = usedCategories.stream()
                        .map(QcKnowledgeFile::getCategoryName)
                        .collect(Collectors.toSet())
                        .stream()
                        .collect(Collectors.joining(","));
                return "【" + usedCategoryNames + "】分类下存在文档，确定后将会清除相关文档的归属分类，确定删除吗？";
            }
        }
        return "";
    }
}
