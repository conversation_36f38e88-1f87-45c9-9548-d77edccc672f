package com.qc.agent.app.knowledge.mapper;

import com.qc.agent.app.agent.model.po.QcAiAgent;
import com.qc.agent.app.knowledge.model.*;
import com.qc.agent.app.workflow.inner.pojo.query.AuthorityKnowledgeQuery;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @className KnowledgeInfoMapper
 * @description TODO
 * @date 2024/1/22 14:14
 */
@Component
public interface KnowledgeInfoMapper {
    /**
     * 【知识库详情查询】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    QcKnowledgeInfo selectQcKnowledgeInfoById(Long id);

    /**
     * 【知识库列表查询】
     *
     * @param qcKnowledgeInfoParams 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    List<QcKnowledgeInfo> selectQcKnowledgeInfoList(QcKnowledgeInfoParams qcKnowledgeInfoParams);

    /**
     * 新增【请填写功能名称】
     *
     * @param qcKnowledgeInfoParams 【请填写功能名称】
     * @return 结果
     */
    int insertQcKnowledgeInfo(QcKnowledgeInfoParams qcKnowledgeInfoParams);

    /**
     * 修改【请填写功能名称】
     *
     * @param qcKnowledgeInfoParams 【请填写功能名称】
     * @return 结果
     */
    int updateQcKnowledgeInfo(QcKnowledgeInfoParams qcKnowledgeInfoParams);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键集合
     * @return 结果
     */
    int deleteQcKnowledgeInfoByIds(String ids);

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    int deleteQcKnowledgeInfoById(Long id);

    /**
     * 获取所有的知识库id
     *
     * @return
     */
    List<Long> queryAllCollectionIds();

    /**
     * 获取问答总数
     *
     * @param qcKnowledgeInfoParams
     * @return
     */
    Long summaryQcKnowledgeQuestion(QcKnowledgeInfoParams qcKnowledgeInfoParams);

    /**
     * 获取文件统计
     *
     * @param qcKnowledgeInfoParams
     * @return
     */
    Long summaryQcKnowledgeFile(QcKnowledgeInfoParams qcKnowledgeInfoParams);

    /**
     * 【知识库列表查询(带文件总数汇总)】
     *
     * @param qcKnowledgeInfoParams 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    List<QcKnowledgeInfoExt> selectQcKnowledgeInfoExtList(QcKnowledgeInfoParams qcKnowledgeInfoParams);

    List<QcKnowledgeInfoExt> selectMyQcKnowledgeInfoList(QcKnowledgeInfoParams qcKnowledgeInfoParams);

    List<QcKnowledgeInfoExt> selectPublicQcKnowledgeInfoList(QcKnowledgeInfoParams qcKnowledgeInfoParams);

    List<QcKnowledgeInfoExt> selectManageQcKnowledgeInfoList(QcKnowledgeInfoParams qcKnowledgeInfoParams);

    List<QcKnowledgeInfoExt> selectAgentQcKnowledgeInfoList(QcKnowledgeInfoParams qcKnowledgeInfoParams);

    /**
     * 查询agent使用的知识库
     *
     * @param agentId
     * @return
     */
    List<QcKnowledgeInfo> selectByAgentId(@Param("agentId") Long agentId);

    /**
     * 启用知识库
     *
     * @param id
     * @param userId
     * @param userName
     */
    void enableKnowledgeInfo(@Param("id") Long id, @Param("userId") Long userId, @Param("userName") String userName);

    /**
     * 停用知识库
     *
     * @param id
     * @param userId
     * @param userName
     */
    void disableKnowledgeInfo(@Param("id") Long id, @Param("userId") Long userId, @Param("userName") String userName);

    /**
     * 发布知识库
     *
     * @param id
     * @param userId
     * @param userName
     */
    void publishKnowledgeInfo(@Param("id") Long id, @Param("userId") Long userId, @Param("userName") String userName);

    /**
     * 取消知识库
     *
     * @param id
     * @param userId
     * @param userName
     */
    void deactivateKnowledgeInfo(@Param("id") Long id, @Param("userId") Long userId, @Param("userName") String userName);

    /**
     * 过滤知识库权限
     *
     * @param query
     * @return
     */
    List<Long> selectFilterAuthorityKnowledgeList(AuthorityKnowledgeQuery query);

    List<QcKnowledgeFileCategory> queryCategorySetting(@Param("collectionId") String collectionId);

    void deleteQcKnowledgeCategoryById(@Param("id")String id,@Param("collectionId") String collectionId);

    void updateQcKnowledgeCategory(QcKnowledgeFileCategory category);

    void insertQcKnowledgeCategory(@Param("name") String item, @Param("collectionId") String collectionId, @Param("id") Long id);

    QcKnowledgeFileCategory selectFileCategoryByName(@Param("categoryName")String categoryName, @Param("collectionId") String collectionId);

    Integer checkCategorySetting(@Param("name") String name);

    void updateQcKnowledgeFileById(@Param("id")String id,@Param("collectionId") String collectionId);

    List<QcKnowledgeFile> selectQcKnowledgeFileByCategoryIds(@Param("ids") List<Long> ids,@Param("collectionId") String collectionId);

    List<QcAiAgent> selectAgentsByKnowledgeId(@Param(("knowledgeId")) Long id);
}
