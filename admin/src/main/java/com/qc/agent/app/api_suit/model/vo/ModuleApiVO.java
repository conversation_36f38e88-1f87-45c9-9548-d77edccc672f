package com.qc.agent.app.api_suit.model.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class ModuleApiVO {

    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;

    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long moduleId;
    private String name;
    private String describe;
    private BigDecimal sequ;
    private String url;
    private String documentUrl;
    private LocalDateTime createTime;

    private String creatorName;

    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long creatorId;

    private LocalDateTime modifyTime;

    @J<PERSON>NField(serializeUsing = ToStringSerializer.class)
    private Long modifyierId;

    private String modifyierName;

    private String requestMethod;

    private String headers;

    private String body;

    private String queryParam;

    private String response;
}
