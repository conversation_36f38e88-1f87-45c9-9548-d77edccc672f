package com.qc.agent.app.agent.service;


import com.alibaba.fastjson.JSONObject;
import com.qc.agent.app.agent.model.po.CredentialRequest;
import com.qc.agent.app.agent.model.po.QcAiAgentModel;
import com.qc.agent.common.core.Message;

import java.util.List;

public interface QcAiAgentModelService {
    int add(QcAiAgentModel model);

    int delete(Long id);

    Message update(QcAiAgentModel model);

    QcAiAgentModel getById(Long id);

    JSONObject list();

    Message remove(QcAiAgentModel model);

    List<QcAiAgentModel> selectEnabledLLM();

    Message updateModelCredentials(CredentialRequest request);

    Message addPrivacyDeployment(QcAiAgentModel model);

    Message privacyModelRemove(QcAiAgentModel model);
}
