package com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.processor.impl;

import com.alibaba.fastjson.JSONObject;
import com.qc.agent.app.agent.mapper.QcAiAgentConversationMapper;
import com.qc.agent.app.agent.model.po.QcAiAgent;
import com.qc.agent.app.agent.model.po.QcAiAgentConversation;
import com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.model.domain.Product;
import com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.model.enums.GoodsIntent;
import com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.processor.IntentProcessor;
import com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.service.llm.ProductIdentification;
import com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.service.tool.ComparisonGenerator;
import com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.service.tool.ProductTool;
import com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.service.tool.RecommendQuestionTool;
import com.qc.agent.app.workflow.inner.impl.tools.LLMTools;
import com.qc.agent.common.core.Message;
import com.qc.agent.lla.model.LLAConfig;
import com.qc.agent.lla.model.LLARequest;
import com.qc.agent.platform.sse.SseClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Optional;
import java.util.regex.Pattern;

/**
 * Processor to find and list competitors for a given product.
 *
 * <AUTHOR>
 * @date 2025-06-20
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ListCompetitorsProcessor implements IntentProcessor {

    private static final Pattern COMPARED_PRODUCT_PATTERN = Pattern.compile("1\\.\\s*(.*?)\\n");

    private final LLMTools llMTools;
    private final ProductTool productTool;
    private final RecommendQuestionTool recommendQuestionTool;
    private final ComparisonGenerator comparisonGenerator;
    private final ProductIdentification primaryIdentification;

    private final QcAiAgentConversationMapper qcAiAgentConversationMapper;


    @Override
    public GoodsIntent getSupportedIntent() {
        return GoodsIntent.LIST_COMPETITORS;
    }

    @Override
    public Message generateRecommend(LLARequest request, LLAConfig config, QcAiAgent agent, JSONObject entities, QcAiAgentConversation conversation) {
        var logPrefix = LLMTools.getLogPrefix(request, agent);
        log.info("{} Processing intent, generate recommend: {}", logPrefix, getSupportedIntent());

        return productTool.extractProductInfo(entities, logPrefix)
                .map(productInfo -> {
                    // 3. 将推荐列表的构建逻辑放在 map 操作中（成功提取到 productInfo 的情况）
                    var recommendList = new ArrayList<String>();

                    // 4. 将正则表达式匹配逻辑提取到私有方法，使代码更清晰
                    extractComparedProduct(conversation.getAnswer(), request, config)
                            .ifPresent(comparedProduct -> {
                                // 生成第一个问题
                                String firstQuestion = generateQuestion(productInfo.getBrand(), productInfo.getName(), comparedProduct);
                                recommendList.add(firstQuestion);
                            });

                    var llmRecommendQuestions = comparisonGenerator.generateRecommendQuestions(request, config, productInfo, logPrefix, 3 - recommendList.size());
                    recommendList.addAll(llmRecommendQuestions);
                    recommendQuestionTool.insertRecommendQuestions(conversation, recommendList, logPrefix);
                    return Message.of().data(recommendList).ok();
                })
                // 6. 如果 Optional 为空，执行 orElseGet 中的逻辑
                .orElseGet(() -> {
                    log.warn("{} Product information is missing in entities. Cannot generate recommendations.", logPrefix);
                    return Message.of().ok();
                });
    }

    public static String generateQuestion(String brand, String name, String comparedProduct) {
        if (StringUtils.isNotEmpty(comparedProduct)) {
            if (brand != null && !brand.trim().isEmpty()) {
                return String.format("%s%s和%s有什么区别？", name, brand, comparedProduct);
            } else {
                return String.format("%s和%s有什么区别？", name, comparedProduct);
            }
        }
        return Strings.EMPTY;

    }

    /**
     * 从对话回答中提取用于比较的第一个产品名称。
     *
     * @param answer  对话回答字符串
     * @param request
     * @param config
     * @return 包含产品名称的 Optional，如果未找到则为空
     */
    private Optional<String> extractComparedProduct(String answer, LLARequest request, LLAConfig config) {
        if (answer == null || answer.isEmpty()) {
            return Optional.empty();
        }
        var matcher = COMPARED_PRODUCT_PATTERN.matcher(answer);
        String firstCompetitor = matcher.find() ? matcher.group(1).trim() : "";
        if (firstCompetitor.isEmpty()) {
            return Optional.empty();
        }
        String productName = primaryIdentification.splitProductName(config, request, firstCompetitor);
        return Optional.of(productName);
    }

    @Override
    public Message process(LLARequest request, LLAConfig config, SseClient client, QcAiAgent agent, JSONObject entities) {
        String logPrefix = LLMTools.getLogPrefix(request, agent);
        log.info("{} Processing intent: {}", logPrefix, getSupportedIntent());
        QcAiAgentConversation conversation = qcAiAgentConversationMapper.selectById(Long.parseLong(request.getId()));
        try {
            // Step 1: Extract and validate product information from entities.
            Optional<Product> productInfoOpt = productTool.extractProductInfo(entities, logPrefix);
            if (productInfoOpt.isEmpty()) {
                log.warn("{} Product name is missing in the entities. Prompting user for more information.", logPrefix);
                sendMessageToClient(client, request, "我需要了解具体商品名称和对应的品牌信息才可以生成更合适的话术哦，请问是想了解什么品牌的什么商品的竞品信息呢？", logPrefix);
                return Message.of().ok();
            }

            Product productInfo = productInfoOpt.get();
            log.info("{} Extracted product info: {}", logPrefix, productInfo);

            // Step 2: If the brand is missing, try to find it using the product name.
            if (StringUtils.isBlank(productInfo.getBrand())) {
                log.info("{} Brand is missing for product '{}'. Attempting to find it.", logPrefix, productInfo.getName());
                Optional<String> foundBrand = productTool.findBrand(productInfo.getName(), logPrefix);

                if (foundBrand.isEmpty()) {
                    sendMessageToClient(client, request, "对不起，我找不到对应的品牌，请提供更多详细的信息", logPrefix);
                    llMTools.updateConversationDuration(conversation, "对不起，我找不到对应的品牌，请提供更多详细的信息", logPrefix);
                    return Message.of().ok();
                }
                // Update productInfo with the newly found brand.
                productInfo = new Product(foundBrand.get(), productInfo.getName());
                log.info("{} Found brand '{}' for product '{}'.", logPrefix, productInfo.getBrand(), productInfo.getName());
            }

            comparisonGenerator.listCompetitors(productInfo, request, config, client, logPrefix);
        } catch (Exception e) {
            log.error("{} Failed to process intent '{}'", logPrefix, getSupportedIntent(), e);
            // Optionally, inform the user about the internal error.
            sendMessageToClient(client, request, "服务器忙，请稍后再试", logPrefix);
            llMTools.updateConversationDuration(conversation, "服务器忙，请稍后再试", logPrefix);
        }
        return Message.of().ok();
    }


    /**
     * Sends a message directly to the SSE client.
     *
     * @param client    The SSE client.
     * @param request   The original request.
     * @param content   The message content to send.
     * @param logPrefix The log prefix for consistent logging.
     */
    private void sendMessageToClient(SseClient client, LLARequest request, String content, String logPrefix) {
        log.info("{} Sending direct message to client: '{}'", logPrefix, content);
        // Assuming LLMTools is a utility class for this purpose. If not, the logic can be inlined.
        com.qc.agent.app.workflow.inner.impl.tools.LLMTools.directReturnMessage(client, request, logPrefix, content);
    }
}