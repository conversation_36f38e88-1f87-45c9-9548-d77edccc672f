package com.qc.agent.app.agent.support;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.qc.agent.app.agent.model.po.QcAiAgent;
import com.qc.agent.common.core.RequestHolder;
import com.qc.agent.platform.util.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

@Slf4j
public class AgentBizDataSupport {

    /**
     * 获取业务数据
     *
     * @param qcAiAgent
     * @param param
     * @return
     */
    public static String fetchBizData(QcAiAgent qcAiAgent, JSONObject param) {
        return fetchBizData(qcAiAgent.getDataFetchUrl(), param);
    }

    /**
     * 获取业务数据
     *
     * @param dataFetchUrl
     * @param param
     * @return
     */
    public static String fetchBizData(String dataFetchUrl, JSONObject param) {
        try {
            String data = fetchBizData(dataFetchUrl, param.toJSONString());
            return JSONObject.parseObject(data).getString("data");
        } catch (Exception e) {
            log.error("获取业务数据异常", e);
            return StringUtils.EMPTY;
        }
    }

    /**
     * 获取业务数据
     *
     * @param dataFetchUrl
     * @param param
     * @return
     */
    public static String postFetchBizData(String dataFetchUrl, JSONObject param) {
        return fetchBizData(dataFetchUrl, param.toJSONString());
    }


    /**
     * 获取业务数据
     *
     * @param url
     * @param param
     * @return
     */
    public static String fetchBizData(String url, String param) {
        Map<String, String> extraHeaders = getAuthHeaders();

        try {
            log.info("fetch data param:{},headers:{}", param, extraHeaders);
            return HttpUtil.post(url, param, extraHeaders);
        } catch (Exception e) {
            log.error("获取业务数据异常", e);
            return StringUtils.EMPTY;
        }
    }

    private static Map<String, String> getAuthHeaders() {
        Map<String, String> extraHeaders = Maps.newHashMap();
        if (StringUtils.isNotEmpty(RequestHolder.getRequestCookie())) {
            extraHeaders.put("Cookie", RequestHolder.getRequestCookie());
        } else if (StringUtils.isNotEmpty(RequestHolder.getRequestToken())) {
            extraHeaders.put("Authorization", RequestHolder.getRequestToken());
        }
        return extraHeaders;
    }

    /**
     * 获取业务数据
     *
     * @param url
     * @return
     */
    public static String getWithAuthHeaders(String url) {
        Map<String, String> extraHeaders = getAuthHeaders();

        try {
            log.info("fetch data ,headers:{}", extraHeaders);
            return HttpUtil.get(url, extraHeaders);
        } catch (Exception e) {
            log.error("获取业务数据异常", e);
            return StringUtils.EMPTY;
        }
    }

}
