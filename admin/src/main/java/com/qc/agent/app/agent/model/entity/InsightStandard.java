package com.qc.agent.app.agent.model.entity;

import java.io.Serializable;

import com.qc.agent.platform.pojo.BaseEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 标准表
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InsightStandard extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 标准 ID，主键
     */
    private Long id;

    /**
     * 标准名称
     */
    private String standardName;

    /**
     * 标准定义
     */
    private String standardDefinition;

    /**
     * 标准类型编码
     */
    private String standardTypeCode;

    /**
     * 标准类型
     */
    private String standardType;

    /**
     * 排序顺序
     */
    private Integer sortOrder;

}