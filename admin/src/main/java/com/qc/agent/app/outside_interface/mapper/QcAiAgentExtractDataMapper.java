package com.qc.agent.app.outside_interface.mapper;

import com.qc.agent.app.outside_interface.pojo.QcAiAgentConversationDO;
import com.qc.agent.app.outside_interface.pojo.query.QcAiAgentExtractDataQuery;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-17
 */
public interface QcAiAgentExtractDataMapper {
    /**
     * 查询租户id
     *
     * @return
     */
    List<Long> selectTenantIds(QcAiAgentExtractDataQuery query);

    /**
     * 根据tenantId查询会话记录
     *
     * @param query
     * @return
     */
    List<QcAiAgentConversationDO> selectConversationRecordList(QcAiAgentExtractDataQuery query);
}
