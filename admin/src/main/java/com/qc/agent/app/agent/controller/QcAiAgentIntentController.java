package com.qc.agent.app.agent.controller;

import com.qc.agent.app.agent.model.query.QcAiAgentIntentQuery;
import com.qc.agent.app.agent.service.QcAiAgentIntentService;
import com.qc.agent.common.core.Message;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-05-29
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@RestController
@RequestMapping("/ai-agent/intent/")
public class QcAiAgentIntentController {

    @Resource
    private QcAiAgentIntentService qcAiAgentIntentService;

    @PostMapping("/generate_prompt")
    public Message generatePrompt(@RequestBody QcAiAgentIntentQuery query) {
        Map<String, Object> resultMap = qcAiAgentIntentService.generatePrompt(query);
        return Message.of().ok().data(resultMap);
    }
}
