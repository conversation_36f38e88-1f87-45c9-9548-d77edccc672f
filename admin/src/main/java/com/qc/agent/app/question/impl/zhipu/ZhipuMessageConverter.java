package com.qc.agent.app.question.impl.zhipu;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.qc.agent.platform.sse.SseMessage;
import com.qc.agent.platform.sse.SseMessageConverter;
import com.qc.agent.platform.sse.SseRequest;
import com.qc.agent.platform.sse.dto.MessageType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

@Slf4j
public class ZhipuMessageConverter implements SseMessageConverter<String> {
    @Override
    public SseMessage convert(String content, SseRequest request) {
        if (!StringUtils.hasText(content)) {
            return SseMessage.builder().message(content).messageType(MessageType.TEXT).build();
        }
        if ("data: [DONE]".equals(content)) {
            return SseMessage.buildEndMessage();
        } else {
            JSONObject jsonObject = extractContentFromLine(content);
            JSONArray choices = jsonObject.getJSONArray("choices");
            StringBuffer result = new StringBuffer();
            for (int i = 0, size = choices.size(); i < size; i++) {
                JSONObject choice = choices.getJSONObject(i);
                JSONObject delta = choice.getJSONObject("delta");
                if (delta != null) {
                    result.append(delta.getString("content"));
                }
            }
            return SseMessage.builder().message(result.toString()).messageType(MessageType.TEXT).build();
        }

    }

    private JSONObject extractContentFromLine(String line) {
        String dataPrefix = "data: {";

        if (line.startsWith(dataPrefix)) {
            return JSONObject.parseObject(line.substring("data: ".length()));
        }

        return null;
    }

}
