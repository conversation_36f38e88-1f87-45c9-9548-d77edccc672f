package com.qc.agent.app.agent.service;

import java.util.List;

import com.qc.agent.app.agent.model.entity.DimensionPromptValue;

/**
 * 维度提示词片段值服务接口
 */
public interface DimensionPromptValueService {

    /**
     * 根据配置ID查询提示词片段值列表
     *
     * @param configId 配置ID（可以是维度配置ID或总结配置ID）
     * @return 提示词片段值列表
     */
    List<DimensionPromptValue> getPromptValuesByConfigId(Long configId);

    /**
     * 根据配置ID和提示词片段ID查询提示词片段值
     *
     * @param configId 配置ID（可以是维度配置ID或总结配置ID）
     * @param promptFragmentId  提示词片段ID
     * @return 提示词片段值
     */
    DimensionPromptValue getPromptValueByConfigIdAndPromptFragmentId(Long configId,
            Long promptFragmentId);

    /**
     * 保存提示词片段值
     *
     * @param dimensionPromptValue 提示词片段值
     * @return 是否保存成功
     */
    boolean savePromptValue(DimensionPromptValue dimensionPromptValue);

    /**
     * 批量保存提示词片段值
     *
     * @param dimensionPromptValues 提示词片段值列表
     * @return 是否保存成功
     */
    boolean savePromptValues(List<DimensionPromptValue> dimensionPromptValues);

    /**
     * 根据配置ID删除提示词片段值
     *
     * @param configId 配置ID（可以是维度配置ID或总结配置ID）
     * @return 是否删除成功
     */
    boolean deletePromptValuesByConfigId(Long configId);

    /**
     * 根据配置ID和提示词片段ID删除提示词片段值
     *
     * @param configId 配置ID（可以是维度配置ID或总结配置ID）
     * @param promptFragmentId  提示词片段ID
     * @return 是否删除成功
     */
    boolean deletePromptValueByConfigIdAndPromptFragmentId(Long configId, Long promptFragmentId);
}