package com.qc.agent.app.question.controller;

import com.qc.agent.app.question.pojo.*;
import com.qc.agent.app.question.service.QcKnowledgeService;
import com.qc.agent.app.workflow.WorkflowLLAClient;
import com.qc.agent.common.core.Message;
import com.qc.agent.lla.LLAClient;
import com.qc.agent.lla.model.LLAConfig;
import com.qc.agent.lla.model.LLAProvider;
import com.qc.agent.lla.model.LLARequest;
import com.qc.agent.platform.sse.AnswerAIClient;
import com.qc.agent.platform.sse.AnswerAISubscriber;
import com.qc.agent.platform.sse.SseClient;
import com.qc.agent.platform.sse.SseRequest;
import com.qc.agent.vectordb.pojo.SearchContent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @description：TODO
 * @author： sc
 * @create： 2024/1/22 10:38
 */

@RestController
@RequestMapping("/ai-agent/question/answer")
public class QcKnowledgeController {
    private static final Logger log = LoggerFactory.getLogger(QcKnowledgeController.class);

    @Resource
    private QcKnowledgeService qcKnowledgeService;

    @Resource
    private SseClient client;

    /**
     * 测试回答
     * @param question
     * @return
     */
    @RequestMapping("test.do")
    public Object search(@RequestBody QcKnowledgeQuestion question){
        try {
            List<SearchContent> data = qcKnowledgeService.search(question);
            return Message.of().data(data).ok();
        } catch (Exception e) {
            log.error("测试回答异常", e);
            return Message.of().error( "测试回答异常");
        }
    }

    /**
     * 历史测试
     * @param question
     * @return
     */
    @RequestMapping("historyTest.do")
    public Object historyTest(@RequestBody QcKnowledgeQuestion question){
        try {
            List<QcKnowledgeQuestion> data = qcKnowledgeService.historyTest(question);
            return Message.of().data(data).ok();
        } catch (Exception e) {
            log.error("获取历史测试异常", e);
            return Message.of().error( "获取历史测试异常");
        }
    }

    /**
     * 新建对话
     * @param qcKnowledgeAnswerSession
     * @return
     */
    @RequestMapping("saveAnswerSession.do")
    public Object saveAnswerSession(@RequestBody QcKnowledgeAnswerSession qcKnowledgeAnswerSession){
        try {
            QcKnowledgeAnswerSession addResult = qcKnowledgeService.saveAnswerSession(qcKnowledgeAnswerSession);
            return Message.of().data(addResult).ok();
        } catch (Exception e) {
            log.error("新增对话异常", e);
            return Message.of().error( "新增对话异常");
        }
    }

    /**
     * 删除对话
     * @param qcKnowledgeAnswerSession
     * @return
     */
    @RequestMapping("deleteAnswerSession.do")
    public Object deleteAnswerSession(@RequestBody QcKnowledgeAnswerSession qcKnowledgeAnswerSession){
        try {
            qcKnowledgeService.deleteAnswerSession(qcKnowledgeAnswerSession);
            return Message.of().ok();
        } catch (Exception e) {
            log.error("删除对话异常", e);
            return Message.of().error( "删除对话异常");
        }
    }

    /**
     * 获取登录人的对话列表
     * @param qcKnowledgeAnswerSession
     * @return
     */
    @RequestMapping("listAnswerSession.do")
    public Object listAnswerSession(@RequestBody QcKnowledgeAnswerSession qcKnowledgeAnswerSession){
        try {
            List<AnswerSessionView> data = qcKnowledgeService.listAnswerSession(qcKnowledgeAnswerSession);
            return Message.of().data(data).ok();
        } catch (Exception e) {
            log.error("获取登录人的对话列表异常", e);
            return Message.of().error( "获取对话列表异常");
        }
    }

    /**
     * 获取对话历史问答
     * @param qcKnowledgeQuestion
     * @return
     */
    @RequestMapping("listAnswerHistory.do")
    public Object listAnswerHistory(@RequestBody QcKnowledgeQuestion qcKnowledgeQuestion){
        try {
            List<QcKnowledgeQuestion> data = qcKnowledgeService.listAnswerHistory(qcKnowledgeQuestion);
            return Message.of().data(data).ok();
        } catch (Exception e) {
            log.error("获取登录人的对话列表异常", e);
            return Message.of().error( "获取对话列表异常");
        }
    }

    /**
     * 获取问答上下文
     * @param qcKnowledgeQuestion
     * @return
     */
    @RequestMapping("listAnswerContext.do")
    public Object listAnswerContext(@RequestBody QcKnowledgeQuestion qcKnowledgeQuestion){
        try {
            List<QcKnowledgeQuestion> data = qcKnowledgeService.listAnswerContext(qcKnowledgeQuestion);
            return Message.of().data(data).ok();
        } catch (Exception e) {
            log.error("获取问答上下文异常", e);
            return Message.of().error( "获取问答上下文异常");
        }
    }

    /**
     * 获取问答引用
     * @param qcKnowledgeQuestion
     * @return
     */
    @RequestMapping("listAnswerReference.do")
    public Object listAnswerReference(@RequestBody QcKnowledgeQuestion qcKnowledgeQuestion){
        try {
            List<SearchContent> data = qcKnowledgeService.listAnswerReference(qcKnowledgeQuestion);
            return Message.of().data(data).ok();
        } catch (Exception e) {
            log.error("获取问答应用异常", e);
            return Message.of().error("获取问答应用异常");
        }
    }

    /**
     * 获取问答的上下文总数和引用数
     * @param qcKnowledgeQuestion
     * @return
     */
    @RequestMapping("queryQuestionSummary.do")
    public Object queryQuestionSummary(@RequestBody QcKnowledgeQuestion qcKnowledgeQuestion){
        try {
            Map<String, Object> data = qcKnowledgeService.queryQuestionSummary(qcKnowledgeQuestion);
            return Message.of().data(data).ok();
        } catch (Exception e) {
            log.error("获取问答的上下文总数和引用数", e);
            return Message.of().error( "获取问答的上下文总数和引用数异常");
        }
    }

    /**
     * 删除问题
     * @param qcKnowledgeQuestion
     * @return
     */
    @RequestMapping("deleteQuestion.do")
    public Object deleteQuestion(@RequestBody QcKnowledgeQuestion qcKnowledgeQuestion){
        try {
            qcKnowledgeService.deleteQuestion(qcKnowledgeQuestion);
            return Message.of().ok();
        } catch (Exception e) {
            log.error("删除问题异常", e);
            return Message.of().error(  "删除问题异常");
        }
    }

    /**
     * 删除回答
     * @param qcKnowledgeQuestion
     * @return
     */
    @RequestMapping("deleteAnswer.do")
    public Object deleteAnswer(@RequestBody QcKnowledgeQuestion qcKnowledgeQuestion){
        try {
            qcKnowledgeService.deleteAnswer(qcKnowledgeQuestion);
            return Message.of().ok();
        } catch (Exception e) {
            log.error("删除回答异常", e);
            return Message.of().error("删除回答异常");
        }
    }

    /**
     * 反馈
     * @param request
     * @return
     */
    @RequestMapping("feedback.do")
    public Object feedback(@RequestBody FeedBackRequest request){
        try {
            qcKnowledgeService.feedback(request);
            return Message.of().ok();
        } catch (Exception e) {
            log.error("删除回答异常", e);
            return Message.of().error(  "删除回答异常");
        }
    }

    /**
     * 问答
     * @param request
     * @return
     */
    @GetMapping(value = "chat.do", produces = "text/event-stream")
    public SseEmitter chat(StreamChatRequest request) {
        return qcKnowledgeService.streamChat(request);
    }

    /**
     * 停止回答
     * @param request
     * @return
     */
    @RequestMapping("stop.do")
    public Object stop(@RequestBody StreamChatRequest request){
        qcKnowledgeService.stopStreamChat(request);
        return Message.of().ok();
    }


    @GetMapping(value = "chatbot.do", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter chatbot(String prompt, String user) {
        return AnswerAIClient.builder().subscribe(AnswerAISubscriber.builder()).execute(SseRequest.builder().sleepTime(50).clientId(user).question(prompt).build());
    }


    /**
     * 发送消息
     * @return
     */
    @GetMapping(value = "sendAsync.do", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter sendAsync(String prompt,String model,String provider,String clientId){
        try
        {
            String clientId1 = String.valueOf(clientId.hashCode());
            SseEmitter sseEmitter = client.subscribe(clientId1);
            new Thread(()->{
                WorkflowLLAClient.sendAsync(LLAConfig.builder().model(model).provider(LLAProvider.getEnumByValue(provider)).build(), LLARequest.builder().id(clientId1).content(prompt).build(),client);
            }).start();
            return sseEmitter;
        }
        catch(Exception e)
        {
            log.error("发送消息异常", e);
            client.complete(clientId);
            return null;
        }

    }

    /**
     * 发送消息
     * @return
     */
    @RequestMapping(value = "stopStream.do")
    public Object stopStream(String clientId){
        client.removeSse(String.valueOf(clientId.hashCode()));
        return Message.of().ok();
    }

    @RequestMapping("send.do")
    public Message send(String prompt,String model,String provider){

        return Message.of().data(LLAClient.send(LLAConfig.builder().model(model).provider(LLAProvider.getEnumByValue(provider)).build(), LLARequest.builder().content(prompt).build())).ok();
    }

}
