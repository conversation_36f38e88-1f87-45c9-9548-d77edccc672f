package com.qc.agent.app.agent.model.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 数据源参数VO
 */
@Data
public class SourceParamVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 参数编码，对应接口的具体参数名
     */
    private String paramCode;

    /**
     * 分组编码
     */
    private String groupCode;

    /**
     * 排序顺序
     */
    private Integer displayOrder;
}
