package com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.model.dto;

import com.alibaba.fastjson.JSONObject;
import com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.model.enums.GoodsIntent;
import com.qc.agent.lla.model.LLAUsage;
import lombok.extern.slf4j.Slf4j;

import java.util.Optional;

@Slf4j
public record BusinessIntent(GoodsIntent type, JSONObject entities, LLAUsage usage) {

    public BusinessIntent(GoodsIntent type, JSONObject entities, LLAUsage usage) {
        this.type = type;
        this.entities = Optional.ofNullable(entities).orElse(new JSONObject());
        this.usage = usage;
    }

    // 快速创建方法
    public static BusinessIntent of(GoodsIntent type) {
        return new BusinessIntent(type, new JSONObject(), new LLAUsage());
    }

    // 快速创建方法
    public static BusinessIntent of(GoodsIntent type, JSONObject jsonObject) {
        return new BusinessIntent(type, jsonObject, new LLAUsage());
    }

    // 快速创建方法
    public static BusinessIntent of(GoodsIntent type, JSONObject jsonObject, LLAUsage usage) {
        return new BusinessIntent(type, jsonObject, usage);
    }
}
