package com.qc.agent.app.agent.model.query;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QcAiAgentPromptQuery {

    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;
    private String status;

    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long creatorId;
    private String creatorName;
    private LocalDateTime createTime;

    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long modifyierId;
    private String modifyierName;
    private LocalDateTime modifyTime;

    /**
     * 名称
     */
    private String name;

    /**
     * 描述
     */
    private String describe;

    /**
     * 提示词
     */
    private String content;

    /**
     * 1：推荐提示词
     */
    private String recommendFlag;

    /**
     * 搜索内容
     */
    private String filter;
}