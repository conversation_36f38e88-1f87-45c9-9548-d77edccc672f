package com.qc.agent.app.question.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.qc.agent.app.question.mapper.QcKnowledgeHistoryQuestionMapper;
import com.qc.agent.app.question.mapper.QcKnowledgeQuestionMapper;
import com.qc.agent.app.question.pojo.QcKnowledgeHistoryQuestion;
import com.qc.agent.app.question.pojo.QcKnowledgeQuestion;
import com.qc.agent.platform.sse.SseRequest;
import com.qc.agent.common.util.ContextUtil;
import com.qc.agent.platform.util.UUIDUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @description：TODO
 * @author： sc
 * @create： 2024/3/2 13:28
 */
public class ChatUtil {

    public static Map<String, Object> queryExtData(SseRequest request) {
        Map<String, Object> extData = Maps.newHashMap();

        extData.put("questionId", String.valueOf(request.getQuestionId()));

        QcKnowledgeHistoryQuestionMapper qcKnowledgeHistoryQuestionMapper = (QcKnowledgeHistoryQuestionMapper) ContextUtil.getBean("qcKnowledgeHistoryQuestionMapper");

        QcKnowledgeQuestionMapper qcKnowledgeQuestionMapper = (QcKnowledgeQuestionMapper) ContextUtil.getBean("qcKnowledgeQuestionMapper");

        if (request.getSimilarQuestionId() != null) {
            addExistsAnswerExt(request, extData, qcKnowledgeHistoryQuestionMapper, qcKnowledgeQuestionMapper);
        } else {
            addNewAnswerExt(request, extData, qcKnowledgeHistoryQuestionMapper, qcKnowledgeQuestionMapper);
        }

        return extData;
    }

    private static void addExistsAnswerExt(SseRequest request, Map<String, Object> extData,
                                           QcKnowledgeHistoryQuestionMapper qcKnowledgeHistoryQuestionMapper, QcKnowledgeQuestionMapper qcKnowledgeQuestionMapper) {
        if (qcKnowledgeHistoryQuestionMapper != null) {
            List<Long> historyQuestionIds = qcKnowledgeHistoryQuestionMapper.queryContextQuestionId(request.getSimilarQuestionId());
            extData.put("contextCount", CollectionUtils.isNotEmpty(historyQuestionIds) ? historyQuestionIds.size() : 0);
            if (CollectionUtils.isNotEmpty(historyQuestionIds)) {
                List<QcKnowledgeHistoryQuestion> insertRecords = Lists.newArrayList();
                for (Long historyQuestionId : historyQuestionIds) {
                    QcKnowledgeHistoryQuestion record = new QcKnowledgeHistoryQuestion();
                    record.setId(UUIDUtils.getUUID2Long());
                    record.setQuestionId(request.getQuestionId());
                    record.setHistoryQuestionId(historyQuestionId);
                    insertRecords.add(record);
                }
                qcKnowledgeHistoryQuestionMapper.batchInsert(insertRecords);
            }
        }

        if (qcKnowledgeQuestionMapper != null && "1".equals(request.getShowReference())) {
            QcKnowledgeQuestion qcKnowledgeQuestion = qcKnowledgeQuestionMapper.selectByPrimaryKey(request.getSimilarQuestionId());
            if (StringUtils.isNotEmpty(qcKnowledgeQuestion.getReferenceContent()) && qcKnowledgeQuestion.getViewCount() != null) {
                qcKnowledgeQuestionMapper.updateReference(request.getQuestionId(), qcKnowledgeQuestion.getViewCount().intValue(), qcKnowledgeQuestion.getReferenceContent());
            }
            extData.put("viewCount", qcKnowledgeQuestion.getViewCount());
        }
    }

    private static void addNewAnswerExt(SseRequest request, Map<String, Object> extData,
                                        QcKnowledgeHistoryQuestionMapper qcKnowledgeHistoryQuestionMapper, QcKnowledgeQuestionMapper qcKnowledgeQuestionMapper) {
        if (qcKnowledgeHistoryQuestionMapper != null) {
            Long contextCount = qcKnowledgeHistoryQuestionMapper.countContext(request.getQuestionId());
            extData.put("contextCount", contextCount);
        }

        if (qcKnowledgeQuestionMapper != null) {
            QcKnowledgeQuestion qcKnowledgeQuestion = qcKnowledgeQuestionMapper.selectByPrimaryKey(request.getQuestionId());
            extData.put("viewCount", qcKnowledgeQuestion.getViewCount());
        }
    }

    /**
     * 提取QA问答内容中的答案
     *
     * @param content
     * @return
     */
    public static String extractAnswer(String content) {
        String text = content.replace("A:", "A：");
        List<String> answers = new ArrayList<>();
        int start = 0;
        while ((start = text.indexOf("A：", start)) != -1) {
            int end = Math.min(text.indexOf("Q：", start), text.indexOf("A：", start + 2));
            if (end == -1) {
                end = text.length();
            }
            String answer = text.substring(start, end).substring(2).trim();
            answers.add(answer);
            start += 2; // 跳过"A："，以便于下一次搜索
        }
        return CollectionUtils.isNotEmpty(answers) && answers.size() == 1 ? answers.get(0) : null;
    }

}
