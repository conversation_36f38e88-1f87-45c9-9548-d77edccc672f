package com.qc.agent.app.knowledge.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @className QcKnowledgeFolder
 * @description TODO
 * @date 2024/1/22 16:49
 */
public class QcKnowledgeFolder implements ITreeEntity<QcKnowledgeFolder>{
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;

    private String folderName;


    private String folderComments;


    private Date modifyTime;


    private String folderLogoUrl;

    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long createUserId;


    private String createUserName;

    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long modifyUserId;


    private String modifyUserName;

    private Date createTime;

    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long parentId;
    private String sequence;


    private List<QcKnowledgeFolder> children;

    @Override
    public List<QcKnowledgeFolder> getChildren() {
        return children;
    }

    @Override
    public void setChildren(List<QcKnowledgeFolder> children) {
        this.children = children;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getSequence() {
        return sequence;
    }

    public void setSequence(String sequence) {
        this.sequence = sequence;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getFolderName() {
        return folderName;
    }

    public void setFolderName(String folderName) {
        this.folderName = folderName;
    }

    public String getFolderComments() {
        return folderComments;
    }

    public void setFolderComments(String folderComments) {
        this.folderComments = folderComments;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getFolderLogoUrl() {
        return folderLogoUrl;
    }

    public void setFolderLogoUrl(String folderLogoUrl) {
        this.folderLogoUrl = folderLogoUrl;
    }

    public Long getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Long createUserId) {
        this.createUserId = createUserId;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public Long getModifyUserId() {
        return modifyUserId;
    }

    public void setModifyUserId(Long modifyUserId) {
        this.modifyUserId = modifyUserId;
    }

    public String getModifyUserName() {
        return modifyUserName;
    }

    public void setModifyUserName(String modifyUserName) {
        this.modifyUserName = modifyUserName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
