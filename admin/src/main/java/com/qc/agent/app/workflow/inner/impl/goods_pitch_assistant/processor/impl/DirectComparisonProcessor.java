package com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.processor.impl;

import com.alibaba.fastjson.JSONObject;
import com.qc.agent.app.agent.mapper.QcAiAgentConversationMapper;
import com.qc.agent.app.agent.model.po.QcAiAgent;
import com.qc.agent.app.agent.model.po.QcAiAgentConversation;
import com.qc.agent.app.workflow.WorkflowLLAClient;
import com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.model.domain.Product;
import com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.model.enums.GoodsIntent;
import com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.processor.IntentProcessor;
import com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.service.tool.ComparisonGenerator;
import com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.service.tool.ProductTool;
import com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.service.tool.RecommendQuestionTool;
import com.qc.agent.app.workflow.inner.impl.tools.LLMTools;
import com.qc.agent.common.core.Message;
import com.qc.agent.lla.model.LLAConfig;
import com.qc.agent.lla.model.LLARequest;
import com.qc.agent.platform.sse.SseClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * Processes direct comparison requests between two products.
 * This processor identifies which of the two mentioned products is our own,
 * then orchestrates the generation of a detailed comparison against the competitor.
 *
 * <AUTHOR>
 * @date 2025-06-23
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DirectComparisonProcessor implements IntentProcessor {

    private final LLMTools llMTools;
    private final ProductTool productTool;
    private final ComparisonGenerator comparisonGenerator;
    private final RecommendQuestionTool recommendQuestionTool;

    private final QcAiAgentConversationMapper qcAiAgentConversationMapper;


    /**
     * Immutable record to hold the names of the two products to be compared.
     */
    private record ProductNames(String productNameA, String productNameB) {
    }

    /**
     * Immutable record to hold the resolved pair for comparison: our recognized product
     * and the name of the competitor.
     */
    private record ComparisonPair(Product ourProduct, String competitorName) {
    }

    @Override
    public GoodsIntent getSupportedIntent() {
        return GoodsIntent.DIRECT_COMPARISON;
    }

    @Override
    public Message generateRecommend(final LLARequest request, final LLAConfig config, final QcAiAgent agent,
                                     final JSONObject entities, final QcAiAgentConversation conversation) {
        final var logPrefix = LLMTools.getLogPrefix(request, agent);
        log.info("{} Processing intent to generate recommendation questions: {}", logPrefix, getSupportedIntent());

        final Optional<ComparisonPair> comparisonPairOpt = prepareComparison(entities, logPrefix);
        config.setEnableSearch(true); // Enable web search for fresh competitor  data
        List<String> questions;
        if (comparisonPairOpt.isPresent()) {
            final var pair = comparisonPairOpt.get();
            log.info("{} Proceeding to generate recommendation questions for Our Product='{}', Competitor='{}'", logPrefix, pair.ourProduct(), pair.competitorName());
            questions = comparisonGenerator.generateRecommendQuestions(request, config, pair.ourProduct(), logPrefix, 3);
        } else {
            questions = comparisonGenerator.generateRecommendQuestions(request, config, logPrefix, 3);
        }
        log.info("{} Successfully generated {} recommendation questions.", logPrefix, questions.size());
        // 将推荐问题存表
        recommendQuestionTool.insertRecommendQuestions(conversation, questions, logPrefix);
        return Message.of().data(questions).ok();
    }


    @Override
    public Message process(final LLARequest request, final LLAConfig config, final SseClient client, final QcAiAgent agent, final JSONObject entities) {
        final var logPrefix = LLMTools.getLogPrefix(request, agent);
        log.info("{} Processing intent to generate detailed comparison: {}", logPrefix, getSupportedIntent());

        final Optional<ComparisonPair> comparisonPairOpt = prepareComparison(entities, logPrefix);

        if (comparisonPairOpt.isPresent()) {
            final var pair = comparisonPairOpt.get();
            log.info("{} Proceeding with detailed comparison for Our Product='{}', Competitor='{}'", logPrefix, pair.ourProduct(), pair.competitorName());
            config.setEnableSearch(true); // Enable web search for fresh competitor data
            comparisonGenerator.generateDetailedComparison(pair.ourProduct(), pair.competitorName(), request, config, client, logPrefix);
        } else {
            // Failure case already logged, send a final message to the client.
            WorkflowLLAClient.sendAsync(config, request, client);
        }
        log.info("{} Finished processing intent: {}", logPrefix, getSupportedIntent());
        return Message.of().ok();
    }

    /**
     * Prepares the comparison by extracting and validating product names and identifying our product.
     * This helper method centralizes the common setup logic for both process and generateRecommend.
     *
     * @param entities  The JSON object containing intent entities.
     * @param logPrefix The logging prefix for the current request.
     * @return An Optional containing a ComparisonPair if successful, otherwise an empty Optional.
     */
    private Optional<ComparisonPair> prepareComparison(final JSONObject entities, final String logPrefix) {
        // Step 1: Extract the two product names from the entities.
        final Optional<ProductNames> productNamesOpt = extractProductNames(entities);
        if (productNamesOpt.isEmpty() || StringUtils.isAnyBlank(productNamesOpt.get().productNameA(), productNamesOpt.get().productNameB())) {
            log.warn("{} Failed to extract one or both product names from entities: {}", logPrefix, entities);
            return Optional.empty();
        }

        final var productNames = productNamesOpt.get();
        log.info("{} Extracted product names for comparison: ProductA='{}', ProductB='{}'", logPrefix, productNames.productNameA(), productNames.productNameB());

        // Step 2: Determine which product is ours and which is the competitor.
        return determineProductsForComparison(productNames.productNameA(), productNames.productNameB(), logPrefix);
    }

    /**
     * Determines which product is "our product" and which is the competitor.
     *
     * @return An Optional containing a ComparisonPair if exactly one product is identified as ours,
     * or if both are found (defaulting to A as ours). Returns empty if neither is found.
     */
    private Optional<ComparisonPair> determineProductsForComparison(final String nameA, final String nameB, final String logPrefix) {
        log.info("{} Attempting to identify products in our system: '{}' and '{}'", logPrefix, nameA, nameB);
        final var productAOpt = productTool.findMostSimilarProduct(nameA, logPrefix);
        final var productBOpt = productTool.findMostSimilarProduct(nameB, logPrefix);

        log.debug("{} Product lookup result: '{}' -> found={}; '{}' -> found={}", logPrefix, nameA, productAOpt.isPresent(), nameB, productBOpt.isPresent());

        // Case 1: Product A is ours, B is the competitor.
        if (productAOpt.isPresent() && productBOpt.isEmpty()) {
            log.info("{} Identified '{}' as our product and '{}' as the competitor.", logPrefix, nameA, nameB);
            return Optional.of(new ComparisonPair(toProduct(productAOpt.get()), nameB));
        }

        // Case 2: Product B is ours, A is the competitor.
        if (productAOpt.isEmpty() && productBOpt.isPresent()) {
            log.info("{} Identified '{}' as our product and '{}' as the competitor.", logPrefix, nameB, nameA);
            return Optional.of(new ComparisonPair(toProduct(productBOpt.get()), nameA));
        }

        // Case 3 (Ambiguous): Both products are in our system. Default to A as "our product".
        if (productAOpt.isPresent()) {
            log.warn("{} Both products ('{}' and '{}') were found in our system. Defaulting to '{}' as our product for comparison.", logPrefix, nameA, nameB, nameA);
            return Optional.of(new ComparisonPair(toProduct(productAOpt.get()), nameB));
        }

        // Case 4 (Failure): Neither product was found in our system.
        log.error("{} Neither '{}' nor '{}' could be identified in our product database.", logPrefix, nameA, nameB);
        return Optional.empty();
    }

    /**
     * Extracts product names from the provided entities using a null-safe approach.
     *
     * @param entities The JSON object containing intent entities.
     * @return An Optional containing ProductNames.
     */
    private Optional<ProductNames> extractProductNames(final JSONObject entities) {
        return Optional.ofNullable(entities)
                .map(e -> new ProductNames(e.getString("productA"), e.getString("productB")));
    }

    /**
     * Converts a product's JSONObject representation into a Product domain object.
     *
     * @param productJson The JSONObject from the ProductTool.
     * @return A new Product instance.
     */
    private Product toProduct(final JSONObject productJson) {
        return new Product(productJson.getString("brandName"), productJson.getString("name"));
    }

    /**
     * Sends a final message directly to the SSE client.
     */
    private void sendMessageToClient(final SseClient client, final LLARequest request, final String content, final String logPrefix) {
        log.info("{} Sending final message to client: '{}'", logPrefix, content);
        LLMTools.directReturnMessage(client, request, logPrefix, content);
    }
}