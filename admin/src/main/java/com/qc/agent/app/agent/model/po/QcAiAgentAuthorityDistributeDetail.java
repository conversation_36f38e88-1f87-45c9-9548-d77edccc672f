package com.qc.agent.app.agent.model.po;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025-03-24
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Builder
@Data
public class QcAiAgentAuthorityDistributeDetail {

    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;

    private String status;

    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long creatorId;

    private LocalDateTime createTime;

    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long modifierId;

    private LocalDateTime modifyTime;

    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long agentId;

    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long deptId;

    private String deptName;

    private String userName;
    /**
     * 类型  '1' : 人员  '2'： 部门
     */
    private String type;

    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long userId;
}
