package com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.service.llm;

import com.qc.agent.app.workflow.WorkflowLLAClient;
import com.qc.agent.lla.model.LLAConfig;
import com.qc.agent.lla.model.LLARequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025-07-03
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ProductIdentification {

    public String splitProductName(LLAConfig config, LLARequest request, String answer) {
        LLARequest llaRequest = LLARequest.builder()
                .id(request.getId())
                .start(LocalDateTime.now())
                .system("提取出商品名称,直接返回商品名称，不要有额外信息")
                .content(answer)
                .skipAnswerUpdate(true)
                .clientId(request.getClientId())
                .build();
        return WorkflowLLAClient.send(config, llaRequest, true).getContent();
    }
}
