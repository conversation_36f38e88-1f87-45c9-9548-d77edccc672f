package com.qc.agent.app.api_suit.service.impl;

import com.qc.agent.app.api_suit.mapper.QcAiAgentModuleApiMapper;
import com.qc.agent.app.api_suit.model.dto.ModuleApiDTO;
import com.qc.agent.app.api_suit.model.po.QcAiAgentModuleApi;
import com.qc.agent.app.api_suit.model.vo.ModuleApiVO;
import com.qc.agent.app.api_suit.service.QcAiAgentModuleApiService;
import com.qc.agent.platform.util.UUIDUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

@Service
public class QcAiAgentModuleApiServiceImpl implements QcAiAgentModuleApiService {

    @Resource
    private QcAiAgentModuleApiMapper qcAiAgentModuleApiMapper;

    @Override
    public ModuleApiVO getById(Long id) {
        return qcAiAgentModuleApiMapper.selectById(id);
    }

    @Override
    public List<ModuleApiVO> list(Long moduleId) {
        return qcAiAgentModuleApiMapper.selectByModuleId(moduleId);
    }

    @Override
    public void save(ModuleApiDTO dto) {
        if (dto.getId() == null) {
            QcAiAgentModuleApi record = QcAiAgentModuleApi.builder()
                    .id(UUIDUtils.getUUID2Long())
                    .status("1")
                    .createTime(LocalDateTime.now())
                    .creatorId(dto.getUserId())
                    .creatorName(dto.getUserName())
                    .moduleId(dto.getModuleId())
                    .name(dto.getName())
                    .describe(dto.getDescribe())
                    .url(dto.getUrl())
                    .documentUrl(dto.getDocumentUrl())
                    .requestMethod(dto.getRequestMethod())
                    .headers(dto.getHeaders())
                    .body(dto.getBody())
                    .queryParam(dto.getQueryParam())
                    .response(dto.getResponse())
                    .sequ(dto.getSequ())
                    .build();
            qcAiAgentModuleApiMapper.insert(record);
        } else {
            QcAiAgentModuleApi record = QcAiAgentModuleApi.builder()
                    .id(dto.getId())
                    .modifyTime(LocalDateTime.now())
                    .modifyierId(dto.getUserId())
                    .modifyierName(dto.getUserName())
                    .name(dto.getName())
                    .describe(dto.getDescribe())
                    .url(dto.getUrl())
                    .documentUrl(dto.getDocumentUrl())
                    .requestMethod(dto.getRequestMethod())
                    .headers(dto.getHeaders())
                    .body(dto.getBody())
                    .queryParam(dto.getQueryParam())
                    .response(dto.getResponse())
                    .sequ(dto.getSequ())
                    .build();
            qcAiAgentModuleApiMapper.update(record);
        }
    }

    @Override
    public void delete(Long id) {
        qcAiAgentModuleApiMapper.delete(id);
    }
}
