package com.qc.agent.app.knowledge.model;

import java.util.List;

/**
 * @description：TODO
 * @author： sc
 * @create： 2024/1/23 15:24
 */
public class BatchImpQcKnowledgeFileParams {

    private Long folderId;
    private Long collectionId;

    private String dataImpType;

    private String segmentLength;

    private String categoryId;

    private String isCovered;

    private String createUserName;

    private String chunkSplitType;

    private List<QcKnowledgeFileParams> files;


    public String getChunkSplitType() {
        return chunkSplitType;
    }

    public void setChunkSplitType(String chunkSplitType) {
        this.chunkSplitType = chunkSplitType;
    }

    public String getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId;
    }

    public String getIsCovered() {
        return isCovered;
    }

    public void setIsCovered(String isCovered) {
        this.isCovered = isCovered;
    }

    public Long getFolderId() {
        return folderId;
    }

    public void setFolderId(Long folderId) {
        this.folderId = folderId;
    }

    public String getDataImpType() {
        return dataImpType;
    }

    public void setDataImpType(String dataImpType) {
        this.dataImpType = dataImpType;
    }

    public String getSegmentLength() {
        return segmentLength;
    }

    public void setSegmentLength(String segmentLength) {
        this.segmentLength = segmentLength;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public List<QcKnowledgeFileParams> getFiles() {
        return files;
    }

    public void setFiles(List<QcKnowledgeFileParams> files) {
        this.files = files;
    }

    public Long getCollectionId() {
        return collectionId;
    }

    public void setCollectionId(Long collectionId) {
        this.collectionId = collectionId;
    }
}
