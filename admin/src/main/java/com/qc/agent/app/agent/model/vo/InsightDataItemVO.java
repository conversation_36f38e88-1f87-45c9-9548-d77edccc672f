package com.qc.agent.app.agent.model.vo;

import java.io.Serializable;
import java.util.List;

import com.alibaba.fastjson.annotation.JSONField;

import lombok.Data;

/**
 * 洞察数据项视图对象
 * 用于查询详情接口中支持 itemDetail 字段的数据项展示
 *
 * <AUTHOR>
 */
@Data
public class InsightDataItemVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private String itemCode;
    private String itemName;
    private String queryBusinessCode;
    private String dataTypeCode;
    private String placeholderName;
    private String description;
    private Integer sortOrder;
    private String queryValue;

    // 树形结构支持字段
    private Long parentId;
    private List<InsightDataItemVO> children;

    /**
     * 详情数据项列表（用于DETAIL类型数据项的子项展示）
     * 当数据项类型为DETAIL时，children字段的别名
     */
    @JSONField(name = "itemDetail")
    private List<InsightDataItemVO> itemDetail;

    /**
     * 是否被选中（用于前端展示）
     */
    private Boolean selected = false;

    /**
     * 判断是否为父级数据项
     * 
     * @return true表示是父级，false表示是子级
     */
    public boolean isParent() {
        return parentId == null;
    }

    /**
     * 获取数据项的层级深度
     * 
     * @return 层级深度，从0开始
     */
    public int getLevel() {
        if (itemCode == null) {
            return 0;
        }
        return (int) itemCode.chars().filter(ch -> ch == '.').count();
    }
}
