package com.qc.agent.app.knowledge.service.impl;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.lang.generator.SnowflakeGenerator;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.qc.agent.app.knowledge.mapper.KnowledgeFileMapper;
import com.qc.agent.app.knowledge.mapper.KnowledgeQuestionAnswerSettingMapper;
import com.qc.agent.app.knowledge.mapper.QcKnowledgeExcelDataMapper;
import com.qc.agent.app.knowledge.model.*;
import com.qc.agent.app.knowledge.service.KnowledgeFileService;
import com.qc.agent.app.knowledge.utils.ExcelHandler;
import com.qc.agent.app.knowledge.utils.KnowledgeUtils;
import com.qc.agent.app.question.pojo.QcKnowledgeQuestion;
import com.qc.agent.app.question.service.QcKnowledgeService;
import com.qc.agent.common.core.Message;
import com.qc.agent.common.core.RequestHolder;
import com.qc.agent.common.core.TenantUser;
import com.qc.agent.common.util.UUIDUtils;
import com.qc.agent.platform.config.FileProperty;
import com.qc.agent.platform.file.dto.UploadRequest;
import com.qc.agent.platform.file.service.FileUploadService;
import com.qc.agent.platform.pojo.AjaxPage;
import com.qc.agent.platform.register.UserManager;
import com.qc.agent.storage.StorageClient;
import com.qc.agent.vectordb.pojo.SearchContent;
import com.qc.agent.vectordb.pojo.VectorDbResponse;
import com.qc.agent.vectordb.tencentdb.tencentDbClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.poi.ss.usermodel.DataFormat;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.xssf.usermodel.*;
import org.dromara.x.file.storage.core.FileInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.io.File;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @className KnowledgeFileServiceImpl
 * @description TODO
 * @date 2024/1/23 10:52
 */
@Component
@Slf4j
public class KnowledgeFileServiceImpl implements KnowledgeFileService {
    @Autowired
    private KnowledgeFileMapper knowledgeFileMapper;

    @Resource
    private QcKnowledgeExcelDataMapper qcKnowledgeExcelDataMapper;

    @Autowired
    private tencentDbClient dbClient;

    @Autowired
    private FileProperty fileProperty;

    @Resource
    private KnowledgeQuestionAnswerSettingMapper knowledgeQuestionAnswerSettingMapper;

    @Resource
    private QcKnowledgeService qcKnowledgeService;

    @Resource
    FileUploadService fileUploadService;

    private final ExecutorService fileUploadThreadPool = Executors.newFixedThreadPool(20);

    @Override
    public int insertQcKnowledgeFile(QcKnowledgeFileParams qcKnowledgeFileParams) {
        // 先根据相同文件名逻辑删除
        int i = knowledgeFileMapper.deleteByFileName(qcKnowledgeFileParams.getFileName(), qcKnowledgeFileParams.getCollectionId());
        if (Objects.isNull(qcKnowledgeFileParams.getFolderId())) {
            qcKnowledgeFileParams.setFolderId(1L);
        }
        Long id = UUIDUtils.getUUID2Long();
        qcKnowledgeFileParams.setId(id);
        qcKnowledgeFileParams.setCreateTime(new Date());
        qcKnowledgeFileParams.setModifyTime(new Date());
        qcKnowledgeFileParams.setCreateUserId(UserManager.getTenantUser().getUserId());
        return knowledgeFileMapper.insertQcKnowledgeFile(qcKnowledgeFileParams);
    }

    @Override
    public int batchInsertQcKnowledgeFile(BatchImpQcKnowledgeFileParams batchImpQcKnowledgeFileParams) {
        if ("1".equals(batchImpQcKnowledgeFileParams.getIsCovered())) {
            batchImpQcKnowledgeFileParams.getFiles().forEach(this::deleteFile);
        }

        String databaseName = "db-" + UserManager.getTenantUser().getTenantId(); //企业数据库
        String collectionName = "collectionVie-" + batchImpQcKnowledgeFileParams.getCollectionId(); //知识库
        int total = 0;
        if (batchImpQcKnowledgeFileParams != null &&
                CollectionUtils.isNotEmpty(batchImpQcKnowledgeFileParams.getFiles())) {
            for (QcKnowledgeFileParams qcKnowledgeFileParam : batchImpQcKnowledgeFileParams.getFiles()) {
                if (StringUtils.isNotEmpty(qcKnowledgeFileParam.getFileUrl())) {

                    // 往向量库中放入文件
                    int chunkCount = loadFile(qcKnowledgeFileParam.getFileUrl(), batchImpQcKnowledgeFileParams.getDataImpType(), databaseName, collectionName, batchImpQcKnowledgeFileParams.getChunkSplitType());

                    // qc_knowledge_file_param 表中插入数据
                    int i = insertQcKnowledgeFileData(qcKnowledgeFileParam, batchImpQcKnowledgeFileParams, chunkCount);
                    total += i;
                }
            }
        }
        return total;
    }

    private void deleteFile(QcKnowledgeFileParams qcKnowledgeFileParam) {
        QcKnowledgeFile qcKnowledgeFile = knowledgeFileMapper.queryByNameAndCollectionId(qcKnowledgeFileParam.getFileName(), qcKnowledgeFileParam.getCollectionId());
        if (qcKnowledgeFile != null && StringUtils.isNotEmpty(qcKnowledgeFile.getFileUrl())) {
            dbClient.initClient();
            String fileName = extractFileName(qcKnowledgeFile.getFileUrl());
            String dataBaseName, collectionName;
            dataBaseName = "db-" + UserManager.getTenantUser().getTenantId();
            collectionName = "collectionVie-" + qcKnowledgeFile.getCollectionId();
            log.info("dbClient.deleteKnowledgeFile, dataBaseName:{}, collectionName:{}, fileName:{}", dataBaseName, collectionName, fileName);
            VectorDbResponse response = dbClient.deleteKnowledgeFile(dataBaseName, collectionName, fileName);
            log.info("dbClient.deleteKnowledgeFile, response:{}", response);
        }
        knowledgeFileMapper.deleteByFileName(qcKnowledgeFileParam.getFileName(), qcKnowledgeFileParam.getCollectionId());
    }

    private int insertQcKnowledgeFileData(QcKnowledgeFileParams qcKnowledgeFileParam, BatchImpQcKnowledgeFileParams batchImpQcKnowledgeFileParams, int chunkCount) {
        qcKnowledgeFileParam.setFolderId(batchImpQcKnowledgeFileParams.getFolderId());
        qcKnowledgeFileParam.setCollectionId(batchImpQcKnowledgeFileParams.getCollectionId());
        qcKnowledgeFileParam.setDataImpType(batchImpQcKnowledgeFileParams.getDataImpType());
        qcKnowledgeFileParam.setFileStatus(QcKnowledgeFileParams.ENABLE_FILE_STATUS);
        qcKnowledgeFileParam.setViewCount(String.valueOf(chunkCount));
        qcKnowledgeFileParam.setCreateUserName(batchImpQcKnowledgeFileParams.getCreateUserName());
        qcKnowledgeFileParam.setCategoryId(batchImpQcKnowledgeFileParams.getCategoryId());
        return insertQcKnowledgeFile(qcKnowledgeFileParam);
    }

    private int loadFile(String filePath, String dataImpType, String databaseName, String collectionName, String chunkSplitType) {
        String localPath = "";
        try {
            String fileUlr = getFileFullPath(filePath);
            log.info("fileUlr:{}", fileUlr);
            StorageClient storageClient = StorageClient.of().buildDownloader(fileUlr);
            localPath = storageClient.downloadStoragePath("/dltemp/" + UserManager.getTenantUser().getTenantId());
            log.info("本次上传文件路径:{}", localPath);
            VectorDbResponse vectorDbResponse;
            if ("QA".equals(dataImpType)) {
                log.info("dbClient.loadAndSplitQAFile,databaseName:{},collectionName:{},localPath:{}", databaseName, collectionName, localPath);
                dbClient.loadAndSplitQAFile(databaseName, collectionName, localPath);
            } else {
                dbClient.loadAndSplitFile(databaseName, collectionName, localPath, chunkSplitType);
            }
            //查询最后分段的数量有多少
            return dbClient.getChunkCount(databaseName, collectionName, new File(localPath).getName());
        } catch (Exception e) {
            log.error("load and split file error", e);
        }
        return 0;
    }

    @Override
    public AjaxPage queryList(QcKnowledgeFileListParams qcKnowledgeFileParams) {
        // 添加内容查找文件名称
        if (StringUtils.isNotEmpty(qcKnowledgeFileParams.getFileName())) {
            qcKnowledgeFileParams.setFileNames(searchFileNameWithContent(qcKnowledgeFileParams));
        }
        AjaxPage data = new AjaxPage();
        int total = knowledgeFileMapper.countFile(qcKnowledgeFileParams);
        data.setTotal(total);
        if (total > 0) {
            qcKnowledgeFileParams.doPage();
            List<QcKnowledgeFile> rows = knowledgeFileMapper.queryFileList(qcKnowledgeFileParams);
            formatFileUrl(rows);
            data.setRows(rows);
        }
        return data;
    }

    private Set<String> searchFileNameWithContent(QcKnowledgeFileListParams params) {
        QcKnowledgeQuestion question = new QcKnowledgeQuestion();
        question.setQuestionContent(params.getFileName());
        question.setCollectionId(params.getCollectionId());
        List<SearchContent> data = qcKnowledgeService.search(question);
        // 判断data是否为空
        if (CollectionUtils.isEmpty(data)) {
            return new HashSet<>();
        } else {
            return data.stream().map(SearchContent::getDocumentName).collect(Collectors.toSet());
        }
    }

    private void formatFileUrl(List<QcKnowledgeFile> rows) {
        log.info("fileProperty-url:{}, fileProperty-rootpath:{}", fileProperty.getUrl(), fileProperty.getRootPath());
        for (QcKnowledgeFile row : rows) {
            if (StringUtils.isNotEmpty(row.getFileUrl())) {
                row.setFileUrl(getFileFullPath(row.getFileUrl()));

            }
        }
    }

    @Override
    public Message updateFileStatus(QcKnowledgeFileParams qcKnowledgeFileParams) {
        QcKnowledgeFile qcKnowledgeFile = knowledgeFileMapper.queryById(qcKnowledgeFileParams.getId());

        if (qcKnowledgeFile.getFileStatus().equals(qcKnowledgeFileParams.getFileStatus()) && StringUtils.isEmpty(qcKnowledgeFileParams.getFileName())) {
            return Message.of().error("文件名称不能为空");
        }
        if (qcKnowledgeFile != null) {

            if (!qcKnowledgeFile.getFileName().equals(qcKnowledgeFileParams.getFileName())) {
                if (knowledgeFileMapper.countByFileNameAndCollectionId(qcKnowledgeFileParams.getFileName(), qcKnowledgeFile.getCollectionId()) > 0) {
                    return Message.of().error(String.format("已存在与%s同名文件，请先修改文件名后再次上传。", qcKnowledgeFileParams.getFileName()));
                }
            }

            boolean isModify = false;

            if (StringUtils.isNotEmpty(qcKnowledgeFileParams.getFileName())) {
                isModify = true;
            }
            if (("QA".equals(qcKnowledgeFile.getDataImpType()) || "PICTURE".equals(qcKnowledgeFile.getDataImpType())) && isModify) {
                dbClient.initClient();
                //结构化文档
                String qaDatabaseName = "QA-database-" + UserManager.getTenantUser().getTenantId();
                String qaCollectionName = "QA-collection-" + qcKnowledgeFile.getCollectionId();
                dbClient.updateDocName(qaDatabaseName, qaCollectionName, qcKnowledgeFile.getFileName(), qcKnowledgeFileParams.getFileName(), qcKnowledgeFileParams.getCategoryId());
            }

            knowledgeFileMapper.updateFileStatus(qcKnowledgeFileParams);
            if (QcKnowledgeFileParams.DELETE_FILE_STATUS.equals(qcKnowledgeFileParams.getFileStatus())) {
                dbClient.initClient();
                String fileName = extractFileName(qcKnowledgeFile.getFileUrl());
                String dataBaseName, collectionName;
                if (("QA".equals(qcKnowledgeFile.getDataImpType()) || "PICTURE".equals(qcKnowledgeFile.getDataImpType()))) {
                    dataBaseName = "QA-database-" + UserManager.getTenantUser().getTenantId();
                    collectionName = "QA-collection-" + qcKnowledgeFile.getCollectionId();
                    log.info("dbClient.deleteQAFile, dataBaseName:{}, collectionName:{}, fileName:{}", dataBaseName, collectionName, fileName);
                    VectorDbResponse response = dbClient.deleteQAFile(dataBaseName, collectionName, fileName);
                    log.info("dbClient.deleteQAFile, response:{}", response);
                } else {
                    dataBaseName = "db-" + UserManager.getTenantUser().getTenantId();
                    collectionName = "collectionVie-" + qcKnowledgeFile.getCollectionId();
                    log.info("dbClient.deleteKnowledgeFile, dataBaseName:{}, collectionName:{}, fileName:{}", dataBaseName, collectionName, fileName);
                    VectorDbResponse response = dbClient.deleteKnowledgeFile(dataBaseName, collectionName, fileName);
                    log.info("dbClient.deleteKnowledgeFile, response:{}", response);
                }
            } else if (!qcKnowledgeFile.getFileStatus().equals(qcKnowledgeFileParams.getFileStatus()) && ("QA".equals(qcKnowledgeFile.getDataImpType()) || "PICTURE".equals(qcKnowledgeFile.getDataImpType()))) {
                Map<String, Object> map = new HashMap<>();
                map.put("fileStatus", qcKnowledgeFileParams.getFileStatus());
                dbClient.initClient();
                String fileName = extractFileName(qcKnowledgeFile.getFileUrl());
                dbClient.updateQAField(UserManager.getTenantUser().getTenantId(), qcKnowledgeFile.getCollectionId(), fileName, map);
            }
        }
        return Message.of().ok();
    }

    public static String extractFileName(String filePath) {
        int lastSlashIndex = filePath.lastIndexOf('/');
        if (lastSlashIndex == -1) {
            // 如果没有找到'/'，则整个字符串都是文件名（或路径不正确）
            return filePath;
        }
        return filePath.substring(lastSlashIndex + 1);
    }

    @Override
    public List<String> parse(QcKnowledgeFileParams qcKnowledgeFileParams) {
        dbClient.initClient();

        String fileUlr = getFileFullPath(qcKnowledgeFileParams.getFileUrl());

        log.info("fileUlr:{}", fileUlr);
        StorageClient storageClient = StorageClient.of().buildDownloader(fileUlr);
        String localPath = storageClient.downloadStoragePath("/dltemp/" + UserManager.getTenantUser().getTenantId());

        VectorDbResponse vectorDbResponse;
        try {
            if (qcKnowledgeFileParams.qaImpType()) {
                vectorDbResponse = dbClient.parseQAFile(localPath, qcKnowledgeFileParams.getSegmentation());
            } else {
                vectorDbResponse = dbClient.parseUnstructuredFile(localPath, qcKnowledgeFileParams.getSegmentation() == 0  ? 100 : qcKnowledgeFileParams.getSegmentation(), qcKnowledgeFileParams.getChunkSplitType());
            }
            if ("1".equals(MapUtils.getString(vectorDbResponse, "code"))) {
                return (List<String>) vectorDbResponse.get("data");
            }
        } catch (Exception e) {
            log.error("parse file error", e);
        }
        return Lists.newArrayList();
    }


    @Override
    public List<SearchContent> searchContent(QcKnowledgeFileParams qcKnowledgeFileParams) {
        String databaseName = "db-" + UserManager.getTenantUser().getTenantId(); //企业数据库
        String collectionName = "collectionVie-" + qcKnowledgeFileParams.getCollectionId(); //知识库
        String localPath = qcKnowledgeFileParams.getFileUrl();
        dbClient.initClient();
        KnowledgeQuestionAnswerSettingInfo settingInfo = queryQuestionAnswerSetting();
        double scoreThresh = 0.4;
        if (settingInfo != null && StringUtils.isNotEmpty(settingInfo.getThreshold())) {
            scoreThresh = Double.parseDouble(settingInfo.getThreshold());
        }
        VectorDbResponse result = dbClient.searchFileContent(databaseName, collectionName, new File(localPath).getName(), "ss", qcKnowledgeFileParams.getSegmentation(), scoreThresh);
        return (List<SearchContent>) result.get("data");
    }

    @Override
    public Map<String, String> findFileNameMapping(FileNameQueryParam fileNameQueryParam) {
        Map<String, String> fileNameMapping = Maps.newHashMap();
        List<String> ossFileNames = fileNameQueryParam.getOssFileName();
        if (CollectionUtils.isNotEmpty(ossFileNames)) {
            for (String ossFileName : ossFileNames) {
                String fileName = knowledgeFileMapper.queryFileNameByOSSUrl(String.format("knowledgeBase/%s", ossFileName));
                if (StringUtils.isNotEmpty(fileName)) {
                    fileNameMapping.put(ossFileName, fileName);
                }
            }
        }
        return fileNameMapping;
    }

    @Override
    public void saveQA(QaSaveRequest request) {
        // 参数断言
        Assert.notNull(request.getCollectionId(), "collectionId is null");
        Assert.notEmpty(request.getFilePaths(), "filePaths is empty");

        if ("1".equals(request.getIsCovered())) {
            deleteQAFile(request);
        }

        // 知识库ID
        String collectionId = request.getCollectionId();

        // 创建qa知识库
        String qaDataBaseName = "QA-database-" + UserManager.getTenantUser().getTenantId();
        String qaCollectionName = "QA-collection-" + collectionId;
        dbClient.initClient();
        dbClient.createQADatabase(qaDataBaseName);
        dbClient.createQACollection(qaDataBaseName, qaCollectionName);

        for (String filePath : request.getFilePaths()) {
            String fileFullPath = getFileFullPath(filePath);
            List<String[]> data = ExcelHandler.readExcel(fileFullPath).stream().map(Pair::getRight).flatMap(list -> list.stream().skip(1)).collect(Collectors.toList());
            FileInfo fileInfo = StorageClient.of().getFileInfo(fileFullPath);
            QcKnowledgeFileParams qcKnowledgeFileParams = saveExcelQcKnowledgeFile(request.getCreateUserName(), fileInfo, data, collectionId, request.getCategoryId());
            // qc_knowledge_excel_data表插入数据，并调ai接口保存qa问答对
            saveDbClientQa(data, qcKnowledgeFileParams, collectionId, request.getCategoryId());
        }

    }

    private void deleteQAFile(QaSaveRequest request) {
        request.getFilePaths().forEach(path -> {
            String fileFullPath = getFileFullPath(path);
            FileInfo fileInfo = StorageClient.of().getFileInfo(fileFullPath);

            QcKnowledgeFile qcKnowledgeFile = knowledgeFileMapper.queryByNameAndCollectionId(fileInfo.getFilename(), Long.valueOf(request.getCollectionId()));
            if (qcKnowledgeFile == null) {
                return;
            }
            knowledgeFileMapper.deleteByFileName(fileInfo.getFilename(), Long.valueOf(request.getCollectionId()));
            qcKnowledgeExcelDataMapper.deleteByFileId(qcKnowledgeFile.getId());

            dbClient.initClient();
            String dataBaseName, collectionName;
            dataBaseName = "QA-database-" + UserManager.getTenantUser().getTenantId();
            collectionName = "QA-collection-" + request.getCollectionId();
            log.info("dbClient.deleteQAFile, dataBaseName:{}, collectionName:{}, fileName:{}", dataBaseName, collectionName, fileInfo.getFilename());
            VectorDbResponse response = dbClient.deleteQAFile(dataBaseName, collectionName, fileInfo.getFilename());
            log.info("dbClient.deleteQAFile, response:{}", response);
        });
    }

    @Override
    public List<String[]> getChunks(String filePath) {
        // 获取目标文件内容
        String fullPath = getFileFullPath(filePath);
        List<Pair<String, List<String[]>>> targetData = ExcelHandler.readExcel(fullPath);

        String[] templateHeader = new String[]{"Q", "A"};

        // 获取目标文件的表头（第一行）
        if (CollectionUtils.isNotEmpty(targetData)) {
            String[] actualHeader = targetData.get(0).getRight().get(0);
            if (!Arrays.equals(templateHeader, actualHeader)) {
                throw new RuntimeException("请按照模板格式上传文件");
            }

            // 返回数据（跳过表头）
            return targetData.stream()
                    .map(Pair::getRight)
                    .flatMap(list -> list.stream().skip(1))
                    .collect(Collectors.toList());
        }
        return Collections.emptyList();

    }


    @Override
    public AjaxPage listExcelFileQA(ListExcelFileQAParams listExcelFileQAParams) {
        AjaxPage data = new AjaxPage();
        int total = qcKnowledgeExcelDataMapper.countQAByFileId(listExcelFileQAParams);
        if (total > 0) {
            data.setTotal(total);
            listExcelFileQAParams.doPage();
            List<QcKnowledgeExcelData> rows = qcKnowledgeExcelDataMapper.listQAByFileId(listExcelFileQAParams);
            data.setRows(rows);
        }
        return data;
    }

    @Override
    public Message addExcelFileQA(QcKnowledgeExcelData qcKnowledgeExcelData) {
        dbClient.initClient();
        String qaDatabase = "QA-database-" + UserManager.getTenantUser().getTenantId();
        qcKnowledgeExcelData.setId(UUIDUtils.getUUID2Long());
        qcKnowledgeExcelData.setStatus("1");
        qcKnowledgeExcelData.setCreateTime(new Date());
        qcKnowledgeExcelData.setCreateUserId(UserManager.getTenantUser().getUserId());
        qcKnowledgeExcelData.setCreateUserName(UserManager.getTenantUser().getUserName());
        qcKnowledgeExcelDataMapper.insertWithMaxSequ(qcKnowledgeExcelData);
        QcKnowledgeFile qcKnowledgeFile = knowledgeFileMapper.queryById(qcKnowledgeExcelData.getFileId());
        if (qcKnowledgeFile != null) {
            String collectionName = "QA-collection-" + qcKnowledgeFile.getCollectionId();
            dbClient.insertQA(qaDatabase, collectionName, qcKnowledgeFile.getFileName(), qcKnowledgeExcelData.getQuestion(), qcKnowledgeExcelData.getAnswer(), String.valueOf(qcKnowledgeExcelData.getId()), qcKnowledgeFile.getCategoryId());
            // 重新上传文件
            reUploadFile(qcKnowledgeFile.getFileUrl(), qcKnowledgeExcelData.getFileId());
        }
        return Message.of().ok();
    }

    private void reUploadFile(String fileUrl, Long fileId) {
        List<QcKnowledgeExcelData> qcKnowledgeExcelDataList = qcKnowledgeExcelDataMapper.findByFileId(fileId);
        List<String[]> collect = qcKnowledgeExcelDataList.stream().map(item -> new String[]{item.getQuestion(), item.getAnswer()}).collect(Collectors.toList());
        String filePath = String.format("%s/%s", UserManager.getTenantUser().getTenantId(), fileUrl);
        TenantUser tenantUser = UserManager.getTenantUser();
        fileUploadThreadPool.execute(() -> {
            RequestHolder.setThreadLocalUser(tenantUser);
            ExcelHandler.writeExcel(new String[]{"Q", "A"}, collect, filePath);
            String fileFullPath = getFileFullPath(filePath);
            List<String[]> data = ExcelHandler.readExcel(fileFullPath).stream().map(Pair::getRight).flatMap(list -> list.stream().skip(1)).collect(Collectors.toList());
            FileInfo fileInfo = StorageClient.of().getFileInfo(fileFullPath);
            QcKnowledgeFileParams qcKnowledgeFileParams = new QcKnowledgeFileParams();
            qcKnowledgeFileParams.setId(fileId);
            qcKnowledgeFileParams.setFileSize(String.valueOf(fileInfo.getSize()));
            qcKnowledgeFileParams.setViewCount(String.valueOf(data.size()));
            knowledgeFileMapper.updateFile(qcKnowledgeFileParams);
        });
    }

    @Override
    public Message editExcelFileQA(QcKnowledgeExcelData qcKnowledgeExcelData) {
        dbClient.initClient();
        String qaDatabase = "QA-database-" + UserManager.getTenantUser().getTenantId();
        qcKnowledgeExcelData.setModifyUserId(UserManager.getTenantUser().getUserId());
        qcKnowledgeExcelData.setModifyUserName(UserManager.getTenantUser().getUserName());
        qcKnowledgeExcelDataMapper.updateByPrimaryKeySelective(qcKnowledgeExcelData);
        QcKnowledgeExcelData data = qcKnowledgeExcelDataMapper.selectByPrimaryKey(qcKnowledgeExcelData.getId());
        QcKnowledgeFile qcKnowledgeFile = knowledgeFileMapper.queryById(data.getFileId());
        if (qcKnowledgeFile != null) {
            String collectionName = "QA-collection-" + qcKnowledgeFile.getCollectionId();
            dbClient.updateQ(qaDatabase, collectionName, qcKnowledgeFile.getFileName(), String.valueOf(qcKnowledgeExcelData.getId()), qcKnowledgeExcelData.getQuestion());
            dbClient.updateA(qaDatabase, collectionName, qcKnowledgeFile.getFileName(), String.valueOf(qcKnowledgeExcelData.getId()), qcKnowledgeExcelData.getAnswer());
            // 重新上传文件
            reUploadFile(qcKnowledgeFile.getFileUrl(), qcKnowledgeFile.getId());
        }
        return Message.of().ok();
    }

    private boolean checkSameResponse(List<SearchContent> searchContents, Long dataId) {
        return CollectionUtils.isNotEmpty(searchContents)
                && searchContents.size() == 1
                && String.valueOf(dataId).equals(searchContents.get(0).getContentId());
    }

    @Override
    public Message deleteExcelFileQA(QcKnowledgeExcelData qcKnowledgeExcelData) {
        QcKnowledgeExcelData qcKnowledgeExcelData1 = qcKnowledgeExcelDataMapper.selectByPrimaryKey(qcKnowledgeExcelData.getId());
        Long fileId = qcKnowledgeExcelData1.getFileId();
        QcKnowledgeFile qcKnowledgeFile = knowledgeFileMapper.queryById(fileId);
        if (qcKnowledgeFile != null) {
            dbClient.initClient();
            String qaDatabase = "QA-database-" + UserManager.getTenantUser().getTenantId();
            String collectionName = "QA-collection-" + qcKnowledgeFile.getCollectionId();
            log.info("dbClient.deleteQuestion, qaDatabase:{}, collectionName:{}, id:{}", qaDatabase, collectionName, String.valueOf(qcKnowledgeExcelData.getId()));
            VectorDbResponse vectorDbResponse = dbClient.deleteQuestion(qaDatabase, collectionName, String.valueOf(qcKnowledgeExcelData.getId()));
            log.info("dbClient.deleteQuestion,response:{}", JSON.toJSONString(vectorDbResponse));
        }
        qcKnowledgeExcelDataMapper.deleteById(qcKnowledgeExcelData.getId());
        reUploadFile(qcKnowledgeFile.getFileUrl(), qcKnowledgeFile.getId());
        return Message.of().ok();
    }

    @Override
    public XSSFWorkbook generateImportTemplate() {
        // 创建工作簿对象
        XSSFWorkbook wb = new XSSFWorkbook();
        // 创建工作表对象
        XSSFSheet sheet = wb.createSheet("sheet1");
        // 创建文件内容普通样式
        XSSFCellStyle normalStyle = createNormalCellStyle(wb);
        // 设置为文本格式
        DataFormat format = wb.createDataFormat();
        normalStyle.setDataFormat(format.getFormat("@"));
        // 表头
        XSSFRow rowTitle = sheet.createRow(0);

        XSSFCellStyle titleStyleBlack = createTitleCellStyle(wb);

        List<String> heads = Lists.newArrayList("Q", "A");

        for (int i = 0; i < heads.size(); i++) {
            sheet.setColumnWidth(i, 30 * 256);
            createCell(rowTitle.createCell(i), heads.get(i), null, null, titleStyleBlack);
        }

        return wb;
    }

    @Override
    public Message uploadQA(QaUploaderRequest request) {
        Assert.notNull(request.getFile(), "文件不能为空");
        Assert.notNull(request.getCollectionId(), "知识库标识不能为空");

        String fileName = org.springframework.util.StringUtils.cleanPath(
                Objects.requireNonNull(request.getFile().getOriginalFilename()));

        /*重复检测*/
        if (knowledgeFileMapper.countByFileNameAndCollectionId(fileName, request.getCollectionId()) > 0) {
            return Message.of().error(String.format("已存在与%s同名文件，请先修改文件名后再次上传。", fileName));
        }

        // 上传
        List<FileInfo> fileInfos = fileUploadService.upload(
                UploadRequest
                        .builder()
                        .uploadDir(String.format("%s/%s", UserManager.getTenantUser().getTenantId(), KnowledgeUtils.getUploadDir(request.getCollectionId())))
                        .files(Lists.newArrayList(request.getFile()))
                        .useDetermineFileName(true)
                        .build());

        return Message.of().data(fileInfos).ok("上传成功！");
    }


    @Override
    public Message findRawFileInfo(QcKnowledgeFile qcKnowledgeFile) {
        dbClient.initClient();
        QcKnowledgeFile existFile = knowledgeFileMapper.queryById(qcKnowledgeFile.getId());
        if (existFile != null) {
            String databaseName = "db-" + UserManager.getTenantUser().getTenantId(); //企业数据库
            String collectionName = "collectionVie-" + existFile.getCollectionId(); //知识库
            VectorDbResponse fileInfo = dbClient.getFileInfo(databaseName, collectionName, existFile.getOriginName(), 1000);
            return Message.of().ok().data(fileInfo.getData());
        }
        return Message.of().ok();
    }

    @Override
    public boolean checkDuplicateFileName(BatchImpQcKnowledgeFileParams batchImpQcKnowledgeFileParams) {
        batchImpQcKnowledgeFileParams.getFiles().forEach(qcKnowledgeFileParams -> {
            if (StringUtils.isNotEmpty(qcKnowledgeFileParams.getFileName())) {
                int count = knowledgeFileMapper.countByFileNameAndCollectionId(qcKnowledgeFileParams.getFileName(), batchImpQcKnowledgeFileParams.getCollectionId());
                if (count > 0) {
                    throw new RuntimeException(String.format("已存在与%s同名文件，请先修改文件名后再次上传。", qcKnowledgeFileParams.getFileName()));
                }
            }
        });

        return false;
    }

    private void createCell(XSSFCell cell, String cellValue, XSSFComment comment, XSSFRichTextString comStr, XSSFCellStyle style) {
        cell.setCellValue(cellValue);
        if (comStr != null && comment != null) {
            comment.setString(comStr);
            cell.setCellComment(comment);
        }
        if (style != null) {
            cell.setCellStyle(style);
        }
    }

    private XSSFCellStyle createNormalCellStyle(XSSFWorkbook workBook) {
        XSSFCellStyle style = workBook.createCellStyle();
        XSSFFont font = workBook.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 11);
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        return style;
    }

    private XSSFCellStyle createTitleCellStyle(XSSFWorkbook workBook) {
        XSSFCellStyle style = workBook.createCellStyle();
        XSSFFont font = workBook.createFont();
        font.setFontName("宋体");
        style.setFont(font);
        return style;
    }

    private void saveDbClientQa(List<String[]> data, QcKnowledgeFileParams qcKnowledgeFileParams, String collectionId, String categoryId) {
        if (CollectionUtils.isNotEmpty(data)) {
            List<QcKnowledgeExcelData> qcKnowledgeExcelData = insertQcKnowledgeExcelData(data, qcKnowledgeFileParams.getId());
            // 调ai接口保存qa问答对
            insertDbClientQA(qcKnowledgeExcelData, qcKnowledgeFileParams.getFileName(), collectionId, categoryId);
        }
    }

    private void insertDbClientQA(List<QcKnowledgeExcelData> qcKnowledgeExcelData, String fileName, String collectionId, String categoryId) {
        String qaDataBaseName = "QA-database-" + UserManager.getTenantUser().getTenantId();
        String qaCollectionName = "QA-collection-" + collectionId;
        for (QcKnowledgeExcelData qcKnowledgeExcelDatum : qcKnowledgeExcelData) {
            VectorDbResponse vectorDbResponse = dbClient.insertQA(qaDataBaseName, qaCollectionName, fileName,
                    qcKnowledgeExcelDatum.getQuestion(), qcKnowledgeExcelDatum.getAnswer(), String.valueOf(qcKnowledgeExcelDatum.getId()), categoryId);
            log.info("dbClient.insertQA, response:{}", JSON.toJSONString(vectorDbResponse));
        }
    }

    private QcKnowledgeFileParams saveExcelQcKnowledgeFile(String createUserName, FileInfo fileInfo, List<String[]> data, String collectionId, String categoryId) {
        QcKnowledgeFileParams qcKnowledgeFileParam = new QcKnowledgeFileListParams();
        qcKnowledgeFileParam.setCollectionId(Long.valueOf(collectionId));
        qcKnowledgeFileParam.setDataImpType("QA");
        qcKnowledgeFileParam.setFileStatus(QcKnowledgeFileParams.ENABLE_FILE_STATUS);
        qcKnowledgeFileParam.setViewCount(String.valueOf(data.size()));
        qcKnowledgeFileParam.setFileName(fileInfo.getFilename());
        qcKnowledgeFileParam.setFileUrl("knowledgeBase" + "/" + collectionId + "/" + fileInfo.getFilename());
        qcKnowledgeFileParam.setFileSize(String.valueOf(fileInfo.getSize()));
        qcKnowledgeFileParam.setFileType(FilenameUtils.getExtension(fileInfo.getFilename()));
        qcKnowledgeFileParam.setCreateUserName(createUserName);
        qcKnowledgeFileParam.setCategoryId(categoryId);
        insertQcKnowledgeFile(qcKnowledgeFileParam);
        return qcKnowledgeFileParam;
    }

    private List<QcKnowledgeExcelData> insertQcKnowledgeExcelData(List<String[]> qaData, Long fileId) {
        List<List<String[]>> partition = Lists.partition(qaData, 500);
        Long sequ = 0L;
        Set<String> questionSet = new HashSet<>();
        List<QcKnowledgeExcelData> datas = Lists.newArrayList();
        for (List<String[]> qaLst : partition) {
            List<QcKnowledgeExcelData> existingData = Lists.newArrayList();
            for (String[] qa : qaLst) {
                if (qa != null && qa.length == 2 && questionSet.add(qa[0])) {
                    QcKnowledgeExcelData data = new QcKnowledgeExcelData();
                    data.setId(UUIDUtils.getUUID2Long());
                    data.setStatus("1");
                    data.setCreateTime(new Date());
                    data.setCreateUserId(UserManager.getTenantUser().getUserId());
                    data.setCreateUserName(UserManager.getTenantUser().getUserName());
                    data.setQuestion(qa[0]);
                    data.setAnswer(qa[1]);
                    data.setFileId(fileId);
                    data.setSequ(sequ);
                    datas.add(data);
                    existingData.add(data);
                    sequ++;
                }
            }
            if (CollectionUtils.isNotEmpty(datas)) {
                qcKnowledgeExcelDataMapper.batchInsert(existingData);
            }
            existingData.clear();
        }
        return datas;
    }

    private KnowledgeQuestionAnswerSettingInfo queryQuestionAnswerSetting() {
        List<KnowledgeQuestionAnswerSettingInfo> list = knowledgeQuestionAnswerSettingMapper.queryList(new KnowledgeQuestionAnswerSettingParams());
        if (CollectionUtils.isNotEmpty(list)) {
            KnowledgeQuestionAnswerSettingInfo info = list.get(0);
            return info;
        } else {
            return null;
        }
    }

    private String getFileFullPath(String filePath) {
        String tenantId = UserManager.getTenantUser().getTenantId() + "";
        if (filePath.startsWith(tenantId)) {
            return String.format("%s/ai-agent/%s", fileProperty.getUrl(), filePath);
        } else {
            return String.format("%s/ai-agent/%s/%s", fileProperty.getUrl(), UserManager.getTenantUser().getTenantId(), filePath);

        }
    }
}
