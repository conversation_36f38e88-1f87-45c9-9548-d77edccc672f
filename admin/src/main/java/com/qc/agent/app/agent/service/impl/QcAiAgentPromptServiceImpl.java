package com.qc.agent.app.agent.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.qc.agent.app.agent.mapper.QcAiAgentModelMapper;
import com.qc.agent.app.agent.mapper.QcAiAgentPromptMapper;
import com.qc.agent.app.agent.model.dto.AgentPromptSaveDTO;
import com.qc.agent.app.agent.model.dto.LLAKeyProperties;
import com.qc.agent.app.agent.model.dto.PromptOptimizedDTO;
import com.qc.agent.app.agent.model.po.QcAiAgentModel;
import com.qc.agent.app.agent.model.po.QcAiAgentPrompt;
import com.qc.agent.app.agent.service.QcAiAgentPromptService;
import com.qc.agent.app.workflow.WorkflowLLAClient;
import com.qc.agent.common.core.RequestHolder;
import com.qc.agent.common.core.TenantUser;
import com.qc.agent.common.exception.BizException;
import com.qc.agent.lla.model.LLAConfig;
import com.qc.agent.lla.model.LLAProvider;
import com.qc.agent.lla.model.LLARequest;
import com.qc.agent.platform.register.UserManager;
import com.qc.agent.platform.sse.SseClient;
import com.qc.agent.platform.util.UUIDUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

@Service
public class QcAiAgentPromptServiceImpl implements QcAiAgentPromptService {

    @Resource
    private QcAiAgentPromptMapper qcAiAgentPromptMapper;

    @Resource
    private QcAiAgentModelMapper qcAiAgentModelMapper;

    @Value("${ai.agent.appsvr-domin-url}")
    private String appsvrDominUrl;

    @Resource
    private LLAKeyProperties llaKeyProperties;

    @Resource
    private SseClient client;

    @Override
    public void savePrompt(AgentPromptSaveDTO agentPromptSaveDTO) {
        if (checkNameRepeat(agentPromptSaveDTO.getId(), agentPromptSaveDTO.getName())) {
            throw new BizException("提示词名称不能重复");
        }
        if (agentPromptSaveDTO.getId() == null) {
            addPrompt(agentPromptSaveDTO);
        } else {
            updatePrompt(agentPromptSaveDTO);
        }
    }

    private boolean checkNameRepeat(Long promptId, String promptName) {
        Long total = qcAiAgentPromptMapper.countByName(promptId, promptName);
        return total != null && total > 0;
    }

    private void updatePrompt(AgentPromptSaveDTO agentPromptSaveDTO) {
        qcAiAgentPromptMapper.update(QcAiAgentPrompt.builder()
                .id(agentPromptSaveDTO.getId())
                .modifyierId(agentPromptSaveDTO.getUserId())
                .modifyierName(agentPromptSaveDTO.getUserName())
                .modifyTime(LocalDateTime.now())
                .name(agentPromptSaveDTO.getName())
                .describe(agentPromptSaveDTO.getDescribe())
                .content(agentPromptSaveDTO.getContent())
                .build());
    }

    private void addPrompt(AgentPromptSaveDTO agentPromptSaveDTO) {
        qcAiAgentPromptMapper.insert(QcAiAgentPrompt.builder()
                .id(UUIDUtils.getUUID2Long())
                .status("1")
                .creatorId(agentPromptSaveDTO.getUserId())
                .creatorName(agentPromptSaveDTO.getUserName())
                .createTime(LocalDateTime.now())
                .name(agentPromptSaveDTO.getName())
                .describe(agentPromptSaveDTO.getDescribe())
                .content(agentPromptSaveDTO.getContent())
                .build());

    }

    @Override
    public void deletePrompt(Long id, Long userId, String userName) {
        qcAiAgentPromptMapper.deleteById(id, userId, userName);
    }

    @Override
    public QcAiAgentPrompt getPromptById(Long id) {
        return qcAiAgentPromptMapper.selectById(id);
    }

    @Override
    public List<QcAiAgentPrompt> getAllPrompts() {
        return qcAiAgentPromptMapper.selectAll();
    }

    @Override
    public List<QcAiAgentPrompt> queryPrompts(String recommend, String mine) {
        return qcAiAgentPromptMapper.queryPrompts(UserManager.getTenantUser().getUserId(), recommend, mine);
    }

    @Override
    public List<QcAiAgentPrompt> searchPrompts(JSONObject jsonObject) {
        return qcAiAgentPromptMapper.searchPrompts(jsonObject);
    }

    public static final String CONDITION_PROMPT = "# 角色\n" +
            "你是一位专业且精准的提示词优化专家，能够通过结构化工作流深入理解用户意图，严格按照【%s】对【%s】进行优化，确保优化后的提示词表意清晰、逻辑严谨且更加准确地符合用户需求。\n" +
            "\n" +
            "## 工作流\n" +
            "1. 分析当前Agent提示词\n" +
            "   - 解构原始提示词的组成要素\n" +
            "   - 识别现有框架中的核心要求\n" +
            "   - 标注需要保留的约束条件\n" +
            "\n" +
            "2. 解析用户需求\n" +
            "   - 区分显性需求与隐性需求\n" +
            "   - 定位需求与当前提示词的差异点\n" +
            "   - 建立需求与优化目标的映射关系\n" +
            "\n" +
            "3. 执行优化迭代\n" +
            "   - 对比原始提示词与用户需求的契合度\n" +
            "   - 采用最小改动原则调整要素\n" +
            "   - 保持原始格式规范不变\n" +
            "\n" +
            "## 技能\n" +
            "### 技能 1: 优化提示词\n" +
            "1. 仔细分析用户给出的待优化提示词，全面理解用户需求与目标\n" +
            "2. 从语言表达、逻辑结构、指令清晰度等多方面进行考量\n" +
            "3. 运用专业知识和丰富经验，生成优化后的提示词内容，使其在准确性上有显著提升\n" +
            "\n" +
            "## 限制\n" +
            "- 仅围绕优化提示词这一任务进行操作，拒绝回答与提示词优化无关的话题\n" +
            "- 所输出的优化内容必须完整、准确地针对用户提供的原始提示词进行改进\n" +
            "- 输出的优化结果应简洁明了，避免产生歧义\n" +
            "- 严格保留原始提示词中的格式要求\n" +
            "- 禁止添加任何非用户指定的新功能\n" +
            "\n" +
            "## 响应格式\n" +
            "- 不使用Markdown语法\n" +
            "- 保持清晰的结构化层级\n" +
            "- 仅输出最终优化完成的完整提示词\n" +
            "- 完全排除说明性文字\n" +
            "- 保留原始提示词中的特殊符号使用规范";

    @Override
    public SseEmitter optimizedPrompt(PromptOptimizedDTO dto) {

        QcAiAgentModel qcAiAgentModel = qcAiAgentModelMapper.selectById(5L);
        String requestId = Objects.toString(UUIDUtils.getUUID2Long());
        LLARequest request = LLARequest.builder()
                .id(requestId)
                .clientId(requestId)
                .content(String.format(CONDITION_PROMPT, dto.getOptimizedPrompt(), dto.getCurrentAgentPrompt()))
                .start(LocalDateTime.now())
                .build();

        LLAConfig config = LLAConfig.builder()
                .apiKey(llaKeyProperties.getDeepseekApikey())
                .stream(true)
                .provider(LLAProvider.getEnumByValue(qcAiAgentModel.getVendor()))
                .model(qcAiAgentModel.getModel())
                .build();
        SseEmitter emitter = client.subscribe(request.getId());
        new Thread(wrapWithContext(() -> {
            WorkflowLLAClient.sendAsync(config, request, client);
        })).start();

        return emitter;
    }

    private Runnable wrapWithContext(Runnable task) {
        TenantUser tenantUser = UserManager.getTenantUser();
        String requestCookie = RequestHolder.getRequestCookie();
        return () -> {
            try {
                RequestHolder.setThreadLocalUser(tenantUser);
                RequestHolder.setRequestCookie(requestCookie);
                task.run();
            } finally {
                RequestHolder.clear();
            }
        };
    }
}
