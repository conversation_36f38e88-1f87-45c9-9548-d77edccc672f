package com.qc.agent.app.agent.model.vo;

import java.io.Serializable;
import java.util.List;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.qc.agent.app.agent.model.dto.DimensionPromptValueDTO;
import com.qc.agent.app.agent.model.dto.PromptFragmentDTO;
import com.qc.agent.app.agent.model.entity.InsightStandard;

import lombok.Data;

/**
 * 洞察总结配置视图对象
 *
 * <AUTHOR>
 */
@Data
public class InsightSummaryConfigVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private Long agentId;

    /**
     * 综合分析结构化提示词片段
     */
    private List<PromptFragmentDTO> comprehensivePromptFragments;

    private JSONObject comprehensivePrompt;

    private JSONObject summaryAdvicePrompt;

    /**
     * 建议结构化提示词片段
     */
    private List<PromptFragmentDTO> summaryAdvicePromptFragments;

    /**
     * 综合分析提示词片段值列表
     */
    private List<DimensionPromptValueDTO> comprehensivePromptValues;

    /**
     * 建议提示词片段值列表
     */
    private List<DimensionPromptValueDTO> summaryAdvicePromptValues;

    private List<InsightStandard> standards;
}