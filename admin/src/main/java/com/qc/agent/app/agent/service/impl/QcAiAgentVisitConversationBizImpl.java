package com.qc.agent.app.agent.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.qc.agent.app.agent.mapper.QcAiAgentConversationMapper;
import com.qc.agent.app.agent.mapper.QcAiAgentMapper;
import com.qc.agent.app.agent.mapper.QcAiAgentModelMapper;
import com.qc.agent.app.agent.mapper.QcAiAgentVisitDetailMapper;
import com.qc.agent.app.agent.model.dto.QcAiAgentChatDTO;
import com.qc.agent.app.agent.model.po.QcAiAgent;
import com.qc.agent.app.agent.model.po.QcAiAgentConversation;
import com.qc.agent.app.agent.model.po.QcAiAgentModel;
import com.qc.agent.app.agent.model.po.QcAiAgentVisitDetail;
import com.qc.agent.app.agent.model.vo.VisitAgentVO;
import com.qc.agent.app.agent.service.QcAiAgentVisitConversationBiz;
import com.qc.agent.app.agent.service.tool.QcAiAgentConversationTool;
import com.qc.agent.app.agent.support.AgentBizDataSupport;
import com.qc.agent.app.agent.util.DoubleFormatter;
import com.qc.agent.app.workflow.WorkflowLLAClient;
import com.qc.agent.app.workflow.inner.impl.VisitAssistant;
import com.qc.agent.app.workflow.inner.impl.tools.LLMTools;
import com.qc.agent.app.workflow.inner.pojo.VisitInfo;
import com.qc.agent.common.core.Message;
import com.qc.agent.common.core.RequestHolder;
import com.qc.agent.common.exception.BizException;
import com.qc.agent.lla.model.LLARequest;
import com.qc.agent.lla.model.LLAResponse;
import com.qc.agent.platform.util.UUIDUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Supplier;

import static com.qc.agent.app.agent.util.Let.of;

@Slf4j
@Service
@RequiredArgsConstructor
public class QcAiAgentVisitConversationBizImpl implements QcAiAgentVisitConversationBiz {

    private static final String VISIT_STATUS_COMPLETED = "1";
    private static final long DEFAULT_AGENT_ID = 2L;
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    private static final String PROMPT_TEMPLATE = """
            请判断以下用户问题是否与客户拜访推荐相关（例如：拜访客户、客户接待、商务拜访、客户约见、销售拜访等）。如果是，则返回1；否则返回0。
            只需返回数字1或0，无需其他解释。
            用户问题：""";

    private final QcAiAgentMapper qcAiAgentMapper;
    private final QcAiAgentModelMapper qcAiAgentModelMapper;
    private final QcAiAgentVisitDetailMapper qcAiAgentVisitDetailMapper;
    private final QcAiAgentConversationServiceImpl qcAiAgentConversationServiceImpl;
    private final VisitAssistant visitAssistant;
    private final QcAiAgentConversationMapper qcAiAgentConversationMapper;

    @Value("${ai.agent.appsvr-domin-url}")
    private String appsvrDominUrl;

    @Override
    public VisitAgentVO chat(QcAiAgentChatDTO qcAiAgentChatDTO) {
        log.info("判断问题是否为拜访类问题，agentId: {}", qcAiAgentChatDTO.getAgentId());

        QcAiAgent qcAiAgent = getAndValidateAgent(qcAiAgentChatDTO.getAgentId());
        QcAiAgentModel qcAiAgentModel = validateAgentModel(qcAiAgent.getModelId());
        QcAiAgentVisitDetail qcAiAgentVisitDetail = getVisitDetail(qcAiAgentChatDTO.getAgentId());

        String answer = buildInitialAnswer(qcAiAgentChatDTO.getAddress(),
                qcAiAgentVisitDetail.getRetrieveRadius(),
                qcAiAgentVisitDetail.getSearchScopeDays());
        QcAiAgentConversation conversation = QcAiAgentConversationTool.assembleConversation(qcAiAgentChatDTO);
        String isVisit = determineIfVisitQuestion(qcAiAgentChatDTO, conversation, qcAiAgentModel, qcAiAgent);
        // 是拜访类问题时，才插入记录
        if (Objects.equals(qcAiAgentChatDTO.getIsTest(), "1")) {
            conversation.setStatus("0");
        }
        conversation.setAnswer(answer);
        conversation.setAnswerTime(LocalDateTime.now());
        qcAiAgentConversationMapper.insert(conversation);

        VisitAgentVO visitAgentVO = buildVisitAgentVO(isVisit, buildInitialAnswer(), qcAiAgentVisitDetail, conversation);
        log.info("判断问题是否为拜访类问题，visitAgentVO: {}", JSONObject.toJSONString(visitAgentVO));
        return visitAgentVO;
    }

    @Override
    public Map<String, Object> customerVisitList(QcAiAgentChatDTO qcAiAgentChatDTO) {
        log.info("查询客户拜访数据，param: {}", JSONObject.toJSON(qcAiAgentChatDTO));

        QcAiAgent qcAiAgent = getAndPrepareAgent(DEFAULT_AGENT_ID);
        String visitData = visitAssistant.queryVisitData(qcAiAgentChatDTO.getVisitAgentParam(), qcAiAgent);
        List<VisitInfo> list = VisitAssistant.processClientVisits(JSON.parseObject(visitData));
        String id = "";
        if (!Objects.equals(qcAiAgentChatDTO.getIsTest(), "1")) {
            String answer = buildInitialAnswer(list);
            QcAiAgentConversation conversation = initConversation(qcAiAgentChatDTO, answer);
            id = conversation.getId().toString();
        }
        return Map.of("rows", list, "id", id);
    }

    private String buildInitialAnswer(List<VisitInfo> list) {
        StringBuilder answer = new StringBuilder("您可以选择全部或者部分客户添加至今日拜访计划：\n");
        list.forEach(customer ->
                answer.append(customer.getName())
                        .append(",")
                        .append(customer.getDay())
                        .append("天未拜访\n")
        );
        if (!answer.isEmpty()) {
            answer.deleteCharAt(answer.length() - 1);
        }
        return answer.toString();
    }

    private QcAiAgent getAndValidateAgent(Long agentId) {
        return Optional.ofNullable(qcAiAgentMapper.selectById(agentId))
                .orElseThrow(() -> new BizException("未找到对应的Agent"));
    }

    private QcAiAgentModel validateAgentModel(Long modelId) {
        return Optional.ofNullable(qcAiAgentModelMapper.selectById(modelId))
                .orElseThrow(() -> new BizException("该AGENT使用的模型的KEY已失效，请联系创建者/管理员调整后重新发布后再使用。"));
    }

    private QcAiAgentVisitDetail getVisitDetail(Long agentId) {
        QcAiAgentVisitDetail qcAiAgentVisitDetail = qcAiAgentVisitDetailMapper.selectByAgentId(agentId, RequestHolder.getThreadLocalUser().getUserId());
        if (qcAiAgentVisitDetail == null) {
            qcAiAgentVisitDetail = new QcAiAgentVisitDetail();
            qcAiAgentVisitDetail.setId(UUIDUtils.getUUID2Long());
            qcAiAgentVisitDetail.setStatus("1");
            qcAiAgentVisitDetail.setCreateTime(LocalDateTime.now());
            qcAiAgentVisitDetail.setCreatorId(RequestHolder.getThreadLocalUser().getUserId());
            qcAiAgentVisitDetail.setUserId(RequestHolder.getThreadLocalUser().getUserId());
            qcAiAgentVisitDetail.setAgentId(agentId);
            qcAiAgentVisitDetail.setRetrieveRadius(20d);
            qcAiAgentVisitDetail.setSearchScopeDays(180d);
            qcAiAgentVisitDetailMapper.insert(qcAiAgentVisitDetail);
        }
        return qcAiAgentVisitDetail;
    }

    public static String buildInitialAnswer() {
        return """
                好的，我将按照以下逻辑为您进行拜访客户推荐：
                1. 根据过去日均的拜访量作为推荐拜访的客户数
                2. 基于当前位置在检索半径内的区域进行检索
                3. 基于您作为客户经理的客户进行检索
                4. 考虑最近180天内有拜访过的客户，但剔除最近一周已拜访过的客户和今日已在拜访计划中的客户
                5. 考虑客户的合作状态，已合作客户优先""";
    }

    public static String buildInitialAnswer(String address, Double retrieveRadius, Double searchScopeDays) {
        return """
                好的，我将按照以下逻辑为您进行拜访客户推荐：
                1. 根据过去日均的拜访量作为推荐拜访的客户数
                2. 基于当前位置在检索半径内的区域进行检索
                3. 基于您作为客户经理的客户进行检索
                4. 考虑最近180天内有拜访过的客户，但剔除最近一周已拜访过的客户和今日已在拜访计划中的客户
                5. 考虑客户的合作状态，已合作客户优先
                以下参数可根据您的需要进行调整。
                您当前位置：%s
                检索半径:%s公里
                检索范围:%s天内有拜访
                确定按以上条件生成今日推荐的拜访客户吗？"""
                .formatted(address, DoubleFormatter.format(retrieveRadius), searchScopeDays.intValue());
    }

    private QcAiAgentConversation initConversation(QcAiAgentChatDTO dto, String answer) {
        dto.setAnswer(answer);
        dto.setAnswerTime(LocalDateTime.now());
        return qcAiAgentConversationServiceImpl.initQcAiAgentConversation(dto);
    }

    private String determineIfVisitQuestion(QcAiAgentChatDTO qcAiAgentChatDTO, QcAiAgentConversation conversation, QcAiAgentModel model, QcAiAgent qcAiAgent) {
        LLARequest request = LLARequest.builder()
                .id(conversation.getId().toString())
                .clientId(qcAiAgentChatDTO.getClientId())
                .start(LocalDateTime.now())
                .content(qcAiAgent.getSplitSqlPrompt() + qcAiAgentChatDTO.getQuestion())
                .skipAnswerUpdate(true)
                .build();

        LLAResponse response = WorkflowLLAClient.send(LLMTools.initLLAConfig(model), request);

        return Optional.ofNullable(response)
                .map(LLAResponse::getContent)
                .orElse("0");
    }

    private VisitAgentVO buildVisitAgentVO(String isVisit, String answer, QcAiAgentVisitDetail detail, QcAiAgentConversation conversation) {
        return VisitAgentVO.builder()
                .id(conversation.getId())
                .isVisit(isVisit)
                .answer(answer)
                .retrieveRadius(detail.getRetrieveRadius())
                .searchScopeDays(detail.getSearchScopeDays())
                .build();
    }

    private QcAiAgent getAndPrepareAgent(Long agentId) {
        QcAiAgent agent = getAndValidateAgent(agentId);
        agent.setDataFetchUrl("%s/%s".formatted(appsvrDominUrl, agent.getDataFetchUrl()));
        return agent;
    }

    private final static String ADD_VISIT_PLAN_URI = "app/customer_visit/common/addVisitPlanAgent.do";

    @Override
    public Message addVisitPlan(QcAiAgentChatDTO dto) {
        log.info("拜访助手-添加拜访计划");

        String url = "%s/%s".formatted(appsvrDominUrl, ADD_VISIT_PLAN_URI);
        String payload = createPayload(dto);

        // 使用 retry 封装处理重试逻辑
        boolean success = retry(3, 1000, () -> isResponseSuccess(fetch(url, payload)));

        String answer = success
                ? "好的，已处理完毕，您可以查看拜访计划。"
                : "不好意思这次拜访计划没有添加成功，您可以重新选择客户再点击【添加至拜访计划】试试。";
        String id = "";
        if (!Objects.equals(dto.getIsTest(), "1")) {
            QcAiAgentConversation conversation = initConversation(dto, answer);
            id = conversation.getId().toString();
        }
        Map<String, String> data = Map.of("id", id);

        return success ? Message.of().ok().data(data) : Message.of().error(answer).data(data);
    }

    private String createPayload(QcAiAgentChatDTO dto) {
        return of(dto.getCmNameList().stream()
                .map(cmName -> Map.of(
                        "cmName", cmName,
                        "planDate", LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE)
                ))
                .toList())
                .let(JSONArray::toJSONString); // 使用 let 是个 Kotlin 风格
    }

    // 实际请求
    private String fetch(String url, String body) {
        return AgentBizDataSupport.fetchBizData(url, body);
    }

    // 检查返回值是否成功
    private boolean isResponseSuccess(String response) {
        return Optional.ofNullable(response)
                .map(resp -> {
                    try {
                        return switch (JSONObject.parseObject(resp).getString("code")) {
                            case "1" -> true;
                            default -> false;
                        };
                    } catch (Exception e) {
                        log.error("响应解析失败", e);
                        return false;
                    }
                })
                .orElse(false);
    }

    // 通用重试逻辑：可复用、简洁
    private boolean retry(int maxRetries, long delayMillis, Supplier<Boolean> task) {
        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                if (task.get()) return true;
                log.warn("请求失败，准备重试 ({}/{})", attempt, maxRetries);
                Thread.sleep(delayMillis);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            } catch (Exception e) {
                log.error("重试出错 ({}/{})", attempt, maxRetries, e);
            }
        }
        return false;
    }


}