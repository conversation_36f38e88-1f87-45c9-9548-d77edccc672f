package com.qc.agent.app.agent.util;

import java.util.function.Function;

/**
 * <AUTHOR>
 * @date 2025-04-14
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
public class FunctionalUtils {
    /**
     * 执行函数并返回结果。
     *
     * @param input 输入值
     * @param block 执行逻辑
     * @return block 返回值
     */
    public static <T, R> R let(T input, Function<T, R> block) {
        return block.apply(input);
    }
}
