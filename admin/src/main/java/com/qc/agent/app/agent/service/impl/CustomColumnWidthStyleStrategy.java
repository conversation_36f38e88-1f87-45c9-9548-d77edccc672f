package com.qc.agent.app.agent.service.impl;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.handler.WriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.style.column.AbstractColumnWidthStyleStrategy;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Sheet;

import java.util.List;

public class CustomColumnWidthStyleStrategy extends AbstractColumnWidthStyleStrategy {
    private static final int MAX_COLUMN_WIDTH = 100;
    private static final int MIN_COLUMN_WIDTH = 10;

    @Override
    protected void setColumnWidth(WriteSheetHolder writeSheetHolder, List<WriteCellData<?>> <PERSON><PERSON><PERSON><PERSON><PERSON>,
                                  Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
        if (cell == null) {
            return;
        }

        Sheet sheet = writeSheetHolder.getSheet();
        int columnIndex = cell.getColumnIndex();
        int width;

        switch (columnIndex) {
            case 0:  // createTime
            case 3:  // answerTime
                width = 20;
                break;
            case 1:  // creatorName
            case 6:  // conversationTime
                width = 15;
                break;
            case 2:  // question
            case 4:  // answer
                width = calculateWidth(cell);
                width = Math.min(MAX_COLUMN_WIDTH, Math.max(MIN_COLUMN_WIDTH, width));
                break;
            default:
                width = calculateWidth(cell);
                width = Math.min(50, Math.max(MIN_COLUMN_WIDTH, width));
        }

        sheet.setColumnWidth(columnIndex, width * 256);
    }

    private int calculateWidth(Cell cell) {
        if (cell.getCellType() == CellType.STRING) {
            String value = cell.getStringCellValue();
            return value.chars().map(ch ->
                    Character.UnicodeBlock.of(ch) == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS ? 2 : 1
            ).sum();
        }
        return 12;
    }
}