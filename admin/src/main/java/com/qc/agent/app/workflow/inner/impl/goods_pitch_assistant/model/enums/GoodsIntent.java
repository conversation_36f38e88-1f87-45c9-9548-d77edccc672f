package com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.model.enums;

/**
 * <AUTHOR>
 * @date 2025-06-26
 */
public enum GoodsIntent {
    // 商品如何卖进相关-流式回答
    SALES_INTO,
    // 商品如何卖进相关-缺少条件，需要前端展示选择框
    SALES_INTO_INPUT,
    // 列举某个商品竞品内容
    LIST_COMPETITORS,
    // 和竞品对比
    DIRECT_COMPARISON,
    // 某客户销售商品信息及价格方案
    CUSTOMER_SCHEME,
    // 某客户销售商品信息及价格方案-缺少条件，需要前端展示选择框
    CUSTOMER_SCHEME_INPUT,
    // 其他
    OTHER
}
