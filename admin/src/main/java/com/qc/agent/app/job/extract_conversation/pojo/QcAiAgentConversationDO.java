package com.qc.agent.app.job.extract_conversation.pojo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025-03-24
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Data
public class QcAiAgentConversationDO {
    private Long id; // 对应数据库中的 "id"

    private String status; // 状态，默认值为 '1'

    private Long creatorId; // 创建者 ID

    private String creatorName; // 创建者名称

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime; // 创建时间

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime answerTime; // 回答时间

    private Long sessionId; // 对话 ID

    private Long modelId; // 大模型 ID

    private Long userId; // 用户 ID

    private Long agentId; // 智能体 ID
    private String agentName; // 智能体 名称
    private Long tenantId; // 智能体 ID

    private String question; // 问题内容

    private BigDecimal questionToken; // 问题 token 数

    private String answer; // 回答内容

    private BigDecimal answerToken; // 回答 token 数

    private BigDecimal conversationTime; // 会话时长（秒）

    private String conversationStatus; // 会话状态：-1 失败，0 进行中，1 成功

    private String source; // 来源：1 Web端，2 客户端
    private String agentType; //
}
