package com.qc.agent.app.knowledge.mapper;

import com.qc.agent.app.knowledge.model.QcKnowledgeFolder;
import com.qc.agent.app.knowledge.model.QcKnowledgeFolderParams;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @className KnowledgeFolderMapper
 * @description TODO
 * @date 2024/1/22 17:35
 */
@Component
public interface KnowledgeFolderMapper {


    QcKnowledgeFolder selectQcKnowledgeFolderById(Long id);

    List<QcKnowledgeFolder> selectDeepListByParentId(Long id);
    List<QcKnowledgeFolder> selectListByParentId(Long id);


    int insertQcKnowledgeFolder(QcKnowledgeFolderParams qcKnowledgeFolderParams);

    int updateQcKnowledgeFolder(QcKnowledgeFolderParams qcKnowledgeFolderParams);

    int deleteQcKnowledgeFolderById(Long id);

    int deleteQcKnowledgeFolderByIds(String[] ids);

}
