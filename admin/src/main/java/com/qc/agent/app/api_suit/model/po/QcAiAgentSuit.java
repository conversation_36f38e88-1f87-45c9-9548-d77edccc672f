package com.qc.agent.app.api_suit.model.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QcAiAgentSuit {

    private Long id;
    private String status;
    private Long creatorId;
    private String creatorName;
    private LocalDateTime createTime;
    private Long modifyierId;
    private String modifyierName;
    private LocalDateTime modifyTime;
    private String name;
    private String describe;
    private BigDecimal sequ;

}
