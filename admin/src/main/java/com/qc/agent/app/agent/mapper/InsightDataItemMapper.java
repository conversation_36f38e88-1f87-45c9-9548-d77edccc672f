package com.qc.agent.app.agent.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.qc.agent.app.agent.model.entity.InsightDataItem;

/**
 * 数据项定义表 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InsightDataItemMapper {

    /**
     * 根据ID查询数据项
     */
    InsightDataItem selectById(@Param("id") Long id);

    /**
     * 根据ID列表批量查询
     */
    List<InsightDataItem> selectBatchIds(@Param("ids") List<Long> ids);

    /**
     * 根据查询业务代码查询数据项列表
     */
    List<InsightDataItem> selectByQueryBusinessCode(@Param("queryBusinessCode") String queryBusinessCode);

    /**
     * 查询所有数据项
     */
    List<InsightDataItem> selectAll();

    /**
     * 插入数据项
     */
    int insert(InsightDataItem dataItem);

    /**
     * 更新数据项
     */
    int updateById(InsightDataItem dataItem);

    /**
     * 根据ID删除数据项
     */
    int deleteById(@Param("id") Long id);
}