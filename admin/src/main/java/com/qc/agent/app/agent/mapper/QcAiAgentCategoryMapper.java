package com.qc.agent.app.agent.mapper;

import com.qc.agent.app.agent.model.po.QcAiAgentCategory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


@Mapper
public interface QcAiAgentCategoryMapper {

    int insert(QcAiAgentCategory category);

    int deleteById(@Param("id") Long id);


    int update(QcAiAgentCategory category);

    QcAiAgentCategory selectById(@Param("id") Long id);

    List<QcAiAgentCategory> selectAll();
}
