package com.qc.agent.app.agent.service.customer_insight_intent.impl;

import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.qc.agent.app.agent.model.query.QcAiCustomerInsightAssistantQuery;
import com.qc.agent.app.agent.service.customer_insight_intent.CustomerInsightIntentHandler;
import com.qc.agent.app.agent.service.goods_intent.impl.tool.IntentTool;
import com.qc.agent.app.workflow.inner.impl.customer_insight_assistant.model.CustomerInsightBusinessIntent;
import com.qc.agent.app.workflow.inner.impl.customer_insight_assistant.model.enums.CustomerInsightIntent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 处理 {@code CUSTOMER_ANALYSIS} 意图的处理器
 * <p>
 * 该处理器的主要作用是验证用户是否提供了足够的具体信息（例如确切的客户名称或客户ID）
 * 来进行客户分析。如果提供的信息模糊或不完整，它会将意图转换为 {@code CUSTOMER_ANALYSIS_INPUT}
 * 以提示用户进行澄清。
 * <p>
 * 无论结果如何，它都会根据用户提供的名称来丰富意图的实体，并添加相应的内部ID
 * （例如 cmId）。
 *
 * <AUTHOR>
 * @since 1.0.0
 * @version 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CustomerAnalysisIntentHandlerImpl implements CustomerInsightIntentHandler {

    private final IntentTool intentTool;

    @Override
    public CustomerInsightIntent getSupportedIntent() {
        return CustomerInsightIntent.CUSTOMER_ANALYSIS;
    }

    /**
     * 处理 {@code CUSTOMER_ANALYSIS} 意图，通过丰富实体并确定下一个逻辑步骤。
     * <p>
     * 它使用 {@link Optional} 来安全地处理意图的实体。如果实体存在，
     * 它会委托给一个辅助方法来执行丰富并决定是否将意图转换为 {@code CUSTOMER_ANALYSIS_INPUT}。
     *
     * @param intent    从用户输入中识别的业务意图。
     * @param query     原始用户查询上下文（接口的一部分，但在此处未使用）。
     * @param logPrefix 用于结构化日志记录的字符串前缀。
     * @return 一个新的 {@code CustomerInsightBusinessIntent}，如果提供的信息不足，
     *         可能会转换为 {@code CUSTOMER_ANALYSIS_INPUT}。否则，返回具有丰富实体的原始意图类型。
     */
    @Override
    public CustomerInsightBusinessIntent handle(CustomerInsightBusinessIntent intent,
            QcAiCustomerInsightAssistantQuery query, String logPrefix) {
        log.info("{} [Handler:CustomerAnalysis] >> 开始处理意图 '{}'，实体: {}",
                logPrefix, intent.intent(), intent.entities());

        return Optional.ofNullable(intent.entities())
                .map(entities -> enrichAndDetermineNextIntent(entities, intent, logPrefix))
                .orElseGet(() -> {
                    log.warn("{} [Handler:CustomerAnalysis] 实体为空。未采取任何操作，返回原始意图。", logPrefix);
                    return intent;
                });
    }

    /**
     * 丰富实体并决定意图是否应该转换。
     *
     * @param entities      来自原始意图的实体的JSON对象。
     * @param initialIntent 初始意图
     * @param logPrefix     用于结构化日志记录的字符串前缀。
     * @return 反映下一步骤的新 {@link CustomerInsightBusinessIntent}。
     */
    private CustomerInsightBusinessIntent enrichAndDetermineNextIntent(JSONObject entities,
            CustomerInsightBusinessIntent initialIntent, String logPrefix) {
        // "精确匹配"是指对实体的查找返回单个、明确的结果。
        // 我们使用AtomicInteger来从lambda表达式内部计算精确匹配。
        var exactMatchCounter = new AtomicInteger(0);

        intentTool.enrichWithCustomerInfo(initialIntent.entities(), exactMatchCounter, logPrefix, "customer");
        // 决策逻辑:
        // 如果我们有1个精确匹配（客户信息），我们有足够的信心继续。
        // 否则，信息模糊或不完整，所以我们转换为 CUSTOMER_ANALYSIS_INPUT
        // 来要求用户重新确认客户。
        if (exactMatchCounter.get() == 1) {
            log.info("{} [Handler:CustomerAnalysis] 找到足够的信息。保持意图为 CUSTOMER_ANALYSIS。", logPrefix);
            return CustomerInsightBusinessIntent.of(CustomerInsightIntent.CUSTOMER_ANALYSIS, entities,
                    initialIntent.usage());
        } else {
            log.info("{} [Handler:CustomerAnalysis] 信息不足或模糊。转换为 CUSTOMER_ANALYSIS_INPUT。", logPrefix);
            return CustomerInsightBusinessIntent.of(CustomerInsightIntent.CUSTOMER_ANALYSIS_INPUT, entities,
                    initialIntent.usage());
        }
    }
}