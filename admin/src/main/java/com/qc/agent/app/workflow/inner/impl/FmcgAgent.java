package com.qc.agent.app.workflow.inner.impl;

import com.qc.agent.app.agent.model.dto.KnowledgeProcessResult;
import com.qc.agent.app.agent.model.po.QcAiAgent;
import com.qc.agent.app.workflow.WorkflowLLAClient;
import com.qc.agent.app.workflow.inner.AgentType;
import com.qc.agent.app.workflow.inner.pojo.QcAiAgentIntentRecognition;
import com.qc.agent.app.workflow.inner.pojo.QcAiKnowledgeRelated;
import com.qc.agent.common.core.Message;
import com.qc.agent.lla.exception.LLAInvokerRequestException;
import com.qc.agent.lla.model.LLAConfig;
import com.qc.agent.lla.model.LLARequest;
import com.qc.agent.platform.register.UserManager;
import com.qc.agent.platform.sse.SseClient;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 快销专家
 *
 * <AUTHOR>
 * {@code @Date} 2025/3/19 15:26:30
 */
@Component
@Slf4j
public class FmcgAgent extends AbstractIAgentInvoker {

    private static final Long PUBLIC_DATABASE_ID = 1000000201304120032L;
    private static final Long COLLECTION_ID = 7252004345761540678L;

    @Resource
    private DefaultAgent defaultAgent;

    @Override
    public AgentType determineAgent() {
        return AgentType.FMCG;
    }

    @Override
    public Message invoke(QcAiAgent qcAiAgent, LLAConfig config, LLARequest request, SseClient client) {
        try {
            String logPrefix = String.format("[tenantId=%s, agentId=%s, requestId=%s]",
                    UserManager.getTenantUser().getTenantId(), qcAiAgent.getId(), request.getId());

            QcAiAgentIntentRecognition defaultIntent = getQcAiAgentIntentRecognition(qcAiAgent);

            // 搜索知识库内容
            KnowledgeProcessResult result = defaultAgent.searchKnowledgeContent(
                    defaultIntent,
                    request,
                    client,
                    logPrefix,
                    config
            );

            // 匹配到qa答案/参数【仅搜索知识库】开启且知识库没有匹配到内容时，直接返回，不走大模型
            if (result.getDirectReturn()) {
                return Message.of().ok();
            }

            request.setContent(String.format("背景：%s。问题：%s", result.getKnowledgeContent(), request.getContent()));
            WorkflowLLAClient.sendAsync(config, request, client);
        } catch (LLAInvokerRequestException e) {
            log.error("调用大模型问答异常", e);
            String errorMessage = "该AGENT使用的模型的KEY已失效，请联系创建者/管理员调整后重新发布后再使用。";
            doErrorSend(errorMessage, request, null, client, false);
        }
        return Message.of().ok();
    }

    private static @NotNull QcAiAgentIntentRecognition getQcAiAgentIntentRecognition(QcAiAgent qcAiAgent) {
        QcAiAgentIntentRecognition defaultIntent = new QcAiAgentIntentRecognition();
        defaultIntent.setTenantId(PUBLIC_DATABASE_ID);
        defaultIntent.setAgentId(qcAiAgent.getId());
        defaultIntent.setQaMinMatchThreshold(qcAiAgent.getQaMinMatchThreshold());
        defaultIntent.setMaxRecallCount(qcAiAgent.getMaxRecallCount());
        defaultIntent.setMinMatchThreshold(qcAiAgent.getMinMatchThreshold());
        defaultIntent.setSearchScope(qcAiAgent.getSearchScope());
        defaultIntent.setNullResultAnswer(qcAiAgent.getNullResultAnswer());

        List<QcAiKnowledgeRelated> relatedKnowledgeList = new ArrayList<>();
        QcAiKnowledgeRelated relatedKnowledge = new QcAiKnowledgeRelated();
        relatedKnowledge.setCollectionId(COLLECTION_ID);
        relatedKnowledge.setName("快销知识库");
        relatedKnowledgeList.add(relatedKnowledge);
        defaultIntent.setRelateKnowledgeList(relatedKnowledgeList);
        return defaultIntent;
    }
}
