package com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.service.tool;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.qc.agent.app.agent.support.AgentBizDataSupport;
import com.qc.agent.utils.StringSimilarityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;

/**
 * A tool for finding and matching customer types by interacting with a remote API.
 * Provides methods for both exact and similarity-based matching.
 *
 * <AUTHOR>
 * @date 2025-06-27
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Slf4j
@Component
public final class CustomerTypeTool {

    private static final String QUERY_CUSTOMER_TYPE_URL = "/platform/combo/v1/getComboBox.do?comboCode=customer_type_ztree";
    private static final String JSON_NAME_FIELD = "name";

    @Value("${ai.agent.appsvr-domin-url}")
    private String appsvrDomainUrl;

    /**
     * Finds the customer type that has the most similar name to the provided input.
     * This method fetches the complete list of customer types and then performs a similarity search.
     *
     * @param customerTypeName The name to find a match for.
     * @param logPrefix        A prefix for log messages to provide context.
     * @return An {@link Optional} containing the most similar {@link JSONObject}, or empty if no suitable match is found or an error occurs.
     */
    public Optional<JSONObject> findMostSimilarCustomerTypeByName(final String customerTypeName, List<JSONObject> dataList, final String logPrefix) {
        if (dataList.isEmpty()) {
            log.warn("{} 客户类型列表为空，无法为 '{}' 查找相似项", logPrefix, customerTypeName);
            return Optional.empty();
        }

        log.info("{} 在 {} 个客户类型中, 为 '{}' 查找最相似的匹配项...", logPrefix, dataList.size(), customerTypeName);

        final JSONObject bestMatch;
        if (dataList.size() > 1) {
            log.info("{} 客户类型列表中有多个候选项（{}个），正在为 '{}' 查找最匹配项...", logPrefix, dataList.size(), customerTypeName);
            Function<JSONObject, String> nameExtractor = item -> item.getString(JSON_NAME_FIELD);
            bestMatch = StringSimilarityUtils.findMostSimilarByDistance(dataList, customerTypeName, nameExtractor);
        } else {
            bestMatch = dataList.get(0);
        }

        log.info("{} 选中的最匹配客户为: {}", logPrefix, bestMatch.toJSONString());
        return Optional.of(bestMatch);

    }

    /**
     * Finds a customer type with a name that exactly matches the provided input.
     *
     * @param customerTypeName The exact name of the customer type to find.
     * @param logPrefix        A prefix for log messages to provide context.
     * @return An {@link Optional} containing the matching {@link JSONObject}, or empty if not found or an error occurs.
     */
    public Optional<JSONObject> findExactCustomerTypeByName(final String customerTypeName, final String logPrefix) {
        return fetchAllCustomerTypes(logPrefix).flatMap(customerTypeList -> {
            log.info("{} 在 {} 个客户类型中, 正在精确查找 '{}'...", logPrefix, customerTypeList.size(), customerTypeName);

            Optional<JSONObject> exactMatch = customerTypeList.stream()
                    .filter(item -> Objects.equals(item.getString(JSON_NAME_FIELD), customerTypeName))
                    .findFirst();

            exactMatch.ifPresentOrElse(
                    item -> log.info("{} 成功找到完全匹配的客户类型: {}", logPrefix, item.toJSONString()),
                    () -> log.warn("{} 未找到名称为 '{}' 的客户类型", logPrefix, customerTypeName)
            );

            return exactMatch;
        });
    }

    /**
     * Fetches the complete list of customer types from the remote API.
     * This method is the single source of data for this tool.
     *
     * @param logPrefix A prefix for log messages to provide context.
     * @return An {@link Optional} containing a list of customer types, or empty if the API call fails or returns no data.
     */
    public Optional<List<JSONObject>> fetchAllCustomerTypes(final String logPrefix) {
        final var url = appsvrDomainUrl + QUERY_CUSTOMER_TYPE_URL;
        log.info("{} 正在调用客户类型API: {}", logPrefix, url);

        try {
            // Assuming postFetchBizData can handle an empty body if new JSONObject() is intended for that.
            final String responseData = AgentBizDataSupport.postFetchBizData(url, new JSONObject());
            if (StringUtils.isBlank(responseData)) {
                log.warn("{} 客户类型 API 响应为空或空白字符串", logPrefix);
                return Optional.empty();
            }

            List<JSONObject> customerTypes = JSONArray.parseArray(responseData)
                    .stream()
                    .filter(JSONObject.class::isInstance) // Safely handle non-JSONObject elements
                    .map(JSONObject.class::cast)
                    .toList();

            log.info("{} 成功从 API 获取到 {} 个客户类型", logPrefix, customerTypes.size());
            return Optional.of(customerTypes);

        } catch (Exception e) {
            // Catching a broad exception is pragmatic at the service boundary.
            log.error("{} 调用或解析客户类型 API 时发生错误. URL: {}", logPrefix, url, e);
            return Optional.empty();
        }
    }
}