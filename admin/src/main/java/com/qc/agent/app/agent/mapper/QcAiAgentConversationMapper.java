package com.qc.agent.app.agent.mapper;

import com.qc.agent.app.agent.model.dto.CommonFeedbackDTO;
import com.qc.agent.app.agent.model.dto.ConversationEvaluationDTO;
import com.qc.agent.app.agent.model.po.QcAiAgentConversation;
import com.qc.agent.app.agent.model.po.QcAiAgentConversationParams;
import com.qc.agent.app.agent.model.vo.ConversationVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface QcAiAgentConversationMapper {

    int insert(QcAiAgentConversation conversation);

    int update(QcAiAgentConversation conversation);

    int updateQuestionAndAnswer(QcAiAgentConversation conversation);

    List<ConversationVO> queryAgentConversation(@Param("agentId") Long agentId, @Param("userId") Long userId,
                                                @Param("limit") Integer limit, @Param("offset") Integer offset,
                                                @Param("status") String status);

    List<ConversationVO> queryAgentHistoryConversation(@Param("agentId") Long agentId, @Param("userId") Long userId,
                                                       @Param("limit") Integer limit, @Param("offset") Integer offset,
                                                       @Param("status") String status);

    List<ConversationVO> queryHistoryConversations(QcAiAgentConversationParams model);

    int updateStreamChatDone(@Param("conversationId") Long conversationId,
                             @Param("content") String content,
                             @Param("promptTokens") int promptTokens,
                             @Param("completionTokens") int completionTokens,
                             @Param("conversationTime") BigDecimal conversationTime,
                             @Param("conversationStatus") String conversationStatus,
                             @Param("conversationStart") LocalDateTime conversationStart);

    QcAiAgentConversation selectById(@Param("id") Long id);

    int queryHistoryConversationsCount(QcAiAgentConversationParams model);

    void updateReference(@Param("id") Long id, @Param("size") int size, @Param("content") String content);

    void updateEvaluation(ConversationEvaluationDTO evaluationDTO);

    List<CommonFeedbackDTO> getCommonFeedback(@Param("feedbackType") String feedbackType);

    void updateAnswerById(@Param("conversationId") Long conversationId, @Param("answer") String answer);

    void updateIntentId(@Param("intentId") Long id, @Param("conversationId") Long requestId);

    int updatePost(@Param("conversationId") Long conversationId,
                   @Param("question") String question,
                   @Param("content") String content,
                   @Param("promptTokens") int promptTokens,
                   @Param("completionTokens") int completionTokens,
                   @Param("conversationTime") BigDecimal conversationTime,
                   @Param("conversationStatus") String conversationStatus,
                   @Param("conversationStart") LocalDateTime conversationStart);

    void updateQuestion(@Param("id") String id, @Param("question") String question);
}
