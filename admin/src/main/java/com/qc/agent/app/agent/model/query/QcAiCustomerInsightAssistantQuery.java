package com.qc.agent.app.agent.model.query;

import com.qc.agent.app.agent.model.dto.QcAiAgentCommonParam;
import lombok.Data;

/**
 * 客户洞察助手查询参数
 * 
 * <AUTHOR>
 * @since 1.0.0
 * @version 1.0.0
 */
@Data
public class QcAiCustomerInsightAssistantQuery {

    /**
     * 代理ID
     */
    private Long agentId;

    /**
     * 会话ID
     */
    private Long sessionId;

    /**
     * 问题内容
     */
    private String question;

    /**
     * 客户ID
     */
    private String cmId;

    /**
     * 是否新会话，1：是
     */
    private String isNew;

    /**
     * 前端传入的唯一ID
     */
    private String chatSessionId;

    /**
     * 来源：web悬浮窗：web-levitate ； web端：web ； app端：app
     */
    private String source;

    /**
     * 上次的会话ID
     */
    private Long conversationId;

    /**
     * 是否点击确定，1：是
     */
    private String isClick;

    /**
     * 测试对话和正式对话都有的公共参数
     */
    private QcAiAgentCommonParam commonParam;
}