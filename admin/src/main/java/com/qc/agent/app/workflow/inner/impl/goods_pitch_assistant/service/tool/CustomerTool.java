package com.qc.agent.app.workflow.inner.impl.goods_pitch_assistant.service.tool;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.qc.agent.app.agent.support.AgentBizDataSupport;
import com.qc.agent.utils.StringSimilarityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;

/**
 * 客户信息查询工具。
 * <p>
 * 通过调用平台 API，根据客户名称模糊查询匹配的客户信息。
 * 当查询结果多于一个时，会使用字符串相似度算法寻找最佳匹配项。
 *
 * <AUTHOR>
 * @date 2025-06-27
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CustomerTool {

    // --- Constants for API and JSON Structure ---

    private static final String QUERY_CUSTOMER_URL = "/app/apaas/subassembly/queryList.do";
    private static final String CUSTOMER_OBJECT_KEY = "bas_cm_customer";
    private static final String CUSTOMER_TEMPLATE_OBJECT_KEY = "bas_cm_customer_template";

    // JSON Keys
    private static final String KEY_PAGE = "page";
    private static final String KEY_ROWS = "rows";
    private static final String KEY_MIXED_MODE = "mixedMode";
    private static final String KEY_TEMPLATE_FLAG = "templateFlag";
    private static final String KEY_OBJECT_KEY = "objectKey";
    private static final String KEY_FIELD_LIST = "fieldList";
    private static final String KEY_SORTS = "sorts";
    private static final String KEY_FIELD_KEY = "field_key";
    private static final String KEY_TYPE = "type";
    private static final String KEY_CONDITION_FILTER_LIST = "conditionFilterList";
    private static final String KEY_CONNECTOR = "connector";
    private static final String KEY_FILTERS = "filters";
    private static final String KEY_OPERATOR = "operator";
    private static final String KEY_FIELD_VALUES = "field_values";
    private static final String KEY_TEMPLATE_OBJECT_KEY_LIST = "templateObjectKeyList";
    private static final String KEY_CONJUNCTIVE_QUERY = "conjunctiveQuery";

    // JSON Values
    private static final String VAL_CREATE_TIME = "create_time";
    private static final String VAL_DESC = "desc";
    private static final String VAL_AND = "AND";
    private static final String VAL_LIKE = "LIKE";

    // Field names to retrieve
    private static final List<Object> CUSTOMER_FIELDS = List.of("name", "code", "addr", "id");

    @Value("${ai.agent.appsvr-domin-url}")
    private String appsvrDomainUrl;

    private static final String API_URL_TEMPLATE = "%s/app/cm/apaas/customer/detail.do?cmId=%s";
    private static final String JSON_KEY_DATA = "data";
    private static final String JSON_KEY_CUSTOMER_OBJECT = "basCmCustomer";

    private final ObjectMapper objectMapper;

        /**
     * Queries external service to get customer type.
     * 使用flatMap优化了Optional的嵌套，使代码更扁平化。
     */
    public String queryClientData(String logPrefix) {
        log.debug("{} Querying customer visit data...", logPrefix);
        long apiStartTime = System.currentTimeMillis();

        try {
            String url = appsvrDomainUrl + "/app/cm/report/cmUseDealerStoreV2.do";
            String response = AgentBizDataSupport.getWithAuthHeaders(url);

            return Optional.ofNullable(response).filter(org.springframework.util.StringUtils::hasText) // 使用hasText更严谨
                    .flatMap(resp -> parseAndExtractData(resp, logPrefix)) // 使用flatMap避免Optional嵌套
                    .orElse(StringUtils.EMPTY);

        } catch (Exception e) {
            log.error("{} Error calling customer visit data API.", logPrefix, e);
            return StringUtils.EMPTY;
        } finally {
            log.debug("{} Customer visit data query finished in {}ms.", logPrefix, System.currentTimeMillis() - apiStartTime);
        }
    }

        /**
     * 从JSON响应中解析并提取"data"字段的辅助方法。
     *
     * @return 包含data字段值的Optional，如果解析失败或字段不存在则为空。
     */
    private Optional<String> parseAndExtractData(String jsonResponse, String logPrefix) {
        try {
            Map<?, ?> map = objectMapper.readValue(jsonResponse, Map.class);
            return Optional.ofNullable(map.get("data")).map(Object::toString);
        } catch (JsonProcessingException e) {
            log.error("{} Failed to parse JSON response from visit data API. Response: {}", logPrefix, jsonResponse, e);
            // 在此层级不抛出异常，而是返回empty，让调用者决定如何处理
            return Optional.empty();
        }
    }

    /**
     * Finds a customer by their unique ID by making an API call.
     * This method is resilient to network errors, empty responses, and unexpected JSON structures.
     *
     * @param cmId      The customer ID (cmId) to search for. Must not be blank.
     * @param logPrefix A prefix for log messages to provide context and traceability.
     * @return An {@link Optional} containing the customer's {@link JSONObject} if found,
     * otherwise an empty Optional.
     */
    public Optional<JSONObject> findCustomerById(String cmId, String logPrefix) {
        // 1. Fail fast with a clear log message if the input is invalid.
        if (cmId == null || cmId.isBlank()) {
            log.warn("{} Cannot search for customer: the provided cmId is null or blank.", logPrefix);
            return Optional.empty();
        }

        // 2. Build the URL and log it for debugging purposes.
        String url = API_URL_TEMPLATE.formatted(appsvrDomainUrl, cmId);
        log.debug("{} Querying customer API for cmId '{}'. URL: {}", logPrefix, cmId, url);

        try {
            // 3. Make the API call.
            String responseBody = AgentBizDataSupport.getWithAuthHeaders(url);

            // 4. Use an Optional chain to safely parse and extract the nested JSON object.
            Optional<JSONObject> customerOpt = Optional.ofNullable(responseBody)
                    .filter(body -> !body.isBlank())
                    .map(JSONObject::parseObject)
                    .map(json -> json.getJSONObject(JSON_KEY_DATA))
                    .map(data -> data.getJSONObject(JSON_KEY_CUSTOMER_OBJECT));

            // 5. Log the outcome clearly.
            if (customerOpt.isPresent()) {
                log.info("{} Successfully found and parsed customer info for cmId '{}'.", logPrefix, cmId);
            } else {
                // This branch is hit if responseBody is null/blank, or if the JSON structure is wrong.
                log.warn("{} Customer not found for cmId '{}'. The API response was empty, malformed, or the customer does not exist in the response structure. Response Body: {}",
                        logPrefix, cmId, responseBody);
            }

            return customerOpt;

        } catch (Exception e) {
            // 6. Catch any exceptions, log with full context, and return empty.
            log.error("{} An unexpected exception occurred while querying for cmId '{}'. URL: {}", logPrefix, cmId, url, e);
            return Optional.empty();
        }
    }

    public Optional<List<JSONObject>> findCustomerList(String customerName, String logPrefix) {
        final var url = appsvrDomainUrl + QUERY_CUSTOMER_URL;
        final var params = buildQueryPayload(customerName);

        log.info("{} 调用客户搜索 API, URL: {}, 参数: {}", logPrefix, url, params);

        try {
            final String data = AgentBizDataSupport.fetchBizData(url, params);
            if (StringUtils.isBlank(data)) {
                log.warn("{} 客户 API 响应为空, 搜索词: '{}'", logPrefix, customerName);
                return Optional.empty();
            }

            JsonNode root = objectMapper.readTree(data);
            JsonNode rowsNode = root.get("rows");
            final List<JSONObject> dataList = objectMapper.readValue(rowsNode.toString(), new TypeReference<>() {
            });
            if (dataList.isEmpty()) {
                log.warn("{} API 未返回任何客户数据, 搜索词: '{}'", logPrefix, customerName);
                return Optional.empty();
            }
            return Optional.of(dataList);

        } catch (JsonProcessingException e) {
            log.error("{} 解析客户 API 的 JSON 响应失败, 搜索词: '{}'", logPrefix, customerName, e);
            return Optional.empty();
        }
    }

    /**
     * 从客户列表中找出与指定名称最相似的客户。
     *
     * @param customerName 要匹配的客户名称。
     * @param logPrefix    日志前缀。
     * @return 最匹配的客户信息，如果列表为空则返回空 Optional。
     */
    public Optional<JSONObject> findMostSimilarCustomer(String customerName, String logPrefix) {
        Optional<List<JSONObject>> customerList = findCustomerList(customerName, logPrefix);
        if (customerList.isEmpty()) {
            return Optional.empty();  // 如果列表为空，直接返回空 Optional
        }

        List<JSONObject> dataList = customerList.get();  // 获取客户列表
        return findMostSimilarCustomer(customerName, dataList, logPrefix);
    }

    /**
     * 从客户列表中找出与指定名称最相似的客户。
     *
     * @param customerName 要匹配的客户名称。
     * @param dataList     候选客户列表。
     * @param logPrefix    日志前缀。
     * @return 最匹配的客户信息，如果列表为空则返回空 Optional。
     */
    public Optional<JSONObject> findMostSimilarCustomer(String customerName, List<JSONObject> dataList, String logPrefix) {
        if (dataList == null || dataList.isEmpty()) {
            log.warn("{} 客户列表为空，无法匹配 '{}'", logPrefix, customerName);
            return Optional.empty();
        }

        final JSONObject customerMatch;
        if (dataList.size() > 1) {
            log.info("{} 客户列表中有多个候选项（{}个），正在为 '{}' 查找最匹配项...", logPrefix, dataList.size(), customerName);
            Function<JSONObject, String> nameExtractor = item -> item.getString("name");
            customerMatch = StringSimilarityUtils.findMostSimilar(dataList, customerName, nameExtractor);
        } else {
            customerMatch = dataList.get(0);
        }

        log.info("{} 选中的最匹配客户为: {}", logPrefix, customerMatch.toJSONString());
        return Optional.of(customerMatch);
    }


    /**
     * 构建用于查询客户列表的 API 请求体。
     *
     * @param customerName 用于模糊查询的客户名称。
     * @return 包含所有查询参数的 {@link JSONObject}。
     */
    private JSONObject buildQueryPayload(String customerName) {
        var params = new JSONObject();

        // 基础分页和标识
        params.put(KEY_PAGE, 1);
        params.put(KEY_ROWS, 100);
        params.put(KEY_MIXED_MODE, true);
        params.put(KEY_TEMPLATE_FLAG, true);
        params.put(KEY_OBJECT_KEY, CUSTOMER_TEMPLATE_OBJECT_KEY);

        // 指定返回字段
        params.put(KEY_FIELD_LIST, new JSONArray(CUSTOMER_FIELDS));

        // 排序条件
        var sortObj = new JSONObject();
        sortObj.put(KEY_FIELD_KEY, VAL_CREATE_TIME);
        sortObj.put(KEY_TYPE, VAL_DESC);
        params.put(KEY_SORTS, new JSONArray(List.of(sortObj)));

        // 查询过滤条件 (使用 customerName 进行模糊查询)
        var filter = new JSONObject();
        filter.put(KEY_FIELD_KEY, KEY_CONJUNCTIVE_QUERY);
        filter.put(KEY_OPERATOR, VAL_LIKE);
        filter.put(KEY_FIELD_VALUES, new JSONArray(List.of(customerName))); // BUG FIX: Use parameter instead of hardcoded value

        var andGroup = new JSONObject();
        andGroup.put(KEY_CONNECTOR, VAL_AND);
        andGroup.put(KEY_FILTERS, new JSONArray(List.of(filter)));
        params.put(KEY_CONDITION_FILTER_LIST, new JSONArray(List.of(andGroup)));

        // 模板对象
        params.put(KEY_TEMPLATE_OBJECT_KEY_LIST, new JSONArray(List.of(CUSTOMER_OBJECT_KEY)));

        return params;
    }
}