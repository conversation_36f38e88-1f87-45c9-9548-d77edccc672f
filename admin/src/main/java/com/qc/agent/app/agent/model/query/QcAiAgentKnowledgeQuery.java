package com.qc.agent.app.agent.model.query;

import com.qc.agent.app.agent.model.dto.QcAiAgentKnowledgeAuthorityDTO;
import com.qc.agent.platform.pojo.QcUserInfo;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-26
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Data
public class QcAiAgentKnowledgeQuery {
    private Long agentId;

    private List<QcUserInfo> userInfoList;

    private List<QcAiAgentKnowledgeAuthorityDTO> list;

    private Long conversationId;

    /**
     * agent日志中是否展示完整对话内容 1展示 0不展示
     */
    private String showChatLogContentType;

}
