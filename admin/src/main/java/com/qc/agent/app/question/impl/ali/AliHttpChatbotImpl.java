package com.qc.agent.app.question.impl.ali;

import com.alibaba.fastjson.JSONObject;
import com.qc.agent.app.question.impl.AbstrctAnswerAIRequest;
import com.qc.agent.platform.sse.AnswerAISubscriber;
import com.qc.agent.platform.sse.SseMessageConverter;
import com.qc.agent.platform.sse.SseRequest;
import com.qc.agent.platform.sse.dto.AIVendors;
import com.qc.agent.platform.sse.dto.AnswerAIMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.util.List;

/**
 *
 * <AUTHOR>
 */

@Component
@Slf4j
public class AliHttpChatbotImpl extends AbstrctAnswerAIRequest
{
    @Value("${ai.agent.ali-apikey}")
    String aliApiKey;
    @Override
    public AIVendors determineAIVendors()
    {
        return AIVendors.ALI;
    }
    @Override
    public SseMessageConverter determineMessageConverter()
    {
        return new AliMessageConverter();
    }

    @Override
    protected String buildAnswer(String defaultAnswer)
    {
        JSONObject data = new JSONObject();
        JSONObject output = new JSONObject();
        output.put("text",defaultAnswer);
        output.put("finish_reason", StringUtils.isNotEmpty(defaultAnswer) ? "" : "stop");
        data.put("output",output);
        return String.format("data:%s", data.toJSONString());
    }
    @Override
    protected void doAnswer(SseRequest request, AnswerAISubscriber subscriber)
    {
        log.info("做文通义千问问答, aliApiKey:{}", aliApiKey);
        JSONObject requestParam = buildRequestBody(request);
        log.info("调用问答接口，request：{}", requestParam);
        HttpRequest httpRequest = HttpRequest.newBuilder()
                .header("Content-Type", "application/json")
                .header("Authorization", String.format("Bearer %s", aliApiKey))
                .header("Accept", "text/event-stream")
                .uri(URI.create("https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"))
                .POST(HttpRequest.BodyPublishers.ofString(requestParam.toJSONString()))
                .build();
        try
        {
            HttpClient.newBuilder().build().sendAsync(httpRequest, HttpResponse.BodyHandlers.fromLineSubscriber(subscriber)).get();
        }

        catch(Exception e)
        {
            throw new RuntimeException(e);
        }

    }
    @NotNull
    private JSONObject buildRequestBody(SseRequest request)
    {
        JSONObject requestBody = new JSONObject();
        JSONObject input = new JSONObject();
        List<AnswerAIMessage> messages = buildMessage(request);
        if(CollectionUtils.isNotEmpty(messages)){
            for (AnswerAIMessage message : messages) {
                if(StringUtils.isNotEmpty(message.getContent()) && message.getContent().length() > 6000){
                    message.setContent(message.getContent().substring(0, 6000));
                }
            }
            if(!"user".equals(messages.get(0).getRole())){
                messages.remove(0);
            }
        }
        input.put("messages", messages);
        requestBody.put("input",input);
        JSONObject parameters = new JSONObject();
        parameters.put("incremental_output",true);
        requestBody.put("parameters",parameters);
        requestBody.put("model", "qwen-max");
        return requestBody;
    }
}
