package com.qc.agent.app.knowledge.service;

import com.qc.agent.app.knowledge.model.QcKnowledgeFolder;
import com.qc.agent.app.knowledge.model.QcKnowledgeFolderParams;

import java.util.List;

/**
 * <AUTHOR>
 * @className KnowledgeFolderService
 * @description TODO
 * @date 2024/1/22 17:35
 */
public interface KnowledgeFolderService {
    //查询所有子集
    List<QcKnowledgeFolder> selectDeepListByParentId(Long id);
    //查询同层下的子集
    List<QcKnowledgeFolder> selectListByParentId(Long id);

    int insertQcKnowledgeFolder(QcKnowledgeFolderParams qcKnowledgeFolderParams);

    int updateQcKnowledgeFolder(QcKnowledgeFolderParams qcKnowledgeFolderParams);

    int deleteQcKnowledgeFolderById(Long id);

    int deleteQcKnowledgeFolderByIds(String[] ids);
}
