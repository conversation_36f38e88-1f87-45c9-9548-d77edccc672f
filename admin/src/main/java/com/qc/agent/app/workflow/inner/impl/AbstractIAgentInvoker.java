package com.qc.agent.app.workflow.inner.impl;

import com.qc.agent.app.workflow.ListenerInvoker;
import com.qc.agent.app.workflow.inner.IAgentInvoker;
import com.qc.agent.common.core.Message;
import com.qc.agent.lla.model.LLARequest;
import com.qc.agent.lla.model.LLAResponse;
import com.qc.agent.platform.sse.SseClient;
import com.qc.agent.platform.sse.SseMessage;
import com.qc.agent.platform.sse.dto.MessageType;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public abstract class AbstractIAgentInvoker implements IAgentInvoker {

    protected void doErrorSend(String errorMsg, LLARequest request, LLAResponse conditionResponse, SseClient client, boolean doInvoke) {
        request.setStart(LocalDateTime.now());
        simulateStreamSend(errorMsg, request,client);
        client.send(request.getClientId(),Message.of().ok().data(SseMessage.builder().messageType(MessageType.TEXT).isEnd(true).build()));
        try {
            Thread.sleep(500);
        } catch (InterruptedException e) {
            log.error("sleep异常", e);
        }
        if (doInvoke) {
            conditionResponse.setContent(errorMsg);
            ListenerInvoker.invoke(conditionResponse, request);
        }
        client.complete(request.getClientId());
    }

    protected void doErrorSend(String errorMsg, LLARequest request, LLAResponse conditionResponse, SseClient client) {
        doErrorSend(errorMsg, request, conditionResponse, client, true);
    }

    protected void simulateStreamSend(String content,LLARequest request, SseClient client) {
        String[] split = content.split("");
        for (String s : split) {
            client.send(request.getClientId(),Message.of().ok().data(SseMessage.builder().message(s).messageType(MessageType.TEXT).isEnd(false).build()));
            try {
                Thread.sleep(10);
            } catch (InterruptedException e) {
                log.error("sleep异常", e);
            }
        }
    }

    protected void simulateStreamSend(String content, SseClient client, int size, String id, String clientId) {
        String[] split = content.split("");
        for (String s : split) {
            Map<String,Object> extData = new HashMap<>();
            extData.put("conversationId", id);
            extData.put("quoteCount", size);
            client.send(clientId,Message.of().ok().data(SseMessage.builder()
                    .message(s)
                            .extData(extData)
                    .messageType(MessageType.TEXT).isEnd(false).build()));
            try {
                Thread.sleep(10);
            } catch (InterruptedException e) {
                log.error("sleep异常", e);
            }
        }
    }
}
