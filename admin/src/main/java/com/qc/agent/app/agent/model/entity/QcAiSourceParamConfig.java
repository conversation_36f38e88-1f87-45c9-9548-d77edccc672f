package com.qc.agent.app.agent.model.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * 数据源参数配置实体类
 * 通过分组概念管理相关参数
 */
@Data
public class QcAiSourceParamConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 数据源ID，关联qc_ai_data_source表
     */
    private Long sourceId;

    /**
     * 参数编码，对应接口的具体参数名，如：startTime, endTime
     */
    private String paramCode;

    /**
     * 分组编码，相关参数的分组标识
     */
    private String groupCode;

    /**
     * 分组名称，前端展示的选项名称
     */
    private String groupName;

    /**
     * 分组枚举值，JSON格式，如：[{"code": "1", "name": "是"}, {"code": "0", "name": "否"}]
     */
    private String groupEnumValues;

    /**
     * 排序顺序，控制前端展示顺序
     */
    private Integer displayOrder;
}
