package com.qc.agent.app.api_suit.mapper;

import com.qc.agent.app.api_suit.model.po.QcAiAgentSuit;
import com.qc.agent.app.api_suit.model.vo.SuitVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface QcAiAgentSuitMapper {
    SuitVO selectById(@Param("id") Long id);

    List<SuitVO> selectAll();

    int insert(QcAiAgentSuit po);

    int update(QcAiAgentSuit po);

    int delete(@Param("id") Long id);
}
