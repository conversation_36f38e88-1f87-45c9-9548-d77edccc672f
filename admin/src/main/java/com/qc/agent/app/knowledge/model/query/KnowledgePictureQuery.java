package com.qc.agent.app.knowledge.model.query;

import com.qc.agent.app.knowledge.model.QcKnowledgeExcelData;
import com.qc.agent.app.workflow.inner.pojo.QcUploadFile;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-07-11
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Data
public class KnowledgePictureQuery {

    /**
     * 文件id
     */
    private Long id;

    /**
     * 知识库id
     */
    private Long collectionId;

    /**
     * 关联文件详情
     */
    private List<QcUploadFile> picturePathList;

    /**
     * 分段详情
     */
    private List<QcKnowledgeExcelData> segmentList;

}
