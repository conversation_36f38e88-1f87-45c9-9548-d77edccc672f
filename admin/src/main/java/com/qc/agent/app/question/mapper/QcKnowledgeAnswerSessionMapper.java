package com.qc.agent.app.question.mapper;

import com.qc.agent.app.question.pojo.AnswerSessionView;
import com.qc.agent.app.question.pojo.QcKnowledgeAnswerSession;

import java.util.List;

public interface QcKnowledgeAnswerSessionMapper {
    int deleteByPrimaryKey(Long id);

    int insert(QcKnowledgeAnswerSession record);

    int insertSelective(QcKnowledgeAnswerSession record);

    QcKnowledgeAnswerSession selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(QcKnowledgeAnswerSession record);

    int updateByPrimaryKey(QcKnowledgeAnswerSession record);

    void delete(QcKnowledgeAnswerSession qcKnowledgeAnswerSession);

    List<AnswerSessionView> listAnswerSession(QcKnowledgeAnswerSession qcKnowledgeAnswerSession);
}
