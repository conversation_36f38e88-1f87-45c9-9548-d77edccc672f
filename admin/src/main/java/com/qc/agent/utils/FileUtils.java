package com.qc.agent.utils;

import com.qc.agent.platform.register.UserManager;
import org.apache.commons.io.FilenameUtils;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;

/**
 * <AUTHOR>
 * @date 2025-07-11
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
public class FileUtils {

    private static final String PARSED_FILE_PATH_PREFIX = "parsed/";
    private static final String PARSED_FILE_SUFFIX = ".json";

    private FileUtils() {
    }


    public static String generateParsedFilePath(String uri) {
        Long tenantId = UserManager.getTenantUser().getTenantId();
        String filename = FilenameUtils.getName(uri);
        return tenantId + "/" + PARSED_FILE_PATH_PREFIX + generateHashId(filename) + PARSED_FILE_SUFFIX;
    }

    public static String generateHashId(String input) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hashBytes = digest.digest(input.getBytes(StandardCharsets.UTF_8));
            StringBuilder hex = new StringBuilder();
            for (byte b : hashBytes) hex.append(String.format("%02x", b));
            return hex.substring(0, 24);
        } catch (Exception e) {
            throw new RuntimeException("生成文件哈希失败", e);
        }
    }
}
