package com.qc.agent.utils;

import com.alibaba.fastjson.JSONObject;

import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025-06-27
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
public class JsonFieldUtils {
    /**
     * Checks if the JSON object contains a value for exactly one of the specified keys.
     */
    public static boolean hasExactlyOneNonBlankField(JSONObject jsonObject, Set<String> keys) {
        long count = keys.stream()
                .map(jsonObject::getString)
                .filter(value -> Objects.nonNull(value) && !value.isBlank())
                .count();
        return count <= 1;
    }
}
