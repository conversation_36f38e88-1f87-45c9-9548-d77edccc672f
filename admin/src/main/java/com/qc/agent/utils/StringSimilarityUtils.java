package com.qc.agent.utils;

import org.apache.commons.text.similarity.JaroWinklerSimilarity;
import org.apache.commons.text.similarity.LevenshteinDistance;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @date 2025-06-23
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
public class StringSimilarityUtils {
    private static final JaroWinklerSimilarity SIMILARITY = new JaroWinklerSimilarity();
    private static final LevenshteinDistance LEVENSHTEIN = new LevenshteinDistance();

    /**
     * 从列表中查找与 target 最相似的对象
     *
     * @param list        待查找的对象列表
     * @param target      目标字符串
     * @param extractor   用于提取每个对象的比较字段（如 name）
     * @param <T>         列表中对象的类型
     * @return            最相似的对象，若无匹配项返回 null
     */
    public static <T> T findMostSimilar(List<T> list, String target, Function<T, String> extractor) {
        if (list == null || list.isEmpty() || target == null || extractor == null) {
            return null;
        }

        return list.stream()
                .filter(Objects::nonNull)
                .max(Comparator.comparingDouble(item -> {
                    String text = extractor.apply(item);
                    return text != null ? SIMILARITY.apply(text, target) : -1.0;
                }))
                .orElse(null);
    }

    /**
     * 新方法：关键词包含优先 + 编辑距离策略
     */
    public static <T> T findMostSimilarByDistance(List<T> list, String target, Function<T, String> extractor) {
        if (list == null || list.isEmpty() || target == null || extractor == null) {
            return null;
        }

        return list.stream()
                .filter(Objects::nonNull)
                .max(Comparator.comparingDouble(item -> {
                    String text = extractor.apply(item);
                    if (text == null) return -1.0;

                    // 如果包含关键词，直接设为高分
                    if (text.contains(target)) {
                        return 2.0; // 高于正常最大分1.0，确保优先
                    }

                    // 编辑距离越小越相似，转成分数：距离为0 → 1.0，距离为1 → 0.5，距离为2 → 0.33...
                    int distance = LEVENSHTEIN.apply(target, text);
                    return 1.0 / (1 + distance);
                }))
                .orElse(null);
    }

}
