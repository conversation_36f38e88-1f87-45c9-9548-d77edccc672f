package com.qc.agent.platform.file.service;

import com.google.common.collect.Lists;
import com.qc.agent.platform.file.dto.UploadRequest;

import com.qc.agent.storage.StorageClient;
import org.dromara.x.file.storage.core.FileInfo;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Optional;

/**
 *
 * <AUTHOR>
 */

@Service
public class FileUploadService
{
    @Value("${dromara.x-file-storage.local-plus[0].platform}")
    String localPlatform;

    public List<FileInfo> upload(UploadRequest request){
        List<FileInfo> fileInfos = Lists.newArrayList();
        for (MultipartFile file : request.getFiles()) {
            String fileName = StringUtils.cleanPath(file.getOriginalFilename());
            StorageClient client;
            if(request.getUseDetermineFileName() != null &&  request.getUseDetermineFileName()){
                client = StorageClient.of().buildUploader(file).setSaveFilename(fileName);
            }else{
                client = StorageClient.of().buildUploader(file);
            }
            if(Optional.ofNullable(request.getUploadDir()).isPresent()){
                String uploadDir = request.getUploadDir();
                if(uploadDir.startsWith("/")){
                    uploadDir = uploadDir.substring(1);
                }
                client.setPath(uploadDir);
            }
            FileInfo fileInfo = client.upload();
            fileInfos.add(fileInfo);
        }
//        StorageClient.of().buildDownloader("https://image-test.waiqin365.com/ai-agent65eab01fe57bd3ab0103c0c1.pdf").downloadStoragePath("dltemp");
        return fileInfos;
    }

    public String download(String url){
        return StorageClient.of().buildDownloader(url).downloadStoragePath("dltemp");
    }

}
