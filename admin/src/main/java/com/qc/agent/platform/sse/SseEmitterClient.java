package com.qc.agent.platform.sse;

import com.qc.agent.common.core.Message;
import com.qc.agent.common.exception.SystemException;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;

/**
 * SSE client
 *
 * <AUTHOR>
 */
@Slf4j
public class SseEmitterClient {
    private SseEmitter emitter;

    @Getter
    private boolean complete = false;


    /**
     * build SSE client
     *
     * @return SseEmitterClient
     */
    public static SseEmitterClient newBuilder() {
        return new SseEmitterClient();
    }


    /**
     * 发送消息
     *
     * @param message 消息
     * @return 是否发送成功
     */
    public boolean send(String message) {
        return execute((emitter -> {
            try {
                if (!complete && emitter != null) {
                    emitter.send(SseEmitter.event().data(message));
                } else {
                    log.debug("send sse message:{}", message);
                }
            } catch (IOException e) {
                throw new SystemException("Failed to send sse message", e);
            }
            return Boolean.TRUE;
        }));
    }

    /**
     * 发送消息
     *
     * @param message 消息
     * @return 是否发送成功
     */
    public boolean send(Message message) {
        return execute((emitter -> {
            try {
                if (!complete && emitter != null) {
                    Thread.sleep(10L);
                    emitter.send(SseEmitter.event().data(message));
                } else {
                    log.debug("send sse message:{}", message);
                }
            } catch (Exception e) {
                throw new SystemException("Failed to send sse message", e);
            }
            return Boolean.TRUE;
        }));
    }

    /**
     * 发送完成
     */
    public void complete() {
        execute(emitter -> {
            if (!complete && emitter != null) {
                emitter.complete();
                log.info("SseClient 连接关闭");
                complete = Boolean.TRUE;
            }
            return Boolean.TRUE;
        });
    }

    /**
     * Get SSE emitter
     *
     * @return SseEmitter
     */
    public SseEmitter getEmitter() {
        return execute(emitter -> emitter);
    }

    public void initSSE() {
        if (emitter == null) {
            emitter = new SseEmitter(0L);
            emitter.onTimeout(() -> {
                emitter.complete();
                log.error("SseEmitter timeout");
            });
            emitter.onError((e) -> {
                emitter.complete();
                log.error("SseEmitter error", e);
            });
        }
    }


    /**
     * SSE 执行器接口
     *
     * @param <T>
     */
    @FunctionalInterface
    public interface SseEmitterExecutor<T> {
        T execute(SseEmitter sseEmitter);
    }

    /**
     * 执行操作
     *
     * @param executor 执行器
     * @param <T>      返回类型
     * @return 返回结果
     */
    private <T> T execute(SseEmitterExecutor<T> executor) {
        initSSE();
        return executor.execute(emitter);
    }


}
