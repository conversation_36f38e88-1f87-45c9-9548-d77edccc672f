package com.qc.agent.platform.sse.dto;

/**
 *
 * <AUTHOR>
 */
public enum AIVendors
{
    TENCENT("Tencent"),
    BAIDU("Baidu"),
    ALI("Ali"),
    ZHIPU("Zhipu"),
    COZE("coze"),

    OPENAI("openai"),

    KIMI("Kimi"),

    DEEPSEEK("Deepseek");

    private String vendor;

    AIVendors(String vendor)
    {
        this.vendor = vendor;
    }

    public String getVendor()
    {
        return vendor;
    }

    public void setVendor(String vendor){
        this.vendor = vendor;
    }

}
