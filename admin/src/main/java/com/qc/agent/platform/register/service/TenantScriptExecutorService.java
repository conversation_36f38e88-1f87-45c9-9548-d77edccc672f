package com.qc.agent.platform.register.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.qc.agent.common.core.Message;
import com.qc.agent.jdbc.datasource.connection.JdbcConnectionBuilder;
import com.qc.agent.jdbc.datasource.connection.JdbcConnectionProperty;
import com.qc.agent.platform.datasource.model.DatasourceConfig;
import com.qc.agent.platform.datasource.service.DatasourceService;
import com.qc.agent.platform.register.model.ScriptExecutorRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Connection;
import java.sql.Statement;
import java.util.List;

/**
 * 企业脚本执行器服务层
 *
 * <AUTHOR>
 * @date 2025/5/9 10:18:35
 *
 */
@Service
@Slf4j
public class TenantScriptExecutorService
{

    @Resource
    DatasourceService datasourceService;
    @Resource
    TenantScriptExecutorLogService tenantScriptExecutorLogService;

    /**
     * 执行脚本
     * @param request 请求参数
     * @return 响应
     */
    public Message execute(ScriptExecutorRequest request){
        try
        {
            // 获取数据源
            List<DatasourceConfig> configs = datasourceService.queryDataSourceWithSqlClause(request.getSqlClause());
            // 记录执行日志
            tenantScriptExecutorLogService.writeScriptExecuteLog(request);
            // 对企业执行脚本
            JSONArray errors = new JSONArray();
            for(DatasourceConfig config : configs)
            {
                JdbcConnectionProperty jdbcConnectionProperty = config.toJdbcConnectionProperty();
                try(Connection connection = JdbcConnectionBuilder.getConnection(jdbcConnectionProperty);
                        Statement statement = connection.createStatement())
                {
                    String executeSql = request.getExecuteSql();
                    if(StringUtils.isNotEmpty(executeSql)){
                        statement.execute(executeSql);
                    }
                }
                catch(Exception e)
                {
                    JSONObject error = new JSONObject();
                    error.put("tenant",config.getId());
                    error.put("error_message",e.getMessage());
                    errors.add(error);
                    log.error(String.format("执行脚本失败. script=%s", request.getExecuteSql()),e);
                }
            }

            JSONObject result = new JSONObject();
            result.put("total",configs.size());
            result.put("errors",errors);
            tenantScriptExecutorLogService.updateWithExecuteStatus(request.getId(),result);
            return Message.of().ok();
        }
        catch(Exception e)
        {
            log.error("execute script error.",e);
            return Message.of().error("执行脚本出错");
        }

    }

    /**
     * 获得执行企业数
     * @param sqlClause 过滤企业片段
     * @return 响应数据
     */
    public Message getTenantTotal(String sqlClause){
        List<DatasourceConfig> configs = datasourceService.queryDataSourceWithSqlClause(sqlClause);
        return Message.of().data(configs.size()).ok();
    }



}
