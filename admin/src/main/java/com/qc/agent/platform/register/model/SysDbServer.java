package com.qc.agent.platform.register.model;

public class SysDbServer {
    private Long id;

    private String userName;

    private String password;

    private Long maxConnCount;

    private Long minConnCount;

    private String status;

    private String code;

    private String ipAddress;

    private int port;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName == null ? null : userName.trim();
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password == null ? null : password.trim();
    }

    public Long getMaxConnCount() {
        return maxConnCount;
    }

    public void setMaxConnCount(Long maxConnCount) {
        this.maxConnCount = maxConnCount;
    }

    public Long getMinConnCount() {
        return minConnCount;
    }

    public void setMinConnCount(Long minConnCount) {
        this.minConnCount = minConnCount;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code == null ? null : code.trim();
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress == null ? null : ipAddress.trim();
    }

    public int getPort()
    {
        return port;
    }
    public void setPort(int port)
    {
        this.port = port;
    }
}
