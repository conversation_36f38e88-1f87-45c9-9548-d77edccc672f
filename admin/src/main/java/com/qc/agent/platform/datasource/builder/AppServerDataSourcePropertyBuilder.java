package com.qc.agent.platform.datasource.builder;

import com.qc.agent.common.config.datasource.DataSourceEnum;
import com.qc.agent.common.config.db.remote.MasterRemoteService;
import com.qc.agent.common.config.db.remote.vo.DBInfo;
import com.qc.agent.common.config.db.remote.vo.MbsTenant;
import com.qc.agent.common.core.RequestHolder;
import com.qc.agent.common.exception.SystemException;
import com.qc.agent.jdbc.datasource.builder.DataSourcePropertyBuilder;
import com.qc.agent.jdbc.datasource.builder.MultiDataSourcePropertyBuilder;
import com.qc.agent.jdbc.datasource.model.DataSourcePoolType;
import com.qc.agent.jdbc.datasource.model.DataSourceProperty;
import com.qc.agent.platform.register.UserManager;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 *
 * <AUTHOR>
 */
@Configuration
public class AppServerDataSourcePropertyBuilder
        implements DataSourcePropertyBuilder, MultiDataSourcePropertyBuilder
{


    @Lazy
    @Resource
    MasterRemoteService masterRemoteService;

    @Override
    public DataSourceProperty buildProperty() {
        try
        {

            // 获取指定企业的AppServer数据源配置
            MbsTenant mbsTenant = masterRemoteService.getExtTenantDBInfo(RequestHolder.getThreadLocalUser()
                    .getTenantId());
            if(Optional.ofNullable(mbsTenant).isEmpty()){
                throw new SystemException("Get app_server datasource config is null");
            }
            // 构建智能客服的数据源配置
            DataSourceProperty property = buildDataSourceProperty(mbsTenant.getDbInfo());
            if(Optional.ofNullable(property).isEmpty()){
                throw new SystemException("Build datasource config is null");
            }
            return property;
        }
        catch(Exception e)
        {
            throw new SystemException("Get datasource config error",e);
        }
    }

    @Override
    public String determineDataSourceName()
    {
        return DataSourceEnum.APP_SERVER.getName();
    }

    @Override
    public String determineDataSourceId()
    {
        return String.valueOf(UserManager.getTenantUser().getTenantId());
    }

    @Override
    public Boolean determineDefault()
    {
        return false;
    }


    private DataSourceProperty buildDataSourceProperty(DBInfo dbInfo) {
        if(Optional.ofNullable(dbInfo).isPresent()){
            List<String> slaveDataSources = dbInfo.getSlaveDataSources();
            DataSourceProperty property = new DataSourceProperty();
            if(Optional.ofNullable(slaveDataSources).isPresent() && slaveDataSources.size() > 0){
                property.setUrl(slaveDataSources.get(0));
            }else{
                property.setUrl(dbInfo.getDbUrl());
            }
            property.setUserName(dbInfo.getDbUsername());
            property.setPassword(dbInfo.getDbPassword());
            property.setMaxConnectionCount(dbInfo.getDbMaximumConnectionCount());
            property.setMinConnectionCount(dbInfo.getDbMinimumConnectionCount());
            property.setMaxLifetime(dbInfo.getDbHouseKeepingSleepTime());
            property.setType(DataSourcePoolType.HIKARI.name());
            property.setDriver("org.postgresql.Driver");
            property.addSlave(property);
            return property;

        }

        return null;
    }

}
