package com.qc.agent.platform.pojo;

import java.io.Serializable;
import java.sql.Timestamp;

import lombok.Data;

/**
 * Base Entity for common fields
 *
 * <AUTHOR>
 */
@Data
public class BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 状态：1-有效，0-无效
     */
    private String status;

    /**
     * 创建人 ID
     */
    private Long creatorId;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private Timestamp createTime;

    /**
     * 修改人 ID
     */
    private Long modifyierId;

    /**
     * 修改人姓名
     */
    private String modifyierName;

    /**
     * 修改时间
     */
    private Timestamp modifyTime;
}