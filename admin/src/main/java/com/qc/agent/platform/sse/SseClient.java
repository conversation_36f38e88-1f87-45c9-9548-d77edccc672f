package com.qc.agent.platform.sse;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 *
 *
 * <AUTHOR>
 * @date 2025/4/1 17:54:41
 *
 */
@Component
@Slf4j
public class SseClient
{
    // SSE cache
    private final Map<String, SseEmitter> emitters = new ConcurrentHashMap<>();

    /**
     * 根据指定客户端ID发送消息
     * @param clientId 客户端ID
     * @param message 消息
     */
    public SseEmitter send(String clientId, Object message) {
        SseEmitter emitter = emitters.get(clientId);
        if (exists(clientId)) {
            try {
                emitter.send(SseEmitter.event().data(message));
            } catch (IOException e) {
                emitter.complete();
            }
        }
        return emitter;
    }

    /**
     * 关闭指定客户ID的SSE
     * @param clientId 客户端ID
     */
    public void complete(String clientId){
        SseEmitter sseEmitter = emitters.get(clientId);
        if(sseEmitter != null){
            sseEmitter.complete();
        }
    }

    /**
     * 移除指定客户ID的SSE
     * @param clientId 客户端ID
     */
    public void removeSse(String clientId){
        emitters.remove(clientId);
    }

    /**
     * 检查指定客户端ID的SSE是否存在
     * @param clientId 客户端ID
     * @return true 如果存在，false 如果不存在
     */
    public boolean exists(String clientId) {
        return emitters.containsKey(clientId);
    }


    /**
     * 订阅指定客户端ID的SSE
     * @param clientId 客户端ID
     * @return SSE对象
     */
    public SseEmitter subscribe(String clientId) {
        SseEmitter emitter = new SseEmitter(300_000L);
        emitters.put(clientId, emitter);

        emitter.onCompletion(() -> {
                    emitters.remove(clientId);
                    log.info("SSE request completion remove clientId:{} SSE cache success.",clientId);
                }
        );
        emitter.onTimeout(() -> {
            emitters.remove(clientId);
            log.info("SSE request timeout remove clientId:{} SSE cache success.",clientId);
        });
        emitter.onError((ex)-> {
            emitters.remove(clientId);
            log.info("SSE request error remove clientId:{} SSE cache success.",clientId);
        });

        return emitter;
    }

}
