package com.qc.agent.platform.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @className FileProperty
 * @description
 * @date 2024/1/30 19:05
 */
@Configuration
@Data
public class FileProperty {

    @Value("${file.rootPath}")
    private String rootPath;

    @Value("${file.dns.url}")
    private String url;

}
