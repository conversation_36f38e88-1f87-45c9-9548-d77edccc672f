package com.qc.agent.platform.sse;

import com.google.common.collect.Lists;
import com.qc.agent.common.core.Message;
import com.qc.agent.common.core.RequestHolder;
import com.qc.agent.common.core.TenantUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.reactivestreams.Subscriber;
import org.reactivestreams.Subscription;

import java.util.List;
import java.util.Map;
import java.util.concurrent.Flow;

/**
 * 智能客服订阅者
 * <AUTHOR>
 */
@Slf4j
public class AnswerAISubscriber<T> implements Flow.Subscriber<T>, Subscriber<T>
{

    private SseEmitterClient sseClient;

    // 消息集合
    List<String> messages = Lists.newArrayList();

    // 请求参数
    SseRequest request;

    // 消息转换器
    SseMessageConverter sseMessageConverter;

    // 完成回调
    SseCompleteCallback callback;

    NextError<T> nextError;

    // 发送消息的切面
    BeforeSendAspect beforeSendAspect;

    //企业用户
    TenantUser tu;


    private AnswerAISubscriber(SseEmitterClient sseClient)
    {
        this.sseClient = sseClient;
    }

    public static AnswerAISubscriber builder(){
        AnswerAISubscriber  subscriber = new AnswerAISubscriber(SseEmitterClient.newBuilder());
        return subscriber;
    }

    public static AnswerAISubscriber builder(SseEmitterClient client){
        AnswerAISubscriber  subscriber = new AnswerAISubscriber(client);
        return subscriber;
    }

    @Override
    public void onSubscribe(Flow.Subscription subscription)
    {
        subscription.request(Long.MAX_VALUE);
        init();
    }

    @Override
    public void onSubscribe(Subscription subscription)
    {
        subscription.request(Long.MAX_VALUE);
        init();
    }



    @Override
    public void onNext(T t)
    {
        try
        {
            // 记录请求耗时日志
            if(request.getStartTime()>0){
                log.info("问题：{}? 回答请求耗时：{}ms",request.getQuestion(),(System.currentTimeMillis() - request.getStartTime()));
                request.setStartTime(0);
            }

            // 消息转换接口
            SseMessage message = sseMessageConverter.convert(t, request).addQuestionId(String.valueOf(request.getQuestionId()));
            if(beforeSendAspect != null){
                beforeSendAspect.execute(message);
            }

            String msg = message.getMessage();
            if(StringUtils.isNotEmpty(msg)){
                boolean isEnd = message.isEnd();
                Map<String, Object> extData = message.getExtData();
                String[] split = msg.split("");
                for (int i = 0; i < split.length; i++) {
                    message.setMessage(split[i]);
                    if(i == split.length - 1 && isEnd){
                        message.setEnd(isEnd);
                        message.setExtData(extData);
                    }else{
                        message.setEnd(false);
                        message.setExtData(null);
                    }
                    sseClient.send(Message.of().data(message).ok().toJsonString());
                    try
                    {
                        if(request.getSleepTime() > 0){
                            Thread.sleep(request.getSleepTime());
                        }
                    }
                    catch(InterruptedException e)
                    {
                        log.error("发送消息异常", e);
                    }
                }
                messages.add(msg);

            }else {
                sseClient.send(Message.of().data(message).ok().toJsonString());
            }

            // 判断消息是否结束
            if(message.isEnd()){
                if(!request.isStopped()){
                    sseClient.complete();
                    // 执行完成回调
                    if(callback != null){
                        callback.onComplete(request,messages);
                    }
                }
            }
        }
        catch(Exception e)
        {

            log.error(String.format("执行onNext方法出错. message=%s", t), e);
            if(nextError != null && messages.isEmpty()){
                nextError.onError(t);
            }
            throw new RuntimeException(e);
        }

    }


    @Override
    public void onError(Throwable throwable)
    {
        log.error("订阅异常", throwable);
        dispose();
    }


    @Override
    public void onComplete()
    {
        log.info("订阅完成");
        dispose();
    }

    public void dispose(){
        if(!sseClient.isComplete()){
            log.info("dispose");
            sseClient.send(Message.of().data(SseMessage.buildEndMessage()).ok().toJsonString());
            sseClient.complete();
        }
    }


    public void addCompleteCallback(SseCompleteCallback callback){
        this.callback = callback;
    }

    public AnswerAISubscriber addRequestParam(SseRequest request){
        this.request = request;
        return this;
    }

    public AnswerAISubscriber addTenantUser(TenantUser tu){
        this.tu = tu;
        return this;
    }

    public AnswerAISubscriber addSseMessageConverter(SseMessageConverter sseMessageConverter){
        this.sseMessageConverter = sseMessageConverter;
        return this;
    }

    public SseEmitterClient  getSseClient(){
        return  sseClient;
    }

    public AnswerAISubscriber addNextError(NextError nextError){
        this.nextError = nextError;
        return this;
    }

    public AnswerAISubscriber addBeforeSendAspect(BeforeSendAspect beforeSendAspect){
        this.beforeSendAspect = beforeSendAspect;
        return this;
    }

    @FunctionalInterface
    public interface NextError<T>{
        void onError(T message);
    }

    @FunctionalInterface
    public interface BeforeSendAspect{
        void execute(SseMessage message);
    }

    private void init(){
//        sseClient.initSSE();
        if(RequestHolder.getThreadLocalUser() == null){
            if(tu != null){
                RequestHolder.setThreadLocalUser(tu);
            }
        }
    }

    public boolean isStop(){
        return this.request.isStopped();
    }

    public void stop(){
        this.request.setStopped(true);
        this.sseClient.complete();
    }

    public String subStringMessage(int index){
        if(CollectionUtils.isNotEmpty(messages)){
            String join = StringUtils.join(messages, "");
            if(join.length() >= index){
                return join.substring(index);
            }
        }
        return "";
    }

}
