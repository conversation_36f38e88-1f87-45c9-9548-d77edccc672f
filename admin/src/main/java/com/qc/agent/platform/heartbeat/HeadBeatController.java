package com.qc.agent.platform.heartbeat;

import com.qc.agent.platform.pojo.Message;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * @description：TODO
 * @author： sc
 * @create： 2024/5/27 16:32
 */
@RestController
@RequestMapping("/ai_agent")
public class HeadBeatController {

    @RequestMapping("/heartBeat.do")
    public Object hearBeat(){
        return Message.success(null);
    }

}
