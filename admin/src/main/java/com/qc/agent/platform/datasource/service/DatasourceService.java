package com.qc.agent.platform.datasource.service;

import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.qc.agent.common.config.datasource.annotation.AnswerAI;
import com.qc.agent.common.core.TenantUser;
import com.qc.agent.platform.datasource.mapper.DatasourceMapper;
import com.qc.agent.platform.datasource.model.DatasourceConfig;
import com.qc.agent.redis.RedisClient;

/**
 *
 * <AUTHOR>
 */
@Service
public class DatasourceService {
    @Resource
    DatasourceMapper datasourceMapper;

    @Resource
    RedisClient redisClient;

    public static final String DATASOURCE_CONFIG_CACHE_KEY = "agent_datasourceConfig:";

    @AnswerAI
    public DatasourceConfig getDatasourceConfig(Long tenantId) {
        // 构建缓存key
        String cacheKey = DATASOURCE_CONFIG_CACHE_KEY + tenantId;

        // 尝试从缓存获取
        Object cachedConfig = redisClient.get(cacheKey);
        if (cachedConfig != null) {
            // 如果缓存存在，反序列化返回完整配置
            try {
                return JSON.parseObject(cachedConfig.toString(), DatasourceConfig.class);
            } catch (Exception e) {
                // 反序列化失败，删除缓存并重新查询
                redisClient.set(cacheKey, "", 1);
            }
        }

        DatasourceConfig config = datasourceMapper.queryDbDataSourceConfig(tenantId);

        // 缓存结果，设置30分钟过期时间
        if (config != null) {
            try {
                String configJson = JSON.toJSONString(config);
                redisClient.set(cacheKey, configJson, 1800);
            } catch (Exception e) {
                // 序列化失败，不缓存
            }
        }

        return config;
    }

    @AnswerAI
    public List<DatasourceConfig> queryDataSourceWithSqlClause(String sqlClause) {
        return datasourceMapper.queryDataSourceWithSqlClause(sqlClause);
    }

    @AnswerAI
    public void addTenantDbDatasource(TenantUser tu) {
        DatasourceConfig datasourceConfig = new DatasourceConfig();
        datasourceConfig.setId(tu.getTenantId());
        datasourceConfig.setCreator(tu.getUserId());
        datasourceConfig.setCreationTime(new Date());
        datasourceConfig.setUserName(String.format("ai_agent_%s", tu.getTenantId()));
        datasourceConfig.setPassword("QC365");
        datasourceConfig.setMaxConnCount(50);
        datasourceConfig.setMinConnCount(10);
        datasourceConfig.setMaxConnLifetime(5000L);
        datasourceConfig.setServerId(1L);
        datasourceConfig.setSleepKeepTime(60000L);
        datasourceConfig.setStatus("1");
        datasourceMapper.insertDbDataSourceConfig(datasourceConfig);

    }
}
