package com.qc.agent.platform.pojo;


import com.alibaba.fastjson.JSON;

import java.util.List;

/**
 *
 *	@描述：Ajax分页对象
 *	@包名：com.fh.iasp.platform.newpage.pojo
 *	@类名：AjaxPage
 *	@日期：Mar 19, 2013
 *	@版权：Copyright ® 烽火星空. All right reserved.
 *	@作者：SUNFG
 */

public class AjaxPage
{
    private int total;
    private List rows;
    private List footer;
    /**
     * 构造对象
     */
    public AjaxPage()
    {

    }

    public int getTotal()
    {
        return total;
    }
    public void setTotal(int total)
    {
        this.total = total;
    }
    public List getRows()
    {
        return rows;
    }
    public void setRows(List rows)
    {
        this.rows = rows;
    }
    /**
     * 分页对象转换为JSON字符串
     * @return JSON字符串
     */
    public String toJsonString()
    {
        return JSON.toJSONString(this);
    }

	public List getFooter()
	{
		return footer;
	}
	public void setFooter(List footer)
	{
		this.footer = footer;
	}
}
