package com.qc.agent.platform.sse;

import com.qc.agent.platform.sse.dto.AIVendors;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */

@Configuration
public class AnswerAIRequestBuilder implements ApplicationContextAware
{
    // 厂商
    private static AIVendors vendor;

    // 工厂商请求实现
    static Map<AIVendors, AnswerAIRequest> requests = new HashMap<>();

    /**
     * 初始化厂商请求实现
     * @param applicationContext
     * @throws BeansException
     */
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException
    {
        applicationContext.getBeansOfType(AnswerAIRequest.class).
                values().
                forEach(request -> requests.put(request.determineAIVendors(), request));
    }

    /**
     * 获取指定厂商的请求实现
     * @return
     */
    public static AnswerAIRequest getRequest(){
        return requests.get(vendor);
    }

    /**
     * 获取指定厂商的请求实现
     * @return
     */
    public static AnswerAIRequest getRequest(AIVendors vendor){
        if(vendor == null){
            return getRequest();
        }else{
            return requests.get(vendor);
        }
    }

    /**
     * 注入当前请求的厂商
     * @param vendor
     */
    @Value("${ai.agent.vendor}")
    public void setVendor(AIVendors vendor){
        this.vendor = vendor;
    }


}
