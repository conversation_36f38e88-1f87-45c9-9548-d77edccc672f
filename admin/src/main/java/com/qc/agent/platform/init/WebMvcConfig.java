package com.qc.agent.platform.init;

import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer
{
    @Resource
    ApplicationContext applicationContext;
    @Override
    public void extendMessageConverters(List<HttpMessageConverter<?>> converters) {
        List<CustomHttpMessageConverter> converterList = applicationContext.getBeansOfType(CustomHttpMessageConverter.class)
                .values().stream().collect(Collectors.toList());

        for(int i = 0; i < converterList.size(); i++)
        {
            converters.add(i,converterList.get(i).buildExtendMessageConverters());
        }

    }

}
