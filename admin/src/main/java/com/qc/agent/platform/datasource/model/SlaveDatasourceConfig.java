package com.qc.agent.platform.datasource.model;

public class SlaveDatasourceConfig
{
    private Long id;

    private String userName;

    private String password;

    private Long maxConnCount;

    private Long minConnCount;

    private Long sleepKeepTime;

    private Long serverId;

    private String status;

    private Long primaryDatasource;

    private Long maxConnLifetime;

    private ServerConfig server;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName == null ? null : userName.trim();
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password == null ? null : password.trim();
    }

    public Long getMaxConnCount() {
        return maxConnCount;
    }

    public void setMaxConnCount(Long maxConnCount) {
        this.maxConnCount = maxConnCount;
    }

    public Long getMinConnCount() {
        return minConnCount;
    }

    public void setMinConnCount(Long minConnCount) {
        this.minConnCount = minConnCount;
    }

    public Long getSleepKeepTime() {
        return sleepKeepTime;
    }

    public void setSleepKeepTime(Long sleepKeepTime) {
        this.sleepKeepTime = sleepKeepTime;
    }

    public Long getServerId() {
        return serverId;
    }

    public void setServerId(Long serverId) {
        this.serverId = serverId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    public Long getPrimaryDatasource() {
        return primaryDatasource;
    }

    public void setPrimaryDatasource(Long primaryDatasource) {
        this.primaryDatasource = primaryDatasource;
    }

    public Long getMaxConnLifetime() {
        return maxConnLifetime;
    }

    public void setMaxConnLifetime(Long maxConnLifetime) {
        this.maxConnLifetime = maxConnLifetime;
    }

    public ServerConfig getServer()
    {
        return server;
    }
    public void setServer(ServerConfig server)
    {
        this.server = server;
    }
}
