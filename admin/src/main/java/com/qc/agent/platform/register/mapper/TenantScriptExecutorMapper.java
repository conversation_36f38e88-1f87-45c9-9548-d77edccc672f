package com.qc.agent.platform.register.mapper;

import com.qc.agent.app.agent.model.po.QcAiAgentCategory;
import com.qc.agent.platform.register.model.ScriptExecutorRequest;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 企业脚本执行器数据层
 *
 * <AUTHOR>
 * @date 2025/5/9 14:08:53
 *
 */
public interface TenantScriptExecutorMapper {

    int insert(ScriptExecutorRequest request);

    int updateWithExecuteStatus(@Param("id") String id,@Param("message") String executeMessage);

    ScriptExecutorRequest selectById(@Param("id") String id);

    List<ScriptExecutorRequest> selectAll(@Param("likeCondition") String likeCondition);
}
