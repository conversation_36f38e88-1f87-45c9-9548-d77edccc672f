package com.qc.agent.platform.user;

import com.qc.agent.common.core.RequestHolder;
import com.qc.agent.common.core.TenantUser;
import com.qc.agent.common.interceptor.ValidatorInterceptor;
import com.qc.agent.platform.user.service.AppServerUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Optional;

/**
 *
 * <AUTHOR>
 */
@Component
public class UserValidator implements ValidatorInterceptor
{
    Logger logger = LoggerFactory.getLogger(UserValidator.class);
    @Lazy
    @Resource
    AppServerUserService appServerUserService;

    @Override
    public ResponseEntity validate(HttpServletRequest request)
    {
        try
        {
            TenantUser user = appServerUserService.getUserByUserId(RequestHolder.getThreadLocalUser());
            if(Optional.ofNullable(user).isPresent()){
                user.setAdminUserId(appServerUserService.getAdminUserId());
                RequestHolder.setThreadLocalUser(user);
                return ResponseEntity.ok("success");
            }else{
                return ResponseEntity.badRequest().body("用户不存在");
            }
        }
        catch(Exception e)
        {
            logger.error("用户验证出错",e);
            return ResponseEntity.badRequest().body("用户验证出错");
        }

    }
}
