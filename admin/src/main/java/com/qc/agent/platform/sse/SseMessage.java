package com.qc.agent.platform.sse;

import com.qc.agent.platform.sse.dto.MessageType;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 *
 * <AUTHOR>
 */
@Data
@Builder
public class SseMessage
{
   private MessageType messageType;
   private String message;
   private boolean isEnd;
   private Map<String, Object> extData;
   private String questionId;

   public SseMessage addQuestionId(String questionId){
      this.questionId = questionId;
      return this;
   }


   public static SseMessage buildEmptyMessage(){
      return SseMessage.builder().message(StringUtils.EMPTY).messageType(MessageType.TEXT).build();
   }

   public static SseMessage buildEndMessage(){
      SseMessage emptyMessage = buildEmptyMessage();
      emptyMessage.setEnd(true);
      return emptyMessage;
   }

}
