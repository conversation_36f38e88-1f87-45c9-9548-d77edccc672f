package com.qc.agent.platform.user.service;

import com.qc.agent.common.config.datasource.annotation.AppServer;
import com.qc.agent.common.core.TenantUser;
import com.qc.agent.platform.pojo.QcProductInfo;
import com.qc.agent.platform.pojo.QcUserInfo;
import com.qc.agent.platform.pojo.SysDepartment;
import com.qc.agent.platform.user.mapper.AppServerUserMapper;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 *
 * <AUTHOR>
 */
@Service
public class AppServerUserService
{
    @Resource
    AppServerUserMapper appServerUserMapper;

    @AppServer
    public TenantUser getUserByUserId(TenantUser tu){
        return appServerUserMapper.selectUserByUserId(tu);
    }

    @AppServer
    public Long getAdminUserId(){
        return appServerUserMapper.selectAdminUserId();
    }

    @AppServer
    public List<Long> getParentDeptIds(Long deptId){
        return appServerUserMapper.getParentDeptIds(deptId);
    }

    @AppServer
    public List<QcUserInfo> getAllDeptList(List<Long> userIdList){
        if (CollectionUtils.isEmpty(userIdList)) {
            return Collections.emptyList();
        }
        return appServerUserMapper.getAllDeptList(userIdList);
    }

    @AppServer
    public SysDepartment getDeptInfo(Long deptId) {
        return appServerUserMapper.queryDeptById(deptId);
    }

    @AppServer
    public Long selectDeptIdByUserId(Long userId) {
        return appServerUserMapper.selectDeptIdByUserId(userId);
    }

    @AppServer
    public List<QcProductInfo> getProductInfo() {
        return appServerUserMapper.getProductInfo();
    }
}
