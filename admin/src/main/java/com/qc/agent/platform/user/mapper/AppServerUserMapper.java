package com.qc.agent.platform.user.mapper;

import com.qc.agent.common.core.TenantUser;
import com.qc.agent.platform.pojo.QcProductInfo;
import com.qc.agent.platform.pojo.QcUserInfo;
import com.qc.agent.platform.pojo.SysDepartment;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface AppServerUserMapper {
    TenantUser selectUserByUserId(TenantUser tu);

    Long selectAdminUserId();

    List<Long> getParentDeptIds(@Param("deptId") Long deptId);

    List<QcUserInfo> getAllDeptList(@Param("userIdList") List<Long> userIdList);

    SysDepartment queryDeptById(@Param("deptId") Long deptId);

    Long selectDeptIdByUserId(@Param("userId") Long userId);

    List<QcProductInfo> getProductInfo();
}
