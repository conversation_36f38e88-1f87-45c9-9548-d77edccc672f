package com.qc.agent.platform.register.controller;

import com.qc.agent.common.core.Message;
import com.qc.agent.platform.register.model.ScriptExecutorRequest;
import com.qc.agent.platform.register.service.TenantScriptExecutorLogService;
import com.qc.agent.platform.register.service.TenantScriptExecutorService;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 企业脚本执行器控制层
 *
 * <AUTHOR>
 * @date 2025/5/9 10:14:31
 *
 */
@RequestMapping("/ai-agent/script")
@RestController
public class TenantScriptExecutorController
{

    @Resource
    TenantScriptExecutorService tenantScriptExecutorService;
    @Resource
    TenantScriptExecutorLogService tenantScriptExecutorLogService;


    /**
     * 获取执行企业数
     * @return 响应结果
     */
    @RequestMapping("/queryLogs.do")
    public Message queryLogs(String likeCondition) {
        return Message.of().data(tenantScriptExecutorLogService.getLogs(likeCondition)).ok();
    }
    /**
     * 获取执行企业数
     * @return 响应结果
     */
    @RequestMapping("/getDetail.do")
    public Message getDetail(String id) {
        return Message.of().data(tenantScriptExecutorLogService.getDetail(id)).ok();
    }

    /**
     * 脚本执行
     * @return 响应结果
     */
    @RequestMapping("/execute.do")
    public Message execute(@RequestBody ScriptExecutorRequest request) {
        return tenantScriptExecutorService.execute(request);
    }
    /**
     * 获取执行企业数
     * @return 响应结果
     */
    @RequestMapping("/getTenantTotal.do")
    public Message getTenantTotal(String sqlClause) {
        return tenantScriptExecutorService.getTenantTotal(sqlClause);
    }
}
