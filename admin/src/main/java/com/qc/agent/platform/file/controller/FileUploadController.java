package com.qc.agent.platform.file.controller;


import com.google.common.collect.Lists;
import com.qc.agent.app.knowledge.utils.ExcelHandler;
import com.qc.agent.common.core.Message;
import com.qc.agent.platform.file.dto.UploadRequest;
import com.qc.agent.platform.file.service.FileUploadService;
import org.dromara.x.file.storage.core.FileInfo;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@RequestMapping("/ai-agent/file")
@RestController
public class FileUploadController
{
    @Resource
    FileUploadService fileUploadService;


    @PostMapping(value = "/fileUpload.do", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Message fileUpload(UploadRequest request) {
        List<FileInfo> fileInfos = fileUploadService.upload(request);
        // 返回成功消息，也可以返回其它响应
        return Message.of().data(fileInfos).ok();
    }

    @PostMapping(value = "/fileDownload.do")
    public Message fileUpload(String url) {
        // 返回成功消息，也可以返回其它响应
        return Message.of().data(fileUploadService.download(url)).ok();
    }

    /**
     * 上传excel
     * @param file 文件
     * @return Message
     */
    @PostMapping(value = "/excelUpload.do", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Message excelUpload(MultipartFile  file) {
        UploadRequest request = UploadRequest.builder().files(Lists.newArrayList(file)).uploadDir("6928113655533238145/knowledgeBase/").useDetermineFileName(true).build();
        ExcelHandler.readExcelAndUpload(request, param -> {
            // TODO 处理数据
            return null;
        });
        // 返回成功消息，也可以返回其它响应
        return Message.of().ok();
    }

    /**
     * 保存excel
     * @param filePath 文件路径
     * @return Message
     */
    @PostMapping(value = "/saveExcel.do")
    public Message saveExcel(String filePath) {

        ExcelHandler.saveExcel(filePath, workbook -> {
            // 新增
            ExcelHandler.addRow(workbook, "Sheet1", new String[]{"30", "30"});
            // 修改
//            ExcelHandler.updateCell(workbook, "Sheet1", 29, 1, "2901");
            // 删除
//            ExcelHandler.deleteRow(workbook, "Sheet1", 3);
            return null;
        });
        // 返回成功消息，也可以返回其它响应
        return Message.of().ok();
    }






}
