package com.qc.agent.platform.register.service;

import com.qc.agent.common.config.datasource.annotation.AppServer;
import com.qc.agent.common.core.TenantUser;
import com.qc.agent.common.exception.SystemException;
import com.qc.agent.jdbc.datasource.connection.JdbcConnectionBuilder;
import com.qc.agent.jdbc.datasource.connection.JdbcConnectionProperty;
import com.qc.agent.jdbc.datasource.model.DataSourceType;
import com.qc.agent.platform.datasource.model.DatasourceConfig;
import com.qc.agent.platform.datasource.service.DatasourceService;
import com.qc.agent.platform.register.UserManager;
import com.qc.agent.platform.register.mapper.TenantRegisterMapper;
import com.qc.agent.redis.RedisClient;
import com.qc.agent.vectordb.tencentdb.tencentDbClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.util.StreamUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Service
public class TenantRegisterService {

    @Resource
    private List<TenantRegisterHook> registerHooks;

    Logger logger = LoggerFactory.getLogger(TenantRegisterService.class);

    @Resource
    TenantRegisterMapper tenantRegisterMapper;

    @Resource
    DatasourceService datasourceService;

    @Resource
    private tencentDbClient dbClient;

    @Resource
    private RedisClient redisClient;


    private static final String REGISTER_TENANT_KEY = "ai-agent:register:tenant";

    @AppServer(type = DataSourceType.SLAVE)
    public void test(TenantUser tu) {
        List<Map<String, Object>> rows = tenantRegisterMapper.getById(tu.getUserId());
        System.out.printf("rows = %s", rows);
    }

    /**
     * 智能客服企业注册
     *
     * @param tu
     */
    public void register(TenantUser tu) {
        DatasourceConfig datasourceConfig = datasourceService.getDatasourceConfig(tu.getTenantId());
        if (Optional.ofNullable(datasourceConfig).isEmpty()) {

            /**
             * 1.创建数据源配置
             */
            logger.info("register tenant:{}", tu.getTenantId());
            datasourceService.addTenantDbDatasource(tu);
            datasourceConfig = datasourceService.getDatasourceConfig(tu.getTenantId());

            /**
             *  2.创建数据库
             */
            buildTenantDatabase(datasourceConfig);
            logger.info(tu.getTenantId() + " database build success");

            /**
             *   3.初始化数据库
             */
            initDatabase(datasourceConfig);
            logger.info(tu.getTenantId() + " init database success");

            /**
             *    4.创建向量库
             */
            dbClient.initClient();
            String databaseName = "db-" + UserManager.getTenantUser().getTenantId(); //企业数据库
            dbClient.createDatabase(databaseName);

            /**
             *    5.创建qa数据库
             */
            String qaDataBaseName = "QA-database-" + UserManager.getTenantUser().getTenantId();
            dbClient.createQADatabase(qaDataBaseName);
            redisClient.sSet(REGISTER_TENANT_KEY, Objects.toString(UserManager.getTenantUser().getTenantId()));
            
            // 调用注册后的hook设置预置问题
            if (registerHooks != null) {
                DatasourceConfig finalDatasourceConfig = datasourceConfig;
                registerHooks.forEach(hook -> hook.afterRegister(finalDatasourceConfig));
            }
        }

    }

    public boolean checkIsRegister() {
        return redisClient.isMember(REGISTER_TENANT_KEY, Objects.toString(UserManager.getTenantUser().getTenantId()));
    }

    /**
     * 构建企业数据库
     *
     * @param datasourceConfig
     */
    private void buildTenantDatabase(DatasourceConfig datasourceConfig) {
        JdbcConnectionProperty property = datasourceConfig.getServer().toJdbcConnectionProperty();
        String dbName = datasourceConfig.getUserName();
        try (Connection connection = JdbcConnectionBuilder.getConnection(property);
             Statement statement = connection.createStatement()) {
            connection.setTransactionIsolation(8);
            // 创建用户
            String userScript = buildrUserScript(dbName, datasourceConfig.getPassword());
            for (String sql : userScript.split(";")) {
                statement.execute(sql);
            }

            // 创建数据库
            String databaseScript = buildrDatabaseScript(dbName, dbName);
            for (String sql : databaseScript.split(";")) {
                statement.execute(sql);
            }
//            statement.executeBatch();
//            statement.clearBatch();
        } catch (SQLException e) {
            throw new SystemException("构建数据库失败！", e);
        }

    }

    /**
     * 初始化数据库
     *
     * @param datasourceConfig
     */
    private void initDatabase(DatasourceConfig datasourceConfig) {
        JdbcConnectionProperty jdbcConnectionProperty = datasourceConfig.toJdbcConnectionProperty();
        try (Connection connection = JdbcConnectionBuilder.getConnection(jdbcConnectionProperty);
             Statement statement = connection.createStatement()) {
            // 初始化表
            statement.execute(buildTablesScript());
            statement.execute(buildDataScript());

        } catch (Exception e) {
            throw new SystemException("初始化数据库失败！", e);
        }
    }

    /**
     * 构建创用户脚本
     *
     * @param userName
     * @param password
     * @return
     */
    private String buildrUserScript(String userName, String password) {
        StringBuffer sqlScript = new StringBuffer();
        sqlScript.append("SET statement_timeout = 0;");
        sqlScript.append("SET client_encoding = 'UTF8';");
        sqlScript.append("SET standard_conforming_strings = off;");
        sqlScript.append("SET client_min_messages = warning;");
        sqlScript.append("SET escape_string_warning = off;");
        sqlScript.append(String.format("CREATE ROLE %s LOGIN ENCRYPTED PASSWORD '%s';", userName, password));
        return sqlScript.toString();
    }

    /**
     * 构建创建数据库脚本
     *
     * @param databaseName
     * @param userName
     * @return
     */
    private String buildrDatabaseScript(String databaseName, String userName) {
        StringBuffer sqlScript = new StringBuffer();
        sqlScript.append(String.format("CREATE DATABASE %s WITH ENCODING='UTF8' TEMPLATE template0 TABLESPACE pg_default CONNECTION LIMIT=-1;", databaseName));
        sqlScript.append(String.format("ALTER DATABASE %s OWNER TO %s", databaseName, userName));
        return sqlScript.toString();
    }

    /**
     * 构建表结构脚本
     *
     * @return
     */
    private String buildTablesScript() {
        try {
            ClassPathResource resource = new ClassPathResource("sql/tables.sql");
            return StreamUtils.copyToString(resource.getInputStream(), StandardCharsets.UTF_8);
        } catch (IOException e) {
            logger.error("构建表结构脚本失败", e);
            throw new SystemException("构建表结构脚本失败", e);
        }
    }

    /**
     * 构建表结构脚本
     *
     * @return
     */
    private String buildDataScript() {
        try {
            ClassPathResource resource = new ClassPathResource("sql/data.sql");
            return StreamUtils.copyToString(resource.getInputStream(), StandardCharsets.UTF_8);
        } catch (IOException e) {
            logger.error("构建表结构脚本失败", e);
            throw new SystemException("构建表结构脚本失败", e);
        }
    }
}
