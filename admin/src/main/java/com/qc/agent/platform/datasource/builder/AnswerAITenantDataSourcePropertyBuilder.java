package com.qc.agent.platform.datasource.builder;

import com.qc.agent.common.config.datasource.DataSourceEnum;
import com.qc.agent.common.core.RequestHolder;
import com.qc.agent.jdbc.datasource.builder.DataSourcePropertyBuilder;
import com.qc.agent.jdbc.datasource.builder.MultiDataSourcePropertyBuilder;
import com.qc.agent.jdbc.datasource.model.DataSourceProperty;
import com.qc.agent.platform.datasource.model.DatasourceConfig;
import com.qc.agent.platform.datasource.service.DatasourceService;
import com.qc.agent.platform.register.UserManager;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

import javax.annotation.Resource;

/**
 *
 * <AUTHOR>
 */
@Configuration
public class AnswerAITenantDataSourcePropertyBuilder
        implements DataSourcePropertyBuilder, MultiDataSourcePropertyBuilder
{

    @Lazy
    @Resource
    DatasourceService datasourceService;

    @Override
    public DataSourceProperty buildProperty() {
        DatasourceConfig datasourceConfig = datasourceService.getDatasourceConfig(RequestHolder.getThreadLocalUser()
                .getTenantId());
        return datasourceConfig.buildDataSourceProperty();
    }

    @Override
    public String determineDataSourceName()
    {
        return DataSourceEnum.ANSWER_AI_TENANT.getName();
    }

    @Override
    public String determineDataSourceId()
    {
        return String.valueOf(UserManager.getTenantUser().getTenantId());
    }

    @Override
    public Boolean determineDefault()
    {
        return true;
    }

}
