package com.qc.agent.platform.config;

import com.alibaba.fastjson.support.config.FastJsonConfig;
import com.alibaba.fastjson.support.spring.FastJsonHttpMessageConverter;
import com.google.common.collect.Lists;
import com.qc.agent.platform.init.CustomHttpMessageConverter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 *
 * <AUTHOR>
 */
@Component
public class CustomFastJsonConverter implements CustomHttpMessageConverter
{
    @Override
    public HttpMessageConverter buildExtendMessageConverters()
    {
        FastJsonHttpMessageConverter converter = new FastJsonHttpMessageConverter();

        FastJsonConfig config = new FastJsonConfig();
        config.setDateFormat("yyyy-MM-dd HH:mm:ss");
        // 其他自定义配置...
        converter.setFastJsonConfig(config);
        converter.setSupportedMediaTypes(Lists.newArrayList(MediaType.APPLICATION_JSON));
        converter.setCharset(StandardCharsets.UTF_8);

        return converter;
    }
}
