package com.qc.agent.platform.register.controller;

import com.qc.agent.common.core.Message;
import com.qc.agent.platform.register.UserManager;
import com.qc.agent.platform.register.service.TenantRegisterService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 *
 * <AUTHOR>
 */
@RequestMapping("/ai-agent/tenant")
@RestController
public class TenantRegisterController
{

    @Resource
    TenantRegisterService tenantRegisterService;

    /**
     * 智能客服企业注册
     * @return
     */
    @RequestMapping("/register.do")
    public Message register() {
        tenantRegisterService.register(UserManager.getTenantUser());
        return Message.of().ok("注册成功！");
    }


    /**
     * 智能客服企业注册
     * @return
     */
    @RequestMapping("/test.do")
    public Message test() {
        tenantRegisterService.test(UserManager.getTenantUser());
        return Message.of().ok();
    }


}
