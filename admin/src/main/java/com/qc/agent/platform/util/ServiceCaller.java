package com.qc.agent.platform.util;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * @description：TODO
 * @author： sc
 * @create： 2024/7/13 15:40
 */
@Component
public class ServiceCaller {

    @Resource
    private RestTemplate restTemplate;

    @Value("${ai.agent.appsvr-domin-url}")
    private String appsvrDominUrl;

    public String postCall(String url, Object request, Map<String, String> extraHeaders) throws Exception {
        if(extraHeaders == null) {
            extraHeaders = new HashMap<>();
        }
        url = appsvrDominUrl + "/" + url;
        return RestTemplateUtils.post(restTemplate, url, request, String.class, extraHeaders);
    }


}
