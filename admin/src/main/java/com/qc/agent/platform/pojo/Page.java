package com.qc.agent.platform.pojo;

/**
 * @description：TODO
 * @author： sc
 * @create： 2024/10/12 14:38
 */
public class Page {

    private Integer page = 1;

    private Integer rows = 100;

    private Integer limit;

    private Integer offset;

    public void doPage(){
        if(rows != null && rows > 0){
            this.limit = rows;
        }else{
            this.limit = 100000;
        }
        if(page != null && page > 0 && rows != null && rows > 0){
            this.offset = (page - 1) * rows;
        }else{
            this.offset = 0;
        }
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getRows() {
        return rows;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getLimit() {
        if(rows != null && rows > 0){
            return rows;
        }else{
            return 100;
        }
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getOffset() {
        if(page != null && page > 0 && rows != null && rows > 0){
            return (page - 1) * rows;
        }else{
            return 0;
        }
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }
}
