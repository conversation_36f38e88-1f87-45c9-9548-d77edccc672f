package com.qc.agent.platform.sse;

import com.qc.agent.platform.sse.dto.AnswerAIConfig;
import com.qc.agent.platform.sse.dto.AnswerAIMessage;
import com.qc.agent.vectordb.pojo.ChatQA;
import com.qc.agent.vectordb.pojo.SearchContent;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
public class SseRequest {
    private String clientId;
    private String user;
    private String question;
    private long sleepTime;
    private long startTime;
    private Long questionId;
    private List<SearchContent> callWords;
    private List<ChatQA> histories;
    private AnswerAIConfig config;
    private Long similarQuestionId;
    private String msgId;
    private String sessionId;
    private boolean stopped;
    /**
     * 问答检索范围
     */
    private String answerSearchRange;
    /**
     * 模型prompt
     */
    private String prompt;
    /**
     * 展示引用
     */
    private String showReference;
    /**
     * 模型无法及时回复提示语
     */
    private String errorAnswer;

    public List<AnswerAIMessage> buildMessage(int limit) {
        List<AnswerAIMessage> messages = new ArrayList<>();

        for (ChatQA qa : histories) {
            messages.add(AnswerAIMessage.builder().role("user").content(qa.getPrompt() + qa.getQuestion()).build());
            messages.add(AnswerAIMessage.builder().role("assistant").content(qa.getAnswer()).build());
        }
        messages.add(AnswerAIMessage.builder().role("user").content(getPrompt() + question).build());
        if (messages.size() > limit) {
            messages = messages.subList(messages.size() - limit, messages.size());
        }
        if (CollectionUtils.isNotEmpty(messages) && !"user".equals(messages.get(0).getRole())) {
            messages.remove(0);
        }
        return messages;
    }

    public String getPrompt() {
        if (CollectionUtils.isNotEmpty(callWords)) {
            StringBuffer prompt = StringUtils.isNotEmpty(this.prompt) ? new StringBuffer(this.prompt) :
                    new StringBuffer("你是一个客服，下面会给你一段背景知识，背景知识中包含多段正文和多个链接，其中每个链接属于它前一个正文。" +
                            "你需要根据背景知识回答我的问题，要求如下：\n" +
                            "1、严格按照背景知识中的内容回答，禁止回答背景知识不包含的内容。\n" +
                            "2、如果背景知识中不存在问题的答案，你要回答不知道。\n" +
                            "3、答案要包含正文和链接，并把链接放在正文后面，。\n" +
                            "4、禁止访问链接。\n" +
                            "背景知识：");
            for (SearchContent content : callWords) {
                prompt.append(content.getContent() + content.getAppendix() + "\n");
            }
            return prompt.toString();
        } else {
            return StringUtils.EMPTY;
        }
    }
}
