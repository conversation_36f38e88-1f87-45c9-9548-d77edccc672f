package com.qc.agent.platform.register.service;

import com.alibaba.fastjson.JSONObject;
import com.qc.agent.common.config.datasource.annotation.AnswerAI;
import com.qc.agent.common.core.RequestHolder;
import com.qc.agent.platform.register.mapper.TenantScriptExecutorMapper;
import com.qc.agent.platform.register.model.ScriptExecutorRequest;
import com.qc.agent.platform.util.UUIDUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR>
 * @date 2025/5/9 15:30:28
 *
 */
@Service
public class TenantScriptExecutorLogService
{

    @Resource
    TenantScriptExecutorMapper tenantScriptExecutorMapper;

    /**
     * 查询日志列表
     * @param likeCondition like 综合查询
     * @return 日志结果
     */
    @AnswerAI
    public List<ScriptExecutorRequest> getLogs(String likeCondition){
        return tenantScriptExecutorMapper.selectAll(likeCondition);
    }

    /**
     * 写脚本执行日志
     *
     * @param request 请求参数
     */
    @AnswerAI
    public void  writeScriptExecuteLog(ScriptExecutorRequest request){
        request.setId(String.valueOf(UUIDUtils.getUUID2Long()) );
        request.setCreationTime(new Date());
        request.setCreator(RequestHolder.getMasterToken().getOperatorName());
        tenantScriptExecutorMapper.insert(request);
    }

    /**
     * 更新脚本执行状态
     * @param id 记录ID
     * @param result 执行统计数据
     */
    @AnswerAI
    public void  updateWithExecuteStatus(String id, JSONObject result){
        tenantScriptExecutorMapper.updateWithExecuteStatus(id,JSONObject.toJSONString(result));
    }

    /**
     * 查询日志明细
     * @param id 记录标识
     * @return 日志结果
     */
    @AnswerAI
    public ScriptExecutorRequest getDetail(String id){
        return tenantScriptExecutorMapper.selectById(id);
    }

}
