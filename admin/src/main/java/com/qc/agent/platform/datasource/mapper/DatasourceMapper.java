package com.qc.agent.platform.datasource.mapper;

import com.qc.agent.platform.datasource.model.DatasourceConfig;
import com.qc.agent.platform.register.model.ScriptExecutorRequest;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DatasourceMapper
{
    DatasourceConfig queryDbDataSourceConfig(Long id);

    List<DatasourceConfig> queryDataSourceWithSqlClause(@Param("sqlClause") String sqlClause);

    int insertDbDataSourceConfig(DatasourceConfig config);
}
