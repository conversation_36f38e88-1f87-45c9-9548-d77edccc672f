package com.qc.agent.platform.sse;

import com.qc.agent.platform.sse.dto.AIVendors;

/**
 * 智能客服问答请求接口
 * <AUTHOR>
 */

public interface AnswerAIRequest
{
    /**
     * 指定问答使用的厂商
     * @return
     */
    AIVendors determineAIVendors();

    /**
     *  指定消息转换器
     * @return
     */
    SseMessageConverter determineMessageConverter();

    /**
     *  执行请求
     * @param subscriber
     */
    void doRequest(SseRequest request, AnswerAISubscriber subscriber);


}
