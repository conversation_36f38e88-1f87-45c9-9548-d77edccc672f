package com.qc.agent.platform.sse;

import com.qc.agent.common.core.RequestHolder;
import com.qc.agent.common.core.TenantUser;
import com.qc.agent.common.exception.SystemException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.Optional;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;



/**
 *  智能客服客户端
 * <AUTHOR>
 */

@Component
@Slf4j
public class AnswerAIClient
{
    static Executor executor = Executors.newFixedThreadPool(10);

    private AnswerAISubscriber subscriber;


    public static AnswerAIClient builder(){
        return new AnswerAIClient();
    }

    /**
     *  初始化订阅者
     */
    public <T extends AnswerAISubscriber> AnswerAIClient subscribe(T subscriber){
        Assert.notNull(subscriber,"订阅者不能为空");
        this.subscriber = subscriber;
        return this;
    }


    /**
     * 添加订阅者回调接口
     * @param callback
     */
    public AnswerAIClient addCallback(SseCompleteCallback callback){
        Assert.notNull(subscriber,"请先调用subscribe方法");
        Assert.notNull(callback,"请传入回调接口");

        subscriber.addCompleteCallback(callback);

        return this;
    }


    /**
     * 智能客服客户端执行
     * @param request 请求参数
     * @return
     */
    public SseEmitter execute(SseRequest request){
        Assert.notNull(request,"请传入请求参数");
        Assert.notNull(subscriber,"请先调用subscribe方法");

        // 设置异步线程内用户
        TenantUser tenantUser = RequestHolder.getThreadLocalUser();

        // 添加answerAIRequest
        AnswerAIRequest answerAIRequest = AnswerAIRequestBuilder.getRequest(
                Optional.ofNullable(request.getConfig()).map(c -> c.getVendor()).orElse(null));

        // 将请求参数添加订阅者中
        subscriber.addRequestParam(request).addTenantUser(tenantUser).addSseMessageConverter(answerAIRequest.determineMessageConverter());

        String cookie = RequestHolder.getRequestCookie();
        executor.execute(()->{
            // 设置异步线程内用户
            RequestHolder.setThreadLocalUser(tenantUser);
            RequestHolder.setRequestCookie(cookie);
            // 执行请求
            try
            {
                answerAIRequest.doRequest(request,subscriber);
            }
            catch(Exception e)
            {
                if(subscriber != null){
                    subscriber.dispose();
                }
                log.error("智能客服执行请求异常",e);
                throw new SystemException("智能客服执行请求异常",e);
            }
            finally
            {
                if(subscriber != null){
                    subscriber.dispose();
                }
            }
        });

        return subscriber.getSseClient().getEmitter();
    }


}
