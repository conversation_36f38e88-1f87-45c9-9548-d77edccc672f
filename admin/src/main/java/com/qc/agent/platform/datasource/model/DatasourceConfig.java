package com.qc.agent.platform.datasource.model;

import com.qc.agent.jdbc.datasource.connection.JdbcConnectionProperty;
import com.qc.agent.jdbc.datasource.model.DataSourcePoolType;
import com.qc.agent.jdbc.datasource.model.DataSourceProperty;

import java.util.Date;
import java.util.List;

public class DatasourceConfig
{
    private Long id;

    private String userName;

    private String password;

    private Integer maxConnCount;

    private Integer minConnCount;

    private Long sleepKeepTime;

    private Long serverId;

    private String status;

    private Long creator;

    private Date creationTime;

    private Long maxConnLifetime;

    private ServerConfig server;

    private List<SlaveDatasourceConfig> slaves;


    public JdbcConnectionProperty toJdbcConnectionProperty() {
        JdbcConnectionProperty property = new JdbcConnectionProperty();
        property.setHost(server.getIpAddress());
        property.setPort(server.getPort());
        property.setUserName(userName);
        property.setPassword(password);
        return property;
    }

    public DataSourceProperty buildDataSourceProperty() {
        DataSourceProperty dataSourceProperty = new DataSourceProperty();
        dataSourceProperty.setType(DataSourcePoolType.HIKARI.name());
        dataSourceProperty.setUrl(buildDiverUrl());
        dataSourceProperty.setUserName(userName);
        dataSourceProperty.setPassword(password);
        dataSourceProperty.setMaxConnectionCount(maxConnCount);
        dataSourceProperty.setMinConnectionCount(minConnCount);
        dataSourceProperty.setMaxLifetime(maxConnLifetime);
        dataSourceProperty.setDriver("org.postgresql.Driver");

        return dataSourceProperty;
    }

    private String buildDiverUrl(){
        return String.format("jdbc:postgresql://%s:%s/%s",server.getIpAddress(),server.getPort(),userName);
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName == null ? null : userName.trim();
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password == null ? null : password.trim();
    }

    public Integer getMaxConnCount() {
        return maxConnCount;
    }

    public void setMaxConnCount(Integer maxConnCount) {
        this.maxConnCount = maxConnCount;
    }

    public Integer getMinConnCount() {
        return minConnCount;
    }

    public void setMinConnCount(Integer minConnCount) {
        this.minConnCount = minConnCount;
    }

    public Long getSleepKeepTime() {
        return sleepKeepTime;
    }

    public void setSleepKeepTime(Long sleepKeepTime) {
        this.sleepKeepTime = sleepKeepTime;
    }

    public Long getServerId() {
        return serverId;
    }

    public void setServerId(Long serverId) {
        this.serverId = serverId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    public Long getCreator() {
        return creator;
    }

    public void setCreator(Long creator) {
        this.creator = creator;
    }

    public Date getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(Date creationTime) {
        this.creationTime = creationTime;
    }

    public Long getMaxConnLifetime() {
        return maxConnLifetime;
    }

    public void setMaxConnLifetime(Long maxConnLifetime) {
        this.maxConnLifetime = maxConnLifetime;
    }

    public ServerConfig getServer()
    {
        return server;
    }
    public void setServer(ServerConfig server)
    {
        this.server = server;
    }
    public List<SlaveDatasourceConfig> getSlaves()
    {
        return slaves;
    }
    public void setSlaves(List<SlaveDatasourceConfig> slaves)
    {
        this.slaves = slaves;
    }

}
