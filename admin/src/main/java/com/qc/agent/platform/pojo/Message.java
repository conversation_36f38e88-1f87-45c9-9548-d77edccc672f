package com.qc.agent.platform.pojo;

import com.alibaba.fastjson.JSONObject;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collection;

public class Message
{
	private Collection<String> messages;
	private Collection<String> errors;
	private Object data;

	public synchronized Message addMessage(String aMessage)
	{
		internalGetMessages().add(aMessage);
		return this;
	}

	public synchronized Message addActionError(String anErrorMessage)
	{
		internalGetErrors().add(anErrorMessage);
		return this;
	}

	private Collection<String> internalGetMessages()
	{
		if (messages == null)
		{
			messages = new ArrayList<String>();
		}
		return messages;
	}

	private Collection<String> internalGetErrors()
	{
		if (errors == null)
		{
			errors = new ArrayList<String>();
		}

		return errors;
	}

	public synchronized boolean hasErrors()
	{
		return (errors != null) && !errors.isEmpty();
	}

	public synchronized boolean hasMessages()
	{
		return (messages != null) && !messages.isEmpty();
	}

	public synchronized void clearMessages()
	{
		internalGetMessages().clear();
	}

	public synchronized void clearErrors()
	{
		internalGetErrors().clear();
	}

	public String toString()
	{
		if (hasErrors())
		{
			return StringUtils.collectionToCommaDelimitedString(errors);
		}
		else
		{
			return StringUtils.collectionToCommaDelimitedString(messages);
		}
	}

	public String toJsonString()
	{
		return toJson().toString();
	}

	public JSONObject toJson()
	{
		JSONObject jo = new JSONObject();
		if (hasErrors())
		{
			jo.put(STATUS_KEY, STATUS_FAIL);
			jo.put(MESSAGE_KEY, StringUtils.collectionToCommaDelimitedString(errors));
		}
		else
		{
			jo.put(STATUS_KEY, STATUS_SUCCESS);
			jo.put(DATA_KEY, data);
			jo.put(MESSAGE_KEY, StringUtils.collectionToCommaDelimitedString(messages));
		}
		return jo;
	}

	public static Message success(Object data){
		Message msg = getInstance();
		msg.setData(data);
		return msg;
	}

	public static Message fail(String errorMessage) {
		Message msg = getInstance();
		msg.addActionError(errorMessage);
		return msg;
	}

	@SuppressWarnings("unchecked")
	public <T extends Object> T getData()
	{
		return (T) data;
	}

	public void setData(Object data)
	{
		this.data = data;
	}

	public static Message getInstance()
	{
		return new Message();
	}

	public static final String STATUS_KEY = "code";
	public static final String MESSAGE_KEY = "message";
	public static final String DATA_KEY = "data";
	public static final String STATUS_SUCCESS = "1";
	public static final String STATUS_FAIL = "0";
}
