package com.qc.agent.platform.datasource.builder;

import com.qc.agent.common.config.datasource.DataSourceEnum;
import com.qc.agent.jdbc.datasource.builder.DataSourcePropertyBuilder;
import com.qc.agent.jdbc.datasource.model.DataSourceProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 *
 * <AUTHOR>
 */
@Configuration
public class AnswerAIDataSourcePropertyBuilder implements DataSourcePropertyBuilder
{
    @Override
    @Bean(name="dataSourceProperty")
    @ConfigurationProperties("spring.database.datasource")
    public DataSourceProperty buildProperty() {
        return new DataSourceProperty();
    }
    @Override
    public Boolean determineDefault()
    {
        return false;
    }

    @Override
    public String determineDataSourceName()
    {
        return DataSourceEnum.ANSWER_AI.getName();
    }


}
