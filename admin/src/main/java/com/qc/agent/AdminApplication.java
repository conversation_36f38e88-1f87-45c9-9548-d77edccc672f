package com.qc.agent;

import org.dromara.x.file.storage.spring.EnableFileStorage;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.WebApplicationType;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableScheduling;


@EnableFileStorage
@SpringBootApplication
@EnableScheduling
public class AdminApplication
{

    public static void main(String[] args) throws NoSuchMethodException
    {
        SpringApplication app = new SpringApplication(AdminApplication.class);
        app.setWebApplicationType(WebApplicationType.REACTIVE);
        app.run(AdminApplication.class, args);
    }

}
