package com.qc.agent.common.config.db.remote.vo;

import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;

/**
 * The Class DBInfo.
 *
 * <AUTHOR>
 */
public class DBInfo implements Serializable
{
	private static final long serialVersionUID = 1L;
	private Long tenantId;
	/** 企业名称. */
	private String tenantCode;
	/** 数据库URL. */
	private String dbUrl;
	/** 数据库访问用户名. */
	private String dbUsername;
	/** 数据库访问密码. */
	private String dbPassword;
	/** 数据库驱动. */
	private String dbDirver;
	/** 最大连接数. */
	private int dbMaximumConnectionCount;
	/** 最小连接数. */
	private int dbMinimumConnectionCount;
	/** 空闲回收时间. */
	private int dbHouseKeepingSleepTime;

	private String dbHost;

	private Integer dbPort;
	private String dbBackupHost;
	private Integer dbBackupPort;


	/**
	 * 从库信息,
	 */
	private List<String> slaveDataSources;


	/**
	 * 统计库信息,
	 */
	private List<String> statisDataSources;


	//是否使用连接池
	private boolean isUseConnPool;




	public boolean isUseConnPool()
	{
		return isUseConnPool;
	}

	public void setUseConnPool(boolean isUseConnPool)
	{
		this.isUseConnPool = isUseConnPool;
	}

	public String getTenantCode()
	{
		return tenantCode;
	}

	public void setTenantCode(String tenantCode)
	{
		this.tenantCode = tenantCode;
	}

	public String getDbUrl()
	{
		return dbUrl;
	}

	public void setDbUrl(String dbUrl)
	{
		this.dbUrl = dbUrl;
	}

	public String getDbUsername()
	{
		return dbUsername;
	}

	public void setDbUsername(String dbUsername)
	{
		this.dbUsername = dbUsername;
	}

	public String getDbPassword()
	{
		return dbPassword;
	}

	public void setDbPassword(String dbPassword)
	{
		this.dbPassword = dbPassword;
	}

	public String getDbDirver()
	{
		return dbDirver;
	}

	public void setDbDirver(String dbDirver)
	{
		this.dbDirver = dbDirver;
	}

	public int getDbMaximumConnectionCount()
	{
		return dbMaximumConnectionCount;
	}

	public void setDbMaximumConnectionCount(int dbMaximumConnectionCount)
	{
		this.dbMaximumConnectionCount = dbMaximumConnectionCount;
	}

	public int getDbMinimumConnectionCount()
	{
		return dbMinimumConnectionCount;
	}

	public void setDbMinimumConnectionCount(int dbMinimumConnectionCount)
	{
		this.dbMinimumConnectionCount = dbMinimumConnectionCount;
	}

	public int getDbHouseKeepingSleepTime()
	{
		return dbHouseKeepingSleepTime;
	}

	public void setDbHouseKeepingSleepTime(int dbHouseKeepingSleepTime)
	{
		this.dbHouseKeepingSleepTime = dbHouseKeepingSleepTime;
	}

	public Long getTenantId()
	{
		return tenantId;
	}

	public void setTenantId(Long tenantId)
	{
		this.tenantId = tenantId;
	}

	public String getDbHost()
	{
		return dbHost;
	}

	public void setDbHost(String dbHost)
	{
		this.dbHost = dbHost;
	}

	public Integer getDbPort()
	{
		return dbPort;
	}

	public void setDbPort(Integer dbPort)
	{
		this.dbPort = dbPort;
	}

	public String getDbBackupHost()
	{
		return dbBackupHost;
	}

	public void setDbBackupHost(String dbBackupHost)
	{
		this.dbBackupHost = dbBackupHost;
	}

	public Integer getDbBackupPort()
	{
		return dbBackupPort;
	}

	public void setDbBackupPort(Integer dbBackupPort)
	{
		this.dbBackupPort = dbBackupPort;
	}



	public void setSlaveDataSources(List<String> slaveDataSources)
	{
		this.slaveDataSources = slaveDataSources;
	}



	public void setStatisDataSources(List<String> statisDataSources)
	{
		this.statisDataSources = statisDataSources;
	}




	public List<String> getSlaveDataSources()
	{
		return slaveDataSources;
	}

	public List<String> getStatisDataSources()
	{
		return statisDataSources;
	}

	/**
	 * 获取从库连接url
	 *
	 * @return
	 */
	@Deprecated
	public String getBackUpUrl(){

		//没有配置从库的话，返回主库的连接
		if(StringUtils.isEmpty(dbBackupHost)||dbBackupPort ==null) {
			return dbUrl;
		}

		StringBuilder sb = new StringBuilder();
		sb.append("jdbc:postgresql://");
		sb.append(dbBackupHost);
		sb.append(":");
		sb.append(dbBackupPort);
		sb.append("/");
		sb.append(dbUsername);
		sb.append("?");
		sb.append("user=");
		sb.append(dbUsername);
		sb.append("&password=");
		sb.append(dbPassword);
		sb.append("&useUnicode=true&characterEncoding=UTF-8");


		return sb.toString();
	}




}
