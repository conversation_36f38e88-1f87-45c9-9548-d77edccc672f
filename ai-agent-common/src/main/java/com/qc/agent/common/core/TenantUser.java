package com.qc.agent.common.core;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.util.Base64;
import java.util.Optional;

/**
 *
 * <AUTHOR>
 */
@Data
public class TenantUser
{

    Long userId;
    String userName;
    Long tenantId;


    /**
     * 9：管理员
     */
    String userType;
    /** 用户类型-管理员 */
    public static final String USERTYPE_ADMIN = "9";
    Long adminUserId;

    public static TenantUser buildBean(String data){

        JSONObject tokenContent = JSONObject.parseObject(new String(Base64.getDecoder().decode(data)));
        JSONObject sub = tokenContent.getJSONObject("sub");
        if(Optional.ofNullable(sub).isPresent()){
            return JSONObject.toJavaObject(sub, TenantUser.class);
        }
        return null;
    }

    public Long getUserId()
    {
        return userId;
    }
    public void setUserId(Long userId)
    {
        this.userId = userId;
    }
    public String getUserName()
    {
        return userName;
    }
    public void setUserName(String userName)
    {
        this.userName = userName;
    }
    public Long getTenantId()
    {
        return tenantId;
    }
    public void setTenantId(Long tenantId)
    {
        this.tenantId = tenantId;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public Long getAdminUserId() {
        return adminUserId;
    }

    public void setAdminUserId(Long adminUserId) {
        this.adminUserId = adminUserId;
    }

    public boolean isAdmin(){
        return USERTYPE_ADMIN.equals(userType) || (adminUserId != null && adminUserId.equals(userId));
    }
}
