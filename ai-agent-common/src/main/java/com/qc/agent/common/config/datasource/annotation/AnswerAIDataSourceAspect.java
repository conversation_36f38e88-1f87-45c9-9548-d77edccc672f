package com.qc.agent.common.config.datasource.annotation;

import com.qc.agent.common.config.datasource.DataSourceEnum;
import com.qc.agent.jdbc.datasource.DataSourceHolder;
import com.qc.agent.jdbc.datasource.router.RouterKey;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.stereotype.Component;

/**
 *  数据源切换切面
 * <AUTHOR>
 */
@Aspect
@Component
public class AnswerAIDataSourceAspect
{


    @Before("@annotation(answerAI)")
    public void setAnswerAIDataSource(JoinPoint joinPoint, AnswerAI answerAI) {
        DataSourceHolder.setRouterKey(RouterKey.build(DataSourceEnum.ANSWER_AI.getName(),answerAI.type(),answerAI.connType()));
    }

    @Before("@annotation(answerAITenant)")
    public void setAnswerAITenantDataSource(JoinPoint joinPoint, AnswerAITenant answerAITenant) {
        DataSourceHolder.setRouterKey(RouterKey.build(DataSourceEnum.ANSWER_AI_TENANT.getName(),answerAITenant.type(),answerAITenant.connType()));
    }

    @Before("@annotation(appServer)")
    public void setAppServerDataSource(JoinPoint joinPoint, AppServer appServer) {
        DataSourceHolder.setRouterKey(RouterKey.build(DataSourceEnum.APP_SERVER.getName(),appServer.type(),appServer.connType()));
    }

    /**
     * 切面后置
     * @param joinPoint
     */
    @After("@annotation(answerAI)")
    public void restoreAnswerAIDataSource(JoinPoint joinPoint,AnswerAI answerAI) {
        // 清除当前请求路由Key
        DataSourceHolder.clearRouterKey();
    }

    @After(" @annotation(answerAITenant)")
    public void restoreAnswerAITenantDataSource(JoinPoint joinPoint,AnswerAITenant answerAITenant) {
        // 清除当前请求路由Key
        DataSourceHolder.clearRouterKey();
    }

    @After("@annotation(appServer)")
    public void restoreAppServerDataSource(JoinPoint joinPoint,AppServer appServer) {
        // 清除当前请求路由Key
        DataSourceHolder.clearRouterKey();
    }
}
