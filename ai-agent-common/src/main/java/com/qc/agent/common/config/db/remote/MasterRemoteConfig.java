package com.qc.agent.common.config.db.remote;

import com.fiberhome.interfaces.appsvr.server.gettenantinfo.interfaces.IGetTenantInfo;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.PropertySource;
import org.springframework.remoting.caucho.HessianProxyFactoryBean;

/**
 *
 * <AUTHOR>
 */
@Configuration
@PropertySource(value = "classpath:application-remote.properties")
public class MasterRemoteConfig
{
    @Value("${remote.host}")
    private String remoteHost;

//    @Bean
//    public IGetTenantDBInfo getTenantDBInfoClient() {
//        HessianProxyFactoryBean factoryBean = new HessianProxyFactoryBean();
//        factoryBean.setServiceUrl(remoteHost + "/remote/GetTenantDBInfo");
//        factoryBean.setServiceInterface(IGetTenantDBInfo.class);
//        factoryBean.afterPropertiesSet();
//        return (IGetTenantDBInfo) factoryBean.getObject();
//    }

    @Lazy
    @Bean
    public IGetTenantInfo getTenantInfoClient() {
        HessianProxyFactoryBean factoryBean = new HessianProxyFactoryBean();
        factoryBean.setServiceUrl(remoteHost + "/remote/GetTenantInfo");
        factoryBean.setServiceInterface(IGetTenantInfo.class);
        factoryBean.afterPropertiesSet();
        return (IGetTenantInfo) factoryBean.getObject();
    }
}
