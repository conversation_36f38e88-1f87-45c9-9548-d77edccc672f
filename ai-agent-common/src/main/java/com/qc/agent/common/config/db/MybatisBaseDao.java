package com.qc.agent.common.config.db;


import org.apache.ibatis.session.SqlSession;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;


public class MybatisBaseDao {

	@Resource(name="sqlSession")
	protected SqlSession sqlSessionTemplate;

	public static final String SQLNAME_SEPARATOR = ".";

	/**
	 * @fields sqlNamespace SqlMapping命名空间 默认为T的全类名
	 */
	private String sqlNamespace = getDefaultSqlNamespace();

	/**
	 * 获取泛型类型的实体对象类全名
	 *
	 * @return
	 */
	protected String getDefaultSqlNamespace() {

		return this.getClass().getName();

	}

	/**
	 * 获取SqlMapping命名空间
	 *
	 */
	public String getSqlNamespace() {
		if (sqlNamespace == null) {
			sqlNamespace = getDefaultSqlNamespace();
		}
		return sqlNamespace;
	}


	// 拼接in语句
	public String listToString(List<Long> list) {

		if (CollectionUtils.isEmpty(list)) {
			return null;
		}

		StringBuffer sb = new StringBuffer();
		for (int i = 0; i < list.size(); i++) {
			sb.append(list.get(i)).append(",");
		}
		String newStr = sb.toString().substring(0, sb.toString().length() - 1);
		return newStr;

	}

}
