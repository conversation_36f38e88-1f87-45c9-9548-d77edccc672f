package com.qc.agent.common.config.init;

import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.ImportResource;

import java.util.HashMap;
import java.util.Map;

/**
 *
 */
@Configuration
@ImportResource({"classpath*:META-INF/spring/spring*.xml"})
public class ResourceLoader
{

	public static Map<String,String> extendConfigMap = new HashMap<>();

	static {
		extendConfigMap.put("core.config.properties", "core.config.properties");
	}


	/**
	 * 获取自定义配置文件的map
	 * @return
	 */
	public static Map<String,String> getExtendConfigMap() {

		return extendConfigMap;
	}



}
