package com.qc.agent.common.util;


import com.qc.agent.common.core.Base64;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.io.Closeable;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;

/**
 * AES 对称算法加密/解密工具类
 *
 */
public class AESUtils
{

    /** 密钥长度: 128, 192 or 256 */
    private static final int KEY_SIZE = 128;

    /** 加密/解密算法名称 */
    private static final String ALGORITHM = "AES";
    private static final String ENCRYPT_MODE_ECB = "AES/ECB/PKCS5Padding";

    private AESUtils()
    {
    }

    public static void main(String[] args) throws Exception
    {
        String content = "io/LIroJaHH8pZJnuEj5DKn/k5jy9lGJjvF1/MCfn4p6dboi8IFkaOmaCQ5684QbEUo cddw7flQXxnpns2fGQ==";

        // 原文内容
        String key = "aP#whfjHiN@8uh0Z";                  // AES加密/解密用的原始密码

        System.out.println(decrypt(content, key));
    }

    public static String encrypt(String text, String key) throws Exception
    {
        byte[] cipherBytes = AESUtils.encrypt(text.getBytes(), key.getBytes(StandardCharsets.UTF_8));
        return Base64.encodeByte(cipherBytes);
    }

    public static String decrypt(String decryptText, String key) throws Exception
    {
        byte[] plainBytes = AESUtils.decrypt(Base64.decodeByte(decryptText), key.getBytes(StandardCharsets.UTF_8));
        return new String(plainBytes, StandardCharsets.UTF_8);
    }

    public static String decrypt(String decryptText, String key, Charset charset) throws Exception
    {
        byte[] plainBytes = AESUtils.decrypt(Base64.decodeByte(decryptText), key.getBytes(charset));
        return new String(plainBytes, charset);
    }

    /**
     * 加密文件: 明文输入 -> 密文输出
     */
    public static void encryptFile(File plainIn, File cipherOut, byte[] key) throws Exception
    {
        aesFile(plainIn, cipherOut, key, true);
    }

    /**
     * 解密文件: 密文输入 -> 明文输出
     */
    public static void decryptFile(File cipherIn, File plainOut, byte[] key) throws Exception
    {
        aesFile(plainOut, cipherIn, key, false);
    }

    /**
     * 生成密钥对象
     */
    private static SecretKey generateKey(byte[] key) throws Exception
    {
        return new SecretKeySpec(key, ALGORITHM);
    }

    /**
     * 数据加密: 明文 -> 密文
     */
    public static byte[] encrypt(byte[] plainBytes, byte[] key) throws Exception
    {
        // 生成密钥对象
        SecretKey secKey = generateKey(key);

        // 获取 AES 密码器
        Cipher cipher = Cipher.getInstance(ENCRYPT_MODE_ECB);
        // 初始化密码器（加密模型）
        cipher.init(Cipher.ENCRYPT_MODE, secKey);

        // 加密数据, 返回密文

        return cipher.doFinal(plainBytes);
    }

    /**
     * 数据解密: 密文 -> 明文
     */
    public static byte[] decrypt(byte[] cipherBytes, byte[] key) throws Exception
    {
        // 生成密钥对象
        SecretKey secKey = generateKey(key);

        // 获取 AES 密码器
        Cipher cipher = Cipher.getInstance(ENCRYPT_MODE_ECB);
        // 初始化密码器（解密模型）
        cipher.init(Cipher.DECRYPT_MODE, secKey);

        // 解密数据, 返回明文

        return cipher.doFinal(cipherBytes);
    }

    /**
     * AES 加密/解密文件
     */
    private static void aesFile(File plainFile, File cipherFile, byte[] key, boolean isEncrypt) throws Exception
    {
        // 获取 AES 密码器
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        // 生成密钥对象
        SecretKey secKey = generateKey(key);
        // 初始化密码器
        cipher.init(isEncrypt ? Cipher.ENCRYPT_MODE : Cipher.DECRYPT_MODE, secKey);

        // 加密/解密数据
        InputStream in = null;
        OutputStream out = null;

        try
        {
            if(isEncrypt)
            {
                // 加密: 明文文件为输入, 密文文件为输出
                in = new FileInputStream(plainFile);
                out = new FileOutputStream(cipherFile);
            }
            else
            {
                // 解密: 密文文件为输入, 明文文件为输出
                in = new FileInputStream(cipherFile);
                out = new FileOutputStream(plainFile);
            }

            byte[] buf = new byte[1024];
            int len = -1;

            // 循环读取数据 加密/解密
            while((len = in.read(buf)) != -1)
            {
                out.write(cipher.update(buf, 0, len));
            }
            out.write(cipher.doFinal());    // 最后需要收尾
            out.flush();
        }
        finally
        {
            close(in);
            close(out);
        }
    }

    private static void close(Closeable c)
    {
        if(c != null)
        {
            try
            {
                c.close();
            }
            catch(IOException e)
            {
                // nothing
            }
        }
    }

}
