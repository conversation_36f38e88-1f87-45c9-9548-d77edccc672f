package com.qc.agent.common.config.db.remote;

import com.fiberhome.interfaces.appsvr.server.common.bean.MbsTenantBean;
import com.fiberhome.interfaces.appsvr.server.gettenantinfo.beans.GetTenantInfoRequest;
import com.fiberhome.interfaces.appsvr.server.gettenantinfo.beans.GetTenantInfoResponse;
import com.fiberhome.interfaces.appsvr.server.gettenantinfo.interfaces.IGetTenantInfo;
import com.qc.agent.common.config.db.remote.vo.DBInfo;
import com.qc.agent.common.config.db.remote.vo.MbsTenant;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 *
 * <AUTHOR>
 */

@Component
public class MasterRemoteService
{
//    @Resource
//    IGetTenantDBInfo getTenantDBInfoClient;

    @Lazy
    @Resource
    IGetTenantInfo getTenantInfoClient;

    public MbsTenant getExtTenantDBInfo(Long tenantId) throws Exception
    {
        if (tenantId == null)
        {
            return null;
        }
        GetTenantInfoRequest request = new GetTenantInfoRequest();
        request.setTenantId(tenantId);
        GetTenantInfoResponse response = getTenantInfoClient.handleGetTenantInfo(request);

        if ("1".equals(response.getResultCode()))
        {
            MbsTenantBean mbsTenantBean = response.getMbsTenant();
            if (mbsTenantBean == null)
            {
                throw new RuntimeException("没找到企业信息！原因：" + response.getResultMessage());
            }
            return conversionMbsTenantBean(mbsTenantBean);
        }
        else
        {
            throw new RuntimeException(response.getResultMessage());
        }
    }

    /**
     * 将接口对象转换为应用对象
     *
     * @param mbsTenantBean
     *            接口Bean对象
     * @return 应用对象
     */
    public static MbsTenant conversionMbsTenantBean(MbsTenantBean mbsTenantBean)
    {
        if (mbsTenantBean == null)
        {
            return null;
        }

        MbsTenant mbsTenant = new MbsTenant();
        mbsTenant.setId(mbsTenantBean.getId());
        mbsTenant.setName(mbsTenantBean.getName());
        mbsTenant.setCode(mbsTenantBean.getCode());
        mbsTenant.setChannelCode(mbsTenantBean.getChannelCode());
        mbsTenant.setChannelId(mbsTenantBean.getChannelId());
        mbsTenant.setChannelName(mbsTenantBean.getChannelName());
        mbsTenant.setClientAcl(mbsTenantBean.getClientAcl());
        mbsTenant.setType(mbsTenantBean.getType());
        mbsTenant.setTenantType(mbsTenantBean.getTenantType());
        mbsTenant.setUsers(mbsTenantBean.getUsers());
        mbsTenant.setStatus(mbsTenantBean.getStatus());
        mbsTenant.setNotStatus(mbsTenantBean.getNotStatus());
        mbsTenant.setCreateTime(mbsTenantBean.getCreateTime());
        mbsTenant.setMobile(mbsTenantBean.getMobile());
        mbsTenant.setEmail(mbsTenantBean.getEmail());
        mbsTenant.setFax(mbsTenantBean.getFax());
        mbsTenant.setIndustry(mbsTenantBean.getIndustry());
        mbsTenant.setLinkman(mbsTenantBean.getLinkman());
        mbsTenant.setPhone(mbsTenantBean.getPhone());
        mbsTenant.setAddress(mbsTenantBean.getAddress());
        mbsTenant.setRegion(mbsTenantBean.getRegion());
        mbsTenant.setRemarks(mbsTenantBean.getRemarks());
        mbsTenant.setSex(mbsTenantBean.getSex());
        mbsTenant.setShortName(mbsTenantBean.getShortName());
        mbsTenant.setSvrId(mbsTenantBean.getSvrId());
        mbsTenant.setUrl(mbsTenantBean.getUrl());
        mbsTenant.setDbPasswd(mbsTenantBean.getDbPasswd());
        mbsTenant.setDbUser(mbsTenantBean.getDbUser());
        mbsTenant.setMaxConnCount(mbsTenantBean.getMaxConnCount());
        mbsTenant.setMinConnCount(mbsTenantBean.getMinConnCount());
        mbsTenant.setSleepKeepTime(mbsTenantBean.getSleepKeepTime());
        mbsTenant.setNetworkType(mbsTenantBean.getNetworkType());
        mbsTenant.setPushEnable(mbsTenantBean.getPushEnable());
        mbsTenant.setStopRemarks(mbsTenantBean.getStopRemarks());
        mbsTenant.setPortalUrl(mbsTenantBean.getPortalUrl());
        mbsTenant.setLocationPolicyA(mbsTenantBean.getLocationPolicyA());
        mbsTenant.setLocationPolicyB(mbsTenantBean.getLocationPolicyB());
        mbsTenant.setMaxStorageSize(mbsTenantBean.getMaxStorageSize());
        mbsTenant.setDbSize(mbsTenantBean.getFileSizeBean().getDbSize());
        mbsTenant.setFileSize(mbsTenantBean.getFileSizeBean().getFileSize());
        mbsTenant.setDbSize(mbsTenantBean.getFileSizeBean().getDbSize());
        mbsTenant.setLbsUsers(mbsTenantBean.getLocationUsers());
        mbsTenant.setLbsEndTime(mbsTenantBean.getLocationEndTime());
        mbsTenant.setRegiterOrigin(mbsTenantBean.getRegiterOrigin());

        mbsTenant.setDbInfo(conversionDBBean(mbsTenantBean.getDbBean()));
        return mbsTenant;
    }

    /**
     * 将接口DB对象转换为标准DB对象
     *
     * @param dbBean
     *            接口对象
     * @return 标准对象
     */
    private static DBInfo conversionDBBean(com.fiberhome.interfaces.appsvr.server.userauth.beans.DBBean dbBean)
    {
        if (dbBean == null)
        {
            return null;
        }
        DBInfo dbInfo = new DBInfo();
        dbInfo.setTenantId(dbBean.getTenantId());
        dbInfo.setTenantCode(dbBean.getTenantCode());
        dbInfo.setDbUrl(dbBean.getDbUrl());
        dbInfo.setDbUsername(dbBean.getDbUsername());
        dbInfo.setDbPassword(dbBean.getDbPassword());
        dbInfo.setDbDirver(dbBean.getDbDirver());
        dbInfo.setDbMinimumConnectionCount(dbBean.getDbMinimumConnectionCount());
        dbInfo.setDbMaximumConnectionCount(dbBean.getDbMaximumConnectionCount());
        dbInfo.setDbHouseKeepingSleepTime(dbBean.getDbHouseKeepingSleepTime());
        dbInfo.setDbHost(dbBean.getDbHost());
        dbInfo.setDbPort(dbBean.getDbPort());
        dbInfo.setDbBackupHost(dbBean.getDbBackupHost());
        dbInfo.setDbBackupPort(dbBean.getDbBackupPort());

        //格式
        dbInfo.setSlaveDataSources(dbBean.getSlaverHostsDbUrl());

        //格式
        dbInfo.setStatisDataSources(dbBean.getStatHostsDbUrl());

        //isDbpoll为1，返回true，为null或者其他，返回false
        dbInfo.setUseConnPool("1".equals(dbBean.getIsDbpool()));
        return dbInfo;
    }

}
