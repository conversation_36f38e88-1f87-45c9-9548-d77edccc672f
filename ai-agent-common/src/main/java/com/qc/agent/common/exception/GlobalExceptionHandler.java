package com.qc.agent.common.exception;

import com.qc.agent.common.core.Message;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

@ControllerAdvice
public class GlobalExceptionHandler {
    Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    @ExceptionHandler(Exception.class)
    @ResponseBody
    public Message handleGlobalException(Exception e) {
        // 记录报错日志
        logger.error("拦截到全局异常",e);
        if(e instanceof SystemException || e instanceof BizException){
            return Message.of().error(e.getMessage());
        }else{

            return Message.of().error("服务器忙，请稍后重试！");
        }
    }

    @ExceptionHandler(BizException.class)
    @ResponseBody
    public Message handleGlobalBizException(BizException e) {
        // 记录报错日志
        logger.error("拦截到全局业务异常",e);
        return Message.of().error("服务器忙，请稍后重试！");
    }
}
