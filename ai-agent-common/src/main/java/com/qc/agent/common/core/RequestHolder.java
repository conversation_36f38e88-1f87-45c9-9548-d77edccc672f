package com.qc.agent.common.core;

/**
 * 请求持有者
 *
 * <AUTHOR>
 */
public class RequestHolder {
    private static final ThreadLocal<TenantUser> THREAD_LOCAL_USER = new ThreadLocal<>();
    private static final ThreadLocal<String> REQUEST_COOKIE = new ThreadLocal<>();

    private static final ThreadLocal<String> REQUEST_TOKEN = new ThreadLocal<>();
    private static final ThreadLocal<MasterToken> REQUEST_MASTER_TOKEN = new ThreadLocal<>();

    public static void setThreadLocalUser(TenantUser tu) {
        THREAD_LOCAL_USER.set(tu);
    }

    public static TenantUser getThreadLocalUser() {
        return THREAD_LOCAL_USER.get();
    }

    public static void setRequestCookie(String cookie) {
        REQUEST_COOKIE.set(cookie);
    }


    public static String getRequestCookie() {
        return REQUEST_COOKIE.get();
    }

    public static void setRequestToken(String token) {
        REQUEST_TOKEN.set(token);
    }

    public static void setMasterToken(MasterToken token) {
        REQUEST_MASTER_TOKEN.set(token);
    }

    public static MasterToken getMasterToken(){
        return REQUEST_MASTER_TOKEN.get();
    }


    public static String getRequestToken() {
        return REQUEST_TOKEN.get();
    }

    public static void clear() {
        THREAD_LOCAL_USER.remove();

        REQUEST_COOKIE.remove();

        REQUEST_TOKEN.remove();
    }

}
