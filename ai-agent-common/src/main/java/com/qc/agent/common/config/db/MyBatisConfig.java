package com.qc.agent.common.config.db;

import com.github.pagehelper.PageHelper;

import com.qc.agent.common.config.datasource.DynamicDataSourceSwitcher;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.transaction.annotation.TransactionManagementConfigurer;

import javax.annotation.Resource;
import java.util.Properties;

/**
 *
 */
@Configuration
@EnableTransactionManagement
public class MyBatisConfig implements TransactionManagementConfigurer {

    @Lazy
    @Resource
    private DynamicDataSourceSwitcher dynamicDataSourceSwitcher;//默认配置文件中的数据源


	private SqlSessionFactory sqlSessionFactory;

    @Bean
    public SqlSessionTemplate sqlSessionTemplate(SqlSessionFactory sqlSessionFactory)
    {
        return new SqlSessionTemplate(getSqlSessionFactory());
    }




    private SqlSessionFactory getSqlSessionFactory() {

    	if(sqlSessionFactory == null) {
    		sqlSessionFactory = sqlSessionFactoryBean();
    	}

    	return sqlSessionFactory;
    }



    @Bean
    @Override
    public PlatformTransactionManager annotationDrivenTransactionManager()
    {
        return new DataSourceTransactionManager(dynamicDataSourceSwitcher);
    }


    @Bean(name = "sqlSessionFactory")
    public SqlSessionFactory sqlSessionFactoryBean()
    {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();

        bean.setDataSource(dynamicDataSourceSwitcher);

        // 分页插件
        PageHelper pageHelper = new PageHelper();
        Properties properties = new Properties();
        //properties.setProperty("dialect", "oracle");

        //oracle,mysql,mariadb,sqlite,hsqldb,postgresql,可选项
        properties.setProperty("dialect", "postgresql");
        pageHelper.setProperties(properties);

        // 添加插件
        bean.setPlugins(new Interceptor[] {pageHelper});

        // 添加XML目录
        ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        try
        {
            bean.setMapperLocations(resolver.getResources("classpath*:com/qc/agent/**/*_mapping.xml"));
            return bean.getObject();
        }
        catch (Exception e)
        {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
    }


    @Bean(name = "sqlSession")
    public SqlSession sqlSessionBean() {

    	SqlSession session = new SqlSessionTemplate(getSqlSessionFactory());

    	return session;
    }




}
