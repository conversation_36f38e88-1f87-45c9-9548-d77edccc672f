package com.qc.agent.common.config.datasource;


import com.qc.agent.common.core.RequestHolder;
import com.qc.agent.common.core.TenantUser;
import com.qc.agent.common.exception.SystemException;
import com.qc.agent.jdbc.datasource.DataSourceSwitcher;
import com.qc.agent.jdbc.datasource.router.RouterKey;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 *
 * <AUTHOR>
 */
@Component
public class DynamicDataSourceSwitcher extends DataSourceSwitcher
{

    @Override
    public RouterKey determineCurrentRouterKey()
    {
        TenantUser tenantUser = RequestHolder.getThreadLocalUser();
        if(Optional.ofNullable(tenantUser).isEmpty()){
            throw new SystemException("Tenant user not null");
        }
        if(Optional.ofNullable(tenantUser.getTenantId()).isPresent()){
            // 企业库数据源
            return RouterKey.build(DataSourceEnum.ANSWER_AI_TENANT.getName());
        }else {
            // 管理库数据源
            return RouterKey.build(DataSourceEnum.ANSWER_AI.getName());
        }
    }
}
