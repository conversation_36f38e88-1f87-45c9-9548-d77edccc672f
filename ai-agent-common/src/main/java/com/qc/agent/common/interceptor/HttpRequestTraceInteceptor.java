package com.qc.agent.common.interceptor;


import com.alibaba.fastjson.JSONObject;
import com.qc.agent.common.core.MasterToken;
import com.qc.agent.common.core.RequestHolder;
import com.qc.agent.common.core.TenantUser;
import com.qc.agent.common.exception.SystemException;
import com.qc.agent.common.util.AESUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Component
public class HttpRequestTraceInteceptor implements HandlerInterceptor, ApplicationContextAware {

    @Value("#{'${ai.agent.white-list}'.split(',')}")
    private List<String> whiteList;

    List<ValidatorInterceptor> validators = new ArrayList<>();

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (whiteList.contains(request.getRequestURI())) {
            return true;
        }

        String masterToken = parseMasterToken(request);
        if(StringUtils.isEmpty(masterToken))
        {
            // 解析token设置到ThreadLocal中,可供后续使用。
            RequestHolder.setThreadLocalUser(parseToken(request));
            for(ValidatorInterceptor validator : validators)
            {
                ResponseEntity<String> re = validator.validate(request);
                if(!re.getStatusCode().equals(HttpStatus.OK))
                {
                    throw new SystemException(re.getBody());
                }
            }
        }
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler,
                           ModelAndView modelAndView) throws Exception {
        HandlerInterceptor.super.postHandle(request, response, handler, modelAndView);
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
            throws Exception {
        RequestHolder.clear();
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        applicationContext.getBeansOfType(ValidatorInterceptor.class).values().stream().forEach(validators::add);
    }

    private static TenantUser parseToken(HttpServletRequest request) {
        String data = fetchDataFromToken(request);
        if (StringUtils.isEmpty(data)) {
            data = fetchDataFromCookie(request);
        }
        if (StringUtils.isNotEmpty(data)) {
            return TenantUser.buildBean(data);
        }
        throw new SystemException("token parse error.");
    }

    private static String fetchDataFromCookie(HttpServletRequest request) {
        Cookie[] cookies = request.getCookies();
        Optional<Cookie> tokenCookie = Arrays.stream(cookies).filter(cookie -> cookie.getName().equals("x-token")).findFirst();
        if (tokenCookie.isPresent()) {
            String token = tokenCookie.get().getValue();

            String[] values = StringUtils.split(token, "\\.");
            if (values.length == 3) {
                // 添加token
                RequestHolder.setRequestCookie(request.getHeader("Cookie"));
                return values[1];
            }
        }
        return null;
    }

    private static String fetchDataFromToken(HttpServletRequest request) {
        String authorization = request.getHeader("Authorization");
        if (StringUtils.isNotEmpty(authorization) && authorization.startsWith("Bearer ")) {
            String[] values = authorization.substring("Bearer ".length()).split("\\.");
            if (values.length == 3) {
                RequestHolder.setRequestToken(authorization);
                return values[1];
            }
        }
        return null;
    }

    private static String parseMasterToken(HttpServletRequest request) {
        String masterToken = request.getHeader("master-token");
        if(StringUtils.isNotEmpty(masterToken)){
            try
            {
                String content = AESUtils.decrypt(masterToken,"aP#whfjHiN@8uh0Z");
                RequestHolder.setMasterToken(JSONObject.toJavaObject(JSONObject.parseObject(content), MasterToken.class));
                return content;
            }
            catch(Exception e)
            {
                throw new SystemException("没有Master执行权限",e);
            }

        }
        return null;

    }

}
