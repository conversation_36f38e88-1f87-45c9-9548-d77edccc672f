package com.qc.agent.common.config.datasource.annotation;

import com.qc.agent.jdbc.datasource.model.ConnectionType;
import com.qc.agent.jdbc.datasource.model.DataSourceType;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface AppServer
{
    //数据源类型
    DataSourceType type() default DataSourceType.SLAVE;

    // 连接类型
    ConnectionType connType() default ConnectionType.POOL;
}
