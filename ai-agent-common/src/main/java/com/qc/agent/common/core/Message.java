package com.qc.agent.common.core;

import com.alibaba.fastjson.JSON;

public class Message
{
	private Object data;
	private  String code;
	private   String message = "";



	public static Message of(){
		return new Message();
	}

	public <T> Message data(T data){
		this.data = data;
		return this;
	}

	public Message ok(){
		this.code =  STATUS_SUCCESS;
		return this;
	}

	public Message ok(String message){
		this.message = message;
		this.code =  STATUS_SUCCESS;
		return this;
	}

	public Message error(String message){
		this.code =   STATUS_FAIL;
		this.message = message;
		return this;
	}

	public boolean isOk(){
		return STATUS_SUCCESS.equals(code);
	}

	public boolean isError(){
		return STATUS_FAIL.equals(code);
	}

	public String toJsonString()
	{
		return JSON.toJSONString(this);
	}

	public <T extends Object> T getData()
	{
		return (T) data;
	}

	public void setData(Object data)
	{
		this.data = data;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public static final String STATUS_SUCCESS = "1";
	public static final String STATUS_FAIL = "0";
}
