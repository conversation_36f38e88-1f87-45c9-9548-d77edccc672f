package com.qc.agent.common.config.db.remote.vo;

import java.io.Serializable;
import java.util.Date;



public class MbsTenant implements Serializable
{
	/**
	 *
	 */
	private static final long serialVersionUID = 2267597905727930346L;


	private Long id;

	/**
	 * mbs_tenant.code Tue Mar 08 15:55:10 CST 2011
	 */
	private String code;
	/**
	 * mbs_tenant.name Tue Mar 08 15:55:10 CST 2011
	 */
	private String name;
	/**
	 * mbs_tenant.linkman Tue Mar 08 15:55:10 CST 2011
	 */
	private String linkman;
	/**
	 * mbs_tenant.phone Tue Mar 08 15:55:10 CST 2011
	 */
	private String phone;
	/**
	 * mbs_tenant.fax Tue Mar 08 15:55:10 CST 2011
	 */
	private String fax;
	/**
	 * mbs_tenant.mobile Tue Mar 08 15:55:10 CST 2011
	 */
	private String mobile;
	/**
	 * mbs_tenant.address Tue Mar 08 15:55:10 CST 2011
	 */
	private String address;
	/**
	 * mbs_tenant.email Tue Mar 08 15:55:10 CST 2011
	 */
	private String email;
	/**
	 * mbs_tenant.url Tue Mar 08 15:55:10 CST 2011
	 */
	private String url;
	/**
	 * mbs_tenant.remarks Tue Mar 08 15:55:10 CST 2011
	 */
	private String remarks;
	/**
	 * mbs_tenant.svr_id Tue Mar 08 15:55:10 CST 2011
	 */
	private Long svrId;
	/**
	 * mbs_tenant.db_user Tue Mar 08 15:55:10 CST 2011
	 */
	private String dbUser;
	/**
	 * mbs_tenant.db_passwd Tue Mar 08 15:55:10 CST 2011
	 */
	private String dbPasswd;
	/**
	 * mbs_tenant.max_conn_count 缺省为账户用户数的1/10,默认策略可调整，可设置修改最大连接数 Tue Mar 08
	 * 15:55:10 CST 2011
	 */
	private int maxConnCount;
	/**
	 * mbs_tenant.min_conn_count Tue Mar 08 15:55:10 CST 2011
	 */
	private int minConnCount;
	/**
	 * mbs_tenant.sleep_keep_time 单位秒，默认值可在系统中统一配置 Tue Mar 08 15:55:10 CST 2011
	 */
	private int sleepKeepTime;
	/**
	 * mbs_tenant.status 0:停用 1:启用 2:禁用、销户 Tue Mar 08 15:55:10 CST 2011
	 */
	private String status;
	/**
	 * mbs_tenant.create_time Tue Mar 08 15:55:10 CST 2011
	 */
	private Date createTime;
	/**
	 * mbs_tenant.users 通过企业系统管理开通的用户数 Tue Mar 08 15:55:10 CST 2011
	 */
	private Long users;
	/**
	 * mbs_tenant.stop_remarks 停用原因 Tue Mar 08 15:55:10 CST 2011
	 */
	private String stopRemarks;
	private String notStatus;
	/** 账户状态 停用 0 */
	public static final String STATUS_STOP = "0";
	/** 账户状态 启用 1 */
	public static final String STATUS_RUN = "1";
	/** 账户状态 禁用 2 */
	public static final String STATUS_FORBIDDEN = "2";

	/** 企业简称 */
	private String shortName;
	/** 访问限制 */
	private String clientAcl;
	/** 地区 */
	private String region;
	/** 所属行业 */
	private String industry;
	/** 联系人性别 */
	private String sex;

	/** 渠道相关信息 */
	/** 渠道Id */
	private Long channelId;
	/** 渠道名称 */
	private String channelName;
	/** 渠道编码 */
	private String channelCode;
	/** 渠道类型 */
	private String type;
	/** 渠道是否用SI验证 */
	private boolean isSiAuth;
	/** 运营商类型 */
	private String networkType;
	/** 渠道门户logo路径 */
	private String portalUrl;
	/** 运营商类型-自运营 */
	public static final String OWNOPERATIONS = "0";
	/** 运营商类型-运营商接入 */
	public static final String CARRIERACCESS = "1";
	/** 企业数据源信息 */
	public DBInfo dbInfo;
	/** PUSH开通状态,true开通，false不开通 */
	private String pushEnable;
	/**
	 * 主动定位
	 */
	private String locationPolicyA;
	/**
	 * 被动定位
	 */
	private String locationPolicyB;

	private String maxStorageSize;
	private String dbSize;
	private String fileSize;

	private int lbsUsers;
	private Date lbsEndTime;

	/** 注册类型 */
	private String regiterOrigin;

	/**
	 * 基站定位 Google API解析
	 */
	public static final String LOCATION_POLICY_GOOGLE = "google";
	/**
	 * 基站定位 Baidu API解析
	 */
	public static final String LOCATION_POLICY_BAIDU = "baidu";
	/**
	 * SI定位
	 */
	public static final String LOCATION_POLICY_SI = "si";

	private String tenantType;
	/**
	 * 0 付费客户 1 免费客户
	 * */
	public static final String TENANTTYPE_VIP = "0";
	public static final String TENANTTYPE_FREE = "1";

	public String getCode()
	{
		return code;
	}

	public void setCode(String code)
	{
		this.code = code;
	}

	public String getName()
	{
		return name;
	}

	public void setName(String name)
	{
		this.name = name;
	}

	public String getLinkman()
	{
		return linkman;
	}

	public void setLinkman(String linkman)
	{
		this.linkman = linkman;
	}

	public String getPhone()
	{
		return phone;
	}

	public void setPhone(String phone)
	{
		this.phone = phone;
	}

	public String getFax()
	{
		return fax;
	}

	public void setFax(String fax)
	{
		this.fax = fax;
	}

	public String getMobile()
	{
		return mobile;
	}

	public void setMobile(String mobile)
	{
		this.mobile = mobile;
	}

	public String getAddress()
	{
		return address;
	}

	public void setAddress(String address)
	{
		this.address = address;
	}

	public String getEmail()
	{
		return email;
	}

	public void setEmail(String email)
	{
		this.email = email;
	}

	public String getUrl()
	{
		return url;
	}

	public void setUrl(String url)
	{
		this.url = url;
	}

	public String getRemarks()
	{
		return remarks;
	}

	public void setRemarks(String remarks)
	{
		this.remarks = remarks;
	}

	public Long getSvrId()
	{
		return svrId;
	}

	public void setSvrId(Long svrId)
	{
		this.svrId = svrId;
	}



	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getDbUser()
	{
		return dbUser;
	}

	public void setDbUser(String dbUser)
	{
		this.dbUser = dbUser;
	}

	public String getDbPasswd()
	{
		return dbPasswd;
	}

	public void setDbPasswd(String dbPasswd)
	{
		this.dbPasswd = dbPasswd;
	}

	public int getMaxConnCount()
	{
		return maxConnCount;
	}

	public void setMaxConnCount(int maxConnCount)
	{
		this.maxConnCount = maxConnCount;
	}

	public int getMinConnCount()
	{
		return minConnCount;
	}

	public void setMinConnCount(int minConnCount)
	{
		this.minConnCount = minConnCount;
	}

	public int getSleepKeepTime()
	{
		return sleepKeepTime;
	}

	public void setSleepKeepTime(int sleepKeepTime)
	{
		this.sleepKeepTime = sleepKeepTime;
	}

	public String getStatus()
	{
		return status;
	}

	public void setStatus(String status)
	{
		this.status = status;
	}

	public Date getCreateTime()
	{
		return createTime;
	}

	public void setCreateTime(Date createTime)
	{
		this.createTime = createTime;
	}

	public Long getUsers()
	{
		return users;
	}

	public void setUsers(Long users)
	{
		this.users = users;
	}

	public String getStopRemarks()
	{
		return stopRemarks;
	}

	public void setStopRemarks(String stopRemarks)
	{
		this.stopRemarks = stopRemarks;
	}

	public String getNotStatus()
	{
		return notStatus;
	}

	public void setNotStatus(String notStatus)
	{
		this.notStatus = notStatus;
	}

	public String getShortName()
	{
		return shortName;
	}

	public void setShortName(String shortName)
	{
		this.shortName = shortName;
	}

	public String getClientAcl()
	{
		return clientAcl;
	}

	public void setClientAcl(String clientAcl)
	{
		this.clientAcl = clientAcl;
	}

	public String getRegion()
	{
		return region;
	}

	public void setRegion(String region)
	{
		this.region = region;
	}

	public String getIndustry()
	{
		return industry;
	}

	public void setIndustry(String industry)
	{
		this.industry = industry;
	}

	public String getSex()
	{
		return sex;
	}

	public void setSex(String sex)
	{
		this.sex = sex;
	}

	public Long getChannelId()
	{
		return channelId;
	}

	public void setChannelId(Long channelId)
	{
		this.channelId = channelId;
	}

	public String getChannelName()
	{
		return channelName;
	}

	public void setChannelName(String channelName)
	{
		this.channelName = channelName;
	}

	public String getChannelCode()
	{
		return channelCode;
	}

	public void setChannelCode(String channelCode)
	{
		this.channelCode = channelCode;
	}

	public String getType()
	{
		return type;
	}

	public void setType(String type)
	{
		this.type = type;
	}

	public String getNetworkType()
	{
		return networkType;
	}

	public void setNetworkType(String networkType)
	{
		this.networkType = networkType;
	}

	/**
	 * @return the dbInfo
	 */
	public DBInfo getDbInfo()
	{
		return dbInfo;
	}

	/**
	 * @param dbInfo
	 *            the dbInfo to set
	 */
	public void setDbInfo(DBInfo dbInfo)
	{
		this.dbInfo = dbInfo;
	}

	public String getPortalUrl()
	{
		return portalUrl;
	}

	public void setPortalUrl(String portalUrl)
	{
		this.portalUrl = portalUrl;
	}

	public String getPushEnable()
	{
		return pushEnable;
	}

	public void setPushEnable(String pushEnable)
	{
		this.pushEnable = pushEnable;
	}

	public boolean isSiAuth()
	{
		return isSiAuth;
	}

	public void setSiAuth(boolean isSiAuth1)
	{
		this.isSiAuth = isSiAuth1;
	}

	public String getLocationPolicyA()
	{
		return locationPolicyA;
	}

	public void setLocationPolicyA(String locationPolicyA)
	{
		this.locationPolicyA = locationPolicyA;
	}

	public String getLocationPolicyB()
	{
		return locationPolicyB;
	}

	public void setLocationPolicyB(String locationPolicyB)
	{
		this.locationPolicyB = locationPolicyB;
	}

	public String getMaxStorageSize()
	{
		return maxStorageSize;
	}

	public void setMaxStorageSize(String maxStorageSize)
	{
		this.maxStorageSize = maxStorageSize;
	}

	public String getDbSize()
	{
		return dbSize;
	}

	public void setDbSize(String dbSize)
	{
		this.dbSize = dbSize;
	}

	public String getFileSize()
	{
		return fileSize;
	}

	public void setFileSize(String fileSize)
	{
		this.fileSize = fileSize;
	}

	public int getLbsUsers()
	{
		return lbsUsers;
	}

	public void setLbsUsers(int lbsUsers)
	{
		this.lbsUsers = lbsUsers;
	}

	public Date getLbsEndTime()
	{
		return lbsEndTime;
	}

	public void setLbsEndTime(Date lbsEndTime)
	{
		this.lbsEndTime = lbsEndTime;
	}

	public String getRegiterOrigin()
	{
		return regiterOrigin;
	}

	public void setRegiterOrigin(String regiterOrigin)
	{
		this.regiterOrigin = regiterOrigin;
	}

	public String getTenantType()
	{
		return tenantType;
	}

	public void setTenantType(String tenantType)
	{
		this.tenantType = tenantType;
	}
}
