<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.qc.agent</groupId>
		<artifactId>qc-ai-agent</artifactId>
		<version>1.0.0</version>
	</parent>
	<artifactId>ai-agent-storage</artifactId>
	<version>1.0.0</version>
	<name>ai-agent-storage</name>
	<description>ai-agent-storage</description>
	<properties>
		<java.version>17</java.version>
	</properties>
	<dependencies>

		<dependency>
			<groupId>com.qc.agent</groupId>
			<artifactId>ai-agent-common</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>org.dromara.x-file-storage</groupId>
			<artifactId>x-file-storage-spring</artifactId>
			<version>2.1.0</version>
		</dependency>

		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-core</artifactId>
			<version>2.13.1</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
			<version>2.13.1</version>
		</dependency>

		<dependency>
			<groupId>com.aliyun.oss</groupId>
			<artifactId>aliyun-sdk-oss</artifactId>
			<version>3.16.1</version>
		</dependency>

	</dependencies>

<!--	<build>-->
<!--		<plugins>-->
<!--			<plugin>-->
<!--				<groupId>org.springframework.boot</groupId>-->
<!--				<artifactId>spring-boot-maven-plugin</artifactId>-->
<!--			</plugin>-->
<!--		</plugins>-->
<!--	</build>-->

</project>
