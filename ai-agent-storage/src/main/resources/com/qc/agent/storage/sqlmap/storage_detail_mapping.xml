<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.qc.agent.storage.mapper.StorageDetailMapper" >
  <resultMap id="BaseResultMap" type="com.qc.agent.storage.model.StorageDetail" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="url" property="url" jdbcType="VARCHAR" />
    <result column="size" property="size" jdbcType="NUMERIC" />
    <result column="filename" property="filename" jdbcType="VARCHAR" />
    <result column="original_filename" property="originalFilename" jdbcType="VARCHAR" />
    <result column="base_path" property="basePath" jdbcType="VARCHAR" />
    <result column="path" property="path" jdbcType="VARCHAR" />
    <result column="ext" property="ext" jdbcType="VARCHAR" />
    <result column="content_type" property="contentType" jdbcType="VARCHAR" />
    <result column="platform" property="platform" jdbcType="VARCHAR" />
    <result column="th_url" property="thUrl" jdbcType="VARCHAR" />
    <result column="th_filename" property="thFilename" jdbcType="VARCHAR" />
    <result column="th_size" property="thSize" jdbcType="NUMERIC" />
    <result column="th_content_type" property="thContentType" jdbcType="VARCHAR" />
    <result column="object_id" property="objectId" jdbcType="VARCHAR" />
    <result column="object_type" property="objectType" jdbcType="VARCHAR" />
    <result column="metadata" property="metadata" jdbcType="VARCHAR" />
    <result column="user_metadata" property="userMetadata" jdbcType="VARCHAR" />
    <result column="th_metadata" property="thMetadata" jdbcType="VARCHAR" />
    <result column="th_user_metadata" property="thUserMetadata" jdbcType="VARCHAR" />
    <result column="attr" property="attr" jdbcType="VARCHAR" />
    <result column="file_acl" property="fileAcl" jdbcType="VARCHAR" />
    <result column="th_file_acl" property="thFileAcl" jdbcType="VARCHAR" />
    <result column="hash_info" property="hashInfo" jdbcType="VARCHAR" />
    <result column="upload_id" property="uploadId" jdbcType="VARCHAR" />
    <result column="upload_status" property="uploadStatus" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, url, size, filename, original_filename, base_path, path, ext, content_type, platform,
    th_url, th_filename, th_size, th_content_type, object_id, object_type, metadata,
    user_metadata, th_metadata, th_user_metadata, attr, file_acl, th_file_acl, hash_info,
    upload_id, upload_status, create_time
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from qc_storage_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectByUrl" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select
    <include refid="Base_Column_List" />
    from qc_storage_detail
    where url = #{url}
    order by create_time desc limit 1
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from qc_storage_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.qc.agent.storage.model.StorageDetail" >
    insert into qc_storage_detail (id, url, size,
      filename, original_filename, base_path,
      path, ext, content_type,
      platform, th_url, th_filename,
      th_size, th_content_type, object_id,
      object_type, metadata, user_metadata,
      th_metadata, th_user_metadata, attr,
      file_acl, th_file_acl, hash_info,
      upload_id, upload_status, create_time
      )
    values (#{id,jdbcType=BIGINT}, #{url,jdbcType=VARCHAR}, #{size,jdbcType=NUMERIC},
      #{filename,jdbcType=VARCHAR}, #{originalFilename,jdbcType=VARCHAR}, #{basePath,jdbcType=VARCHAR},
      #{path,jdbcType=VARCHAR}, #{ext,jdbcType=VARCHAR}, #{contentType,jdbcType=VARCHAR},
      #{platform,jdbcType=VARCHAR}, #{thUrl,jdbcType=VARCHAR}, #{thFilename,jdbcType=VARCHAR},
      #{thSize,jdbcType=NUMERIC}, #{thContentType,jdbcType=VARCHAR}, #{objectId,jdbcType=VARCHAR},
      #{objectType,jdbcType=VARCHAR}, #{metadata,jdbcType=VARCHAR}, #{userMetadata,jdbcType=VARCHAR},
      #{thMetadata,jdbcType=VARCHAR}, #{thUserMetadata,jdbcType=VARCHAR}, #{attr,jdbcType=VARCHAR},
      #{fileAcl,jdbcType=VARCHAR}, #{thFileAcl,jdbcType=VARCHAR}, #{hashInfo,jdbcType=VARCHAR},
      #{uploadId,jdbcType=VARCHAR}, #{uploadStatus,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.qc.agent.storage.model.StorageDetail" >
    insert into qc_storage_detail
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="url != null" >
        url,
      </if>
      <if test="size != null" >
        size,
      </if>
      <if test="filename != null" >
        filename,
      </if>
      <if test="originalFilename != null" >
        original_filename,
      </if>
      <if test="basePath != null" >
        base_path,
      </if>
      <if test="path != null" >
        path,
      </if>
      <if test="ext != null" >
        ext,
      </if>
      <if test="contentType != null" >
        content_type,
      </if>
      <if test="platform != null" >
        platform,
      </if>
      <if test="thUrl != null" >
        th_url,
      </if>
      <if test="thFilename != null" >
        th_filename,
      </if>
      <if test="thSize != null" >
        th_size,
      </if>
      <if test="thContentType != null" >
        th_content_type,
      </if>
      <if test="objectId != null" >
        object_id,
      </if>
      <if test="objectType != null" >
        object_type,
      </if>
      <if test="metadata != null" >
        metadata,
      </if>
      <if test="userMetadata != null" >
        user_metadata,
      </if>
      <if test="thMetadata != null" >
        th_metadata,
      </if>
      <if test="thUserMetadata != null" >
        th_user_metadata,
      </if>
      <if test="attr != null" >
        attr,
      </if>
      <if test="fileAcl != null" >
        file_acl,
      </if>
      <if test="thFileAcl != null" >
        th_file_acl,
      </if>
      <if test="hashInfo != null" >
        hash_info,
      </if>
      <if test="uploadId != null" >
        upload_id,
      </if>
      <if test="uploadStatus != null" >
        upload_status,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="url != null" >
        #{url,jdbcType=VARCHAR},
      </if>
      <if test="size != null" >
        #{size,jdbcType=NUMERIC},
      </if>
      <if test="filename != null" >
        #{filename,jdbcType=VARCHAR},
      </if>
      <if test="originalFilename != null" >
        #{originalFilename,jdbcType=VARCHAR},
      </if>
      <if test="basePath != null" >
        #{basePath,jdbcType=VARCHAR},
      </if>
      <if test="path != null" >
        #{path,jdbcType=VARCHAR},
      </if>
      <if test="ext != null" >
        #{ext,jdbcType=VARCHAR},
      </if>
      <if test="contentType != null" >
        #{contentType,jdbcType=VARCHAR},
      </if>
      <if test="platform != null" >
        #{platform,jdbcType=VARCHAR},
      </if>
      <if test="thUrl != null" >
        #{thUrl,jdbcType=VARCHAR},
      </if>
      <if test="thFilename != null" >
        #{thFilename,jdbcType=VARCHAR},
      </if>
      <if test="thSize != null" >
        #{thSize,jdbcType=NUMERIC},
      </if>
      <if test="thContentType != null" >
        #{thContentType,jdbcType=VARCHAR},
      </if>
      <if test="objectId != null" >
        #{objectId,jdbcType=VARCHAR},
      </if>
      <if test="objectType != null" >
        #{objectType,jdbcType=VARCHAR},
      </if>
      <if test="metadata != null" >
        #{metadata,jdbcType=VARCHAR},
      </if>
      <if test="userMetadata != null" >
        #{userMetadata,jdbcType=VARCHAR},
      </if>
      <if test="thMetadata != null" >
        #{thMetadata,jdbcType=VARCHAR},
      </if>
      <if test="thUserMetadata != null" >
        #{thUserMetadata,jdbcType=VARCHAR},
      </if>
      <if test="attr != null" >
        #{attr,jdbcType=VARCHAR},
      </if>
      <if test="fileAcl != null" >
        #{fileAcl,jdbcType=VARCHAR},
      </if>
      <if test="thFileAcl != null" >
        #{thFileAcl,jdbcType=VARCHAR},
      </if>
      <if test="hashInfo != null" >
        #{hashInfo,jdbcType=VARCHAR},
      </if>
      <if test="uploadId != null" >
        #{uploadId,jdbcType=VARCHAR},
      </if>
      <if test="uploadStatus != null" >
        #{uploadStatus,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.qc.agent.storage.model.StorageDetail" >
    update qc_storage_detail
    <set >
      <if test="url != null" >
        url = #{url,jdbcType=VARCHAR},
      </if>
      <if test="size != null" >
        size = #{size,jdbcType=NUMERIC},
      </if>
      <if test="filename != null" >
        filename = #{filename,jdbcType=VARCHAR},
      </if>
      <if test="originalFilename != null" >
        original_filename = #{originalFilename,jdbcType=VARCHAR},
      </if>
      <if test="basePath != null" >
        base_path = #{basePath,jdbcType=VARCHAR},
      </if>
      <if test="path != null" >
        path = #{path,jdbcType=VARCHAR},
      </if>
      <if test="ext != null" >
        ext = #{ext,jdbcType=VARCHAR},
      </if>
      <if test="contentType != null" >
        content_type = #{contentType,jdbcType=VARCHAR},
      </if>
      <if test="platform != null" >
        platform = #{platform,jdbcType=VARCHAR},
      </if>
      <if test="thUrl != null" >
        th_url = #{thUrl,jdbcType=VARCHAR},
      </if>
      <if test="thFilename != null" >
        th_filename = #{thFilename,jdbcType=VARCHAR},
      </if>
      <if test="thSize != null" >
        th_size = #{thSize,jdbcType=NUMERIC},
      </if>
      <if test="thContentType != null" >
        th_content_type = #{thContentType,jdbcType=VARCHAR},
      </if>
      <if test="objectId != null" >
        object_id = #{objectId,jdbcType=VARCHAR},
      </if>
      <if test="objectType != null" >
        object_type = #{objectType,jdbcType=VARCHAR},
      </if>
      <if test="metadata != null" >
        metadata = #{metadata,jdbcType=VARCHAR},
      </if>
      <if test="userMetadata != null" >
        user_metadata = #{userMetadata,jdbcType=VARCHAR},
      </if>
      <if test="thMetadata != null" >
        th_metadata = #{thMetadata,jdbcType=VARCHAR},
      </if>
      <if test="thUserMetadata != null" >
        th_user_metadata = #{thUserMetadata,jdbcType=VARCHAR},
      </if>
      <if test="attr != null" >
        attr = #{attr,jdbcType=VARCHAR},
      </if>
      <if test="fileAcl != null" >
        file_acl = #{fileAcl,jdbcType=VARCHAR},
      </if>
      <if test="thFileAcl != null" >
        th_file_acl = #{thFileAcl,jdbcType=VARCHAR},
      </if>
      <if test="hashInfo != null" >
        hash_info = #{hashInfo,jdbcType=VARCHAR},
      </if>
      <if test="uploadId != null" >
        upload_id = #{uploadId,jdbcType=VARCHAR},
      </if>
      <if test="uploadStatus != null" >
        upload_status = #{uploadStatus,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.qc.agent.storage.model.StorageDetail" >
    update qc_storage_detail
    set url = #{url,jdbcType=VARCHAR},
      size = #{size,jdbcType=NUMERIC},
      filename = #{filename,jdbcType=VARCHAR},
      original_filename = #{originalFilename,jdbcType=VARCHAR},
      base_path = #{basePath,jdbcType=VARCHAR},
      path = #{path,jdbcType=VARCHAR},
      ext = #{ext,jdbcType=VARCHAR},
      content_type = #{contentType,jdbcType=VARCHAR},
      platform = #{platform,jdbcType=VARCHAR},
      th_url = #{thUrl,jdbcType=VARCHAR},
      th_filename = #{thFilename,jdbcType=VARCHAR},
      th_size = #{thSize,jdbcType=NUMERIC},
      th_content_type = #{thContentType,jdbcType=VARCHAR},
      object_id = #{objectId,jdbcType=VARCHAR},
      object_type = #{objectType,jdbcType=VARCHAR},
      metadata = #{metadata,jdbcType=VARCHAR},
      user_metadata = #{userMetadata,jdbcType=VARCHAR},
      th_metadata = #{thMetadata,jdbcType=VARCHAR},
      th_user_metadata = #{thUserMetadata,jdbcType=VARCHAR},
      attr = #{attr,jdbcType=VARCHAR},
      file_acl = #{fileAcl,jdbcType=VARCHAR},
      th_file_acl = #{thFileAcl,jdbcType=VARCHAR},
      hash_info = #{hashInfo,jdbcType=VARCHAR},
      upload_id = #{uploadId,jdbcType=VARCHAR},
      upload_status = #{uploadStatus,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
