<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.qc.agent.storage.mapper.StoragePartDetailMapper" >
  <resultMap id="BaseResultMap" type="com.qc.agent.storage.model.StoragePartDetail" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="platform" property="platform" jdbcType="VARCHAR" />
    <result column="upload_id" property="uploadId" jdbcType="VARCHAR" />
    <result column="e_tag" property="eTag" jdbcType="VARCHAR" />
    <result column="part_number" property="partNumber" jdbcType="NUMERIC" />
    <result column="part_size" property="partSize" jdbcType="NUMERIC" />
    <result column="hash_info" property="hashInfo" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, platform, upload_id, e_tag, part_number, part_size, hash_info, create_time
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from qc_storage_part_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from qc_storage_part_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.qc.agent.storage.model.StoragePartDetail" >
    insert into qc_storage_part_detail (id, platform, upload_id,
      e_tag, part_number, part_size,
      hash_info, create_time)
    values (#{id,jdbcType=BIGINT}, #{platform,jdbcType=VARCHAR}, #{uploadId,jdbcType=VARCHAR},
      #{eTag,jdbcType=VARCHAR}, #{partNumber,jdbcType=NUMERIC}, #{partSize,jdbcType=NUMERIC},
      #{hashInfo,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.qc.agent.storage.model.StoragePartDetail" >
    insert into qc_storage_part_detail
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="platform != null" >
        platform,
      </if>
      <if test="uploadId != null" >
        upload_id,
      </if>
      <if test="eTag != null" >
        e_tag,
      </if>
      <if test="partNumber != null" >
        part_number,
      </if>
      <if test="partSize != null" >
        part_size,
      </if>
      <if test="hashInfo != null" >
        hash_info,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="platform != null" >
        #{platform,jdbcType=VARCHAR},
      </if>
      <if test="uploadId != null" >
        #{uploadId,jdbcType=VARCHAR},
      </if>
      <if test="eTag != null" >
        #{eTag,jdbcType=VARCHAR},
      </if>
      <if test="partNumber != null" >
        #{partNumber,jdbcType=NUMERIC},
      </if>
      <if test="partSize != null" >
        #{partSize,jdbcType=NUMERIC},
      </if>
      <if test="hashInfo != null" >
        #{hashInfo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.qc.agent.storage.model.StoragePartDetail" >
    update qc_storage_part_detail
    <set >
      <if test="platform != null" >
        platform = #{platform,jdbcType=VARCHAR},
      </if>
      <if test="uploadId != null" >
        upload_id = #{uploadId,jdbcType=VARCHAR},
      </if>
      <if test="eTag != null" >
        e_tag = #{eTag,jdbcType=VARCHAR},
      </if>
      <if test="partNumber != null" >
        part_number = #{partNumber,jdbcType=NUMERIC},
      </if>
      <if test="partSize != null" >
        part_size = #{partSize,jdbcType=NUMERIC},
      </if>
      <if test="hashInfo != null" >
        hash_info = #{hashInfo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.qc.agent.storage.model.StoragePartDetail" >
    update qc_storage_part_detail
    set platform = #{platform,jdbcType=VARCHAR},
      upload_id = #{uploadId,jdbcType=VARCHAR},
      e_tag = #{eTag,jdbcType=VARCHAR},
      part_number = #{partNumber,jdbcType=NUMERIC},
      part_size = #{partSize,jdbcType=NUMERIC},
      hash_info = #{hashInfo,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
