package com.qc.agent.storage.model;

import java.math.BigDecimal;
import java.util.Date;

public class StorageDetail {
    private Long id;

    private String url;

    private BigDecimal size;

    private String filename;

    private String originalFilename;

    private String basePath;

    private String path;

    private String ext;

    private String contentType;

    private String platform;

    private String thUrl;

    private String thFilename;

    private BigDecimal thSize;

    private String thContentType;

    private String objectId;

    private String objectType;

    private String metadata;

    private String userMetadata;

    private String thMetadata;

    private String thUserMetadata;

    private String attr;

    private String fileAcl;

    private String thFileAcl;

    private String hashInfo;

    private String uploadId;

    private Integer uploadStatus;

    private Date createTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url == null ? null : url.trim();
    }

    public BigDecimal getSize() {
        return size;
    }

    public void setSize(BigDecimal size) {
        this.size = size;
    }

    public String getFilename() {
        return filename;
    }

    public void setFilename(String filename) {
        this.filename = filename == null ? null : filename.trim();
    }

    public String getOriginalFilename() {
        return originalFilename;
    }

    public void setOriginalFilename(String originalFilename) {
        this.originalFilename = originalFilename == null ? null : originalFilename.trim();
    }

    public String getBasePath() {
        return basePath;
    }

    public void setBasePath(String basePath) {
        this.basePath = basePath == null ? null : basePath.trim();
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path == null ? null : path.trim();
    }

    public String getExt() {
        return ext;
    }

    public void setExt(String ext) {
        this.ext = ext == null ? null : ext.trim();
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType == null ? null : contentType.trim();
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform == null ? null : platform.trim();
    }

    public String getThUrl() {
        return thUrl;
    }

    public void setThUrl(String thUrl) {
        this.thUrl = thUrl == null ? null : thUrl.trim();
    }

    public String getThFilename() {
        return thFilename;
    }

    public void setThFilename(String thFilename) {
        this.thFilename = thFilename == null ? null : thFilename.trim();
    }

    public BigDecimal getThSize() {
        return thSize;
    }

    public void setThSize(BigDecimal thSize) {
        this.thSize = thSize;
    }

    public String getThContentType() {
        return thContentType;
    }

    public void setThContentType(String thContentType) {
        this.thContentType = thContentType == null ? null : thContentType.trim();
    }

    public String getObjectId() {
        return objectId;
    }

    public void setObjectId(String objectId) {
        this.objectId = objectId == null ? null : objectId.trim();
    }

    public String getObjectType() {
        return objectType;
    }

    public void setObjectType(String objectType) {
        this.objectType = objectType == null ? null : objectType.trim();
    }

    public String getMetadata() {
        return metadata;
    }

    public void setMetadata(String metadata) {
        this.metadata = metadata == null ? null : metadata.trim();
    }

    public String getUserMetadata() {
        return userMetadata;
    }

    public void setUserMetadata(String userMetadata) {
        this.userMetadata = userMetadata == null ? null : userMetadata.trim();
    }

    public String getThMetadata() {
        return thMetadata;
    }

    public void setThMetadata(String thMetadata) {
        this.thMetadata = thMetadata == null ? null : thMetadata.trim();
    }

    public String getThUserMetadata() {
        return thUserMetadata;
    }

    public void setThUserMetadata(String thUserMetadata) {
        this.thUserMetadata = thUserMetadata == null ? null : thUserMetadata.trim();
    }

    public String getAttr() {
        return attr;
    }

    public void setAttr(String attr) {
        this.attr = attr == null ? null : attr.trim();
    }

    public String getFileAcl() {
        return fileAcl;
    }

    public void setFileAcl(String fileAcl) {
        this.fileAcl = fileAcl == null ? null : fileAcl.trim();
    }

    public String getThFileAcl() {
        return thFileAcl;
    }

    public void setThFileAcl(String thFileAcl) {
        this.thFileAcl = thFileAcl == null ? null : thFileAcl.trim();
    }

    public String getHashInfo() {
        return hashInfo;
    }

    public void setHashInfo(String hashInfo) {
        this.hashInfo = hashInfo == null ? null : hashInfo.trim();
    }

    public String getUploadId() {
        return uploadId;
    }

    public void setUploadId(String uploadId) {
        this.uploadId = uploadId == null ? null : uploadId.trim();
    }

    public Integer getUploadStatus() {
        return uploadStatus;
    }

    public void setUploadStatus(Integer uploadStatus) {
        this.uploadStatus = uploadStatus;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
