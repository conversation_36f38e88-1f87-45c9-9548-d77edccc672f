package com.qc.agent.storage.mapper;

import com.qc.agent.storage.model.StoragePartDetail;

public interface StoragePartDetailMapper {
    int deleteByPrimaryKey(Long id);

    int insert(StoragePartDetail record);

    int insertSelective(StoragePartDetail record);

    StoragePartDetail selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(StoragePartDetail record);

    int updateByPrimaryKey(StoragePartDetail record);
}
