package com.qc.agent.storage.model;

import java.math.BigDecimal;
import java.util.Date;

public class StoragePartDetail {
    private Long id;

    private String platform;

    private String uploadId;

    private String eTag;

    private Long partNumber;

    private BigDecimal partSize;

    private String hashInfo;

    private Date createTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform == null ? null : platform.trim();
    }

    public String getUploadId() {
        return uploadId;
    }

    public void setUploadId(String uploadId) {
        this.uploadId = uploadId == null ? null : uploadId.trim();
    }

    public String geteTag() {
        return eTag;
    }

    public void seteTag(String eTag) {
        this.eTag = eTag == null ? null : eTag.trim();
    }

    public Long getPartNumber() {
        return partNumber;
    }

    public void setPartNumber(Long partNumber) {
        this.partNumber = partNumber;
    }

    public BigDecimal getPartSize() {
        return partSize;
    }

    public void setPartSize(BigDecimal partSize) {
        this.partSize = partSize;
    }

    public String getHashInfo() {
        return hashInfo;
    }

    public void setHashInfo(String hashInfo) {
        this.hashInfo = hashInfo == null ? null : hashInfo.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
