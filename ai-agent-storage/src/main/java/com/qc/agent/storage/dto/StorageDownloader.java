package com.qc.agent.storage.dto;

import org.dromara.x.file.storage.core.Downloader;
import org.dromara.x.file.storage.core.FileInfo;

/**
 *
 * <AUTHOR>
 */

public class StorageDownloader
{
    private Downloader downloader;

    private FileInfo fileInfo;

    public StorageDownloader(Downloader downloader, String url){
        this.downloader = downloader;
        fileInfo = new FileInfo();
        fileInfo.setUrl(url);
    }

    public StorageDownloader(Downloader downloader, FileInfo fileInfo){
        this.downloader = downloader;
        this.fileInfo = fileInfo;
    }

    public Downloader getDownloader()
    {
        return downloader;
    }
    public void setDownloader(Downloader downloader)
    {
        this.downloader = downloader;
    }
    public FileInfo getFileInfo()
    {
        return fileInfo;
    }
    public void setFileInfo(FileInfo fileInfo)
    {
        this.fileInfo = fileInfo;
    }

}
