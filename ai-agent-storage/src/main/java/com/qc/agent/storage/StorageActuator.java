package com.qc.agent.storage;


import org.dromara.x.file.storage.core.Downloader;
import org.dromara.x.file.storage.core.FileInfo;
import org.dromara.x.file.storage.core.FileStorageProperties;
import org.dromara.x.file.storage.core.FileStorageService;
import org.dromara.x.file.storage.core.upload.UploadPretreatment;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 *
 * <AUTHOR>
 */
@Configuration
public class StorageActuator<T>
{
    @Resource
    FileStorageService fileStorageService;

    @Resource
    StorageFileRecorder storageFileRecorder;


    public UploadPretreatment of(){
        return fileStorageService.of();
    }

    public UploadPretreatment of(T source){
        return fileStorageService.of(source);
    }

    public Downloader download(String url){
        fileStorageService.setFileRecorder(storageFileRecorder);
        FileInfo fileInfo = fileStorageService.getFileInfoByUrl(url);
        if(Optional.ofNullable(fileInfo.getPlatform()).isEmpty()){
            fileInfo.setPlatform(fileStorageService.getProperties().getDefaultPlatform());
        }
        return download(fileInfo);

    }

    public Downloader download(FileInfo fileInfo){
        return fileStorageService.download(fileInfo);
    }

    public FileInfo getFileInfoByUrl(String url) {
        return fileStorageService.getFileInfoByUrl(url);
    }

    public String getLocalStoragePath(){
        List<? extends FileStorageProperties.LocalPlusConfig> localPlus = fileStorageService.getProperties()
                .getLocalPlus();
        if(CollectionUtils.isEmpty(localPlus)){
            throw new RuntimeException("没有配置本地存储");
        }else{
            return localPlus.get(0).getStoragePath();
        }
    }

    public String getLocalPlus(){
        List<? extends FileStorageProperties.LocalPlusConfig> localPlus = fileStorageService.getProperties()
                .getLocalPlus();
        if(CollectionUtils.isEmpty(localPlus)){
            throw new RuntimeException("没有配置本地存储");
        }else{
            return localPlus.get(0).getPlatform();
        }
    }


}
