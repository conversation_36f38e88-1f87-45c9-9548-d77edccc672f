package com.qc.agent.storage;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.qc.agent.common.util.UUIDUtils;
import com.qc.agent.storage.mapper.StorageDetailMapper;
import com.qc.agent.storage.model.StorageDetail;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.utils.URIBuilder;
import org.dromara.x.file.storage.core.FileInfo;
import org.dromara.x.file.storage.core.hash.HashInfo;
import org.dromara.x.file.storage.core.recorder.FileRecorder;
import org.dromara.x.file.storage.core.upload.FilePartInfo;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.net.URISyntaxException;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
@Component
public class StorageFileRecorder implements FileRecorder
{
    @Resource
    StorageDetailMapper storageDetailMapper;
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public boolean save(FileInfo fileInfo) {
        StorageDetail storageDetail = toStorageDetail(fileInfo);
        storageDetail.setId(UUIDUtils.getUUID2Long());
        int insert = storageDetailMapper.insert(storageDetail);
        if(insert > 0){
            fileInfo.setId(String.valueOf(storageDetail.getId()));
        }
        return true;
    }

    public void update(FileInfo fileInfo) {}

    @Override
    public FileInfo getByUrl(String url) {
        FileInfo fileInfo = getFileInfoByUrl(url);
        if(fileInfo == null){
            fileInfo = new FileInfo();
            fileInfo.setUrl(url);
            try
            {
                URIBuilder uriBuilder = new URIBuilder(fileInfo.getUrl());
                String path = uriBuilder.getPath();
                if(StringUtils.isNotEmpty(path)){
                    fileInfo.setPath(FilenameUtils.getPath(path));
                }
            }
            catch(URISyntaxException e)
            {
                throw new RuntimeException(e);
            }
            fileInfo.setFilename(FilenameUtils.getName(fileInfo.getUrl()));
        }
        return fileInfo;
    }

    public  FileInfo getFileInfoByUrl(String url){
        StorageDetail storageDetail = storageDetailMapper.selectByUrl(url);
        if(storageDetail != null){

            return toFileInfo(storageDetail);
        }
        return null;

    }

    @Override
    public boolean delete(String url) {
        return true;
    }

    @Override
    public void saveFilePart(FilePartInfo filePartInfo) {}

    @Override
    public void deleteFilePartByUploadId(String uploadId) {}


    /**
     * 将 FileInfo 转为 FileDetail
     */
    private StorageDetail toStorageDetail(FileInfo info)
    {
        StorageDetail detail = BeanUtil.copyProperties(
                info, StorageDetail.class, "metadata", "userMetadata", "thMetadata", "thUserMetadata", "attr", "hashInfo");

        // 这里手动获 元数据 并转成 json 字符串，方便存储在数据库中
        detail.setMetadata(valueToJson(info.getMetadata()));
        detail.setUserMetadata(valueToJson(info.getUserMetadata()));
        detail.setThMetadata(valueToJson(info.getThMetadata()));
        detail.setThUserMetadata(valueToJson(info.getThUserMetadata()));
        // 这里手动获 取附加属性字典 并转成 json 字符串，方便存储在数据库中
        detail.setAttr(valueToJson(info.getAttr()));
        // 这里手动获 哈希信息 并转成 json 字符串，方便存储在数据库中
        detail.setHashInfo(valueToJson(info.getHashInfo()));
        return detail;
    }

    /**
     * 将 FileDetail 转为 FileInfo
     */
    public FileInfo toFileInfo(StorageDetail detail) {
        FileInfo info = BeanUtil.copyProperties(
                detail, FileInfo.class, "metadata", "userMetadata", "thMetadata", "thUserMetadata", "attr", "hashInfo");

        // 这里手动获取数据库中的 json 字符串 并转成 元数据，方便使用
        info.setMetadata(jsonToMetadata(detail.getMetadata()));
        info.setUserMetadata(jsonToMetadata(detail.getUserMetadata()));
        info.setThMetadata(jsonToMetadata(detail.getThMetadata()));
        info.setThUserMetadata(jsonToMetadata(detail.getThUserMetadata()));
        // 这里手动获取数据库中的 json 字符串 并转成 附加属性字典，方便使用
        info.setAttr(jsonToDict(detail.getAttr()));
        // 这里手动获取数据库中的 json 字符串 并转成 哈希信息，方便使用
        info.setHashInfo(jsonToHashInfo(detail.getHashInfo()));
        return info;
    }

    /**
     * 将指定值转换成 json 字符串
     */
    private String valueToJson(Object value) {
        if (value == null) return null;
        try
        {
            return objectMapper.writeValueAsString(value);
        }
        catch(JsonProcessingException e)
        {
            throw new RuntimeException(e);
        }
    }

    /**
     * 将 json 字符串转换成元数据对象
     */
    public Map<String, String> jsonToMetadata(String json){
        if (StrUtil.isBlank(json)) return null;
        try
        {
            return objectMapper.readValue(json, new TypeReference<Map<String, String>>() {});
        }
        catch(JsonProcessingException e)
        {
            throw new RuntimeException(e);
        }
    }

    /**
     * 将 json 字符串转换成字典对象
     */
    public Dict jsonToDict(String json){
        if (StrUtil.isBlank(json)) return null;
        try
        {
            return objectMapper.readValue(json, Dict.class);
        }
        catch(JsonProcessingException e)
        {
            throw new RuntimeException(e);
        }
    }

    /**
     * 将 json 字符串转换成哈希信息对象
     */
    public HashInfo jsonToHashInfo(String json){
        if (StrUtil.isBlank(json)) return null;
        try
        {
            return objectMapper.readValue(json, HashInfo.class);
        }
        catch(JsonProcessingException e)
        {
            throw new RuntimeException(e);
        }
    }
}
