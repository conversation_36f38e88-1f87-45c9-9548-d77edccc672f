package com.qc.agent.storage.mapper;

import com.qc.agent.storage.model.StorageDetail;

public interface StorageDetailMapper {
    int deleteByPrimaryKey(Long id);

    int insert(StorageDetail record);

    int insertSelective(StorageDetail record);

    StorageDetail selectByPrimaryKey(Long id);
    StorageDetail selectByUrl(String url);

    int updateByPrimaryKeySelective(StorageDetail record);

    int updateByPrimaryKey(StorageDetail record);
}
