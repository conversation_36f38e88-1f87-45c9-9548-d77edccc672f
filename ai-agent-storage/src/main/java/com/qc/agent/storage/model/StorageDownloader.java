package com.qc.agent.storage.model;

import org.dromara.x.file.storage.core.Downloader;
import org.dromara.x.file.storage.core.FileInfo;

import java.io.InputStream;
import java.util.function.Consumer;

/**
 *
 * <AUTHOR>
 */

public class StorageDownloader
{
    private Downloader downloader;

    private FileInfo fileInfo;

    public StorageDownloader(Downloader downloader, String url){
        this.downloader = downloader;
        fileInfo = new FileInfo();
        fileInfo.setUrl(url);
    }

    public StorageDownloader(Downloader downloader, FileInfo fileInfo){
        this.downloader = downloader;
        this.fileInfo = fileInfo;
    }

    public void inputStream(Consumer<InputStream> consumer) {
        downloader.inputStream(consumer);
    }

    public Downloader getDownloader()
    {
        return downloader;
    }
    public void setDownloader(Downloader downloader)
    {
        this.downloader = downloader;
    }
    public FileInfo getFileInfo()
    {
        return fileInfo;
    }
    public void setFileInfo(FileInfo fileInfo)
    {
        this.fileInfo = fileInfo;
    }

}
