package com.qc.agent.storage;

import com.qc.agent.common.util.SpringApplicationUtils;
import com.qc.agent.storage.model.StorageDownloader;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.dromara.x.file.storage.core.FileInfo;
import org.dromara.x.file.storage.core.file.ByteFileWrapper;
import org.dromara.x.file.storage.core.upload.UploadPretreatment;

import java.io.File;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.function.BiConsumer;
import java.util.function.Consumer;

/**
 *
 * <AUTHOR>
 */

public class StorageClient
{
    private StorageActuator actuator;
    private UploadPretreatment uploadPretreatment;
    private StorageDownloader storageDownloader;

    public static StorageClient of(){
        StorageClient storageClient = new StorageClient();
        storageClient.actuator = SpringApplicationUtils.getBean(StorageActuator.class);
        return storageClient;
    }

    public StorageClient buildUploader(){
        this.uploadPretreatment = actuator.of();
        return this;
    }

    public <T> StorageClient buildUploader(T source){
        uploadPretreatment = actuator.of(source);
        return this;
    }


    public StorageClient buildUploader(byte[] inputStream,String name, String contentType,long size) {
        this.uploadPretreatment = actuator.of();
        uploadPretreatment.setFileWrapper(new ByteFileWrapper(inputStream,name,contentType,size));
        return this;
    }

    public StorageClient switchPlatform(String platform){
        uploadPretreatment.setPlatform(platform);
        return this;
    }

    public StorageClient addProgressListener(BiConsumer<Long,Long> progressListener){
        uploadPretreatment.setProgressListener(progressListener);
        return this;
    }



    public StorageClient setSaveFilename(String saveFilename){
        uploadPretreatment.setSaveFilename(saveFilename);
        return this;
    }

    public StorageClient setPath(String path){
        uploadPretreatment.setPath(path);
        return this;
    }

    public FileInfo getFileInfo(String filePath){
        return actuator.getFileInfoByUrl(filePath);
    }

    public FileInfo upload(){
        return uploadPretreatment.upload();
    }
    public FileInfo uploadLocal(){
        uploadPretreatment.setPlatform(actuator.getLocalPlus());
        return upload();
    }

    public StorageClient buildDownloader(String url){
        storageDownloader = new StorageDownloader(actuator.download(url),url);
        return this;
    }


    public StorageClient buildDownloader(FileInfo fileInfo){
        storageDownloader = new StorageDownloader(actuator.download(fileInfo),fileInfo);
        return this;
    }


    /**
     * 下载到本地
     * @param dir 本地目录
     */
    public String downloadStoragePath(String dir){
        String url = storageDownloader.getFileInfo().getUrl();
        if(StringUtils.isEmpty(url)){
            throw  new IllegalArgumentException("url is null");
        }
        return downloadStoragePath(dir, FilenameUtils.getName(url));
    }

    /**
     * 下载到全地
     * @param dir 目录
     * @param filename 文件名
     * @return
     */
    public String downloadStoragePath(String dir,String filename){
        String storagePath = actuator.getLocalStoragePath();
        if(!storagePath.endsWith(File.separator) && !storagePath.endsWith("/")){
            storagePath += File.separator;
        }
        if(dir == null){
            dir = StringUtils.EMPTY;
        }

        if(StringUtils.isNotEmpty(dir)){
            if(!dir.endsWith(File.separator) && !dir.endsWith("/")){
                dir += File.separator;
            }
            if(StringUtils.isNotEmpty(storagePath) && (dir.startsWith(File.separator) || dir.startsWith("/"))){
                dir = dir.substring(1);
            }
        }
        String url = storagePath + dir + filename;
        storageDownloader.getDownloader().file(url);
        return url;
    }

    public void downloadFile(String localPath){
        storageDownloader.getDownloader().file(localPath);
    }
    public void downloadFile(File file){
        storageDownloader.getDownloader().file(file);
    }
    public void downloadFile(OutputStream out){
        storageDownloader.getDownloader().outputStream(out);
    }

    public void inputStream(Consumer<InputStream> consumer) {
        storageDownloader.inputStream(consumer);
    }


}
