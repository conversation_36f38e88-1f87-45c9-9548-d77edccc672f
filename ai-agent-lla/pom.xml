<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.qc.agent</groupId>
		<artifactId>qc-ai-agent</artifactId>
		<version>1.0.0</version>
	</parent>
	<artifactId>ai-agent-lla</artifactId>
	<version>1.0.0</version>
	<name>ai-agent-lla</name>
	<description>ai-agent-lla</description>
	<url/>
	<licenses>
		<license/>
	</licenses>
	<developers>
		<developer/>
	</developers>
	<scm>
		<connection/>
		<developerConnection/>
		<tag/>
		<url/>
	</scm>
	<properties>
		<java.version>17</java.version>
	</properties>
	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>com.qc.agent</groupId>
			<artifactId>ai-agent-common</artifactId>
			<version>1.0.0</version>
		</dependency>

		<dependency>
			<groupId>com.volcengine</groupId>
			<artifactId>volcengine-java-sdk-ark-runtime</artifactId>
			<version>0.2.16</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/com.openai/openai-java -->
		<dependency>
			<groupId>com.openai</groupId>
			<artifactId>openai-java</artifactId>
			<version>2.7.0</version>
		</dependency>
	</dependencies>

<!--	<build>-->
<!--		<plugins>-->
<!--			<plugin>-->
<!--				<groupId>org.springframework.boot</groupId>-->
<!--				<artifactId>spring-boot-maven-plugin</artifactId>-->
<!--			</plugin>-->
<!--		</plugins>-->
<!--	</build>-->

</project>
