package com.qc.agent.lla.adapter;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.qc.agent.lla.ILLAConverter;
import com.qc.agent.lla.exception.LLAInvokerRequestException;
import com.qc.agent.lla.model.LLAResponse;
import com.qc.agent.lla.model.LLAUsage;
import org.apache.commons.lang3.StringUtils;

/**
 *
 *
 * <AUTHOR>
 * @date 2025/3/6 14:24:35
 *
 */
public class CommonLLAConverter implements ILLAConverter
{
    @Override
    public LLAResponse convert(Object t)
    {
        String content = (String) t;
        if(!org.springframework.util.StringUtils.hasText(content)){
            return LLAResponse.builder().content(content).stop(false).build();
        }
        if("data: [DONE]".equals(content)){
            return LLAResponse.builder().content(StringUtils.EMPTY).stop(Boolean.TRUE).build();
        }else{
            if (content.startsWith("data: {")) {
                return parseResponse(content.replaceFirst("data: ", ""));
            }else{
                return parseResponse(content);
            }
        }

    }

    private LLAResponse parseResponse(String response) {
        JSONObject result =  JSONObject.parseObject(response);
        if(result.containsKey("error")){
            throw new LLAInvokerRequestException(result.getString("error"));
        }
        JSONArray choices = result.getJSONArray("choices");
        StringBuilder sb = new StringBuilder();
        JSONObject usage = result.getJSONObject("usage");
        for(int i = 0,size = choices.size(); i < size; i++)
        {
            JSONObject choice = choices.getJSONObject(i);
            JSONObject delta = choice.getJSONObject("delta");
            if(delta != null){
                if(delta.containsKey("content")){
                    sb.append(delta.getString("content") == null ? "" : delta.getString("content"));
                }
            }else{
                JSONObject message = choice.getJSONObject("message");
                if(message != null){
                    sb.append(message.getString("content"));
                }
            }
            if(usage == null){
                usage = choice.getJSONObject("usage");
            }
        }
        return LLAResponse.builder()
                .content(sb.toString())
                .stop(usage != null)
                .usage(usage == null ? null : usage.toJavaObject(LLAUsage.class))
                .created(result.getInteger("created"))
                .build();

    }
}
