package com.qc.agent.lla.sdk.model_supplier.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.qc.agent.lla.model.LLAProvider;
import com.qc.agent.lla.model.LLAResponse;
import com.qc.agent.lla.sdk.model.ModelMessage;
import com.qc.agent.lla.sdk.model.ModelParam;
import com.qc.agent.lla.sdk.model.context_cache.ContextCacheParam;
import com.qc.agent.lla.sdk.model.context_cache.ContextCacheResult;
import com.qc.agent.lla.sdk.model_supplier.AiClient;
import com.qc.agent.lla.util.OkHttpUtils;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-06-17
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Slf4j
//@Component("doubaoOpenAIClient")
public class DoubaoOpenAIClient implements AiClient {

    private static final ObjectMapper MAPPER = new ObjectMapper();

    @Override
    public LLAProvider getType() {
//        return LLAProvider.DOUBAO;
        return null;
    }


    @Override
    public LLAResponse chat(ModelParam param, ModelMessage modelMessage) {
        return null;
    }

    @Override
    public void chatStream(ModelParam param, ModelMessage modelMessage) {

    }

    @Override
    public ContextCacheResult createContext(ModelParam param) throws JsonProcessingException {
        String logPrefix = param.getLogPrefix();
        ContextCacheParam cacheParam = param.getCacheParam();
        if (cacheParam == null) {
            log.error("{} cacheParam is null", logPrefix);
            return null;
        }
        OkHttpClient client = OkHttpUtils.getClient();

        Map<String, Object> requestBody = new HashMap<>();
//        requestBody.put("model", param.getModel());
        requestBody.put("model", "ep-20250616211321-nwml4");

        requestBody.put("input", cacheParam.getMessages());

//        Map<String, Object> extraBody = new HashMap<>();
        requestBody.put("caching", Map.of("type", "enabled"));
        requestBody.put("thinking", Map.of("type", "disabled"));

//        requestBody.put("extra_body", extraBody);

        Request request = new Request.Builder()
                .url(param.getCacheUrl()+ "/responses")
                .addHeader("Authorization", "Bearer " + param.getApiKey())
                .post(RequestBody.create(
                        MAPPER.writeValueAsString(requestBody),
                        MediaType.parse("application/json")
                ))
                .build();

        try (Response response = client.newCall(request).execute()) {
            String body = response.body().string();
            System.out.println("Initial Response:\n" + body);
            Map<?, ?> result = MAPPER.readValue(body, Map.class);
            return null; // 获取缓存 response ID
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
