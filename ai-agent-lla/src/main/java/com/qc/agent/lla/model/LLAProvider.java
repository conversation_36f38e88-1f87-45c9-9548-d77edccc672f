package com.qc.agent.lla.model;

import lombok.Getter;

/**
 *
 *
 * <AUTHOR>
 * @date 2025/3/4 20:13:11
 *
 */
public enum LLAProvider
{
    /**
     * Tencent
     */
    TENCENT("Tencent","kimiFileContextCachingService"),
    /**
     * Baidu
     */
    BAIDU("Baidu","kimiFileContextCachingService"),
    /**
     * Ali
     */
    ALI("Ali","kimiFileContextCachingService"),
    /**
     * Zhipu
     */
    ZHIPU("Zhipu","kimiFileContextCachingService"),
    /**
     * Coze
     */
    COZE("Coze","kimiFileContextCachingService"),
    /**
     * Doubao
     */
    DOUBAO("Doubao","doubaoFileContextCachingServiceImpl"),
    /**
     * Openai
     */
    OPENAI("Openai","kimiFileContextCachingService"),
    /**
     * Azure Openai
     */
    AZURE_OPENAI("Azure_OpenAI","kimiFileContextCachingService"),

    /**
     * Kimi
     */
    KIMI("Kimi","kimiFileContextCachingService"),
    /**
     * kimi的联网搜索
     */
    KIMI_SEARCH("Kimi_search",""),
    /**
     * Deepseek
     */
    DEEPSEEK("Deepseek","kimiFileContextCachingService");

    private String lla;

    @Getter
    private String fileParseServiceImplName;

    LLAProvider(String lla,String fileParseServiceImplName)
    {
        this.lla = lla;
        this.fileParseServiceImplName = fileParseServiceImplName;
    }

    public String getLLA()
    {
        return lla;
    }

    public void setLLA(String lla)
    {
        this.lla = lla;
    }

    /**
     * 通过枚举值获取枚举对象
     */
    public static LLAProvider getEnumByValue(String value){
        for(LLAProvider llaEnum : LLAProvider.values()){
            if(llaEnum.getLLA().equals(value)){
                return llaEnum;
            }
        }
        return null;
    }
}
