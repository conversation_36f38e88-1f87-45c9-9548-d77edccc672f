package com.qc.agent.lla.adapter.impl.kimi;

import cn.hutool.http.Header;
import cn.hutool.http.Method;
import com.qc.agent.lla.adapter.impl.CommonLLAAdapter;
import com.qc.agent.lla.model.*;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/3/6 15:00:57
 */
@Slf4j
@Component
public class KimiAdapter extends CommonLLAAdapter {
    private static final String PROVIDER_DOMAIN = "https://api.moonshot.cn";
    private static final String FILES_URL = "https://api.moonshot.cn/v1/files";

    @Value("${ai.agent.kimi-apikey}")
    private String kimiApiKey;

    @Override
    public LLAProvider determineLLA() {
        return LLAProvider.KIMI;
    }

    @Override
    protected String getProviderDomain() {
        return PROVIDER_DOMAIN;
    }

    @Override
    protected String getApiKey() {
        return kimiApiKey;
    }

    @Override
    public String parseUploadFile(@NonNull File file) {
        return getCommonRequest(FILES_URL, getApiKey())
                .method(Method.POST)
                .header("purpose", "file-extract")
                .form("file", file)
                .execute()
                .body();
    }

    @Override
    public String getFileContent(@NonNull String fileId) {
        return getCommonRequest(FILES_URL + "/" + fileId + "/content", getApiKey())
                .execute()
                .body();
    }

    private static cn.hutool.http.HttpRequest getCommonRequest(String url, String apiKey) {
        return cn.hutool.http.HttpRequest.of(url).header(Header.AUTHORIZATION, "Bearer " + apiKey);
    }

    /**
     * 构建请求
     *
     * @param config  模型配置
     * @param request 请求参数
     * @return 请求参数
     */
    protected LLARequestParam buildRequestBody(LLAConfig config, LLARequest request) {
        LLARequestParam param = LLARequestParam.builder()
                .model(config.getModel())
                .stream(config.getStream())
                .messages(LLAMessageBuilder.of()
                        .addSystemMessage(request.getCacheInfoList())
                        .addSystemMessage(request.getSystem())
                        .addHistoryMessage(request.getQas())
                        .addUserMessage(request.getContent()).build())
                .temperature(config.getModelTemperature() == null ? 0.8 : config.getModelTemperature().doubleValue())
                .top_p(config.getModelTopP() == null ? 0.5 : config.getModelTopP().doubleValue())
                .max_tokens(config.getModelMaxTokens() == null ? 1000 : config.getModelMaxTokens().intValue())
                .build();
        if (config.isEnableSearch()) {
            param.setTools(getTools());
        }
        return param;
    }

    /**
     * 开启联网搜索功能
     *
     * @return
     */
    private List<Map<String, Object>> getTools() {
        List<Map<String, Object>> tools = new ArrayList<>();
        Map<String, Object> tool = new HashMap<>();
        tool.put("type", "builtin_function");
        Map<String, Object> function = new HashMap<>();
        function.put("name", "$web_search");
        tool.put("function", function);
        tools.add(tool);
        return tools;
    }
}
