package com.qc.agent.lla.adapter.impl.tencent;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.qc.agent.lla.ILLAConverter;
import com.qc.agent.lla.exception.LLAInvokerRequestException;
import com.qc.agent.lla.model.LLAResponse;
import com.qc.agent.lla.model.LLAUsage;
import org.springframework.util.StringUtils;

/**
 *
 *
 * <AUTHOR>
 * @date 2025/3/6 17:17:23
 *
 */
public class TencentLLAConverter implements ILLAConverter
{
    @Override
    public LLAResponse convert(Object message)
    {
        String data = (String) message;

        if(!StringUtils.hasText(data)){
            return LLAResponse.builder().content(data).build();
        }
        String[] datas = data.split("\n\n");
        StringBuilder sb = new StringBuilder();
        boolean stop = false;
        LLAUsage usage = null;
        Integer created = null;
        for (String s : datas) {
            if (s.startsWith("data: ")) {
                JSONObject responseData = JSON.parseObject(s.replaceFirst("data: ", ""));
                JSONObject usageJson = responseData.getJSONObject("Usage");
                if(usageJson != null){
                    usage = new LLAUsage();
                    usage.setCompletionTokens(usageJson.getIntValue("CompletionTokens"));
                    usage.setPromptTokens(usageJson.getIntValue("PromptTokens"));
                    usage.setTotalTokens(usageJson.getIntValue("TotalTokens"));
                }
                created = responseData.getIntValue("Created");
                JSONObject choice = responseData.getJSONArray("Choices").getJSONObject(0);
                JSONObject delta = choice.getJSONObject("Delta");
                sb.append(delta.getString("Content"));
                stop = "stop".equals(choice.getString("FinishReason"));
                if(stop){
                    break;
                }

            }else{
                throw new LLAInvokerRequestException(s);
            }
        }
        return LLAResponse.builder().content(sb.toString()).usage(usage).created(created).stop(stop).build();
    }
}
