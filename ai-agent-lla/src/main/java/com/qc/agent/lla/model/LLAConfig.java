package com.qc.agent.lla.model;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/3/4 19:14:58
 */
@Data
@Builder
public class LLAConfig {
    private LLAProvider provider;
    private String model;
    private String apiKey;
    private String secretKey;
    private Boolean stream;

    /**
     * 采样温度
     */
    private BigDecimal modelTemperature;

    /**
     * 核采样的概率阈值
     */
    private BigDecimal modelTopP;

    /**
     * 请求返回的最大 Token 数
     */
    private BigDecimal modelMaxTokens;

    private LLAThinkingOptions thinkingOptions;

    /**
     * 是否启用联网搜索，目前仅通义、Kimi支持 启用：true
     */
    private boolean enableSearch;

    // --- Start: 新增的 Azure OpenAI 特定字段 ---
    /**
     * API的端点地址 (例如: "https://<your-resource>.openai.azure.com/")
     * 对于您的场景，应该是 "https://rgsikadev-apim.azure-api.cn/api/aoai/openai"
     */
    private String endpoint;

    /**
     * 自定义 Header: aip-application-id
     */
    private String aipApplicationId;

    /**
     * 自定义 Header: aip-user-id
     */
    private String aipUserId;

}
