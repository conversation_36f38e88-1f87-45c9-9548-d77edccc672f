package com.qc.agent.lla.model;

import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/3/5 10:16:23
 */
@Data
@Builder
public class LLARequestParam {
    private String model;
    private boolean stream;
    @Builder.Default
    private double temperature = 0.8;

    private double top_p;

    private int max_tokens;

    private String context_id;

    /**
     * 豆包输出usage参数
     */
    private LLAUsageOptions stream_options;

    /**
     * 豆包思考参数
     */
    private LLAThinkingOptions thinking;

    private List<LLAMessage> messages;

    /**
     * tool_calls
     */
    private List<Map<String, Object>> tools;

    private Map<String, Object> extra_body;
}
