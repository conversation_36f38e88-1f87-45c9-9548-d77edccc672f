package com.qc.agent.lla.adapter;

import com.qc.agent.lla.ILLAConverter;
import com.qc.agent.lla.ILLASubscriber;
import com.qc.agent.lla.LLASubscriber;
import com.qc.agent.lla.exception.LLAInvokerException;
import com.qc.agent.lla.exception.LLAInvokerRequestException;
import com.qc.agent.lla.model.*;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;

/**
 * 抽象的大模型适配器
 *
 * <AUTHOR>
 * @date 2025/3/4 20:06:55
 */
@Slf4j
public abstract class AbstrctLLAAdapter implements LLAAdapter {

    /**
     * 发送异步请求
     *
     * @param config     模型配置
     * @param request    请求参数
     * @param subscriber 订阅者
     */
    @Override
    public void sendAsync(LLAConfig config, LLARequest request, ILLASubscriber subscriber) {
        log.info("send async request system:{} user:{} to {} LLA use model:{}", request.getSystem(), request.getContent(), config.getProvider(), config.getModel());
        try {
            HttpClient.newBuilder().build().sendAsync(buildRequest(config, request),
                    HttpResponse.BodyHandlers.fromLineSubscriber(
                    LLASubscriber.of(subscriber).converter(buildConverter()))).get();
        } catch (Exception e) {
            log.error(String.format("send async request %s to %s LLA use model:%s error", request.getContent(), config.getProvider(), config.getModel()), e);
            throw new LLAInvokerException(e.getMessage(), e);
        }
    }

    /**
     * 发送同步请求
     *
     * @param config  模型配置
     * @param request 请求参数
     * @return 响应结果
     */
    @Override
    public LLAResponse send(LLAConfig config, LLARequest request) {
        log.info("send request system:{} user: {} to {} LLA use model:{}", request.getSystem(), request.getContent(), config.getProvider(), config.getModel());
        try {
            HttpResponse<String> response = HttpClient.newBuilder().build().send(buildRequest(config, request), HttpResponse.BodyHandlers.ofString());
            LLAResponseResult rr = parseResponse(response);
            if (rr.isSuccess()) {
                return buildConverter().convert(rr.getContent());
            } else {
                log.error("request {} to {} LLA use model:{} error, statusCode:{}, response:{}", request.getContent(), config.getProvider(), config.getModel(), response.statusCode(), response.body());
                throw new LLAInvokerRequestException(rr.getContent());
            }
        } catch (Exception e) {
            log.error(String.format("request %s to %s LLA use model:%s error", request.getContent(), config.getProvider(), config.getModel()), e);
            throw new LLAInvokerException(e.getMessage(), e);
        }
    }

    public String parseUploadFile(@NonNull File file) {
        return "";
    }


    public String getFileContent(@NonNull String fileId) {
        return "";
    }

    /**
     * @param config  模型配置
     * @param request 请求参数
     * @return 请求对象
     */
    protected abstract HttpRequest buildRequest(LLAConfig config, LLARequest request);

    /**
     * 构建转换器
     *
     * @return 转换器
     */
    protected abstract ILLAConverter buildConverter();


    /**
     * 构建请求
     *
     * @param config  模型配置
     * @param request 请求参数
     * @return 请求参数
     */
    protected LLARequestParam buildRequestBody(LLAConfig config, LLARequest request) {
        return LLARequestParam.builder()
                .model(config.getModel())
                .stream(config.getStream())
                .messages(LLAMessageBuilder.of()
                        .addSystemMessage(request.getCacheInfoList())
                        .addSystemMessage(request.getSystem())
                        .addHistoryMessage(request.getQas())
                        .addUserMessage(request.getContent()).build())
                .temperature(config.getModelTemperature() == null ? 0.8 : config.getModelTemperature().doubleValue())
                .top_p(config.getModelTopP() == null ? 0.5 : config.getModelTopP().doubleValue())
                .max_tokens(config.getModelMaxTokens() == null ? 1000 : config.getModelMaxTokens().intValue())
                .build();
    }

    protected abstract LLAResponseResult parseResponse(HttpResponse<String> response);
}

