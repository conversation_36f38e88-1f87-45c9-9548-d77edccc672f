package com.qc.agent.lla.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Represents a standardized response from a Large Language Agent (LLA) provider.
 * It can hold either a successful response with content and usage data, or an error state.
 *
 * <AUTHOR>
 * @date 2025/3/4 19:16:10
 */
@Data
@Builder
@NoArgsConstructor      // Recommended for frameworks like Jackson
@AllArgsConstructor     // Recommended for creating instances manually
public class LLAResponse {

    /**
     * Indicates if the response generation is complete.
     * True for a final response or an error, false for intermediate streaming chunks.
     */
    @Builder.Default
    private Boolean stop = false;

    /**
     * Unix timestamp (seconds) when the response was created by the provider.
     */
    private Integer created;

    /**
     * The main text content of the response.
     * In case of an error, this field will contain the error message.
     */
    private String content;

    /**
     * Token usage statistics for the API call.
     * This will likely be null in an error response.
     */
    private LLAUsage usage;

    /**
     * The specific model that generated the response (e.g., "doubao-pro-32k").
     * Added to track which model was used for the response.
     */
    private String model;

    /**
     * The provider that served the model (e.g., DOUBAO, QWEN).
     * Added to identify the source of the response.
     */
    private LLAProvider provider;

    /**
     * A flag to explicitly indicate if this response represents an error.
     * This is clearer than inferring an error from a null `usage` field.
     */
    @Builder.Default
    private Boolean isError = false;


    /**
     * Static factory method to create an LLAResponse object that represents an error.
     * This centralizes error response creation, ensuring consistency.
     *
     * @param errorMessage The descriptive error message.
     * @return A new LLAResponse instance configured to represent an error.
     */
    public static LLAResponse buildError(String errorMessage) {
        return LLAResponse.builder()
                .content(errorMessage) // The error message becomes the content.
                .isError(true)         // Explicitly mark this as an error response.
                .stop(true)            // An error is a terminal state, so stop is true.
                // All other fields (usage, created, model, provider) will be null by default,
                // which is appropriate for an error that likely occurred before a full response was generated.
                .build();
    }
}