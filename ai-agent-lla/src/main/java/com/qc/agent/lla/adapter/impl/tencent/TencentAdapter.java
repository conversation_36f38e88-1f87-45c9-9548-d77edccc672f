package com.qc.agent.lla.adapter.impl.tencent;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.qc.agent.lla.ILLAConverter;
import com.qc.agent.lla.adapter.AbstrctLLAAdapter;
import com.qc.agent.lla.exception.LLAInvokerException;
import com.qc.agent.lla.model.LLAConfig;
import com.qc.agent.lla.model.LLAProvider;
import com.qc.agent.lla.model.LLARequest;
import com.qc.agent.lla.model.LLAResponseResult;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.URI;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.sql.Date;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.TimeZone;

/**
 *
 *
 * <AUTHOR>
 * @date 2025/3/6 16:08:32
 *
 */
@Slf4j
@Component
public class TencentAdapter extends AbstrctLLAAdapter
{
    @Override
    protected HttpRequest buildRequest(LLAConfig config, LLARequest request)
    {
        JSONObject requestParam = new JSONObject();
        JSONArray messages = new JSONArray();
        JSONObject message = new JSONObject();
        message.put("Role", "user");
        message.put("Content", request.getContent());
        messages.add(message);
        requestParam.put("Messages",messages);

        List<String> headerList = new ArrayList<>();
        buildHeaders(config,requestParam).iterator().forEachRemaining(header ->{
            headerList.add(header.getFirst());
            headerList.add(header.getSecond());
        });
        return HttpRequest.newBuilder()
                .headers(headerList.toArray(new String[0]))
                .uri(URI.create("https://hunyuan.tencentcloudapi.com/"))
                .POST(HttpRequest.BodyPublishers.ofByteArray(requestParam.toJSONString().getBytes(StandardCharsets.UTF_8)))
                .build();
    }

    @Override
    protected ILLAConverter buildConverter()
    {
        return new TencentLLAConverter();
    }

    @Override
    protected LLAResponseResult parseResponse(HttpResponse<String> response)
    {
        if(response.body().startsWith("data: ")){
            return LLAResponseResult.builder().success(true).content(response.body()).build();
        }else{
            JSONObject result = JSONObject.parseObject(response.body());
            return LLAResponseResult.builder().success(false).content(result.getJSONObject("Response").toJSONString()).build();

        }

    }
    @Override
    public LLAProvider determineLLA()
    {
        return LLAProvider.TENCENT;
    }


    public Headers buildHeaders(LLAConfig config, JSONObject requestParam){
        String httpRequestMethod = "POST";
        byte[]  requestPayload = requestParam.toJSONString().getBytes(StandardCharsets.UTF_8);
        String  contentType = "application/json; charset=utf-8";
        String endpoint ="hunyuan.tencentcloudapi.com";
        String canonicalUri = "/";
        String canonicalQueryString = "";
        String canonicalHeaders = "content-type:" + contentType + "\nhost:" + endpoint + "\n";
        String signedHeaders = "content-type;host";

        String hashedRequestPayload = sha256Hex(requestPayload);

        String canonicalRequest =
                httpRequestMethod
                        + "\n"
                        + canonicalUri
                        + "\n"
                        + canonicalQueryString
                        + "\n"
                        + canonicalHeaders
                        + "\n"
                        + signedHeaders
                        + "\n"
                        + hashedRequestPayload;

        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
        String date = sdf.format(new Date(Long.parseLong(timestamp + "000")));
        String service = endpoint.split("\\.")[0];
        String credentialScope = date + "/" + service + "/" + "tc3_request";
        String hashedCanonicalRequest =
                sha256Hex(canonicalRequest.getBytes(StandardCharsets.UTF_8));
        String stringToSign =
                "TC3-HMAC-SHA256\n" + timestamp + "\n" + credentialScope + "\n" + hashedCanonicalRequest;

        String authorization = "";

        String secretId = StringUtils.isEmpty(config.getSecretKey()) ? "AKIDTlvQoxqUyC95mCLDo8BwQdfRxDCuMXR5" : config.getSecretKey();
        String secretKey = StringUtils.isEmpty(config.getApiKey()) ? "SfcwzlS3UgmkTxCAzZ8WfdKp66pGQL0P" : config.getApiKey();
        byte[] secretDate = hmac256(("TC3" + secretKey).getBytes(StandardCharsets.UTF_8), date);
        byte[] secretService = hmac256(secretDate, service);
        byte[] secretSigning = hmac256(secretService, "tc3_request");
        String signature =
                printHexBinary(hmac256(secretSigning, stringToSign)).toLowerCase();
        authorization =
                "TC3-HMAC-SHA256 "
                        + "Credential="
                        + secretId
                        + "/"
                        + credentialScope
                        + ", "
                        + "SignedHeaders="
                        + signedHeaders
                        + ", "
                        + "Signature="
                        + signature;


        Headers.Builder hb = new Headers.Builder();
        hb.add("Content-Type", contentType)
                .add("Authorization", authorization)
                .add("X-TC-Action", "ChatStd")
                .add("X-TC-Timestamp", timestamp)
                .add("X-TC-Version", "2023-09-01")
                .add("X-TC-RequestClient", "SDK_JAVA_3.1.956");


        hb.add("X-TC-Region", "ap-guangzhou");
        return hb.build();
    }

    public String sha256Hex(byte[] b) {
        MessageDigest md = null;
        try {
            md = MessageDigest.getInstance("SHA-256");
        } catch (NoSuchAlgorithmException e) {
            throw new LLAInvokerException(e);
        }
        byte[] d = md.digest(b);
        return printHexBinary(d).toLowerCase();
    }

    public byte[] hmac256(byte[] key, String msg) {
        Mac mac = null;
        try {
            mac = Mac.getInstance("HmacSHA256");
        } catch (NoSuchAlgorithmException e) {
            throw new LLAInvokerException(e);
        }
        SecretKeySpec secretKeySpec = new SecretKeySpec(key, mac.getAlgorithm());
        try {
            mac.init(secretKeySpec);
        } catch (InvalidKeyException e) {
            throw new LLAInvokerException(e);
        }
        return mac.doFinal(msg.getBytes(StandardCharsets.UTF_8));
    }

    public String printHexBinary(byte[] data) {
        char[] hexCode = "0123456789ABCDEF".toCharArray();
        StringBuilder r = new StringBuilder(data.length * 2);
        for (byte b : data) {
            r.append(hexCode[(b >> 4) & 0xF]);
            r.append(hexCode[(b & 0xF)]);
        }
        return r.toString();
    }


}
