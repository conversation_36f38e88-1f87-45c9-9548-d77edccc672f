package com.qc.agent.lla.util;

import com.qc.agent.common.util.ContextUtil;
import com.qc.agent.lla.ILLACompletionListener;

import java.util.List;

/**
 *
 *
 * <AUTHOR>
 * @date 2025/3/12 09:18:07
 *
 */
public final class LLAUtils
{

    /**
     * 获取实现ILLACompletionListener接口的所有监听
     * @return 监听列表
     */
    public static  List<ILLACompletionListener> getListeners() {
        return ContextUtil.getContext()
                .getBeansOfType(ILLACompletionListener.class)
                .values()
                .stream()
                .toList();
    }
}
