package com.qc.agent.lla.adapter.impl.zhipu;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.qc.agent.lla.adapter.impl.CommonLLAAdapter;
import com.qc.agent.lla.model.LLAProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.Date;
import java.util.Map;

/**
 *
 *
 * <AUTHOR>
 * @date 2025/3/6 16:00:07
 *
 */
@Slf4j
@Component
public class ZhipuAdapter extends CommonLLAAdapter
{
    static final String PROVIDER_DOMAIN = "https://open.bigmodel.cn";

    @Value("${ai.agent.zhipu-apikey}")
    String securityCredentialKeyApiKey;


    @Override
    protected String getProviderDomain()
    {
        return PROVIDER_DOMAIN;
    }

    @Override
    protected String getPath()
    {
        return "/api/paas/v4/chat/completions";
    }

    @Override
    protected String getApiKey()
    {
        return generateToken(securityCredentialKeyApiKey, 60);
    }

    @Override
    public LLAProvider determineLLA()
    {
        return LLAProvider.ZHIPU;
    }


    public static String generateToken(String apiKey, int expSeconds) {
        try {
            String[] parts = apiKey.split("\\.");
            if (parts.length != 2) {
                throw new IllegalArgumentException("Invalid apikey format");
            }
            String id = parts[0];
            String secret = parts[1];

            Instant now = Instant.now();
            Instant expirationTime = now.plusSeconds(expSeconds);

            return JWT.create()
                    .withClaim("api_key", id)
                    .withClaim("exp", Date.from(expirationTime).getTime())
                    .withClaim("timestamp", Date.from(now).getTime())
                    .withHeader(Map.of("alg", "HS256", "sign_type", "SIGN"))
                    .sign(Algorithm.HMAC256(secret));
        } catch (Exception e) {
            throw new RuntimeException("Error generating token: " + e.getMessage(), e);
        }
    }
}
