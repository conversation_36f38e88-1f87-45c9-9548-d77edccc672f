package com.qc.agent.lla.sdk.factory;

import com.qc.agent.lla.model.LLAProvider;
import com.qc.agent.lla.sdk.model_supplier.AiClient;
import com.qc.agent.lla.sdk.model_supplier.impl.DoubaoClient;
import com.qc.agent.lla.sdk.model_supplier.impl.KimiChatWithSearch;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-06-16
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
public class AiClientFactory {
    private static final Map<LLAProvider, AiClient> CLIENTS = new HashMap<>();

    static {
        register(new DoubaoClient());
        register(new KimiChatWithSearch());
    }

    public static void register(AiClient client) {
        CLIENTS.put(client.getType(), client);
    }

    public static AiClient getClient(LLAProvider type) {
        AiClient client = CLIENTS.get(type);
        if (client == null) {
            throw new IllegalArgumentException("Unsupported AI type: " + type);
        }
        return client;
    }
}
