package com.qc.agent.lla.sdk.model.context_cache;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.qc.agent.lla.model.LLAMessage;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-06-16
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Data
public class ContextCacheParam {
    @JsonProperty("model")
    private String model;

    @JsonProperty("mode")
    private String mode;

    @JsonProperty("messages")
    private List<LLAMessage> messages;

    @JsonProperty("ttl")
    private Integer ttl;

    public static ContextCacheParam build() {
        return new ContextCacheParam();
    }

    public ContextCacheParam withModel(String model) {
        this.model = model;
        return this;
    }

    public ContextCacheParam withMode(String mode) {
        this.mode = mode;
        return this;
    }

    public ContextCacheParam withMessages(List<LLAMessage> messages) {
        this.messages = messages;
        return this;
    }

    public ContextCacheParam withTtl(Integer ttl) {
        this.ttl = ttl;
        return this;
    }
}
