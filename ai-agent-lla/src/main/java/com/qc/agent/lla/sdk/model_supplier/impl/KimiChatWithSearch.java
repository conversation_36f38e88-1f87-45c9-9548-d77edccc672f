package com.qc.agent.lla.sdk.model_supplier.impl;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.qc.agent.lla.model.LLAProvider;
import com.qc.agent.lla.model.LLAResponse;
import com.qc.agent.lla.model.LLAUsage;
import com.qc.agent.lla.sdk.model.ModelMessage;
import com.qc.agent.lla.sdk.model.ModelParam;
import com.qc.agent.lla.sdk.model.context_cache.ContextCacheResult;
import com.qc.agent.lla.sdk.model_supplier.AiClient;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * An implementation of AiClient for Kimi (Moonshot AI) supporting function/tool
 * calling for web search. This version is optimized for Java 17+ and uses
 * standard POJOs with Lombok for DTOs to ensure serialization compatibility.
 * It uses Jackson ObjectMapper for JSON processing.
 *
 * <AUTHOR>
 * @date 2025-07-01
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Slf4j
@Component
public class KimiChatWithSearch implements AiClient {

    private static final String API_URL = "https://api.moonshot.cn/v1/chat/completions";
    private static final MediaType JSON_MEDIA_TYPE = MediaType.get("application/json; charset=utf-8");

    /**
     * A pre-configured, reusable, and thread-safe Jackson ObjectMapper instance.
     * It's configured to convert snake_case JSON keys to camelCase Java fields and pretty-print JSON.
     */
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper()
            .setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE)
            .enable(SerializationFeature.INDENT_OUTPUT)
            .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);

    private static final OkHttpClient OK_HTTP_CLIENT = new OkHttpClient.Builder()
            .connectTimeout(60, TimeUnit.SECONDS) // Increased timeout for potentially long tool calls
            .readTimeout(60, TimeUnit.SECONDS)
            .writeTimeout(60, TimeUnit.SECONDS)
            .retryOnConnectionFailure(true)
            .build();

    // --- Data Transfer Objects (DTOs) as standard POJOs with Lombok ---

    @Data @AllArgsConstructor @NoArgsConstructor @JsonIgnoreProperties(ignoreUnknown = true)
    static class Tool {
        private String type;
        private FunctionDefinition function;
    }

    @Data @AllArgsConstructor @NoArgsConstructor @JsonIgnoreProperties(ignoreUnknown = true)
    static class FunctionDefinition {
        private String name;
    }

    @Data @AllArgsConstructor @NoArgsConstructor @JsonIgnoreProperties(ignoreUnknown = true)
    static class ChatRequest {
        private String model;
        private List<ChatMessage> messages;
        private double temperature;
        private List<Tool> tools;
    }

    @Data @AllArgsConstructor @NoArgsConstructor @JsonIgnoreProperties(ignoreUnknown = true)
    static class ChatResponse {
        private List<Choice> choices;
        private Usage usage;
        private Integer created;
    }

    @Data @AllArgsConstructor @NoArgsConstructor @JsonIgnoreProperties(ignoreUnknown = true)
    static class Usage {
        private Integer promptTokens;
        private Integer completionTokens;
        private Integer totalTokens;
    }

    @Data @AllArgsConstructor @NoArgsConstructor @JsonIgnoreProperties(ignoreUnknown = true)
    static class Choice {
        private ChatMessage message;
        private String finishReason;
    }

    @Data @AllArgsConstructor @NoArgsConstructor @JsonIgnoreProperties(ignoreUnknown = true)
    static class ToolCall {
        private String id;
        private String type;
        private FunctionCall function;
    }

    @Data @AllArgsConstructor @NoArgsConstructor @JsonIgnoreProperties(ignoreUnknown = true)
    static class FunctionCall {
        private String name;
        private String arguments;
    }

    @Data @AllArgsConstructor @NoArgsConstructor @JsonIgnoreProperties(ignoreUnknown = true)
    static class ChatMessage {
        private String role;
        private String content;
        private List<ToolCall> toolCalls;
        private String toolCallId;
        private String name;

        public static ChatMessage user(String content) { return new ChatMessage("user", content, null, null, null); }
        public static ChatMessage system(String content) { return new ChatMessage("system", content, null, null, null); }
        public static ChatMessage assistant(List<ToolCall> toolCalls) { return new ChatMessage("assistant", null, toolCalls, null, null); }
        public static ChatMessage tool(String content, String toolCallId, String name) { return new ChatMessage("tool", content, null, toolCallId, name); }
    }

    /**
     * The implementation for the '$web_search' tool.
     * In a real-world scenario, this would perform an actual web search.
     */
    private Map<String, Object> performWebSearch(Map<String, Object> arguments, String logPrefix) {
        log.info("{} Executing '$web_search' tool with arguments: {}", logPrefix, arguments);
        // A real implementation would connect to a search API and return the results.
        return Map.of("status", "success", "query", arguments.getOrDefault("query", "N/A"));
    }

    private ChatResponse makeApiCall(String apiKey, String model, List<ChatMessage> messages, String logPrefix) {
        var tools = List.of(new Tool("builtin_function", new FunctionDefinition("$web_search")));
        var chatRequest = new ChatRequest(model, messages, 0.3, tools);

        String jsonBody;
        try {
            jsonBody = OBJECT_MAPPER.writeValueAsString(chatRequest);
        } catch (JsonProcessingException e) {
            log.error("{} FATAL: Failed to serialize Kimi request to JSON. Request: {}", logPrefix, chatRequest, e);
            return null;
        }

        log.debug("{} Requesting Kimi API. URL: {}\nBody:\n{}", logPrefix, API_URL, jsonBody);

        var requestBody = RequestBody.create(jsonBody, JSON_MEDIA_TYPE);
        var request = new Request.Builder()
                .url(API_URL)
                .post(requestBody)
                .header("Authorization", "Bearer " + apiKey)
                .header("Content-Type", "application/json")
                .build();

        try (var response = OK_HTTP_CLIENT.newCall(request).execute()) {
            String responseBodyString = Objects.requireNonNull(response.body(), "Response body was null").string();

            if (!response.isSuccessful()) {
                log.error("{} API Request to {} Failed: {} - {}\nResponse Body: {}",
                        logPrefix, API_URL, response.code(), response.message(), responseBodyString);
                return null;
            }

            log.debug("{} API Response Body:\n{}", logPrefix, responseBodyString);
            var chatResponse = OBJECT_MAPPER.readValue(responseBodyString, ChatResponse.class);

            if (chatResponse == null || chatResponse.getChoices() == null || chatResponse.getChoices().isEmpty()) {
                log.error("{} Failed to parse a valid response or choices were empty. Body: {}", logPrefix, responseBodyString);
                return null;
            }
            return chatResponse;

        } catch (JsonProcessingException e) {
            log.error("{} Failed to parse Kimi API JSON response", logPrefix, e);
            return null;
        } catch (IOException e) {
            log.error("{} Network or IO exception occurred during Kimi API call", logPrefix, e);
            return null;
        }
    }

    @Override
    public LLAProvider getType() {
        return LLAProvider.KIMI_SEARCH;
    }

    @Override
    public LLAResponse chat(ModelParam param, ModelMessage modelMessage) {
        String logPrefix = param.getLogPrefix();
        log.info("{} Starting Kimi chat with search capability for model '{}'...", logPrefix, param.getModel());

        final String apiKey = param.getApiKey();
        if (apiKey == null || apiKey.isBlank()) {
            log.error("{} FATAL: MOONSHOT_API_KEY is not configured or is empty.", logPrefix);
            throw new IllegalStateException("MOONSHOT_API_KEY is not configured.");
        }

        var messages = new ArrayList<ChatMessage>();
        messages.add(ChatMessage.system(modelMessage.getSystemMessage()));
        messages.add(ChatMessage.user(modelMessage.getUserMessage()));

        Choice lastChoice;
        LLAUsage totalUsage = new LLAUsage();
        totalUsage.setPromptTokens(0);
        totalUsage.setCompletionTokens(0);

        Integer created = 0;
        int turnCounter = 1;

        // This loop handles the multi-turn conversation required for tool calling.
        while (true) {
            log.info("{} Conversation turn {}: Sending chat request with {} messages...", logPrefix, turnCounter++, messages.size());
            var response = makeApiCall(apiKey, param.getModel(), messages, logPrefix);
            if (response == null) {
                log.error("{} API call failed. Aborting conversation.", logPrefix);
                return null;
            }
            lastChoice = response.getChoices().get(0);

            // Aggregate token usage across all turns
            totalUsage.setPromptTokens(totalUsage.getPromptTokens() + response.getUsage().getPromptTokens());
            totalUsage.setCompletionTokens(totalUsage.getCompletionTokens() + response.getUsage().getCompletionTokens());

            if (created == 0) { // Capture the timestamp from the first response
                created = response.getCreated();
            }

            var responseMessage = lastChoice.getMessage();
            String finishReason = lastChoice.getFinishReason();

            if (!"tool_calls".equals(finishReason)) {
                log.info("{} Final response received. Finish reason: {}", logPrefix, finishReason);
                break;
            }

            log.info("{} Received tool_calls request from the model.", logPrefix);
            messages.add(responseMessage);

            for (var toolCall : responseMessage.getToolCalls()) {
                String toolCallName = toolCall.getFunction().getName();
                log.info("{} Processing tool call '{}' with ID '{}'", logPrefix, toolCallName, toolCall.getId());

                Map<String, Object> arguments;
                try {
                    arguments = OBJECT_MAPPER.readValue(toolCall.getFunction().getArguments(), new TypeReference<>() {});
                } catch (JsonProcessingException e) {
                    log.error("{} Could not parse tool arguments JSON for tool call ID '{}': {}", logPrefix, toolCall.getId(), toolCall.getFunction().getArguments(), e);
                    messages.add(ChatMessage.tool("Error: Invalid JSON in arguments: " + e.getMessage(), toolCall.getId(), toolCallName));
                    continue;
                }

                Object toolResult;
                if ("$web_search".equals(toolCallName)) {
                    toolResult = performWebSearch(arguments, logPrefix);
                } else {
                    log.warn("{} Unknown tool name encountered: {}", logPrefix, toolCallName);
                    toolResult = Map.of("error", "Tool not found: " + toolCallName);
                }

                String toolResultJson;
                try {
                    toolResultJson = OBJECT_MAPPER.writeValueAsString(toolResult);
                } catch (JsonProcessingException e) {
                    log.error("{} Could not serialize tool result to JSON for tool call ID '{}'", logPrefix, toolCall.getId(), e);
                    toolResultJson = "{\"error\": \"Failed to serialize tool result to JSON.\"}";
                }
                messages.add(ChatMessage.tool(toolResultJson, toolCall.getId(), toolCallName));
            }
        }

        totalUsage.setTotalTokens(totalUsage.getPromptTokens() + totalUsage.getCompletionTokens());

        if (lastChoice.getMessage().getContent() != null) {
            log.info("{} Kimi chat finished successfully. Total tokens used: {}", logPrefix, totalUsage.getTotalTokens());
            return LLAResponse.builder()
                    .usage(totalUsage)
                    .stop(true)
                    .content(lastChoice.getMessage().getContent())
                    .created(created)
                    .build();
        } else {
            log.error("{} Kimi chat finished but could not retrieve a final answer from the model.", logPrefix);
            return null;
        }
    }

    @Override
    public void chatStream(ModelParam param, ModelMessage modelMessage) {
        log.warn("{} chatStream is not yet implemented for KimiChatWithSearch.", param.getLogPrefix());
    }

    @Override
    public ContextCacheResult createContext(ModelParam param) {
        log.warn("{} createContext is not yet implemented for KimiChatWithSearch.", param.getLogPrefix());
        return null;
    }
}