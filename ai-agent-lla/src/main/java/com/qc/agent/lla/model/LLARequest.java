package com.qc.agent.lla.model;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/4 19:16:21
 */
@Data
@Builder
public class LLARequest {
    String id;

    Long sessionId;

    String clientId;

    Long userId;

    String userName;

    Long deptId;

    String deptName;

    private String system;
    
    String content;
    /**
     * 知识库引用数量
     */
    private Integer quoteCount;

    /**
     * 拜访助手是否展示【需要】按钮 1:展示
     */
    private String isShowButton;

    LocalDateTime start;

    /**
     * kimi返回的缓存信息
     */
    private List<LLAMessage> cacheInfoList;

    /**
     * 豆包的缓存id
     */
    private String contextId;

    List<LLAQa> qas;

    /**
     * 是否跳过更新答案内容 true: 不更新答案 false: 更新答案
     */
    private boolean skipAnswerUpdate;
}
