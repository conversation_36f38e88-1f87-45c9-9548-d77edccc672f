package com.qc.agent.lla.adapter.impl.azure; 

import com.alibaba.fastjson.JSONObject;
import com.qc.agent.lla.ILLAConverter;
import com.qc.agent.lla.exception.LLAInvokerRequestException;
import com.qc.agent.lla.model.LLAResponse;
import com.qc.agent.lla.model.LLAUsage;

import lombok.extern.slf4j.Slf4j;

import org.springframework.util.StringUtils;

import java.util.Optional;

/**
 * Azure OpenAI 响应转换器。
 * 兼容流式 (String) 和非流式 (ChatCompletions) 两种输入。
 * 
 * <AUTHOR>
 * @since 1.0.0
 * @version 1.0.0
 */
@Slf4j
public class AzureOpenAILLAConverter implements ILLAConverter {

    private static final String DATA_PREFIX = "data: ";
    private static final String DONE_SIGNAL = "[DONE]";

    @Override
    public LLAResponse convert(Object input) {
        // 1. 验证输入
        if (!isValidInput(input)) {
            return createEmptyResponse();
        }

        String content = (String) input;
        
        // 2. 处理SSE格式
        String jsonContent = extractJsonContent(content);
        
        // 3. 检查结束信号
        if (isDoneSignal(jsonContent)) {
            return createStopResponse();
        }
        
        // 4. 检查空内容
        if (!StringUtils.hasText(jsonContent)) {
            return createEmptyResponse();
        }
        
        // 5. 解析JSON并处理错误
        JSONObject result = parseJsonContent(jsonContent);
        validateResponse(result);
        
        // 6. 提取响应内容
        String deltaContent = extractDeltaContent(result);
        LLAUsage usage = extractUsage(result);
        boolean isStopped = isResponseStopped(result);
        
        // 7. 构建响应
        return LLAResponse.builder()
                .content(deltaContent)
                .stop(isStopped)
                .usage(usage)
                .build();
    }

    /**
     * 验证输入是否有效
     */
    private boolean isValidInput(Object input) {
        return input instanceof String && StringUtils.hasText((String) input);
    }

    /**
     * 从SSE格式中提取JSON内容
     */
    private String extractJsonContent(String content) {
        return content.startsWith(DATA_PREFIX) 
                ? content.substring(DATA_PREFIX.length()).trim() 
                : content;
    }

    /**
     * 检查是否为结束信号
     */
    private boolean isDoneSignal(String content) {
        return DONE_SIGNAL.equalsIgnoreCase(content);
    }

    /**
     * 创建空响应
     */
    private LLAResponse createEmptyResponse() {
        return LLAResponse.builder()
                .content("")
                .stop(false)
                .build();
    }

    /**
     * 创建停止响应
     */
    private LLAResponse createStopResponse() {
        return LLAResponse.builder()
                .content("")
                .stop(true)
                .build();
    }

    /**
     * 解析JSON内容
     */
    private JSONObject parseJsonContent(String jsonContent) {
        return JSONObject.parseObject(jsonContent);
    }

    /**
     * 验证响应是否包含错误
     */
    private void validateResponse(JSONObject result) {
        if (result.containsKey("error")) {
            String errorMessage = result.getJSONObject("error").getString("message");
            throw new LLAInvokerRequestException(errorMessage);
        }
    }

    /**
     * 提取增量内容
     */
    private String extractDeltaContent(JSONObject result) {
        var choices = result.getJSONArray("choices");
        if (choices == null || choices.isEmpty()) {
            return "";
        }

        JSONObject firstChoice = choices.getJSONObject(0);
        
        // 尝试从delta中提取内容
        String content = extractContentFromObject(firstChoice, "delta");
        
        // 如果delta中没有内容，尝试从message中提取
        // 使用更现代的判断字符串是否为空的方法
        if (content == null || content.isBlank()) {
            content = extractContentFromObject(firstChoice, "message");
        }
        
        return content;
    }

    /**
     * 从指定对象中提取content字段
     */
    private String extractContentFromObject(JSONObject obj, String fieldName) {
        return Optional.ofNullable(obj.getJSONObject(fieldName))
                .map(field -> field.getString("content"))
                .orElse("");
    }

    /**
     * 提取使用情况信息
     */
    private LLAUsage extractUsage(JSONObject result) {
        return Optional.ofNullable(result.getJSONObject("usage"))
                .map(u -> u.toJavaObject(LLAUsage.class))
                .orElse(null);
    }

    /**
     * 检查响应是否已停止
     */
    private boolean isResponseStopped(JSONObject result) {
        var choices = result.getJSONArray("choices");
        if (choices == null || choices.isEmpty()) {
            return false;
        }
        
        return Optional.ofNullable(choices.getJSONObject(0))
                .map(choice -> choice.getString("finish_reason"))
                .isPresent();
    }
}