package com.qc.agent.lla.adapter;

import com.qc.agent.lla.ILLASubscriber;
import com.qc.agent.lla.model.LLAConfig;
import com.qc.agent.lla.model.LLAProvider;
import com.qc.agent.lla.model.LLARequest;
import com.qc.agent.lla.model.LLAResponse;
import lombok.NonNull;

import java.io.File;

/**
 * 大模型适配器
 *
 * <AUTHOR>
 * @date 2025/3/4 19:10:51
 */
public interface LLAAdapter {
    /**
     * 指定大模型
     *
     * @return 大模型
     */
    LLAProvider determineLLA();

    /**
     * 异步请求
     *
     * @param config     模型配置
     * @param request    请求参数
     * @param subscriber 订阅者
     */
    void sendAsync(LLAConfig config, LLARequest request, ILLASubscriber subscriber);

    /**
     * 同步请求
     *
     * @param config  模型配置
     * @param request 请求参数
     * @return 响应结果
     */
    LLAResponse send(LLAConfig config, LLARequest request);

    /**
     * 解析上传文件
     *
     * @param file
     * @return
     */
    String parseUploadFile(@NonNull File file);

    /**
     * 获取文件内容
     *
     * @param fileId
     */
    String getFileContent(@NonNull String fileId);

}
