package com.qc.agent.lla.model;

import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 *
 *
 * <AUTHOR>
 * @date 2025/3/5 09:30:51
 *
 */
public class LLAMessageBuilder
{
    List<LLAMessage> messages = new ArrayList<>(0);



    public static LLAMessageBuilder of(){
        return new LLAMessageBuilder();
    }

    public LLAMessageBuilder addSystemMessage(List<LLAMessage> cacheInfoList){
        if (CollectionUtils.isNotEmpty(cacheInfoList)) {
            messages.addAll(cacheInfoList);
        }
        return this;
    }

    public LLAMessageBuilder addSystemMessage(String content){
        if (content != null) {
            LLAMessage systemMessage = LLAMessage.builder()
                    .role("system")
                    .content(content)
                    .build();
            messages.add(systemMessage);
        }
        return this;
    }

    public LLAMessageBuilder addUserMessage(String content){
        LLAMessage userMessage = LLAMessage.builder()
                .role("user")
                .content(content)
                .build();
        messages.add(userMessage);
        return this;
    }

   public LLAMessageBuilder addHistoryMessage(List<LLAQa> qas){
        if(qas != null){
            for (LLAQa qa : qas) {
                LLAMessage userMessage = LLAMessage.builder()
                        .role("user")
                        .content(qa.getQuestion())
                        .build();
                messages.add(userMessage);
                LLAMessage assistantMessage = LLAMessage.builder()
                        .role("assistant")
                        .content(qa.getAnswer())
                        .build();
                messages.add(assistantMessage);
            }
        }
        return this;
    }

    public List<LLAMessage> build(){
        return messages;
    }
}
