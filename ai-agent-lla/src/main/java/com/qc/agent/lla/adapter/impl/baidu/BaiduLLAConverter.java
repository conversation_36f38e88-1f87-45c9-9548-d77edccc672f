package com.qc.agent.lla.adapter.impl.baidu;

import com.alibaba.fastjson.JSONObject;
import com.qc.agent.lla.ILLAConverter;
import com.qc.agent.lla.exception.LLAInvokerRequestException;
import com.qc.agent.lla.model.LLAResponse;
import org.springframework.util.StringUtils;

/**
 *
 *
 * <AUTHOR>
 * @date 2025/3/6 15:31:11
 *
 */
public class BaiduLLAConverter implements ILLAConverter
{
    @Override
    public LLAResponse convert(Object message)
    {
        String content = String.valueOf(message) ;
        if(!StringUtils.hasText(content)){
            return LLAResponse.builder().content(content).stop(false).build();
        }
        if (content.startsWith("data: {")) {
            JSONObject jsonObject =   JSONObject.parseObject(content.replaceFirst("data: ", ""));
            String result = jsonObject.getString("result");
            return LLAResponse.builder().content(result).stop(Boolean.parseBoolean(jsonObject.getString("is_end"))).build();
        }else{
            JSONObject result =  JSONObject.parseObject(content);
            if(result.containsKey("error_code")){
                throw new LLAInvokerRequestException(content);
            }else{
                return LLAResponse.builder().content(result.getString("result")).stop(Boolean.TRUE).build();
            }
        }

    }
}
