package com.qc.agent.lla.adapter.impl.ali;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.qc.agent.lla.ILLAConverter;
import com.qc.agent.lla.model.LLAResponse;
import com.qc.agent.lla.model.LLAUsage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.StringReader;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
// Removed Pattern and Matcher as direct JSON parsing is more robust

/**
 * Converts responses from Alibaba's Large Language Model (e.g., Qwen) to LLAResponse.
 * Supports both single "data:" prefixed messages (streaming/async-like) and
 * blocks of text containing multiple "data:" prefixed messages (sync-like).
 *
 * <AUTHOR>
 * @date 2025/3/6 15:54:38
 * @updatedBy AI Assistant
 * @updateDate <current date>
 */
@Slf4j
public class AliLLAConverter implements ILLAConverter {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    private static final String DATA_PREFIX = "data:";
    private static final String DONE_MARKER_WITH_PREFIX = "data: [DONE]";

    @Override
    public LLAResponse convert(Object t) {
        if (!(t instanceof String content) || !org.springframework.util.StringUtils.hasText(content)) {
            return LLAResponse.builder().content(String.valueOf(t)).stop(false).build();
        }

        String trimmedContent = content.trim();
        if (DONE_MARKER_WITH_PREFIX.equals(trimmedContent)) {
            return LLAResponse.builder().content(StringUtils.EMPTY).stop(false).build();
        }

        if (trimmedContent.startsWith(DATA_PREFIX)) {
            return parseSingleDataMessage(trimmedContent);
        } else {
            return parseSyncResponseBlock(trimmedContent);
        }
    }

    /**
     * Parses a single message that starts with "data:".
     * This is typical for Server-Sent Events (SSE) chunks.
     */
    private LLAResponse parseSingleDataMessage(String dataLine) {
//        log.debug("Parsing single data message: {}", dataLine);
        String jsonPayload = dataLine.substring(DATA_PREFIX.length()).trim();

        try {
            JsonNode rootNode = OBJECT_MAPPER.readTree(jsonPayload);
            JsonNode outputNode = rootNode.path("output"); // Use path to avoid NullPointerException if "output" is missing
            String textContent = outputNode.path("text").asText(StringUtils.EMPTY); // Default to empty if "text" is missing

            LLAUsage usage = parseUsage(rootNode.path("usage"));

            String finishReason = outputNode.path("finish_reason").asText();
            boolean stop = StringUtils.isNotEmpty(finishReason) && !Objects.equals(finishReason, "null");

            return LLAResponse.builder()
                    .content(textContent)
                    .usage(usage)
                    .stop(stop)
                    .build();
        } catch (JsonProcessingException e) {
            log.error("Failed to parse JSON from single data message. Payload: '{}', Error: {}", jsonPayload, e.getMessage(), e);
            return LLAResponse.builder().content(dataLine).stop(true).build(); // Or perhaps indicate error
        }
    }

    /**
     * Parses a "synchronous" response, which might be a block of text containing
     * multiple "data:" prefixed lines (like a full SSE stream concatenated)
     * or potentially a plain text response if no "data:" lines are found.
     * This method aggregates text from all "data:" lines. Usage is not aggregated from sync blocks
     * in this implementation, following the original logic's focus on text.
     */
    private LLAResponse parseSyncResponseBlock(String blockContent) {
        var fullText = new StringBuilder();
        boolean dataLineProcessed = false;
        List<JsonNode> usageNodes = new ArrayList<>();
        try (var reader = new BufferedReader(new StringReader(blockContent))) {
            String line;
            while ((line = reader.readLine()) != null) {
                String trimmedLine = line.trim();
                if (trimmedLine.startsWith(DATA_PREFIX)) {
                    if (DONE_MARKER_WITH_PREFIX.equals(trimmedLine)) {
                        dataLineProcessed = true;
                        break;
                    }

                    String jsonPayload = trimmedLine.substring(DATA_PREFIX.length()).trim();
                    if (jsonPayload.isEmpty() || "[DONE]".equals(jsonPayload)) {
//                        log.trace("Skipping empty or [DONE] payload in sync block line: '{}'", trimmedLine);
                        continue;
                    }

                    try {
                        JsonNode dataNode = OBJECT_MAPPER.readTree(jsonPayload);
                        // Ali/Qwen format typically has text under output.text
                        JsonNode outputNode = dataNode.path("output");
                        JsonNode usage = dataNode.path("usage");
                        usageNodes.add(usage);
                        String textChunk = outputNode.path("text").asText();

                        if (StringUtils.isNotEmpty(textChunk)) {
                            fullText.append(textChunk);
                            dataLineProcessed = true;
                        }

                    } catch (JsonProcessingException e) {
                        log.warn("Failed to parse JSON line in sync block. Line: '{}', Error: {}", trimmedLine, e.getMessage());
                    }
                } else if (org.springframework.util.StringUtils.hasText(trimmedLine)) {
                    log.trace("Skipping non-data line in sync block: '{}'", trimmedLine);
                }
            }
        } catch (IOException e) {
            log.error("IOException while reading sync block content (should not happen with StringReader). Error: {}", e.getMessage(), e);
            return LLAResponse.builder().content(blockContent).stop(true).build(); // Return original on unexpected IO error
        }

        String resultText = fullText.toString();
        if (dataLineProcessed || !org.springframework.util.StringUtils.hasText(blockContent)) {
            log.debug("Successfully parsed sync block. Total text length: {}", resultText.length());
        }
        LLAUsage usage = new LLAUsage();
        int completionTokens = 0;
        int promptTokens = 0;

        if (!usageNodes.isEmpty()) {
            completionTokens = usageNodes.get(0).path("input_tokens").asInt();
        }

        for (JsonNode usageNode : usageNodes) {
            promptTokens = promptTokens + usageNode.path("output_tokens").asInt();
        }
        usage.setCompletionTokens(completionTokens);
        usage.setPromptTokens(promptTokens);
        usage.setTotalTokens(completionTokens + promptTokens);
        return LLAResponse.builder()
                .content(resultText)
                .usage(usage)
                .stop(false)
                .build();
    }

    /**
     * Helper method to parse usage information from a JsonNode.
     */
    private LLAUsage parseUsage(JsonNode usageNode) {
        if (usageNode == null || usageNode.isMissingNode() || usageNode.isEmpty()) {
            return null;
        }
        // Use .asInt(0) or .asInt() depending on whether missing fields should default to 0 or throw error/return default by Jackson
        var usage = new LLAUsage(); // Assuming LLAUsage has setters or a builder
        usage.setCompletionTokens(usageNode.path("out_tokens").asInt(0)); // qwen uses out_tokens
        usage.setPromptTokens(usageNode.path("input_tokens").asInt(0));   // qwen uses input_tokens
        // qwen has total_tokens, but if LLAUsage calculates it, this might be redundant or a cross-check
        if (usageNode.has("total_tokens")) {
            usage.setTotalTokens(usageNode.path("total_tokens").asInt(0));
        } else {
            usage.setTotalTokens(usage.getCompletionTokens() + usage.getPromptTokens());
        }

        // Check if any tokens were actually parsed, otherwise, it's an empty usage object.
        if (usage.getCompletionTokens() == 0 && usage.getPromptTokens() == 0 && usage.getTotalTokens() == 0) {
            // This might happen if the "usage" object was present but all token fields were missing or zero.
            JsonNode parentNode = usageNode.findParent("output"); // Check if it's from an actual response part
            if (parentNode != null && parentNode.has("text")) { // If there's text, zero tokens might be valid for some models/cases
//                log.trace("Usage data indicates zero tokens, but text is present.");
            } else if (parentNode == null || parentNode.isEmpty()) { // If it's an empty usage object not associated with output
//                log.trace("Usage node found but contained no token values or was empty: {}", usageNode.toString());
                return null;
            }
        }
        return usage;
    }
}