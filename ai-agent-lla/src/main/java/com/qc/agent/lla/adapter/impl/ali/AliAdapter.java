package com.qc.agent.lla.adapter.impl.ali;

import com.alibaba.fastjson.JSONObject;
import com.qc.agent.lla.ILLAConverter;
import com.qc.agent.lla.adapter.AbstrctLLAAdapter;
import com.qc.agent.lla.model.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.net.HttpURLConnection;
import java.net.URI;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/3/6 15:43:36
 */
@Slf4j
@Component
public class AliAdapter extends AbstrctLLAAdapter {
    @Value("${ai.agent.ali-apikey}")
    String aliApiKey;

    @Override
    protected HttpRequest buildRequest(LLAConfig config, LLARequest request) {
        JSONObject requestParam = buildRequestParam(config, request);
        String apiKey = StringUtils.isEmpty(config.getApiKey()) ? aliApiKey : config.getApiKey();
        return HttpRequest.newBuilder()
                .header("Content-Type", "application/json")
                .header("Authorization", String.format("Bearer %s", apiKey))
                .header("Accept", "text/event-stream")
                .uri(URI.create("https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"))
                .POST(HttpRequest.BodyPublishers.ofString(requestParam.toJSONString()))
                .build();
    }

    @Override
    protected ILLAConverter buildConverter() {
        return new AliLLAConverter();
    }

    @Override
    protected LLAResponseResult parseResponse(HttpResponse<String> response) {
        if (HttpURLConnection.HTTP_OK == response.statusCode()) {
            return LLAResponseResult.builder().success(true).content(response.body()).build();
        } else {
            return LLAResponseResult.builder().success(false).content(response.body()).build();
        }
    }

    @Override
    public LLAProvider determineLLA() {
        return LLAProvider.ALI;
    }

    private JSONObject buildRequestParam(LLAConfig config, LLARequest request) {
        JSONObject requestBody = new JSONObject();
        JSONObject input = new JSONObject();
        input.put("messages", LLAMessageBuilder.of()
                .addSystemMessage(request.getCacheInfoList())
                .addSystemMessage(request.getSystem())
                .addUserMessage(request.getContent()).addHistoryMessage(request.getQas()).build());
        requestBody.put("input", input);
        JSONObject parameters = new JSONObject();
        parameters.put("incremental_output", true);
        parameters.put("temperature", config.getModelTemperature());
        parameters.put("top_p", config.getModelTopP());
        parameters.put("max_tokens", config.getModelMaxTokens());
        requestBody.put("parameters", parameters);
        requestBody.put("model", StringUtils.isEmpty(config.getModel()) ? "qwen-max" : config.getModel());
        return requestBody;
    }

    /**
     * 构建请求
     *
     * @param config  模型配置
     * @param request 请求参数
     * @return 请求参数
     */
    protected LLARequestParam buildRequestBody(LLAConfig config, LLARequest request) {
        LLARequestParam param = LLARequestParam.builder()
                .model(config.getModel())
                .stream(config.getStream())
                .messages(LLAMessageBuilder.of()
                        .addSystemMessage(request.getCacheInfoList())
                        .addSystemMessage(request.getSystem())
                        .addHistoryMessage(request.getQas())
                        .addUserMessage(request.getContent()).build())
                .temperature(config.getModelTemperature() == null ? 0.8 : config.getModelTemperature().doubleValue())
                .top_p(config.getModelTopP() == null ? 0.5 : config.getModelTopP().doubleValue())
                .max_tokens(config.getModelMaxTokens() == null ? 1000 : config.getModelMaxTokens().intValue())
                .build();
        if (config.isEnableSearch()) {
            param.setExtra_body(Map.of("enable_search", true));
        }
        return param;
    }
}
