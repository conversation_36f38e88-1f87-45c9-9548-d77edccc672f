package com.qc.agent.lla.adapter;

import com.qc.agent.lla.model.LLAProvider;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.Assert;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 大模型适配器构建器
 *
 * <AUTHOR>
 * @date 2025/3/5 14:29:28
 *
 */
@Configuration
public class LLAAdapterBuilder implements ApplicationContextAware
{

    static Map<LLAProvider, LLAAdapter> requests = new ConcurrentHashMap<>();

    /**
     * 注册适配器
     * @param applicationContext Spring 上下文
     * @throws BeansException 异常
     */
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException
    {
        applicationContext.getBeansOfType(LLAAdapter.class).
                values().
                forEach(adapter -> requests.put(adapter.determineLLA(), adapter));
    }

    /**
     * 获取适配器
     * @param provider 大模型提供者
     * @return 适配器对象
     */
    public static LLAAdapter getAdapter(LLAProvider provider){
        LLAAdapter llaAdapter = requests.get(provider);
        Assert.notNull(llaAdapter, String.format("%s LLA adapter not found", provider));
        return llaAdapter;
    }
}
