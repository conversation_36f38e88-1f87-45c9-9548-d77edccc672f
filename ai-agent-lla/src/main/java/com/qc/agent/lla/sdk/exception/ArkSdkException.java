package com.qc.agent.lla.sdk.exception;

import java.io.Serial;
import java.io.Serializable;

/**
 * Ark SDK 基础异常类。
 * 当 SDK 内部操作发生无法恢复的错误时抛出。
 * 包含一个错误码，便于调用者进行程序化错误处理。
 *
 * <AUTHOR>
 * @date 2025-07-12
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
public class ArkSdkException extends RuntimeException implements Serializable {

    @Serial
    private static final long serialVersionUID = 20240521L; // 使用日期作为版本号

    private static final String ERROR_MESSAGE_PREFIX = "Ark SDK Error: ";

    /**
     * 唯一的错误码，用于程序识别。
     */
    private final String errorCode;

    /**
     * 构造一个新的 ArkSdkException。
     *
     * @param errorCode 唯一的错误码 (例如："NETWORK_ERROR", "INVALID_PARAM")
     * @param message   详细的错误信息
     */
    public ArkSdkException(String errorCode, String message) {
        super(formatMessage(errorCode, message));
        this.errorCode = errorCode;
    }

    /**
     * 构造一个新的 ArkSdkException，并包含根本原因（cause）。
     *
     * @param errorCode 唯一的错误码
     * @param message   详细的错误信息
     * @param cause     导致此异常的根本原因
     */
    public ArkSdkException(String errorCode, String message, Throwable cause) {
        super(formatMessage(errorCode, message), cause);
        this.errorCode = errorCode;
    }

    /**
     * 获取此异常的错误码。
     *
     * @return 错误码字符串
     */
    public String getErrorCode() {
        return errorCode;
    }

    /**
     * 格式化最终的异常消息。
     *
     * @param errorCode 错误码
     * @param message   原始消息
     * @return 格式化后的完整消息
     */
    private static String formatMessage(String errorCode, String message) {
        return String.format("%s[Code: %s] %s", ERROR_MESSAGE_PREFIX, errorCode, message);
    }

    // 建议定义一个包含标准错误码的内部类或枚举
    public static final class ErrorCodes {
        public static final String INVALID_ARGUMENT = "Invalid.Argument";
        public static final String AUTHENTICATION_FAILED = "Auth.Failed";
        public static final String NETWORK_ERROR = "Network.Error";
        public static final String API_LIMIT_EXCEEDED = "Api.LimitExceeded";
        public static final String UNKNOWN_ERROR = "Unknown.Error";
    }
}