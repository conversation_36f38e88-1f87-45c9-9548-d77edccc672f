package com.qc.agent.lla;

import com.qc.agent.lla.adapter.LLAAdapter;
import com.qc.agent.lla.adapter.LLAAdapterBuilder;
import com.qc.agent.lla.model.LLAConfig;
import com.qc.agent.lla.model.LLARequest;
import com.qc.agent.lla.model.LLAResponse;
import lombok.NonNull;

import java.io.File;

/**
 * 大模型客户端
 *
 * <AUTHOR>
 * @date 2025/3/5 14:28:33
 */
public class LLAClient {

    /**
     * 发送异步请求
     *
     * @param config     模型配置
     * @param request    请求参数
     * @param subscriber 订阅者
     */
    public static void sendAsync(LLAConfig config, LLARequest request, ILLASubscriber subscriber) {

        LLAAdapter adapter = LLAAdapterBuilder.getAdapter(config.getProvider());
        config.setStream(true);
        adapter.sendAsync(config, request, subscriber);
    }

    /**
     * 发送同步请求
     *
     * @param config  模型配置
     * @param request 请求参数
     * @return 响应结果
     */
    public static LLAResponse send(LLAConfig config, LLARequest request) {
        LLAAdapter adapter = LLAAdapterBuilder.getAdapter(config.getProvider());
        config.setStream(false);
        return adapter.send(config, request);
    }


    /**
     * 解析上传文件
     *
     * @param file
     * @param config
     * @return
     */
    public static String parseUploadFile(@NonNull File file, LLAConfig config) {
        LLAAdapter adapter = LLAAdapterBuilder.getAdapter(config.getProvider());
        return adapter.parseUploadFile(file);
    }

    /**
     * 获取文件内容
     *
     * @param fileId
     * @param config
     * @return
     */
    public static String getFileContent(@NonNull String fileId, LLAConfig config) {
        LLAAdapter adapter = LLAAdapterBuilder.getAdapter(config.getProvider());
        return adapter.getFileContent(fileId);
    }


}
