package com.qc.agent.lla.adapter.impl.azure;

import com.alibaba.fastjson.JSONObject;
import com.qc.agent.lla.ILLAConverter;
import com.qc.agent.lla.adapter.AbstrctLLAAdapter;
import com.qc.agent.lla.model.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.net.URI;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;

/**
 * Azure OpenAI Adapter (最终版)
 * 使用 HttpClient 实现以兼容最终确认的、高度自定义的网关URL。
 */
@Slf4j
@Component
public class AzureOpenAIAdapter extends AbstrctLLAAdapter {

    private static final String API_VERSION = "2025-01-01-preview";

    @Override
    public LLAProvider determineLLA() {
        return LLAProvider.AZURE_OPENAI;
    }

    /**
     * 实现 buildRequest 方法，这是此适配器的核心。
     */
    @Override
    protected HttpRequest buildRequest(LLAConfig config, LLARequest request) {
        String endpoint = config.getEndpoint(); // 应该是 "https://rgsikadev-apim.azure-api.cn"
        String apiKey = config.getApiKey();
        String aipApplicationId = config.getAipApplicationId();
        String aipUserId = config.getAipUserId();

        Assert.notNull(endpoint, "Endpoint is required for Azure OpenAI");
        Assert.notNull(apiKey, "API Key (api-key) is required for Azure OpenAI");

        // --- 关键修正：构建最终确认的、不带 "/chat/completions" 的URL ---
        String fullUrl = String.format("%s/api/aoai/openai/deployments/%s?api-version=%s",
                endpoint,
                config.getModel(), // model 就是部署名 "gpt-4o-qc"
                API_VERSION
        );
        // --- 修正结束 ---

        LLARequestParam param = buildRequestBody(config, request);
        String requestBodyJson = JSONObject.toJSONString(param);

        log.debug("Final Azure OpenAI Request URL: {}", fullUrl);
        log.debug("Final Azure OpenAI Request Body: {}", requestBodyJson);

        HttpRequest.Builder builder = HttpRequest.newBuilder()
                .uri(URI.create(fullUrl))
                .header(HttpHeaders.CONTENT_TYPE, "application/json")
                .header("api-key", apiKey)
                .POST(HttpRequest.BodyPublishers.ofString(requestBodyJson));

        if (aipApplicationId != null) {
            builder.header("aip-application-id", aipApplicationId);
        }
        if (aipUserId != null) {
            builder.header("aip-user-id", aipUserId);
        }

        return builder.build();
    }

    @Override
    protected ILLAConverter buildConverter() {
        // 这个转换器负责解析 HttpClient 返回的原始 SSE 字符串，逻辑是正确的
        return new AzureOpenAILLAConverter();
    }

    @Override
    protected LLAResponseResult parseResponse(HttpResponse<String> response) {
        // 这个方法用于同步调用，逻辑也是正确的
        if (response.statusCode() >= 200 && response.statusCode() < 300) {
            return LLAResponseResult.builder().success(true).content(response.body()).build();
        } else {
            return LLAResponseResult.builder().success(false).content(response.body()).build();
        }
    }
}