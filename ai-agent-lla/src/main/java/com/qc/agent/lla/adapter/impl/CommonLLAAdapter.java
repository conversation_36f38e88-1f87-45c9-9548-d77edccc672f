package com.qc.agent.lla.adapter.impl;

import com.alibaba.fastjson.JSONObject;
import com.qc.agent.lla.ILLAConverter;
import com.qc.agent.lla.adapter.AbstrctLLAAdapter;
import com.qc.agent.lla.adapter.CommonLLAConverter;
import com.qc.agent.lla.model.LLAConfig;
import com.qc.agent.lla.model.LLARequest;
import com.qc.agent.lla.model.LLARequestParam;
import com.qc.agent.lla.model.LLAResponseResult;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpHeaders;
import org.springframework.util.Assert;

import java.net.HttpURLConnection;
import java.net.URI;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;

/**
 *
 *
 * <AUTHOR>
 * @date 2025/3/4 20:52:20
 *
 */
public abstract class CommonLLAAdapter extends AbstrctLLAAdapter
{

    protected abstract String getProviderDomain();
    protected abstract String getApiKey();


    protected String getPath() {
        return "/v1/chat/completions";
    }



    @Override
    protected ILLAConverter buildConverter() {
        return new CommonLLAConverter();

    }

    @Override
    protected HttpRequest buildRequest(LLAConfig config, LLARequest request) {
        LLARequestParam param = buildRequestBody(config, request);
        String apiKey =  StringUtils.isNotEmpty(config.getApiKey()) ?  config.getApiKey() : getApiKey();
        Assert.notNull(apiKey, "apiKey is required");

        return HttpRequest.newBuilder()
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + apiKey)
                .header("Content-Type", "application/json")
                .uri(URI.create(URI.create(getProviderDomain()) + getPath()))
                .POST(HttpRequest.BodyPublishers.ofString(JSONObject.toJSONString(param)))
                .build();
    }

    @Override
    protected LLAResponseResult parseResponse(HttpResponse<String> response){
        if(HttpURLConnection.HTTP_OK == response.statusCode()){
            return LLAResponseResult.builder().success(true).content(response.body()).build();
        }else{
            JSONObject result = JSONObject.parseObject(response.body());
            result.put("statusCode",response.statusCode());
            return LLAResponseResult.builder().success(false).content(result.toString()).build();
        }
    }


}
