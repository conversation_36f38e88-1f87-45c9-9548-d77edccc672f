package com.qc.agent.lla.adapter.impl.doubao;

import com.alibaba.fastjson.JSONObject;
import com.qc.agent.lla.ILLAConverter;
import com.qc.agent.lla.exception.LLAInvokerRequestException;
import com.qc.agent.lla.model.LLAResponse;
import com.qc.agent.lla.model.LLAUsage;
import org.springframework.util.StringUtils;

import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-06-16
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
public class DoubaoLLAConverter implements ILLAConverter {

    // 提取魔法字符串为常量，提高可维护性
    private static final String DATA_PREFIX = "data: ";
    private static final String DONE_SIGNAL = "data: [DONE]";

    @Override
    public LLAResponse convert(Object t) {
        // 使用模式匹配 for instanceof，更安全、简洁
        if (!(t instanceof String content) || !StringUtils.hasText(content)) {
            return LLAResponse.builder().content("").stop(false).build();
        }

        if (DONE_SIGNAL.equals(content)) {
            return LLAResponse.builder().content("").stop(true).build();
        }

        // 使用三元运算符简化逻辑
        final String jsonContent = content.startsWith(DATA_PREFIX)
                ? content.substring(DATA_PREFIX.length())
                : content;

        return parseLLAResponse(jsonContent);
    }

    public LLAResponse parseLLAResponse(String response) {
        // 使用 var 简化局部变量声明
        var result = JSONObject.parseObject(response);

        // 优先处理错误情况
        if (result.containsKey("error")) {
            // 可以考虑更具体的错误信息提取
            throw new LLAInvokerRequestException(result.get("error").toString());
        }

        var choices = result.getJSONArray("choices");
        if (choices == null || choices.isEmpty()) {
            // 优化 usage 对象的查找逻辑：首先从根对象找，如果找不到，再从 choices 中查找第一个有效的
            final JSONObject usageJson = result.getJSONObject("usage");
            return LLAResponse.builder()
                    .content("")
                    .usage(usageJson == null ? null : usageJson.toJavaObject(LLAUsage.class))
                    .stop(true) // 如果没有 choices，通常意味着响应结束
                    .created(result.getInteger("created"))
                    .build();
        }

        // 使用 Stream API 以函数式风格处理内容拼接
        final String content = choices.stream()
                .map(JSONObject.class::cast)
                .map(this::extractContentFromChoice)
                .collect(Collectors.joining());

        // 优化 usage 对象的查找逻辑：首先从根对象找，如果找不到，再从 choices 中查找第一个有效的
        final JSONObject usageJson = Optional.ofNullable(result.getJSONObject("usage"))
                .orElseGet(() -> choices.stream()
                        .map(JSONObject.class::cast)
                        .map(choice -> choice.getJSONObject("usage"))
                        .filter(Objects::nonNull)
                        .findFirst()
                        .orElse(null));

        return LLAResponse.builder()
                .content(content)
                .stop(usageJson != null) // 响应是否结束取决于是否收到了 usage 信息
                .usage(usageJson == null ? null : usageJson.toJavaObject(LLAUsage.class))
                .created(result.getInteger("created"))
                .build();
    }

    /**
     * 从单个 choice JSON 对象中提取 content 字符串。
     * 兼容 streaming (delta) 和 non-streaming (message) 两种格式。
     *
     * @param choice a JSONObject from the 'choices' array
     * @return The content string, or an empty string if not found.
     */
    private String extractContentFromChoice(JSONObject choice) {
        // 使用 Optional 链式调用，避免多层 if-null-check
        return Optional.ofNullable(choice.getJSONObject("delta"))
                .map(delta -> delta.getString("content"))
                .or(() -> Optional.ofNullable(choice.getJSONObject("message"))
                        .map(message -> message.getString("content")))
                .orElse(""); // 如果两者都无内容，返回空字符串
    }
}