package com.qc.agent.lla.sdk.model_supplier.impl;

import com.qc.agent.lla.model.LLAMessage;
import com.qc.agent.lla.model.LLAProvider;
import com.qc.agent.lla.model.LLAResponse;
import com.qc.agent.lla.model.LLAUsage;
import com.qc.agent.lla.sdk.exception.ArkSdkException;
import com.qc.agent.lla.sdk.model.ModelMessage;
import com.qc.agent.lla.sdk.model.ModelParam;
import com.qc.agent.lla.sdk.model.context_cache.ContextCacheParam;
import com.qc.agent.lla.sdk.model.context_cache.ContextCacheResult;
import com.qc.agent.lla.sdk.model_supplier.AiClient;
import com.volcengine.ark.runtime.model.completion.chat.*;
import com.volcengine.ark.runtime.model.context.CreateContextRequest;
import com.volcengine.ark.runtime.model.context.CreateContextResult;
import com.volcengine.ark.runtime.service.ArkService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Client for interacting with the Doubao (Volcengine Ark) AI models.
 * This class handles chat completions, image analysis, and context management.
 *
 * <AUTHOR>
 * @date 2025-06-16
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Slf4j
@Component("doubaoClient")
public class DoubaoClient implements AiClient {

    @Value("${ai.agent.doubao-second-apikey}")
    private String apiKey;

    /**
     * The default model to use if not specified in the request.
     */
    private static final String DEFAULT_MODEL = "doubao-seed-1.6-flash-250615"; // Updated to a common general-purpose model

    /**
     * The primary, shared ArkService instance, configured at startup.
     * Using a single instance is more efficient as it reuses underlying resources like connection pools.
     */
    private ArkService defaultService;

    @PostConstruct
    public void init() {
        log.info("Initializing DoubaoClient...");
        if (apiKey == null || apiKey.isBlank()) {
            log.error("Doubao API key ('ai.agent.doubao-second-apikey') is not configured. The client will not work.");
            // Or throw an exception to prevent application startup
            // throw new IllegalStateException("Doubao API key is not configured.");
            return;
        }
        // Initialize the default service instance with the configured API key.
        this.defaultService = createService(this.apiKey);
        log.info("DoubaoClient initialized successfully with default model [{}].", DEFAULT_MODEL);
    }

    /**
     * Creates a new ArkService instance. This is a factory method.
     * @param serviceApiKey The API key for the service.
     * @return A new ArkService instance.
     */
    private ArkService createService(String serviceApiKey) {
        // The builder is lightweight. The underlying OkHttpClient is what holds resources.
        // For simplicity, we create a new service, assuming the SDK manages resources efficiently.
        // For high-load scenarios with multiple API keys, a cache of services could be implemented.
        return ArkService.builder().apiKey(serviceApiKey).build();
    }

    @Override
    public LLAProvider getType() {
        return LLAProvider.DOUBAO;
    }

    @Override
    public void chatStream(ModelParam param, ModelMessage modelMessage) {
        log.warn("chatStream is not yet implemented for DoubaoClient.");
        // Implementation for streaming would go here.
    }

    @Override
    public LLAResponse chat(ModelParam param, ModelMessage modelMessage) {
        final var logPrefix = param.getLogPrefix();
        final var modelToUse = param.getModel() != null ? param.getModel() : DEFAULT_MODEL;
        log.info("{} Starting Doubao chat request with model [{}].", logPrefix, modelToUse);

        try {
            // Use the per-request API key if provided, otherwise fall back to the default service.
            // This is a simplified approach. For many different API keys, caching services would be better.
            var service = (param.getApiKey() != null && !param.getApiKey().equals(this.apiKey))
                    ? createService(param.getApiKey())
                    : this.defaultService;

            if (service == null) {
                throw new IllegalStateException("Doubao service is not available. Check configuration.");
            }

            final var messages = List.of(
                    ChatMessage.builder().role(ChatMessageRole.SYSTEM).content(modelMessage.getSystemMessage()).build(),
                    ChatMessage.builder().role(ChatMessageRole.USER).content(modelMessage.getUserMessage()).build()
            );

            var chatCompletionRequest = ChatCompletionRequest.builder()
                    .model(modelToUse)
                    .messages(messages)
                    .build();

            log.debug("{} Sending chat completion request: {}", logPrefix, chatCompletionRequest);
            var result = service.createChatCompletion(chatCompletionRequest);
            log.debug("{} Received chat completion result: {}", logPrefix, result);

            String content = result.getChoices().stream()
                    .findFirst()
                    .map(choice -> choice.getMessage().getContent())
                    .map(Objects::toString)
                    .orElse("");

            var usage = new LLAUsage();
            if (result.getUsage() != null) {
                BeanUtils.copyProperties(result.getUsage(), usage);
                log.info("{} Token usage - Prompt: {}, Completion: {}, Total: {}",
                        logPrefix, usage.getPromptTokens(), usage.getCompletionTokens(), usage.getTotalTokens());
            }

            var finishReason = result.getChoices().stream().findFirst().map(ChatCompletionChoice::getFinishReason).orElse("");

            return LLAResponse.builder()
                    .content(content)
                    .usage(usage)
                    .stop("stop".equalsIgnoreCase(finishReason))
                    .created((int) result.getCreated())
                    .model(result.getModel())
                    .provider(getType())
                    .build();

        } catch (ArkSdkException e) {
            log.error("{} Error calling Doubao API for chat: {}", logPrefix, e.getMessage(), e);
            return LLAResponse.buildError(e.getMessage());
        } catch (Exception e) {
            log.error("{} An unexpected error occurred during Doubao chat: {}", logPrefix, e.getMessage(), e);
            return LLAResponse.buildError("An unexpected error occurred: " + e.getMessage());
        }
    }

    @Override
    public ContextCacheResult createContext(ModelParam param) {
        log.info("Creating Doubao context with model [{}].", param.getModel());
        try {
            var service = (param.getApiKey() != null && !param.getApiKey().equals(this.apiKey))
                    ? createService(param.getApiKey())
                    : this.defaultService;

            if (service == null) {
                throw new IllegalStateException("Doubao service is not available. Check configuration.");
            }

            ContextCacheParam cacheParam = param.getCacheParam();
            List<ChatMessage> messages = cacheParam.getMessages().stream()
                    .map(this::toChatMessage)
                    .collect(Collectors.toList());

            var request = CreateContextRequest.builder()
                    .mode(cacheParam.getMode())
                    .model(cacheParam.getModel())
                    .ttl(cacheParam.getTtl())
                    .messages(messages)
                    .build();

            log.debug("Sending create context request: {}", request);
            CreateContextResult context = service.createContext(request);
            log.info("Successfully created context with ID [{}]", context.getId());

            return ContextCacheResult.builder()
                    .id(context.getId())
                    .model(context.getModel())
                    .mode(context.getMode())
                    .ttl(context.getTtl())
                    .usage(context.getUsage())
                    .build();
        } catch (ArkSdkException e) {
            log.error("Error creating Doubao context: {}", e.getMessage(), e);
            // Depending on desired behavior, re-throw a custom exception or return an error object.
            throw new RuntimeException("Failed to create Doubao context", e);
        } catch (Exception e) {
            log.error("An unexpected error occurred during context creation: {}", e.getMessage(), e);
            throw new RuntimeException("An unexpected error occurred during context creation", e);
        }
    }

    /**
     * Converts an LLAMessage to the SDK's ChatMessage format with safe role mapping.
     */
    private ChatMessage toChatMessage(LLAMessage message) {
        var role = switch (message.getRole().toUpperCase()) {
            case "SYSTEM" -> ChatMessageRole.SYSTEM;
            case "ASSISTANT" -> ChatMessageRole.ASSISTANT;
            case "USER" -> ChatMessageRole.USER;
            default -> {
                log.warn("Unknown message role '{}', defaulting to USER.", message.getRole());
                yield ChatMessageRole.USER;
            }
        };
        return ChatMessage.builder()
                .role(role)
                .content(message.getContent())
                .build();
    }


    public LLAResponse analyzeImageByUrl(String imageUrl, String prompt) {
        if (imageUrl == null || imageUrl.isBlank() || prompt == null || prompt.isBlank()) {
            log.warn("Image URL and prompt must not be null or empty. Aborting image analysis request.");
            return LLAResponse.buildError("Image URL and prompt are required.");
        }
        log.info("Starting image analysis for URL [{}] with model [{}].", imageUrl, DEFAULT_MODEL);

        try {
            if (this.defaultService == null) {
                throw new IllegalStateException("Doubao service is not available. Check configuration.");
            }

            var textPart = ChatCompletionContentPart.builder()
                    .type("text")
                    .text(prompt)
                    .build();

            var imageDetail = new ChatCompletionContentPart.ChatCompletionContentPartImageURL();
            imageDetail.setUrl(imageUrl);
            imageDetail.setDetail("auto");

            var imagePart = ChatCompletionContentPart.builder()
                    .type("image_url")
                    .imageUrl(imageDetail)
                    .build();

            var userMessage = ChatMessage.builder()
                    .role(ChatMessageRole.USER)
                    .multiContent(Arrays.asList(textPart, imagePart))
                    .build();

            ChatCompletionRequest.ChatCompletionRequestThinking thinking = new ChatCompletionRequest.ChatCompletionRequestThinking("disabled");
            var request = ChatCompletionRequest.builder()
                    .model(DEFAULT_MODEL) // Multi-modal models may differ, e.g., "doubao-vision-pro"
                    .messages(Collections.singletonList(userMessage))
                    .thinking(thinking)
                    .build();

            log.debug("Sending image analysis request: {}", request);
            var result = defaultService.createChatCompletion(request);

            var content = result.getChoices().stream()
                    .findFirst()
                    .map(choice -> choice.getMessage().getContent())
                    .map(Object::toString)
                    .orElse("");

            var usage = new LLAUsage();
            if(result.getUsage() != null){
                BeanUtils.copyProperties(result.getUsage(), usage);
                log.info("Image analysis token usage - Prompt: {}, Completion: {}, Total: {}",
                        usage.getPromptTokens(), usage.getCompletionTokens(), usage.getTotalTokens());
            }

            return LLAResponse.builder()
                    .usage(usage)
                    .stop(true)
                    .content(content)
                    .created((int) result.getCreated())
                    .build();

        } catch (ArkSdkException e) {
            log.error("Error calling Doubao API for image analysis: {}", e.getMessage(), e);
            return LLAResponse.buildError("Error from AI service: " + e.getMessage());
        } catch (Exception e) {
            log.error("An unexpected error occurred during image analysis: {}", e.getMessage(), e);
            return LLAResponse.buildError("An unexpected error occurred: " + e.getMessage());
        }
    }

    @PreDestroy
    public void shutdown() {
        log.info("Shutting down DoubaoClient...");
        if (defaultService != null) {
            try {
                defaultService.shutdownExecutor();
                log.info("Default ArkService shut down successfully.");
            } catch (Exception e) {
                log.error("Error shutting down default ArkService", e);
            }
        }
        log.info("DoubaoClient shut down successfully.");
    }
}