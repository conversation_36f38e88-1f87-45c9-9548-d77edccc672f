package com.qc.agent.lla.sdk.model;

import com.qc.agent.lla.sdk.model.context_cache.ContextCacheParam;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025-06-16
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Data
public class ModelParam {
    private String apiKey;
    private String model;
    private String cacheUrl = "http://ark.cn-beijing.volces.com/api/v3";

    private String logPrefix;

    /**
     * 创建缓存的参数
     */
    private ContextCacheParam cacheParam;

    private ExtParam extParam;

    public static ModelParam build() {
        return new ModelParam();
    }

    public ModelParam withApiKey(String apiKey) {
        this.apiKey = apiKey;
        return this;
    }

    public ModelParam withModel(String model) {
        this.model = model;
        return this;
    }

    public ModelParam withCacheUrl(String model) {
        this.cacheUrl = cacheUrl;
        return this;
    }


    public ModelParam withLogPrefix(String logPrefix) {
        this.logPrefix = logPrefix;
        return this;
    }

    public ModelParam withCacheParam(ContextCacheParam cacheParam) {
        this.cacheParam = cacheParam;
        return this;
    }
}
