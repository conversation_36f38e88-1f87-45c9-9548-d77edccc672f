package com.qc.agent.lla.sdk.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.volcengine.ark.runtime.model.CompletionTokensDetails;
import com.volcengine.ark.runtime.model.PromptTokensDetails;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025-06-16
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Data
public class ModelUsage {
    @JsonProperty("prompt_tokens")
    long promptTokens;

    /**
     * The number of completion tokens used.
     */
    @JsonProperty("completion_tokens")
    long completionTokens;

    /**
     * The number of total tokens used
     */
    @JsonProperty("total_tokens")
    long totalTokens;

    @JsonProperty("prompt_tokens_details")
    private PromptTokensDetails promptTokensDetails;

    @JsonProperty("completion_tokens_details")
    private CompletionTokensDetails completionTokensDetails;
}
