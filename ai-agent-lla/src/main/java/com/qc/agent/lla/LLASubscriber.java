package com.qc.agent.lla;

import com.qc.agent.lla.model.LLARequest;
import com.qc.agent.lla.model.LLAResponse;

import java.util.concurrent.Flow;
import java.util.function.Function;

/**
 *
 *
 * <AUTHOR>
 * @date 2025/3/6 10:49:27
 *
 */
public class LLASubscriber<T> implements Flow.Subscriber<T>
{
    ILLASubscriber subscriber;
    ILLAConverter converter;

    public static <T> LLASubscriber<T> of(ILLASubscriber subscriber)
    {
        LLASubscriber<T> llaSubscriber = new LLASubscriber<T>();
        llaSubscriber.subscriber = subscriber;
        return llaSubscriber;
    }

    public LLASubscriber<T> converter(ILLAConverter converter)
    {
        this.converter = converter;
        return this;
    }


    @Override
    public void onSubscribe(Flow.Subscription subscription)
    {
        subscription.request(Long.MAX_VALUE);
        subscriber.onSubscribe();
    }
    @Override
    public void onNext(T item)
    {
        subscriber.onNext(converter.convert(item));
    }
    @Override
    public void onError(Throwable throwable)
    {
        subscriber.onError(throwable);
    }
    @Override
    public void onComplete()
    {
        subscriber.onComplete();
    }
}
