package com.qc.agent.lla.util;

import lombok.Getter;
import okhttp3.OkHttpClient;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025-06-17
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
public class OkHttpUtils {
    // 创建 OkHttpClient 实例，配置连接池、超时等参数
    @Getter
    private static final OkHttpClient client;

    static {
        client = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS) // 连接超时时间
                .readTimeout(30, TimeUnit.SECONDS)    // 读取超时时间
                .writeTimeout(30, TimeUnit.SECONDS)   // 写入超时时间
                .retryOnConnectionFailure(true)       // 连接失败重试
                .build();
    }
}
