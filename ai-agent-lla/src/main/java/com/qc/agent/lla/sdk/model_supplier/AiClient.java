package com.qc.agent.lla.sdk.model_supplier;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.qc.agent.lla.model.LLAProvider;
import com.qc.agent.lla.model.LLAResponse;
import com.qc.agent.lla.sdk.model.ModelMessage;
import com.qc.agent.lla.sdk.model.ModelParam;
import com.qc.agent.lla.sdk.model.context_cache.ContextCacheResult;

/**
 * <AUTHOR>
 * @date 2025-06-16
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
public interface AiClient {

    /**
     *
     * @return
     */
    LLAProvider getType();

    /**
     * 聊天-非流式
     *
     * @param param
     * @param modelMessage
     * @return
     */
    LLAResponse chat(ModelParam param, ModelMessage modelMessage);

    /**
     * 聊天-流式
     *
     * @param param
     * @param modelMessage
     */
    void chatStream(ModelParam param, ModelMessage modelMessage);

    /**
     * 创建上下文缓存
     *
     * @param param
     * @return
     */
    ContextCacheResult createContext(ModelParam param) throws JsonProcessingException;
}
