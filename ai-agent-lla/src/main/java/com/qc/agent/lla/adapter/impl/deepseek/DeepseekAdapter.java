package com.qc.agent.lla.adapter.impl.deepseek;

import com.qc.agent.lla.adapter.impl.CommonLLAAdapter;
import com.qc.agent.lla.model.LLAProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 *
 *
 * <AUTHOR>
 * @date 2025/3/6 15:13:00
 *
 */
@Component
@Slf4j
public class DeepseekAdapter extends CommonLLAAdapter
{
    private static final String PROVIDER_DOMAIN = "https://api.deepseek.com";

    @Value("${ai.agent.deepseek-apikey}")
    private String deepseekApiKey;


    @Override
    protected String getProviderDomain()
    {
        return PROVIDER_DOMAIN;
    }
    @Override
    protected String getApiKey()
    {
        return deepseekApiKey;
    }
    @Override
    public LLAProvider determineLLA()
    {
        return LLAProvider.DEEPSEEK;
    }
}
