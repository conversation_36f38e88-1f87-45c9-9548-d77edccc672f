package com.qc.agent.lla.adapter.impl.openai;

import com.qc.agent.lla.adapter.impl.CommonLLAAdapter;
import com.qc.agent.lla.model.LLAProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 *
 *
 * <AUTHOR>
 * @date 2025/3/6 15:15:39
 *
 */
@Slf4j
@Component
public class OpenAIAdapter extends CommonLLAAdapter
{
    static final String PROVIDER_DOMAIN = "https://api.chatanywhere.tech";

    @Value("${ai.agent.openai-apikey}")
    String openapiKey;



    @Override
    protected String getProviderDomain()
    {
        return PROVIDER_DOMAIN;
    }
    @Override
    protected String getApiKey()
    {
        return openapiKey;
    }
    @Override
    public LLAProvider determineLLA()
    {
        return LLAProvider.OPENAI;
    }
}
