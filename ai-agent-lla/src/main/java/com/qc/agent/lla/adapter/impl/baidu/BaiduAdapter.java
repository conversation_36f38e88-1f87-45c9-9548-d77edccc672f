package com.qc.agent.lla.adapter.impl.baidu;

import com.alibaba.fastjson.JSONObject;
import com.qc.agent.lla.ILLAConverter;
import com.qc.agent.lla.adapter.AbstrctLLAAdapter;
import com.qc.agent.lla.model.LLAConfig;
import com.qc.agent.lla.model.LLAProvider;
import com.qc.agent.lla.model.LLARequest;
import com.qc.agent.lla.model.LLARequestParam;
import com.qc.agent.lla.model.LLAResponseResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.net.URLEncoder;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.charset.StandardCharsets;
import java.time.Duration;

/**
 *
 *
 * <AUTHOR>
 * @date 2025/3/6 15:19:28
 *
 */
@Slf4j
@Component
public class BaiduAdapter extends AbstrctLLAAdapter
{
    private static final String PROVIDER_DOMAIN = "https://aip.baidubce.com/rpc/2.0/ai_custom";

    @Value("${ai.agent.baidu-apikey}")
    String securityCredentialKeyApiKey;

    @Value("${ai.agent.baidu-secretkey}")
    String securityCredentialKeySecretKey;


    @Override
    protected HttpRequest buildRequest(LLAConfig config, LLARequest request)
    {
        LLARequestParam param = buildRequestBody(config,request);

        return HttpRequest.newBuilder()
                .header("Content-Type", "application/json")
                .uri(URI.create(URI.create(PROVIDER_DOMAIN) + String.format("/v1/wenxinworkshop/chat/%s", config.getModel()) +
                        "?access_token=" + getAccessToken(config)))
                .POST(HttpRequest.BodyPublishers.ofString(JSONObject.toJSONString(param)))
                .build();

    }
    @Override
    protected ILLAConverter buildConverter()
    {
        return new BaiduLLAConverter();
    }
    @Override
    protected LLAResponseResult parseResponse(HttpResponse<String> response)
    {
        if(response.body().startsWith("data: ")){
            return LLAResponseResult.builder().success(true).content(response.body()).build();
        }else{
            return LLAResponseResult.builder().success(false).content(response.body()).build();
        }
    }
    @Override
    public LLAProvider determineLLA()
    {
        return LLAProvider.BAIDU;
    }

    private String getAccessToken(LLAConfig config) {
        String apiKey = StringUtils.isEmpty(config.getApiKey())? securityCredentialKeyApiKey:config.getApiKey();
        String secretKey = StringUtils.isEmpty(config.getSecretKey())? securityCredentialKeySecretKey:config.getSecretKey();
        HttpRequest request = buildFetchTokenRequest(apiKey, secretKey);

        try {
            HttpClient httpClient = HttpClient.newBuilder()
                    .connectTimeout(Duration.ofMinutes(5))
                    .build();
            HttpResponse<String> response= httpClient.send(request, HttpResponse.BodyHandlers.ofString());
            JSONObject jsonResponse  =  JSONObject.parseObject(response.body());
            return jsonResponse.getString("access_token");
        } catch (Exception e) {
            log.error("Failed to fetch token, the error is: ", e);
            return null;
        }
    }

    private HttpRequest buildFetchTokenRequest(String apiKey, String secretKey) {
        String url = "https://aip.baidubce.com/oauth/2.0/token";
        String params = "grant_type=client_credentials"
                + "&client_id=" + URLEncoder.encode(apiKey, StandardCharsets.UTF_8)
                + "&client_secret=" + URLEncoder.encode(secretKey, StandardCharsets.UTF_8);
        return HttpRequest.newBuilder()
                .uri(URI.create(url + "?" + params))
                .header("Content-Type", "application/json")
                .GET()
                .build();
    }


}
