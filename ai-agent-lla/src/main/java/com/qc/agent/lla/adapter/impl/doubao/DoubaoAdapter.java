package com.qc.agent.lla.adapter.impl.doubao;

import com.alibaba.fastjson.JSONObject;
import com.qc.agent.lla.ILLAConverter;
import com.qc.agent.lla.adapter.impl.CommonLLAAdapter;
import com.qc.agent.lla.model.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpHeaders;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.net.URI;
import java.net.http.HttpRequest;

/**
 * <AUTHOR>
 * @date 2025/3/6 15:00:57
 */
@Slf4j
@Component
public class DoubaoAdapter extends CommonLLAAdapter {
    private static final String PROVIDER_DOMAIN = "https://ark.cn-beijing.volces.com";

    @Value("${ai.agent.doubao-apikey}")
    private String doubaoApiKey;

    @Override
    public LLAProvider determineLLA() {
        return LLAProvider.DOUBAO;
    }

    @Override
    protected String getProviderDomain() {
        return PROVIDER_DOMAIN;
    }

    @Override
    protected String getPath() {
        return "/api/v3/chat/completions";
    }

    protected String getContextPath() {
        return "/api/v3/context/chat/completions";
    }


    @Override
    protected String getApiKey() {
        return doubaoApiKey;
    }

    @Override
    protected ILLAConverter buildConverter() {
        return new DoubaoLLAConverter();
    }

    @Override
    protected HttpRequest buildRequest(LLAConfig config, LLARequest request) {
        LLARequestParam param = buildRequestBody(config, request);
        String apiKey = StringUtils.isNotEmpty(config.getApiKey()) ? config.getApiKey() : getApiKey();
        Assert.notNull(apiKey, "apiKey is required");
        URI uri = URI.create(URI.create(getProviderDomain()) + getPath());
        // 带缓存的问答
        if (request.getContextId() != null) {
            uri = URI.create(URI.create(getProviderDomain()) + getContextPath());
            param.setContext_id(request.getContextId());
            param.setModel("ep-20250617155022-qcjgs");
        }

        return HttpRequest.newBuilder()
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + apiKey)
                .header("Content-Type", "application/json")
                .uri(uri)
                .POST(HttpRequest.BodyPublishers.ofString(JSONObject.toJSONString(param)))
                .build();
    }

    /**
     * 构建请求
     *
     * @param config  模型配置
     * @param request 请求参数
     * @return 请求参数
     */
    protected LLARequestParam buildRequestBody(LLAConfig config, LLARequest request) {
        LLAUsageOptions usageOptions = LLAUsageOptions.builder()
                .include_usage(true)
                .build();
        LLARequestParam param = LLARequestParam.builder()
                .model(config.getModel())
                .stream(config.getStream())
                .messages(LLAMessageBuilder.of()
                        .addSystemMessage(request.getCacheInfoList())
                        .addSystemMessage(request.getSystem())
                        .addHistoryMessage(request.getQas())
                        .addUserMessage(request.getContent()).build())
                .temperature(config.getModelTemperature() == null ? 0.8 : config.getModelTemperature().doubleValue())
                .top_p(config.getModelTopP() == null ? 0.5 : config.getModelTopP().doubleValue())
                .max_tokens(config.getModelMaxTokens() == null ? 1000 : config.getModelMaxTokens().intValue())
                // 输出token
                .stream_options(usageOptions)
                .build();
        if (config.getThinkingOptions() == null) {
            // 是否开启思考参数
            param.setThinking(LLAThinkingOptions.builder().type("disabled").build());
        }
        return param;
    }
}
