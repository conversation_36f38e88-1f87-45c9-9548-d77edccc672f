package com.qc.agent.lla.sdk.model.context_cache;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.volcengine.ark.runtime.model.Usage;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025-06-16
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Data
@Builder
@AllArgsConstructor
public class ContextCacheResult {
    @JsonProperty("id")
    private String id;

    @JsonProperty("model")
    private String model;

    @JsonProperty("mode")
    private String mode;

    @JsonProperty("ttl")
    private Integer ttl;


    @JsonProperty("usage")
    private Usage usage;
}
