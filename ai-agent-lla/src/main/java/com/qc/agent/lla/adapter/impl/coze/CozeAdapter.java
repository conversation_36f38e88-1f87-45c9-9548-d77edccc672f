package com.qc.agent.lla.adapter.impl.coze;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.qc.agent.lla.ILLAConverter;
import com.qc.agent.lla.adapter.AbstrctLLAAdapter;
import com.qc.agent.lla.model.LLAConfig;
import com.qc.agent.lla.model.LLAMessage;
import com.qc.agent.lla.model.LLAMessageBuilder;
import com.qc.agent.lla.model.LLAProvider;
import com.qc.agent.lla.model.LLAQa;
import com.qc.agent.lla.model.LLARequest;
import com.qc.agent.lla.model.LLAResponseResult;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpHeaders;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.util.List;

/**
 *
 *
 * <AUTHOR>
 * @date 2025/3/12 11:17:20
 *
 */
@Component
public class CozeAdapter extends AbstrctLLAAdapter
{
    @Value("${ai.agent.coze-token}")
    private String token;

    @Value("${ai.agent.coze-botid}")
    private String botId;

    @Override
    protected HttpRequest buildRequest(LLAConfig config, LLARequest request)
    {
        JSONObject param = new JSONObject();
        JSONArray hisMessages = buildHisMessages(request.getQas());
        if(CollectionUtils.isNotEmpty(hisMessages)){
            param.put("chat_history", hisMessages);
        }
        String secretKey = StringUtils.isNotEmpty(config.getSecretKey()) ? config.getSecretKey() : botId;
        param.put("bot_id", secretKey);
        param.put("user", "勤策");
        param.put("query", request.getContent());
        param.put("stream", config.getStream());

        String apiKey = StringUtils.isNotEmpty(config.getApiKey()) ? config.getApiKey() : token;
        return  HttpRequest.newBuilder()
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + apiKey)
                .header("Content-Type", "application/json")
                .uri(URI.create("https://api.coze.cn/open_api/v2/chat"))
                .POST(HttpRequest.BodyPublishers.ofString(param.toJSONString()))
                .build();
    }
    @Override
    protected ILLAConverter buildConverter()
    {
        return new CozeLLAConverter();
    }
    @Override
    protected LLAResponseResult parseResponse(HttpResponse<String> response)
    {
        if(response.body().startsWith("data: ")){
            return LLAResponseResult.builder().success(true).content(response.body().substring(6)).build();
        }else{
            return LLAResponseResult.builder().success(false).content(response.body()).build();
        }
    }
    @Override
    public LLAProvider determineLLA()
    {
        return LLAProvider.COZE;
    }

    private JSONArray buildHisMessages(List<LLAQa> qas){
        if(CollectionUtils.isNotEmpty(qas)){
            List<LLAMessage> messages = LLAMessageBuilder.of().addHistoryMessage(qas).build();
            JSONArray hisMessages = JSONArray.parseArray(JSONArray.toJSONString(messages));
            hisMessages.forEach(hisMessage -> {
                JSONObject hisMessageJson = (JSONObject) hisMessage;
                if("user".equals(hisMessageJson.getString("role"))){
                    hisMessageJson.put("content_type", "text");
                }else if("assistant".equals(hisMessageJson.getString("role"))){
                    hisMessageJson.put("type", "answer");
                    hisMessageJson.put("content_type", "text");
                }
            });
            return hisMessages;
        }
        return new JSONArray();
    }
}
