package com.qc.agent.lla.adapter.impl.coze;

import com.alibaba.fastjson.JSONObject;
import com.qc.agent.lla.ILLAConverter;
import com.qc.agent.lla.exception.LLAInvokerRequestException;
import com.qc.agent.lla.model.LLAResponse;
import org.apache.commons.lang3.StringUtils;

/**
 *
 *
 * <AUTHOR>
 * @date 2025/3/6 17:17:23
 *
 */
public class CozeLLAConverter implements ILLAConverter
{
    @Override
    public LLAResponse convert(Object message)
    {
        String data = (String) message;
        boolean stop = false;
        StringBuilder sb = new StringBuilder();
        if(StringUtils.isNotEmpty(data)){
            JSONObject jsonObject =  extractContentFromLine(data);
            if("done".equals(jsonObject.getString("event"))){
                stop  = true;
            }else{
                JSONObject messageObj = jsonObject.getJSONObject("message");
                if("answer".equals(messageObj.getString("type"))){
                    sb.append(messageObj.getString("content"));
                }
            }
        }
        return LLAResponse.builder().content(sb.toString()).stop(stop).build();
    }

    private JSONObject extractContentFromLine(String line) {
         String dataPrefix = "data:{";

        if (line.startsWith(dataPrefix)) {
            return JSONObject.parseObject(line.substring("data:".length()));
        }else{
            JSONObject result = JSONObject.parseObject(line);
            if(result.containsKey("code")){
                throw new LLAInvokerRequestException(result.getString("message"));
            }
        }

        return null;
    }
}
