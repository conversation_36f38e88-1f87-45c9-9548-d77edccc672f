package com.qc.agent.app.agent.controller;

import javax.annotation.Resource;

import com.qc.agent.platform.register.UserManager;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.qc.agent.app.agent.model.query.QcAiCustomerInsightAssistantQuery;
import com.qc.agent.app.agent.service.CustomerInsightService;
import com.qc.agent.common.core.Message;

/**
 * 客户洞察助手控制器
 * 
 * <AUTHOR>
 * @since 1.0.0
 * @version 1.0.0
 */
@RestController
@RequestMapping("/ai-agent/assistant/customer-insight")
public class CustomerInsightAssistantController {

    @Resource
    private CustomerInsightService customerInsightService;

    /**
     * 客户洞察意图识别
     * 
     * @param query 客户洞察查询参数
     * @return 意图识别结果
     */
    @PostMapping("/intent")
    public Message intent(@RequestBody QcAiCustomerInsightAssistantQuery query) {
        return Message.of().ok().data(customerInsightService.intent(query));
    }

    /**
     * 查询洞察可用选项（维度、数据源等）
     * 返回所有可用的配置选项，不根据模式进行限制
     */
    @PostMapping("/available-options")
    public Message getAvailableOptions() {
        return Message.of().ok().data(customerInsightService.getAvailableOptions());
    }

    /**
     * 查询某个门店的洞察报告
     */
    @PostMapping("/report")
    public Message getReport(@RequestBody QcAiCustomerInsightAssistantQuery query) {
        return Message.of().ok().data(customerInsightService.getReport(query));
    }

}