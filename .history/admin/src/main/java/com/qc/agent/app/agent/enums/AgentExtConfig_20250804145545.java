package com.qc.agent.app.agent.enums;

/**
 * 智能体扩展配置枚举类
 * 用于定义Agent的扩展配置项的键名和描述
 */
public enum AgentExtConfig {

    /**
     * 商品话术助手
     */
    SHOW_IN_VISIT_TASK("showInVisitTask", "商品话术助手", "是否在拜访任务页面展示 1-展示，0-不展示 (商品话术助手)"),
    salesIntoRecommendInterface("salesIntoRecommendInterface", "商品话术助手", "买进商品推荐比对数据源 1-有效订单 2-铺货记录 3-销量上报 4-库存上报"),


    /**
     * 客户洞察助手
     */
    insightShowInVisitTask("insightShowInVisitTask", "客户洞察助手", "是否在拜访任务页面展示 1-展示，0-不展示 (客户洞察助手)"),
    insightShowOnCustomerDetail("showOnCustomerDetail", "客户洞察助手", "是否在客户详情页展示 1 -展示，0-不展示 (客户洞察助手)");
    

    private final String key;
    private final String agentName;
    private final String description;

    AgentExtConfig(String key, String agentName, String description) {
        this.key = key;
        this.agentName = agentName;
        this.description = description;
    }

    public String getKey() {
        return key;
    }

    public String getAgentId() {
        return agentName;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据键名查找对应的枚举值
     *
     * @param key 配置键名
     * @return 对应的枚举值，如果未找到则返回null
     */
    public static AgentExtConfig fromKey(String key) {
        for (AgentExtConfig config : AgentExtConfig.values()) {
            if (config.getKey().equals(key)) {
                return config;
            }
        }
        return null;
    }

    /**
     * 根据名称查找对应的枚举值
     *
     * @param agentName 配置名称
     * @return 对应的枚举值，如果未找到则返回null
     */
    public static AgentExtConfig fromName(String agentName) {
        for (AgentExtConfig config : AgentExtConfig.values()) {
            if (config.getAgentId().equals(agentName)) {
                return config;
            }
        }
        return null;
    }
}
