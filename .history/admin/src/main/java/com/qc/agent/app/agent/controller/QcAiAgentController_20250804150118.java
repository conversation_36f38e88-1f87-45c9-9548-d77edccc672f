package com.qc.agent.app.agent.controller;

import com.google.common.collect.ImmutableMap;
import com.qc.agent.app.agent.enums.AgentExtConfig;
import com.qc.agent.app.agent.model.dto.QcAiAgentPublishDTO;
import com.qc.agent.app.agent.model.dto.QcAiAgentSaveDTO;
import com.qc.agent.app.agent.model.po.QcAiAgent;
import com.qc.agent.app.agent.model.query.QcAiAgentBaseQuery;
import com.qc.agent.app.agent.model.query.QcAiAgentQuery;
import com.qc.agent.app.agent.model.vo.AgentsClientQueryVO;
import com.qc.agent.app.agent.model.vo.QcAiAgentDetailVO;
import com.qc.agent.app.agent.model.vo.QcAiAgentVO;
import com.qc.agent.app.agent.service.QcAiAgentService;
import com.qc.agent.common.core.Message;
import com.qc.agent.common.core.RequestHolder;
import com.qc.agent.common.core.TenantUser;
import com.qc.agent.common.exception.BizException;
import com.qc.agent.platform.datasource.model.DatasourceConfig;
import com.qc.agent.platform.datasource.service.DatasourceService;
import com.qc.agent.platform.register.UserManager;
import com.qc.agent.platform.register.service.TenantRegisterService;
import com.qc.agent.platform.user.service.AppServerUserService;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@RestController
@RequestMapping("/ai-agent/agents")
public class QcAiAgentController {

    @Resource
    private QcAiAgentService qcAiAgentService;

    @Resource
    private AppServerUserService appServerUserService;

    @Resource
    private TenantRegisterService tenantRegisterService;

    @Resource
    DatasourceService datasourceService;

    /**
     * 查询智能体详情
     *
     * @return
     */
    @GetMapping("/{id}")
    public Message queryAgentDetail(@PathVariable Long id) {
        QcAiAgentDetailVO data = qcAiAgentService.queryAgentDetail(id);
        return Message.of().ok().data(data);
    }

    /**
     * 查询内置智能体
     *
     * @return
     */
    @GetMapping("/query-internal")
    public Message queryInternalAgents() {
        Long deptId = appServerUserService.selectDeptIdByUserId(RequestHolder.getThreadLocalUser().getUserId());
        QcAiAgentBaseQuery query = new QcAiAgentBaseQuery();
        query.setDeptIdList(appServerUserService.getParentDeptIds(deptId));
        query.setEnable("1");
        List<QcAiAgentVO> data = qcAiAgentService.queryInternalAgents(query);
        return Message.of().ok().data(data);
    }

    /**
     * 查询内置智能体
     *
     * @return
     */
    @GetMapping("/query-internal-web")
    public Message queryInternalAgentsWeb() {
        Long deptId = appServerUserService.selectDeptIdByUserId(RequestHolder.getThreadLocalUser().getUserId());
        QcAiAgentBaseQuery query = new QcAiAgentBaseQuery();
        query.setDeptIdList(appServerUserService.getParentDeptIds(deptId));
        List<QcAiAgentVO> data = qcAiAgentService.queryInternalAgents(query);
        return Message.of().ok().data(data);
    }

    /**
     * 查询自定义智能体
     *
     * @param name        智能体名称
     * @param publishFlag 1；已发布，0：未发布
     * @return
     */
    @GetMapping("/query-custom")
    public Message queryCustomAgents(String name, String publishFlag) {
        List<QcAiAgentVO> data = qcAiAgentService.queryCustomAgents(name, publishFlag);
        return Message.of().ok().data(data);
    }

    /**
     * 查询最近使用智能体
     *
     * @return
     */
    @PostMapping("/client-query")
    public Message queryClientAgents() {
        if (!tenantRegisterService.checkIsRegister()) {
            tenantRegisterService.register(UserManager.getTenantUser());
        }
        Long deptId = appServerUserService.selectDeptIdByUserId(RequestHolder.getThreadLocalUser().getUserId());
        List<Long> deptIds = appServerUserService.getParentDeptIds(deptId);
        AgentsClientQueryVO data = qcAiAgentService.queryClientAgents(deptIds);
        return Message.of().ok().data(data);
    }

    /**
     * 查询所有的智能体
     *
     * @return
     */
    @PostMapping("/query-all")
    public Message queryAllAgents(@RequestBody QcAiAgentQuery query) {
        query.setUserId(RequestHolder.getThreadLocalUser().getUserId());
        Long deptId = appServerUserService.selectDeptIdByUserId(query.getUserId());
        query.setDeptIdList(appServerUserService.getParentDeptIds(deptId));
        List<QcAiAgentVO> data = qcAiAgentService.queryAllAgents(query);
        return Message.of().ok().data(data);
    }

    /**
     * 探索智能体
     *
     * @param query
     * @return
     */
    @PostMapping("/seek")
    public Message seekAgents(@RequestBody QcAiAgentQuery query) {
        if (StringUtils.isEmpty(query.getDeptId())) {
            Long deptId = appServerUserService.selectDeptIdByUserId(RequestHolder.getThreadLocalUser().getUserId());
            query.setDeptId(deptId);
        }
        query.setDeptIdList(appServerUserService.getParentDeptIds(query.getDeptId()));
        List<QcAiAgentVO> data = qcAiAgentService.seekAgents(query);
        return Message.of().ok().data(data);
    }

    /**
     * 保存智能体配置
     *
     * @param agent
     * @return
     */
    @PostMapping("/save")
    public Message save(@RequestBody QcAiAgentSaveDTO agent) {
        QcAiAgent save = qcAiAgentService.save(agent);
        return Message.of().ok().data(ImmutableMap.of("agentId", Objects.toString(save.getId())));
    }

    /**
     * 发布智能体配置
     *
     * @param
     * @return
     */
    @PostMapping("/{id}/publish")
    public Message publish(@RequestBody QcAiAgentPublishDTO publishDTO) {
        if (CollectionUtils.isEmpty(publishDTO.getUserList()) && CollectionUtils.isEmpty(publishDTO.getDeptList())) {
            throw new BizException("授权范围不能为空");
        }
        qcAiAgentService.publish(publishDTO);
        return Message.of().ok();
    }

    /**
     * 发布智能体配置
     *
     * @param
     * @return
     */
    @PostMapping("/authority")
    public Message authority(@RequestBody QcAiAgentPublishDTO publishDTO) {
        if (CollectionUtils.isEmpty(publishDTO.getUserList()) && CollectionUtils.isEmpty(publishDTO.getDeptList())) {
            throw new BizException("授权范围不能为空");
        }
        qcAiAgentService.authorityDetail(publishDTO);
        return Message.of().ok();
    }

    /**
     * 取消发布智能体配置
     *
     * @param id
     * @return
     */
    @PostMapping("/{id}/deactivate")
    public Message deactivate(@PathVariable Long id, Long userId, String userName) {
        qcAiAgentService.deactivate(id, userId, userName);
        return Message.of().ok();
    }

    /**
     * 启用
     *
     * @param id
     * @return
     */
    @PostMapping("/{id}/enable")
    public Message enable(@PathVariable Long id, Long userId, String userName) {
        qcAiAgentService.enable(id, userId, userName);
        return Message.of().ok();
    }

    /**
     * 停用
     *
     * @param id
     * @return
     */
    @PostMapping("/{id}/disable")
    public Message disable(@PathVariable Long id, Long userId, String userName) {
        qcAiAgentService.disable(id, userId, userName);
        return Message.of().ok();
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @PostMapping("/{id}/delete")
    public Message delete(@PathVariable Long id, Long userId, String userName) {
        qcAiAgentService.delete(id, userId, userName);
        return Message.of().ok();
    }

    /**
     * 为现有租户生成预置问题
     *
     * @param tenantId
     * @return
     */
    @PostMapping("/generate-leading-questions")
    public Message generateLeadingQuestions(@RequestParam(required = false) Long tenantId) {
        qcAiAgentService.generateLeadingQuestionsForExistingTenants(tenantId);
        String message = tenantId != null ? "开始为租户 " + tenantId + " 生成预置问题" : "开始为所有租户生成预置问题";
        return Message.of().ok(message);
    }

    /**
     * 为现有租户初始化客户洞察模式配置
     *
     * @param tenantId 租户ID（可选，不传则为所有租户初始化）
     * @return
     */
    @PostMapping("/init-insight-mode")
    public Message initInsightMode(@RequestParam(required = false) Long tenantId) {
        qcAiAgentService.initInsightModeForExistingTenants(tenantId);
        String message = tenantId != null ? "开始为租户 " + tenantId + " 初始化客户洞察模式配置" : "开始为所有租户初始化客户洞察模式配置";
        return Message.of().ok(message);
    }

    /**
     * 出现AI助理入口的条件：该人员有分配AI助理产品+商品话术助手授权+商品话术助手中开启在拜访任务页面显示
     * 出现AI助理入口的条件：该人员有分配AI助理产品+商品话术助手/客户洞察助手授权+商品话术助手中开启在拜访任务页面显示/客户洞察助手中开启在拜访任务页面显示
     * 
     * @return
     */
    @GetMapping("/showInVisitTask")
    public Message showInVisitTask(@RequestParam(value = "tenantId", required = false) Long tenantId,
            @RequestParam(value = "userId", required = false) Long userId) {
        if (tenantId == null || userId == null) {
            return Message.of().data(ImmutableMap.of(AgentExtConfig.SHOW_IN_VISIT_TASK.getKey(), false)).error("租户ID或用户ID不能为空");
        }
        TenantUser tenantUser = new TenantUser();
        tenantUser.setUserId(userId);
        tenantUser.setTenantId(tenantId);
        RequestHolder.setThreadLocalUser(tenantUser);


        DatasourceConfig datasourceConfig = datasourceService.getDatasourceConfig(tenantId);
        if (datasourceConfig == null) {
            return Message.of().data(ImmutableMap.of(AgentExtConfig.SHOW_IN_VISIT_TASK.getKey(), false)).ok("未找到数据源配置");
        }
        Long deptId = appServerUserService.selectDeptIdByUserId(userId);
        List<Long> deptIds = appServerUserService.getParentDeptIds(deptId);
        //商品话术助手是否开启
        Map<String, Boolean> map = new HashMap<>();
        boolean hasAiAssistant = qcAiAgentService.showInVisitTask(deptIds);
        map.put()
        //客户洞察助手是否在拜访任务页面显示
        boolean hasInsight = qcAiAgentService.insightShowInVisitTask(deptIds);


        return Message.of().ok().data(ImmutableMap.of(AgentExtConfig.SHOW_IN_VISIT_TASK.getKey(), hasAiAssistant));
    }
}
