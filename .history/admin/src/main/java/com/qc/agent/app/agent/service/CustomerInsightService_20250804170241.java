package com.qc.agent.app.agent.service;

import java.util.Map;

import com.qc.agent.app.agent.model.dto.InsightConfigWorkspaceDTO;
import com.qc.agent.app.agent.model.query.QcAiCustomerInsightAssistantQuery;
import com.qc.agent.app.agent.model.vo.InsightConfigWorkspaceVO;

/**
 * 客户洞察服务接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 * @version 1.0.0
 */
public interface CustomerInsightService {

    /**
     * 客户洞察意图识别
     *
     * @param query 查询参数
     * @return 意图识别结果
     */
    Map<String, Object> intent(QcAiCustomerInsightAssistantQuery query);

    /**
     * 根据模式获取指定Agent的完整配置工作区
     * (包括当前配置和所有可用选项)
     *
     * @param agentId     智能体ID
     * @param insightMode 洞察模式：0-客户模式，1-终端经销商模式
     * @return 配置工作区视图对象
     */
    InsightConfigWorkspaceVO getWorkspaceByMode(Long agentId, String insightMode);

    /**
     * 根据模式全量保存指定Agent的配置
     *
     * @param workspaceDTO 包含agentId和完整配置列表的数据
     * @param insightMode  洞察模式：0-客户模式，1-终端经销商模式
     * @return 是否成功
     */
    boolean saveWorkspaceByMode(InsightConfigWorkspaceDTO workspaceDTO, String insightMode);

    /**
     * 获取所有可用的洞察配置选项（维度、数据源等）
     *
     * @return 洞察可用选项
     */
    InsightConfigWorkspaceVO.AvailableOptions getAvailableOptions();

    Object getReport(QcAiCustomerInsightAssistantQuery query);
}