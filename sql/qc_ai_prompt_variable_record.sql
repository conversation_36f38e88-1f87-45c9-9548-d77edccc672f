-- 提示词变量记录表
CREATE TABLE qc_ai_prompt_variable_record (
    id BIGSERIAL PRIMARY KEY,
    status CHAR(1) DEFAULT '1',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modify_time TIMESTAMP,
    
    -- 关联信息
    config_id BIGINT NOT NULL,                    -- 配置ID，关联qc_ai_dimension_config.id
    dimension_code VARCHAR(20) NOT NULL,          -- 维度代码
    prompt_type VARCHAR(100) NOT NULL,            -- 提示词类型
    prompt_fragment_id BIGINT NOT NULL,           -- 提示词片段ID，关联qc_ai_prompt_fragment.id
    
    -- 变量信息
    variable_key VARCHAR(100) NOT NULL,           -- 变量的key（如OrderSatisfactionRate）
    variable_order INTEGER NOT NULL,              -- 变量在该片段中的出现顺序
    variable_value TEXT,                          -- 业务查询时设置的具体数据值
    
    -- 索引约束
    CONSTRAINT uk_config_fragment_variable_order UNIQUE (config_id, prompt_fragment_id, variable_order)
);

-- 创建索引
CREATE INDEX idx_prompt_variable_config_dimension ON qc_ai_prompt_variable_record(config_id, dimension_code);
CREATE INDEX idx_prompt_variable_fragment ON qc_ai_prompt_variable_record(prompt_fragment_id);
CREATE INDEX idx_prompt_variable_key ON qc_ai_prompt_variable_record(variable_key);

-- 表注释
COMMENT ON TABLE qc_ai_prompt_variable_record IS '提示词变量记录表';
COMMENT ON COLUMN qc_ai_prompt_variable_record.id IS '主键ID';
COMMENT ON COLUMN qc_ai_prompt_variable_record.status IS '状态：1-有效，0-无效';
COMMENT ON COLUMN qc_ai_prompt_variable_record.config_id IS '配置ID，关联qc_ai_dimension_config.id';
COMMENT ON COLUMN qc_ai_prompt_variable_record.dimension_code IS '维度代码';
COMMENT ON COLUMN qc_ai_prompt_variable_record.prompt_type IS '提示词类型';
COMMENT ON COLUMN qc_ai_prompt_variable_record.prompt_fragment_id IS '提示词片段ID';
COMMENT ON COLUMN qc_ai_prompt_variable_record.variable_key IS '变量的key';
COMMENT ON COLUMN qc_ai_prompt_variable_record.variable_order IS '变量在片段中的出现顺序';
COMMENT ON COLUMN qc_ai_prompt_variable_record.variable_value IS '业务查询时的具体数据值';
