-- 提示词变量记录表（配置表）
CREATE TABLE qc_ai_prompt_variable_record (
    id BIGSERIAL PRIMARY KEY,
    status CHAR(1) DEFAULT '1',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modify_time TIMESTAMP,

    -- 关联信息
    config_id BIGINT,                             -- 配置ID，关联qc_ai_dimension_config.id
    dimension_code VARCHAR(20),                   -- 维度代码
    prompt_type VARCHAR(100),                     -- 提示词类型
    prompt_fragment_id BIGINT,                    -- 提示词片段ID，关联qc_ai_prompt_fragment.id

    -- 变量信息
    variable_key VARCHAR(100),                    -- 变量的key（如OrderSatisfactionRate）
    variable_order INTEGER                        -- 变量在该片段中的出现顺序
);

-- 创建索引
CREATE INDEX idx_prompt_variable_config_dimension ON qc_ai_prompt_variable_record(config_id, dimension_code);
CREATE INDEX idx_prompt_variable_fragment ON qc_ai_prompt_variable_record(prompt_fragment_id);
CREATE INDEX idx_prompt_variable_key ON qc_ai_prompt_variable_record(variable_key);

-- 表注释
COMMENT ON TABLE qc_ai_prompt_variable_record IS '提示词变量记录表';
COMMENT ON COLUMN qc_ai_prompt_variable_record.id IS '主键ID';
COMMENT ON COLUMN qc_ai_prompt_variable_record.status IS '状态：1-有效，0-无效';
COMMENT ON COLUMN qc_ai_prompt_variable_record.config_id IS '配置ID，关联qc_ai_dimension_config.id';
COMMENT ON COLUMN qc_ai_prompt_variable_record.dimension_code IS '维度代码';
COMMENT ON COLUMN qc_ai_prompt_variable_record.prompt_type IS '提示词类型';
COMMENT ON COLUMN qc_ai_prompt_variable_record.prompt_fragment_id IS '提示词片段ID';
COMMENT ON COLUMN qc_ai_prompt_variable_record.variable_key IS '变量的key';
COMMENT ON COLUMN qc_ai_prompt_variable_record.variable_order IS '变量在片段中的出现顺序';

-- 客户洞察变量数据表（实际数据表）
CREATE TABLE qc_ai_customer_variable_data (
    id BIGSERIAL PRIMARY KEY,
    status CHAR(1) DEFAULT '1',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modify_time TIMESTAMP,
    customer_id BIGINT NOT NULL,                  -- 客户ID
    prompt_variable_record_id BIGINT NOT NULL,    -- 提示词变量记录ID，关联qc_ai_prompt_variable_record表
    conversation_id BIGINT NOT NULL,              -- 对话ID，关联qc_ai_agent_conversation表
    variable_value TEXT NOT NULL                  -- 变量值
);

-- 创建索引
CREATE INDEX idx_customer_variable_customer ON qc_ai_customer_variable_data(customer_id);
CREATE INDEX idx_customer_variable_conversation ON qc_ai_customer_variable_data(conversation_id);
CREATE INDEX idx_customer_variable_record ON qc_ai_customer_variable_data(prompt_variable_record_id);
CREATE INDEX idx_customer_variable_customer_conversation ON qc_ai_customer_variable_data(customer_id, conversation_id);

-- 表注释
COMMENT ON TABLE qc_ai_customer_variable_data IS '客户洞察变量数据表';
COMMENT ON COLUMN qc_ai_customer_variable_data.id IS '客户洞察变量数据ID，主键';
COMMENT ON COLUMN qc_ai_customer_variable_data.status IS '逻辑删除状态：1-有效，0-无效';
COMMENT ON COLUMN qc_ai_customer_variable_data.create_time IS '创建时间';
COMMENT ON COLUMN qc_ai_customer_variable_data.modify_time IS '修改时间';
COMMENT ON COLUMN qc_ai_customer_variable_data.customer_id IS '客户ID';
COMMENT ON COLUMN qc_ai_customer_variable_data.prompt_variable_record_id IS '提示词变量记录ID，关联qc_ai_prompt_variable_record表';
COMMENT ON COLUMN qc_ai_customer_variable_data.conversation_id IS '对话ID，关联qc_ai_agent_conversation表';
COMMENT ON COLUMN qc_ai_customer_variable_data.variable_value IS '变量值';
