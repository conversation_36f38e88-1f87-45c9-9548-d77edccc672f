package com.qc.agent.jdbc.datasource;

import com.qc.agent.jdbc.datasource.builder.DataSourceBuilder;
import com.qc.agent.jdbc.datasource.connection.JdbcConnectionBuilder;
import com.qc.agent.jdbc.datasource.router.DataSourceRouter;
import com.qc.agent.jdbc.datasource.router.RouterKey;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.jdbc.datasource.DelegatingDataSource;

import javax.annotation.Resource;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.Optional;

/**
 * 数据源切换
 * <AUTHOR>
 */
public abstract class DataSourceSwitcher extends DelegatingDataSource
{
   static final Logger LOG = LoggerFactory.getLogger(DataSourceSwitcher.class);
    // 数据源属性构建器注册器
    @Resource
    DataSourceBuilder dataSourceBuilder;

    @Lazy
    @Resource
    JdbcConnectionBuilder jdbcConnectionBuilder;

    /**
     * 属性加载之后检测是否有属性构建器
     */
    @Override
    public void afterPropertiesSet()
    {
        if(dataSourceBuilder.hasPropertyBuilder()){
            throw new IllegalArgumentException("数据模板未配置");
        }
    }

    /**
     * 子类需指定当前路由Key
     * @return
     */
    public abstract RouterKey determineCurrentRouterKey();

    /**
     * 获取数据源
     * @return
     * @throws SQLException
     */
    @Override
    public Connection getConnection() throws SQLException
    {
        /**
         * 1.获取路由Key
         */
        RouterKey currentRouterKey  = DataSourceRouter.getCurrentRouterKey();
        // 指定路由key优先级高于子类实现的
        if(Optional.ofNullable(currentRouterKey).isEmpty()){
            currentRouterKey = determineCurrentRouterKey();
        }

        if(Optional.ofNullable(currentRouterKey).isEmpty()){
            throw new IllegalArgumentException("currentRouterKey is null");
        }

        /**
         * 2.获取连接
         */
        if(currentRouterKey.isJdbc()){
            LOG.info("use jdbc connection");
            return jdbcConnectionBuilder.getConnection(currentRouterKey);
        }else{
            LOG.info("use dataSource connection");
            return dataSourceBuilder.build(currentRouterKey).getConnection();
        }


    }
}
