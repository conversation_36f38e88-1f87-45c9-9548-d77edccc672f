package com.qc.agent.jdbc.datasource.pool;

import com.qc.agent.jdbc.datasource.model.DataSourcePoolType;
import com.qc.agent.jdbc.datasource.model.DataSourceProperty;

import javax.sql.DataSource;

/**
 * 数据源池接口
 */
public interface DataSourcePool
{
    /**
     * 指定池类型
     * @return
     */
    DataSourcePoolType determinePoolType();

    /**
     * 构建池
     * @param property
     * @return
     */
    DataSource buildPool(DataSourceProperty property);
}
