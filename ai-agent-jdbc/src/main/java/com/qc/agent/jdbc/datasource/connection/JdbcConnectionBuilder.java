package com.qc.agent.jdbc.datasource.connection;

import com.qc.agent.jdbc.datasource.builder.DataSourcePropertyBuilder;
import com.qc.agent.jdbc.datasource.dialect.ConnectionDialectFactory;
import com.qc.agent.jdbc.datasource.model.DataSourceProperty;
import com.qc.agent.jdbc.datasource.register.DataSourceConfigRegister;
import com.qc.agent.jdbc.datasource.router.RouterKey;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Connection;
import java.util.Optional;

@Component
public class JdbcConnectionBuilder {

    // 数据源属性构建器注册器
    @Resource
    DataSourceConfigRegister dataSourceConfigRegister;

    public static Connection getConnection(JdbcConnectionProperty property) {
        return ConnectionDialectFactory.getIntstance(property.getDialect()).getConnection(property);
    }

    public Connection getConnection(RouterKey currentRouterKey) {
        /**
         * 1.获取指定路由的属性构建器
         */
        DataSourcePropertyBuilder builder = dataSourceConfigRegister.getPropertyBuilder(currentRouterKey.getDataSourceName());
        /**
         * 2. 获取数据源配置
         */
        DataSourceProperty property = builder.buildProperty();
        if(Optional.ofNullable(property).isEmpty()){
            throw new IllegalArgumentException("DataSource property is null");
        }

        if(currentRouterKey.isSlave()){
            if(Optional.ofNullable(property.getSlaves()).isPresent() && property.getSlaves().size() > 0){
                property = property.getSlaves().get(0);
            }
        }

        JdbcConnectionProperty jdbcConnectionProperty = property.toJdbcConnectionProperty();
       return ConnectionDialectFactory.getIntstance(jdbcConnectionProperty.getDialect()).getConnection(jdbcConnectionProperty);


    }
}
