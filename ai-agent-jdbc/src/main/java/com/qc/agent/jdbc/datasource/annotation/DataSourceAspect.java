package com.qc.agent.jdbc.datasource.annotation;

import com.qc.agent.jdbc.datasource.DataSourceHolder;
import com.qc.agent.jdbc.datasource.router.DataSourceRouter;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

/**
 *  数据源切换切面
 * <AUTHOR>
 */
@Aspect
@Component
public class DataSourceAspect
{
    /**
     *  切面
     */
    @Pointcut("@annotation(com.qc.agent.jdbc.datasource.annotation.SwitchDataSource)")
    public void dataSourcePointCut() {
    }

    /**
     * 切面前置
     * @param joinPoint
     * @param switchDataSource
     */
    @Before("dataSourcePointCut() && @annotation(switchDataSource)")
    public void switchDataSource(JoinPoint joinPoint, SwitchDataSource switchDataSource) {
        DataSourceRouter.switchDataSource(switchDataSource);
    }

    /**
     * 切面后置
     * @param joinPoint
     */
    @After("dataSourcePointCut()")
    public void restoreDataSource(JoinPoint joinPoint) {
        // 清除当前请求路由Key
        DataSourceHolder.clearRouterKey();
    }

}
