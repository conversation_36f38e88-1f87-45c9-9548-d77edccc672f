package com.qc.agent.jdbc.datasource.model;

import com.qc.agent.jdbc.datasource.connection.JdbcConnectionProperty;
import com.qc.agent.jdbc.datasource.exception.JdbcException;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.List;

/**
 *  数据源配置
 * <AUTHOR>
 */
public class DataSourceProperty
{
    // 数据源类型
    private DataSourcePoolType type;

    // 驱动URL
    private String url;

    // 用户名
    private String userName;

    // 密码
    private String password;

    // 驱动
    private String driver;

    //  最大连接数
    private int maxConnectionCount;

    // 最小连接数
    private int minConnectionCount;

    // 连接的最大生命周期
    private long maxLifetime;

    // 从库列表
    List<DataSourceProperty> slaves;

    public DataSourcePoolType getType()
    {
        return type;
    }

    public JdbcConnectionProperty toJdbcConnectionProperty(){
        JdbcConnectionProperty jdbcConnectionProperty = new JdbcConnectionProperty();
        try
        {
            URI uri = new URI(url.substring(5));
            jdbcConnectionProperty.setHost(uri.getHost());
            jdbcConnectionProperty.setPort(uri.getPort());
        }
        catch(URISyntaxException e)
        {
            throw new JdbcException("Failed to resolve the URL. url=" + url,e);
        }
        jdbcConnectionProperty.setUserName(userName);
        jdbcConnectionProperty.setPassword(password);
        return  jdbcConnectionProperty;
    }

    public void addSlave(DataSourceProperty slave){
        if(slaves == null){
            slaves = new ArrayList<>();
        }
        slaves.add(slave);
    }


    public void setType(String type)
    {
        this.type = DataSourcePoolType.valueOf(type);
    }
    public String getUrl()
    {
        return url;
    }
    public void setUrl(String url)
    {
        this.url = url;
    }
    public String getUserName()
    {
        return userName;
    }
    public void setUserName(String userName)
    {
        this.userName = userName;
    }
    public String getPassword()
    {
        return password;
    }
    public void setPassword(String password)
    {
        this.password = password;
    }
    public String getDriver()
    {
        return driver;
    }
    public void setDriver(String driver)
    {
        this.driver = driver;
    }
    public int getMaxConnectionCount()
    {
        return maxConnectionCount;
    }
    public void setMaxConnectionCount(int maxConnectionCount)
    {
        this.maxConnectionCount = maxConnectionCount;
    }
    public int getMinConnectionCount()
    {
        return minConnectionCount;
    }
    public void setMinConnectionCount(int minConnectionCount)
    {
        this.minConnectionCount = minConnectionCount;
    }
    public long getMaxLifetime()
    {
        return maxLifetime;
    }
    public void setMaxLifetime(long maxLifetime)
    {
        this.maxLifetime = maxLifetime;
    }

    public List<DataSourceProperty> getSlaves()
    {
        return slaves;
    }
    public void setSlaves(List<DataSourceProperty> slaves)
    {
        this.slaves = slaves;
    }


}
