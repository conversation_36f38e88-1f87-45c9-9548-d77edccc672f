package com.qc.agent.jdbc.datasource.router;

import com.qc.agent.jdbc.datasource.DataSourceHolder;
import com.qc.agent.jdbc.datasource.annotation.SwitchDataSource;

/**
 *  数据源路由器
 * <AUTHOR>
 */
public class DataSourceRouter
{

    /**
     * 注解切换数据源
     * @param switchDataSource
     */
   public static void switchDataSource(SwitchDataSource switchDataSource){
       DataSourceHolder.setRouterKey(
               RouterKey.build(switchDataSource.name(),switchDataSource.type(),switchDataSource.connType()));
   }

    /**
     * 路由key切换数据源
     * @param routerKey
     */
   public static void switchDataSource(RouterKey routerKey){
       DataSourceHolder.setRouterKey(routerKey);
   }

    /**
     * 获取当前路由key
     * @return
     */
   public static RouterKey getCurrentRouterKey(){
       return DataSourceHolder.getRouterKey();
   }


}
