package com.qc.agent.jdbc.datasource.dialect;

import com.qc.agent.jdbc.datasource.connection.JdbcConnectionProperty;
import org.springframework.util.StringUtils;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;

public interface ConnectionDialect {

    DialectEnum determineDialectName();

    String determineDialectDriver();

    default Connection getConnection(JdbcConnectionProperty property){
        String driverUrl = String.format("jdbc:%s://%s:%s/%s?useUnicode=true&characterEncoding=UTF-8",new Object[]{
                determineDialectName().name,
                property.getHost(),
                property.getPort(),
                property.getDatabaseName() == null ? property.getUserName() : property.getDatabaseName()
        });
        String driver = determineDialectDriver();
        if(!StringUtils.hasText(driver)){
            throw new IllegalArgumentException("Driver not found");
        }
        try
        {
            Class.forName(driver);
            return DriverManager.getConnection(driverUrl, property.getUserName(), property.getPassword());
        }
        catch(ClassNotFoundException e)
        {
            throw new IllegalArgumentException("Failed to load driver class: "+ driver, e);
        }
        catch(SQLException e)
        {
            throw new IllegalArgumentException("Failed to connect to database", e);
        }

    }

}
