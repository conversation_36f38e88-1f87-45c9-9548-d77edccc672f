package com.qc.agent.jdbc.datasource.pool.impl;

import com.qc.agent.jdbc.datasource.model.DataSourcePoolType;
import com.qc.agent.jdbc.datasource.model.DataSourceProperty;
import com.qc.agent.jdbc.datasource.pool.DataSourcePool;
import com.zaxxer.hikari.HikariDataSource;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;

/**
 * Hikari连接池实现
 * <AUTHOR>
 */
@Component
public class HikariPoolImpl implements DataSourcePool
{

    @Override
    public DataSourcePoolType determinePoolType()
    {
        return DataSourcePoolType.HIKARI;
    }
    @Override
    public DataSource buildPool(DataSourceProperty property)
    {
        HikariDataSource dataSource = new HikariDataSource();
        dataSource.setJdbcUrl(property.getUrl());
        dataSource.setUsername(property.getUserName());
        dataSource.setPassword(property.getPassword());
        dataSource.setDriverClassName(property.getDriver());
        dataSource.setMaximumPoolSize(property.getMaxConnectionCount());
        dataSource.setMinimumIdle(property.getMinConnectionCount());
        dataSource.setMaxLifetime(property.getMaxLifetime());
        return dataSource;
    }
}
