package com.qc.agent.jdbc.datasource.register;

import com.qc.agent.jdbc.datasource.builder.DataSourcePropertyBuilder;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 数据源属性构建器注册
 * <AUTHOR>
 */

@Configuration
public class DataSourceConfigRegister implements ApplicationContextAware
{
    Map<String, DataSourcePropertyBuilder> dataSourceBuilderMap = new HashMap<>();

    /**
     * 初始化数据源属性构建器
     * @param applicationContext
     * @throws BeansException
     */
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException
    {
        applicationContext.getBeansOfType(DataSourcePropertyBuilder.class)
                .values()
                .forEach(dataSourceBuilder -> dataSourceBuilderMap.put(dataSourceBuilder.determineDataSourceName(), dataSourceBuilder));
    }


    /**
     * 获取指定类型的数据源属性构建器
     * @param dataSourceName
     * @return
     */
    public DataSourcePropertyBuilder getPropertyBuilder(String dataSourceName){
        DataSourcePropertyBuilder dataSourcePropertyBuilder;
        // 如果未指定数据名称，则获取默认的数据配置构建器
        if(Optional.ofNullable(dataSourceName).isEmpty()){
            dataSourcePropertyBuilder = getDefaultPropertyBuilder();
        }else{
            dataSourcePropertyBuilder = dataSourceBuilderMap.get(dataSourceName);
        }
        if(Optional.ofNullable(dataSourcePropertyBuilder).isEmpty()){
            throw new IllegalArgumentException("未找到指定类型的数据源属性构建器");
        }
        return dataSourcePropertyBuilder;
    }

    /**
     *  判断是否包含指定类型的数据源属性构建器
     * @return
     */
    public boolean hasPropertyBuilder(){
        return Optional.ofNullable(dataSourceBuilderMap).map(Map::isEmpty).orElse(false);
    }

    /**
     *  获取默认的数据源属性构建器
     * @return
     */
    public DataSourcePropertyBuilder getDefaultPropertyBuilder(){
        Optional<DataSourcePropertyBuilder> first = dataSourceBuilderMap.values().stream()
                .filter(dataSourcePropertyBuilder -> dataSourcePropertyBuilder.determineDefault()).findFirst();
        return first.get();
    }

}
