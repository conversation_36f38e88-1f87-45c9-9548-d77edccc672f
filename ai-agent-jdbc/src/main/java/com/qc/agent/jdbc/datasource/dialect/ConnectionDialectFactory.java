package com.qc.agent.jdbc.datasource.dialect;

import com.qc.agent.jdbc.datasource.exception.JdbcException;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Configuration;

import java.util.Optional;

@Configuration
public class ConnectionDialectFactory implements ApplicationContextAware {

    static  ApplicationContext applicationContext;
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    public static ConnectionDialect getIntstance(DialectEnum dialect) {
        Optional<ConnectionDialect> first = applicationContext.getBeansOfType(ConnectionDialect.class).values()
                .stream().filter(dcd -> dcd.determineDialectName().equals(dialect)).findFirst();
        if(first.isPresent()){
            return first.get();
        }else{
            throw new JdbcException(dialect+" is not supported");
        }
    }
}

