package com.qc.agent.jdbc.datasource.pool;

import com.qc.agent.jdbc.datasource.model.DataSourcePoolType;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 * 数据源池工厂类
 * <AUTHOR>
 */
@Configuration
public class DataSourcePoolFactory implements ApplicationContextAware
{

    static Map<DataSourcePoolType, DataSourcePool> dataSourcePoolMap = new HashMap<>();

    // 初始化池实例
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException
    {
        applicationContext.getBeansOfType(DataSourcePool.class)
                .values()
                .forEach(dataSourcePool -> dataSourcePoolMap.put(dataSourcePool.determinePoolType(), dataSourcePool));
    }

    /**
     * 获取指定池类型的池实例
     * @param type
     * @return
     */
    public static DataSourcePool getInstance(DataSourcePoolType type)
    {
        return dataSourcePoolMap.get(type);
    }
}
