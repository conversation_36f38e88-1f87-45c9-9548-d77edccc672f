package com.qc.agent.jdbc.datasource.dialect.impl;

import com.qc.agent.jdbc.datasource.dialect.DialectEnum;
import com.qc.agent.jdbc.datasource.dialect.ConnectionDialect;
import org.springframework.stereotype.Component;

@Component
public class PostgresDialect implements ConnectionDialect {
    @Override
    public DialectEnum determineDialectName() {
        return DialectEnum.POSTGRES;
    }

    @Override
    public String determineDialectDriver() {
        return "org.postgresql.Driver";
    }
}
