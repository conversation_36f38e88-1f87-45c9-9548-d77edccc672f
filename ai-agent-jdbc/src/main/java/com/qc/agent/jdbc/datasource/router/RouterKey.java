package com.qc.agent.jdbc.datasource.router;

import com.qc.agent.jdbc.datasource.model.ConnectionType;
import com.qc.agent.jdbc.datasource.model.DataSourceType;

/**
 *  路由Key
 * <AUTHOR>
 */
public class RouterKey
{
    //数据源类型
    String dataSourceName;
    // 数据源集群类型
    DataSourceType dataSourceType;
    // 连接类型
    ConnectionType connectionType;

    public static RouterKey build(String dataSourceName, DataSourceType dataSourceType){

        return build(dataSourceName,dataSourceType,ConnectionType.POOL);
    }

    public static RouterKey build(String dataSourceName, DataSourceType dataSourceType,ConnectionType connectionType){
        RouterKey determineCurrentLookupKey = new RouterKey();
        determineCurrentLookupKey.setDataSourceName(dataSourceName);
        determineCurrentLookupKey.setDataSourceType(dataSourceType);
        determineCurrentLookupKey.setConnectionType(connectionType);
        return determineCurrentLookupKey;
    }

    public boolean isSlave(){
        return DataSourceType.SLAVE.equals(dataSourceType);
    }

    public boolean isJdbc(){
        return ConnectionType.JDBC.equals(connectionType);
    }

    public static RouterKey build(String dataSourceName){
        return build(dataSourceName,DataSourceType.PRIMARY);
    }

    public String getDataSourceName()
    {
        return dataSourceName;
    }
    public void setDataSourceName(String dataSourceName)
    {
        this.dataSourceName = dataSourceName;
    }
    public DataSourceType getDataSourceType()
    {
        return dataSourceType;
    }
    public void setDataSourceType(DataSourceType dataSourceType)
    {
        this.dataSourceType = dataSourceType;
    }

    public ConnectionType getConnectionType()
    {
        return connectionType;
    }
    public void setConnectionType(ConnectionType connectionType)
    {
        this.connectionType = connectionType;
    }

}
