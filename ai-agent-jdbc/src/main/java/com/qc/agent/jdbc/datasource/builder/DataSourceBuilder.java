package com.qc.agent.jdbc.datasource.builder;

import com.qc.agent.jdbc.datasource.exception.JdbcException;
import com.qc.agent.jdbc.datasource.model.DataSourceProperty;
import com.qc.agent.jdbc.datasource.pool.DataSourcePoolFactory;
import com.qc.agent.jdbc.datasource.register.DataSourceConfigRegister;
import com.qc.agent.jdbc.datasource.router.RouterKey;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 数据源构建器
 * <AUTHOR>
 */
@Component
public class DataSourceBuilder
{
    Logger logger = LoggerFactory.getLogger(DataSourceBuilder.class);
    // 数据源属性构建器注册器
    @Resource
    DataSourceConfigRegister dataSourceConfigRegister;
    // 数据源缓存
    Map<String,DataSource> dataSourceCache = new ConcurrentHashMap<>();


    /**
     * 根据指定数据源属性构建数据源
     * @param currentRouterKey
     * @return
     */
    public DataSource build(RouterKey currentRouterKey)
    {

        /**
         * 1.获取指定路由的属性构建器
         */
        DataSourcePropertyBuilder builder = dataSourceConfigRegister.getPropertyBuilder(currentRouterKey.getDataSourceName());

        /**
         * 2. 从缓存中获取数据源配置
         */
        String cacheKey = getCacheKey(currentRouterKey, builder);

        logger.info("DataSource is key:{}",cacheKey);

        DataSource dataSource =  dataSourceCache.get(cacheKey);
        if(Optional.ofNullable(dataSource).isPresent()){
            return dataSource;
        }


        /**
         * 3. 获取数据源配置
         */
        DataSourceProperty property = builder.buildProperty();
        if(Optional.ofNullable(property).isEmpty()){
            logger.error("DataSource property is null");
            throw new JdbcException("DataSource property is null");
        }

        /**
         * 4. 检测是否为从库数据源
         */
        if(currentRouterKey.isSlave()){
            if(Optional.ofNullable(property.getSlaves()).isPresent() && property.getSlaves().size() > 0){
                property = property.getSlaves().get(0);
            }else{
                throw new JdbcException("DataSource property slave is null");
            }
        }


        /**
         * 5.缓存中没有，则构建新的数据源
         */
        dataSource = DataSourcePoolFactory.getInstance(property.getType()).buildPool(property);

        /**
         * 6.新创建数据放到缓存集合中
         */
        dataSourceCache.put(cacheKey, dataSource);
        return dataSource;
    }

    /**
     * 获取缓存Key
     * @param currentRouterKey
     * @param builder
     * @return
     */
    private String getCacheKey(RouterKey currentRouterKey,DataSourcePropertyBuilder builder){
        String dataSourceName = currentRouterKey.getDataSourceName();
        if(!StringUtils.hasText(dataSourceName)){
            dataSourceName = builder.determineDataSourceName();
        }

        String slaveKey = currentRouterKey.isSlave() ? "-slave" : "";

        // 实现多数据源
        if(builder instanceof MultiDataSourcePropertyBuilder){
            // 多数据源必须指定数据源ID
            String dataSourceId = ((MultiDataSourcePropertyBuilder) builder).determineDataSourceId();
            if(StringUtils.hasText(dataSourceId)){
                return String.format("%s-%s%s",dataSourceName,dataSourceId,slaveKey);
            }else{
                throw new JdbcException("Multi-dataSource must set dataSourceId");
            }

        }else {
            return dataSourceName+slaveKey;
        }

    }

    public boolean hasPropertyBuilder(){
        return dataSourceConfigRegister.hasPropertyBuilder();
    }
}
