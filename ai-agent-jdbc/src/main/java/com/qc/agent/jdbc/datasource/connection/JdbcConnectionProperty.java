package com.qc.agent.jdbc.datasource.connection;

import com.qc.agent.jdbc.datasource.dialect.DialectEnum;

public class JdbcConnectionProperty {


    private DialectEnum dialect=DialectEnum.POSTGRES;
    private String host;
    private int port;
    private String userName;
    private String password;
    private String databaseName;

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public int getPort() {
        return port;
    }

    public void setPort(int port) {
        this.port = port;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getDatabaseName() {
        return databaseName;
    }

    public void setDatabaseName(String databaseName) {
        this.databaseName = databaseName;
    }

    public DialectEnum getDialect() {
        return dialect;
    }

    public void setDialect(DialectEnum dialect) {
        this.dialect = dialect;
    }
}
