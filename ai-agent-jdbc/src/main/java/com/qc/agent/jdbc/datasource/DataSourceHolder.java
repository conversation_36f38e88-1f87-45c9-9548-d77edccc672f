package com.qc.agent.jdbc.datasource;

import com.qc.agent.jdbc.datasource.router.RouterKey;

/**
 * 数据源持有者
 * <AUTHOR>
 */
public class DataSourceHolder
{
    private static final ThreadLocal<RouterKey> routerKey = new ThreadLocal<>();
    public static void setRouterKey(RouterKey key) {
        routerKey.set(key);
    }

    public static RouterKey getRouterKey() {
        return routerKey.get();
    }


    public static void clearRouterKey() {
        routerKey.remove();
    }


    public  static void clear() {
        clearRouterKey();
    }
}
