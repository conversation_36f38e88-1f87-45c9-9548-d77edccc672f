/*
package com.qc.agent.redis;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.params.SetParams;

import java.util.Collections;
import java.util.concurrent.TimeUnit;

*/
/**
 *
 *
 * <AUTHOR>
 * @date 2024/7/13 15:34:55
 *
 *//*

@Component
public class RedisLock
{
    private static final String LOCK_SUCCESS = "OK";


    // 要确保上述操作是原子性的,要使用Lua语言来实现.
    // 首先获取锁对应的value值，检查是否与token相等，如果相等则删除锁（解锁）
    private static final String LUA_SCRIPT = "if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end";

    RedisTemplate redisTemplate;

    @Autowired
    public RedisLock (RedisTemplate redisTemplate){
        this.redisTemplate = redisTemplate;
    }

    //锁key的前缀
    private final static String prefix_key = "redisLock:";
    //释放锁的lua脚本
    private final static  String unLockScript = "if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end";
    //执行unLockScript脚本，释放锁成功值
    private final static  Long UNLOCK_SUCCESS = 1L;



    public boolean tryLock(String key, long timeout, TimeUnit unit)
    {

        try(Jedis jedis = getJedis();)
        {
            Assert.notNull(jedis, "jedis can not be null");

            TimeUnit timeUnit = TimeUnit.SECONDS;

            long seconds = timeUnit.convert(timeout, unit);

			SetParams setParams = new SetParams();

			setParams.nx().ex(Integer.parseInt(String.valueOf(seconds)));
            String result = jedis.set(key, key, setParams);

            return LOCK_SUCCESS.equals(result);

        }


    }


    public boolean unlock(String key){

        try(Jedis jedis = getJedis();)
        {
            Assert.notNull(jedis, "jedis can not be null");

            return  jedis.eval(unLockScript, Collections.singletonList(key), Collections.singletonList(key)) == UNLOCK_SUCCESS;
        }
        catch(Exception e)
        {
            throw new RuntimeException(e);
        }

    }


    private Jedis getJedis() {

        Assert.notNull(redisTemplate, "redisTemplate can not be null");
        return (Jedis) redisTemplate.getConnectionFactory().getConnection().getNativeConnection();
    }


}
*/
