//package com.qc.answer_ai.redis;
//
//import jakarta.annotation.Resource;
//import org.redisson.api.RedissonClient;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.util.concurrent.TimeUnit;
//
///**
// *
// *
// * <AUTHOR>
// * @date 2024/7/13 09:55:04
// *
// */
//@Component
//public class RedissonLock
//{
//    @Resource
//    RedissonClient redisson;
//
//    public RedissonLock(RedissonClient redisson) {
//           this.redisson = redisson;
//    }
//
//    public boolean tryLock(String key,long timeout, TimeUnit unit){
//        try
//        {
//            return redisson.getLock(key).tryLock(timeout,unit);
//        }
//        catch(InterruptedException e)
//        {
//            throw new IllegalStateException(e);
//        }
//
//    }
//
//    public  void unlock(String key){
//        redisson.getLock(key).unlock();
//    }
//}
