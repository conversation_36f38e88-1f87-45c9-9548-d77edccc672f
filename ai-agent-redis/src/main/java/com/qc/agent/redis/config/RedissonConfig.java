//package com.qc.agent.redis.config;
//
//import org.redisson.Redisson;
//import org.redisson.api.RedissonClient;
//import org.redisson.config.Config;
//import org.redisson.config.SingleServerConfig;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
///**
// *
// *
// * <AUTHOR>
// * @date 2024/7/13 10:54:51
// *
// */
//@Configuration
//public class RedissonConfig
//{
//
//
//    @Bean(name="singleServerConfig")
//    @ConfigurationProperties("spring.redisson.config.single")
//    public SingleServerConfig buildConfig() {
//        return new Config().useSingleServer();
//    }
//
//    @Bean(destroyMethod = "shutdown")
//    public RedissonClient redissonClient() {
//        Config config = new RedissonSingleServerConfig(buildConfig());
//        return Redisson.create(config);
//    }
//}
