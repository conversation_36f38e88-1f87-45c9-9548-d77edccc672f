# 客户洞察助手 - 模块划分与开发任务规划

---

## 1. 综述 (Overview)

本文档基于《客户洞察助手功能设计文档》与《客户洞察助手系统数据库表结构说明》，旨在将整个系统需求分解为可管理、可执行的开发模块。每个模块都有明确的开发目标、前后端任务及依赖关系，以便于团队分工协作和项目进度跟踪。

**V3 版本说明**: 根据最新开发进展，洞察配置管理模块的核心功能已完成，包括工作区配置的完整 CRUD 操作。当前重点转向 AI 模型集成和实际数据源对接。

### 模块总览清单

| 模块名称 (Module Name)     | 简要说明 (Description)                                                                 | 核心关联数据表 (Core Tables)                                                                                                                                                                             | 开发状态  |
| -------------------------- | -------------------------------------------------------------------------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | --------- |
| **1. 洞察配置管理模块**    | 提供后台管理功能，用于配置洞察分析的各个方面，是整个系统的基础。                       | `qc_ai_dimension_config`, `qc_ai_data_source`, `qc_ai_data_source_ref_data_item`, `qc_ai_measurement_standard`, `qc_ai_summary_config`, `qc_ai_dimension_ref_data_item_rel`, `qc_ai_config_standard_rel` | ✅ 已完成 |
| **2. 洞察生成核心模块**    | 系统的核心引擎，负责根据配置执行内置 SQL、调用大模型、编排分析流程，最终生成洞察结果。 | (读) 所有配置表, (写) `qc_ai_insight_data_source_record`, `qc_ai_conversation_ext`                                                                                                                       | 🔄 进行中 |
| **3. 用户交互与 API 模块** | 面向最终用户的接口层，处理用户请求，触发洞察分析，并展示结果。                         | (读) `qc_ai_conversation_ext`, `qc_ai_agent_conversation`, (触发) 洞察生成核心模块                                                                                                                       | 🔄 进行中 |
| **4. 洞察历史与日志模块**  | 提供查询和展示历史洞察报告的功能，便于结果追溯和分析。                                 | (读) `qc_ai_conversation_ext`, `qc_ai_insight_data_source_record`                                                                                                                                        | 📋 待开发 |

---

## 2. 建议开发顺序与优先级 (Suggested Development Order & Priority)

### ✅ P0 (已完成)

1. **洞察配置管理模块 (后端)**:
   - ✅ 维度配置 CRUD 操作
   - ✅ 总结配置 CRUD 操作
   - ✅ 数据源和数据项管理
   - ✅ 衡量标准管理
   - ✅ 工作区配置的完整 API 接口
   - ✅ 事务管理和异常处理

### 🔄 P1 (当前重点)

2. **洞察生成核心模块**:

   - 🔄 内置数据查询逻辑开发
   - 🔄 AI 模型集成（腾讯云混元大模型）
   - 🔄 洞察分析流程编排
   - 🔄 结果生成和存储

3. **用户交互与 API 模块**:
   - 🔄 对话发起接口优化
   - 🔄 洞察结果查询接口
   - 🔄 前端页面集成测试

### 📋 P2 (后续计划)

4. **洞察配置管理模块 (前端)**:

   - 📋 后台管理页面开发
   - 📋 配置可视化界面
   - 📋 用户体验优化

5. **洞察历史与日志模块**:
   - 📋 历史报告查询接口
   - 📋 前端展示页面
   - 📋 数据分析和统计功能

---

## 3. 模块详细拆解 (Detailed Module Breakdown)

### 3.1 洞察配置管理模块 (Configuration Management) ✅ 已完成

#### 3.1.1. 功能说明

提供一个管理后台，允许管理员对洞察助手的分析维度、数据源、衡量标准、总结建议等所有可配置项进行增、删、改、查操作。

#### 3.1.2. 相关数据表

- `qc_ai_dimension_config`: 维度配置
- `qc_ai_data_source`: 数据源定义 (内置 SQL 映射)
- `qc_ai_data_source_ref_data_item`: 原子数据项定义
- `qc_ai_dimension_ref_data_item_rel`: 维度与数据项关系
- `qc_ai_measurement_standard`: 衡量标准定义
- `qc_ai_summary_config`: 总结配置
- `qc_ai_config_standard_rel`: 配置与标准关系

#### 3.1.3. 已完成功能

- **工作区配置管理**:

  - ✅ `GET /ai-agent/assistant/customer-insight/{agentId}` - 获取工作区配置
  - ✅ `POST /ai-agent/assistant/customer-insight/saveWorkspace` - 保存工作区配置
  - ✅ 维度配置的完整 CRUD 操作
  - ✅ 总结配置的完整 CRUD 操作
  - ✅ 数据项关联关系管理
  - ✅ 标准关联关系管理

- **核心业务逻辑**:

  - ✅ 维度编码自动推导逻辑
  - ✅ 数据项树形结构处理
  - ✅ 总结配置唯一性保证
  - ✅ 删除操作的特殊处理逻辑

- **数据访问层**:
  - ✅ 完整的 MyBatis 映射文件
  - ✅ 所有 Mapper 接口实现
  - ✅ 事务管理支持

#### 3.1.4. 后端接口与功能点 (Backend APIs & Functional Points)

遵循 `admin` 模块的 `controller/service/mapper` 结构。

- **CustomerInsightAssistantController**:

  - ✅ `GET /ai-agent/assistant/customer-insight/{agentId}`: 获取工作区配置
  - ✅ `POST /ai-agent/assistant/customer-insight/saveWorkspace`: 保存工作区配置
  - 🔄 `POST /ai-agent/assistant/customer-insight/intent`: 客户洞察意图识别

#### 3.1.5. 技术实现亮点

1. **智能配置管理**:

   - 维度配置支持多条记录，总结配置保证唯一性
   - 通过 agentId 自动判断新增/更新/删除操作
   - 支持部分字段更新，避免全量覆盖

2. **数据项关联优化**:

   - 支持树形结构的数据项选择
   - 自动处理父子关系，避免重复选择
   - 灵活的查询参数配置

3. **标准管理统一化**:
   - 维度标准和总结标准的统一管理
   - 支持标准的增删改查操作
   - 通过配置类型区分不同用途

### 3.2 洞察生成核心模块 (Insight Generation Core) 🔄 进行中

#### 3.2.1. 功能说明

系统的核心引擎，负责根据配置执行内置 SQL、调用大模型、编排分析流程，最终生成洞察结果。

#### 3.2.2. 相关数据表

- (读) 所有配置表
- (写) `qc_ai_insight_data_source_record`: 数据源记录
- (写) `qc_ai_conversation_ext`: 对话扩展记录

#### 3.2.3. 开发计划

1. **数据查询引擎**:

   - 📋 内置 SQL 查询逻辑实现
   - 📋 数据源接口对接
   - 📋 查询结果缓存机制

2. **AI 模型集成**:

   - 📋 腾讯云混元大模型集成
   - 📋 提示词模板管理
   - 📋 模型调用优化

3. **分析流程编排**:

   - 📋 维度分析流程
   - 📋 总结分析流程
   - 📋 结果聚合逻辑

4. **结果存储**:
   - 📋 洞察结果存储
   - 📋 数据源记录存储
   - 📋 历史数据管理

### 3.3 用户交互与 API 模块 (User Interaction & API) 🔄 进行中

#### 3.3.1. 功能说明

面向最终用户的接口层，处理用户请求，触发洞察分析，并展示结果。

#### 3.3.2. 相关数据表

- (读) `qc_ai_conversation_ext`
- (读) `qc_ai_agent_conversation`
- (触发) 洞察生成核心模块

#### 3.3.3. 开发计划

1. **对话接口优化**:

   - 🔄 意图识别逻辑完善
   - 📋 对话上下文管理
   - 📋 用户输入验证

2. **结果展示接口**:

   - 📋 洞察结果格式化
   - 📋 历史记录查询
   - 📋 实时状态更新

3. **前端集成**:
   - 📋 API 接口文档完善
   - 📋 前端页面集成
   - 📋 用户体验优化

### 3.4 洞察历史与日志模块 (Insight History & Logging) 📋 待开发

#### 3.4.1. 功能说明

提供查询和展示历史洞察报告的功能，便于结果追溯和分析。

#### 3.4.2. 相关数据表

- (读) `qc_ai_conversation_ext`
- (读) `qc_ai_insight_data_source_record`

#### 3.4.3. 开发计划

1. **历史查询功能**:

   - 📋 历史报告列表查询
   - 📋 报告详情查看
   - 📋 搜索和筛选功能

2. **数据分析功能**:

   - 📋 洞察趋势分析
   - 📋 数据统计报表
   - 📋 导出功能

3. **日志管理**:
   - 📋 操作日志记录
   - 📋 错误日志管理
   - 📋 性能监控

---

## 4. 技术架构与设计原则

### 4.1 架构设计

1. **分层架构**:

   - Controller 层：处理 HTTP 请求和响应
   - Service 层：业务逻辑处理
   - Mapper 层：数据访问
   - Model 层：数据结构定义

2. **模块化设计**:

   - 高内聚、低耦合
   - 清晰的职责划分
   - 可扩展的接口设计

3. **数据一致性**:
   - 事务管理
   - 异常处理
   - 数据验证

### 4.2 开发规范

1. **命名规范**:

   - 类名：PascalCase
   - 方法名：camelCase
   - 常量：UPPER_SNAKE_CASE

2. **代码质量**:

   - 单元测试覆盖
   - 代码审查
   - 性能优化

3. **文档维护**:
   - API 文档更新
   - 代码注释
   - 变更记录

---

## 5. 项目里程碑 (Project Milestones)

### ✅ 里程碑 1：基础架构完成 (已完成)

- 数据库设计完成
- 核心业务逻辑实现
- API 接口开发完成
- 基础测试通过

### 🔄 里程碑 2：核心功能实现 (进行中)

- AI 模型集成
- 数据源对接
- 洞察生成流程
- 用户交互优化

### 📋 里程碑 3：完整系统上线 (计划中)

- 前端页面完成
- 系统集成测试
- 性能优化
- 生产环境部署

### 📋 里程碑 4：功能完善 (计划中)

- 历史记录功能
- 数据分析功能
- 监控告警
- 运维工具

---

## 6. 风险评估与应对策略

### 6.1 技术风险

1. **AI 模型集成风险**:

   - 风险：模型调用不稳定
   - 应对：实现重试机制和降级方案

2. **数据源对接风险**:

   - 风险：外部接口变更
   - 应对：设计适配器模式，支持多数据源

3. **性能风险**:
   - 风险：大量数据处理性能问题
   - 应对：实现缓存机制和分页处理

### 6.2 项目风险

1. **进度风险**:

   - 风险：开发进度延迟
   - 应对：合理分配任务，及时调整计划

2. **质量风险**:
   - 风险：代码质量不达标
   - 应对：加强代码审查，完善测试用例

---

## 7. 总结

客户洞察助手项目已经完成了核心的配置管理功能，为后续的 AI 模型集成和洞察生成奠定了坚实的基础。当前开发重点转向核心业务逻辑的实现，预计在下一个里程碑中完成完整的洞察分析功能。

**文档版本**：v3.0.0  
**最后更新**：2025-01-29  
**更新说明**：反映当前开发进展，更新已完成功能和后续计划
