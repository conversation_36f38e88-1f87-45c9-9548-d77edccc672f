{"code": "1", "data": {"dimensions": [{"dataSources": [{"apiUrl": "", "belongDimensionCode": "VIVID", "belongDimensionName": "生动化", "dataItemsByType": [{"dataItems": [{"dataTypeCode": "DETAIL", "description": "拜访表单子任务字段、表单中的字段表单单", "id": 8, "itemCode": "visitSubtaskFieldValue", "itemName": "拜访子任务字段值", "level": 0, "parent": true, "placeholderName": "${visitSubtaskFieldValue}", "queryBusinessCode": "visit", "selected": false, "sortOrder": 0, "status": "1"}], "dataTypeCode": "DETAIL", "dataTypeName": "明细"}], "description": "拜访相关数据源", "httpMethod": "", "id": 4, "paramGroups": [{"displayOrder": 1, "enumValues": [{"code": "1", "name": "最近1个月"}, {"code": "2", "name": "最近2个月"}, {"code": "3", "name": "最近3个月"}, {"code": "6", "name": "最近6个月"}, {"code": "12", "name": "最近1年"}], "groupCode": "TIME_APPROVE", "groupName": "按审核通过时间", "params": [{"displayOrder": 1, "groupCode": "TIME_APPROVE", "paramCode": "approveStartTime"}, {"displayOrder": 1, "groupCode": "TIME_APPROVE", "paramCode": "approveEndTime"}]}, {"displayOrder": 2, "enumValues": [{"code": "1", "name": "最近1个月"}, {"code": "2", "name": "最近2个月"}, {"code": "3", "name": "最近3个月"}, {"code": "6", "name": "最近6个月"}, {"code": "12", "name": "最近1年"}], "groupCode": "TIME_REPORT", "groupName": "按上报时间", "params": [{"displayOrder": 2, "groupCode": "TIME_REPORT", "paramCode": "reportStartTime"}, {"displayOrder": 2, "groupCode": "TIME_REPORT", "paramCode": "reportEndTime"}]}], "sourceCode": "visit", "sourceName": "拜访", "status": "1"}], "dimensionCode": "VIVID", "dimensionName": "生动化", "interpretationPromptFragments": [{"fragmentKey": "ROLE", "fragmentName": "角色定义", "id": 25, "mainPromptId": 3, "sortOrder": 1}, {"fragmentKey": "DATA", "fragmentName": "数据输入", "id": 26, "mainPromptId": 3, "sortOrder": 2}, {"fragmentKey": "ANALYSIS_REQUIREMENTS", "fragmentName": "分析与输出要求", "id": 27, "mainPromptId": 3, "sortOrder": 3}, {"fragmentKey": "SCORING_STANDARD", "fragmentName": "模型维度打分标准", "id": 28, "mainPromptId": 3, "sortOrder": 4}, {"fragmentKey": "EXAMPLE", "fragmentName": "示例", "id": 29, "mainPromptId": 3, "sortOrder": 5}]}, {"dataSources": [{"apiUrl": "", "belongDimensionCode": "ORDER", "belongDimensionName": "订单", "dataItemsByType": [{"dataItems": [{"dataTypeCode": "METRIC", "description": "已发货SKU数/订单中SKU数,转成基本单位", "id": 2, "itemCode": "distributionOrderAllocationRate", "itemName": "订单配货率", "level": 0, "parent": true, "placeholderName": "${distributionOrderAllocationRate}", "queryBusinessCode": "distributionOrder", "selected": false, "sortOrder": 0, "status": "1"}, {"dataTypeCode": "METRIC", "description": "订单状态（已批通过）的发货SKU数/订单中SKU数,转成基本单位", "id": 1, "itemCode": "distributionOrderSatisfactionRate", "itemName": "订单满足率", "level": 0, "parent": true, "placeholderName": "${distributionOrderSatisfactionRate}", "queryBusinessCode": "distributionOrder", "selected": false, "sortOrder": 0, "status": "1"}], "dataTypeCode": "METRIC", "dataTypeName": "指标"}, {"dataItems": [{"dataTypeCode": "DETAIL", "description": "下单时间2025-06-18，最后发货时间2025-06-20", "id": 3, "itemCode": "distributionOrderDetailFields", "itemName": "分销订单明细", "level": 0, "parent": true, "placeholderName": "${distributionOrderDetailFields}", "queryBusinessCode": "distributionOrder", "selected": false, "sortOrder": 0, "status": "1"}], "dataTypeCode": "DETAIL", "dataTypeName": "明细"}], "description": "分销订单相关数据源", "httpMethod": "", "id": 1, "paramGroups": [{"displayOrder": 1, "enumValues": [{"code": "1", "name": "最近1个月"}, {"code": "2", "name": "最近2个月"}, {"code": "3", "name": "最近3个月"}, {"code": "6", "name": "最近6个月"}, {"code": "12", "name": "最近1年"}], "groupCode": "TIME_APPROVE", "groupName": "按审核通过时间", "params": [{"displayOrder": 1, "groupCode": "TIME_APPROVE", "paramCode": "startTime"}, {"displayOrder": 1, "groupCode": "TIME_APPROVE", "paramCode": "endTime"}]}, {"displayOrder": 2, "enumValues": [{"code": "1", "name": "最近1个月"}, {"code": "2", "name": "最近2个月"}, {"code": "3", "name": "最近3个月"}, {"code": "6", "name": "最近6个月"}, {"code": "12", "name": "最近1年"}], "groupCode": "TIME_REPORT", "groupName": "按上报时间", "params": [{"displayOrder": 2, "groupCode": "TIME_REPORT", "paramCode": "reportStartTime"}, {"displayOrder": 2, "groupCode": "TIME_REPORT", "paramCode": "reportEndTime"}]}, {"displayOrder": 3, "enumValues": [{"code": "1", "name": "是"}, {"code": "0", "name": "否"}], "groupCode": "STATUS_ACTIVE", "groupName": "当前在投", "params": [{"displayOrder": 3, "groupCode": "STATUS_ACTIVE", "paramCode": "status"}]}], "sourceCode": "distributionOrder", "sourceName": "分销订单", "status": "1"}], "dimensionCode": "ORDER", "dimensionName": "订单", "interpretationPromptFragments": [{"fragmentKey": "ROLE", "fragmentName": "角色定义", "id": 15, "mainPromptId": 1, "sortOrder": 1}, {"fragmentKey": "DATA", "fragmentName": "数据输入", "id": 16, "mainPromptId": 1, "sortOrder": 2}, {"fragmentKey": "ANALYSIS_REQUIREMENTS", "fragmentName": "分析与输出要求", "id": 17, "mainPromptId": 1, "sortOrder": 3}, {"fragmentKey": "SCORING_STANDARD", "fragmentName": "模型维度打分标准", "id": 18, "mainPromptId": 1, "sortOrder": 4}, {"fragmentKey": "EXAMPLE", "fragmentName": "示例", "id": 19, "mainPromptId": 1, "sortOrder": 5}]}, {"dataSources": [{"apiUrl": "", "belongDimensionCode": "DISPLAY", "belongDimensionName": "铺货", "dataItemsByType": [{"dataItems": [{"dataTypeCode": "DETAIL", "description": "BY铺货记录看是否合格", "id": 6, "itemCode": "stockReportQualifiedStatus", "itemName": "铺货记录合格状态", "level": 0, "parent": true, "placeholderName": "${stockReportQualifiedStatus}", "queryBusinessCode": "stockReport", "selected": false, "sortOrder": 0, "status": "1"}], "dataTypeCode": "DETAIL", "dataTypeName": "明细"}], "description": "铺货上报相关数据源", "httpMethod": "", "id": 2, "paramGroups": [{"displayOrder": 1, "enumValues": [{"code": "1", "name": "最近1个月"}, {"code": "2", "name": "最近2个月"}, {"code": "3", "name": "最近3个月"}, {"code": "6", "name": "最近6个月"}, {"code": "12", "name": "最近1年"}], "groupCode": "TIME_REPORT", "groupName": "按上报时间", "params": [{"displayOrder": 1, "groupCode": "TIME_REPORT", "paramCode": "reportStartTime"}, {"displayOrder": 1, "groupCode": "TIME_REPORT", "paramCode": "reportEndTime"}]}, {"displayOrder": 2, "enumValues": [{"code": "1", "name": "是"}, {"code": "0", "name": "否"}], "groupCode": "STATUS_ACTIVE", "groupName": "当前在投", "params": [{"displayOrder": 2, "groupCode": "STATUS_ACTIVE", "paramCode": "status"}]}, {"displayOrder": 3, "enumValues": [{"code": "1", "name": "是"}, {"code": "0", "name": "否"}], "groupCode": "STATUS_INACTIVE", "groupName": "当前未使用", "params": [{"displayOrder": 3, "groupCode": "STATUS_INACTIVE", "paramCode": "isUsed"}]}], "sourceCode": "stockReport", "sourceName": "铺货上报", "status": "1"}, {"apiUrl": "", "belongDimensionCode": "DISPLAY", "belongDimensionName": "铺货", "dataItemsByType": [{"dataItems": [{"dataTypeCode": "METRIC", "description": "铺货SKU/铺货标准SKU", "id": 7, "itemCode": "stockCustom", "itemName": "铺货率（定制）", "level": 0, "parent": true, "placeholderName": "${stockCustom}", "queryBusinessCode": "stockOld", "selected": false, "sortOrder": 0, "status": "1"}], "dataTypeCode": "METRIC", "dataTypeName": "指标"}], "description": "铺货上报（old）相关数据源", "httpMethod": "", "id": 3, "paramGroups": [], "sourceCode": "stockOld", "sourceName": "铺货上报（old）", "status": "1"}], "dimensionCode": "DISPLAY", "dimensionName": "铺货"}, {"dataSources": [{"apiUrl": "", "belongDimensionCode": "COUPON", "belongDimensionName": "兑换券", "dataItemsByType": [{"dataItems": [{"dataTypeCode": "DETAIL", "description": "BY门店在途或已兑换的兑换券的发放明细、名称、有效期", "id": 10, "itemCode": "couponDetail", "itemName": "兑换券明细", "level": 0, "parent": true, "placeholderName": "${couponDetail}", "queryBusinessCode": "coupon", "selected": false, "sortOrder": 0, "status": "1"}], "dataTypeCode": "DETAIL", "dataTypeName": "明细"}], "description": "兑换券相关数据源", "httpMethod": "", "id": 6, "paramGroups": [{"displayOrder": 1, "enumValues": [{"code": "1", "name": "最近1个月"}, {"code": "2", "name": "最近2个月"}, {"code": "3", "name": "最近3个月"}, {"code": "6", "name": "最近6个月"}, {"code": "12", "name": "最近1年"}], "groupCode": "TIME_VERIFICATION", "groupName": "按核销时间", "params": [{"displayOrder": 1, "groupCode": "TIME_VERIFICATION", "paramCode": "verificationStartTime"}, {"displayOrder": 1, "groupCode": "TIME_VERIFICATION", "paramCode": "verificationEndTime"}]}, {"displayOrder": 2, "enumValues": [{"code": "1", "name": "是"}, {"code": "0", "name": "否"}], "groupCode": "STATUS_ACTIVE", "groupName": "当前在投", "params": [{"displayOrder": 2, "groupCode": "STATUS_ACTIVE", "paramCode": "status"}]}, {"displayOrder": 3, "enumValues": [{"code": "1", "name": "是"}, {"code": "0", "name": "否"}], "groupCode": "STATUS_INACTIVE", "groupName": "当前未使用", "params": [{"displayOrder": 3, "groupCode": "STATUS_INACTIVE", "paramCode": "isUsed"}]}], "sourceCode": "coupon", "sourceName": "兑换券", "status": "1"}], "dimensionCode": "COUPON", "dimensionName": "兑换券", "interpretationPromptFragments": [{"fragmentKey": "ROLE", "fragmentName": "角色定义", "id": 35, "mainPromptId": 5, "sortOrder": 1}, {"fragmentKey": "DATA", "fragmentName": "数据输入", "id": 36, "mainPromptId": 5, "sortOrder": 2}, {"fragmentKey": "ANALYSIS_REQUIREMENTS", "fragmentName": "分析与输出要求", "id": 37, "mainPromptId": 5, "sortOrder": 3}, {"fragmentKey": "SCORING_STANDARD", "fragmentName": "模型维度打分标准", "id": 38, "mainPromptId": 5, "sortOrder": 4}, {"fragmentKey": "EXAMPLE", "fragmentName": "示例", "id": 39, "mainPromptId": 5, "sortOrder": 5}]}, {"dataSources": [{"apiUrl": "", "belongDimensionCode": "AUTHENTICITY", "belongDimensionName": "真实性", "dataItemsByType": [{"dataItems": [{"dataTypeCode": "DETAIL", "description": "BY客户翻拍照片数量及明细", "id": 12, "itemCode": "rephotographDetail", "itemName": "翻拍明细", "level": 0, "parent": true, "placeholderName": "${rephotographDetail}", "queryBusinessCode": "rephotograph", "selected": false, "sortOrder": 0, "status": "1"}], "dataTypeCode": "DETAIL", "dataTypeName": "明细"}], "description": "翻拍相关数据源", "httpMethod": "", "id": 8, "paramGroups": [{"displayOrder": 1, "enumValues": [{"code": "1", "name": "最近1个月"}, {"code": "2", "name": "最近2个月"}, {"code": "3", "name": "最近3个月"}, {"code": "6", "name": "最近6个月"}, {"code": "12", "name": "最近1年"}], "groupCode": "TIME_REPORT", "groupName": "按上报时间", "params": [{"displayOrder": 1, "groupCode": "TIME_REPORT", "paramCode": "reportStartTime"}, {"displayOrder": 1, "groupCode": "TIME_REPORT", "paramCode": "reportEndTime"}]}], "sourceCode": "rephotograph", "sourceName": "翻拍", "status": "1"}, {"apiUrl": "", "belongDimensionCode": "AUTHENTICITY", "belongDimensionName": "真实性", "dataItemsByType": [{"dataItems": [{"dataTypeCode": "DETAIL", "description": "基于客户的窜拍数量及明细", "id": 13, "itemCode": "crossPhotographDetail", "itemName": "窜拍明细", "level": 0, "parent": true, "placeholderName": "${crossPhotographDetail}", "queryBusinessCode": "crossPhotograph", "selected": false, "sortOrder": 0, "status": "1"}], "dataTypeCode": "DETAIL", "dataTypeName": "明细"}], "description": "窜拍相关数据源", "httpMethod": "", "id": 9, "paramGroups": [{"displayOrder": 1, "enumValues": [{"code": "1", "name": "最近1个月"}, {"code": "2", "name": "最近2个月"}, {"code": "3", "name": "最近3个月"}, {"code": "6", "name": "最近6个月"}, {"code": "12", "name": "最近1年"}], "groupCode": "TIME_REPORT", "groupName": "按上报时间", "params": [{"displayOrder": 1, "groupCode": "TIME_REPORT", "paramCode": "reportStartTime"}, {"displayOrder": 1, "groupCode": "TIME_REPORT", "paramCode": "reportEndTime"}]}], "sourceCode": "crossPhotograph", "sourceName": "窜拍", "status": "1"}, {"apiUrl": "", "belongDimensionCode": "AUTHENTICITY", "belongDimensionName": "真实性", "dataItemsByType": [{"dataItems": [{"dataTypeCode": "DETAIL", "description": "客户主数据表，地址是否标准、地理是否标准", "id": 14, "itemCode": "customerMasterData", "itemName": "客户主数据", "level": 0, "parent": true, "placeholderName": "${customerMasterData}", "queryBusinessCode": "customerMaster", "selected": false, "sortOrder": 0, "status": "1"}], "dataTypeCode": "DETAIL", "dataTypeName": "明细"}], "description": "客户主数据相关数据源", "httpMethod": "", "id": 10, "paramGroups": [{"displayOrder": 1, "enumValues": [{"code": "1", "name": "是"}, {"code": "0", "name": "否"}], "groupCode": "STATUS_ACTIVE", "groupName": "当前在投", "params": [{"displayOrder": 1, "groupCode": "STATUS_ACTIVE", "paramCode": "status"}]}, {"displayOrder": 2, "enumValues": [{"code": "1", "name": "是"}, {"code": "0", "name": "否"}], "groupCode": "STATUS_INACTIVE", "groupName": "当前未使用", "params": [{"displayOrder": 2, "groupCode": "STATUS_INACTIVE", "paramCode": "isUsed"}]}], "sourceCode": "customerMaster", "sourceName": "客户主数据", "status": "1"}, {"apiUrl": "", "belongDimensionCode": "AUTHENTICITY", "belongDimensionName": "真实性", "dataItemsByType": [{"dataItems": [{"dataTypeCode": "DETAIL", "description": "疑似虚假照片：当一个活动周期内门店比如拍货架or冰箱的照片相似度为某个较低区间，即完全不一样", "id": 16, "itemCode": "SimilarPhotos", "itemName": "相似照片", "level": 0, "parent": true, "placeholderName": "${SimilarPhotos}", "queryBusinessCode": "customerContact", "selected": false, "sortOrder": 0, "status": "1"}, {"dataTypeCode": "DETAIL", "description": "联系人表，联系人数量、联系人信息补全、联系人手机号是否标准", "id": 15, "itemCode": "customerContact", "itemName": "客户联系人", "level": 0, "parent": true, "placeholderName": "${customerContact}", "queryBusinessCode": "customerContact", "selected": false, "sortOrder": 0, "status": "1"}], "dataTypeCode": "DETAIL", "dataTypeName": "明细"}], "description": "客户联系人相关数据源", "httpMethod": "", "id": 11, "paramGroups": [{"displayOrder": 1, "enumValues": [{"code": "1", "name": "是"}, {"code": "0", "name": "否"}], "groupCode": "STATUS_ACTIVE", "groupName": "当前在投", "params": [{"displayOrder": 1, "groupCode": "STATUS_ACTIVE", "paramCode": "status"}]}, {"displayOrder": 2, "enumValues": [{"code": "1", "name": "是"}, {"code": "0", "name": "否"}], "groupCode": "STATUS_INACTIVE", "groupName": "当前未使用", "params": [{"displayOrder": 2, "groupCode": "STATUS_INACTIVE", "paramCode": "isUsed"}]}], "sourceCode": "customerContact", "sourceName": "客户联系人", "status": "1"}], "dimensionCode": "AUTHENTICITY", "dimensionName": "真实性"}, {"dataSources": [{"apiUrl": "", "belongDimensionCode": "ASSET", "belongDimensionName": "资产", "dataItemsByType": [{"dataItems": [{"dataTypeCode": "DETAIL", "description": "当前在投", "id": 11, "itemCode": "assetPlacementDetail", "itemName": "资产投放明细", "level": 0, "parent": true, "placeholderName": "${assetPlacementDetail}", "queryBusinessCode": "assetPlacement", "selected": false, "sortOrder": 0, "status": "1"}], "dataTypeCode": "DETAIL", "dataTypeName": "明细"}], "description": "资产投放相关数据源", "httpMethod": "", "id": 7, "paramGroups": [{"displayOrder": 1, "enumValues": [{"code": "1", "name": "最近1个月"}, {"code": "2", "name": "最近2个月"}, {"code": "3", "name": "最近3个月"}, {"code": "6", "name": "最近6个月"}, {"code": "12", "name": "最近1年"}], "groupCode": "TIME_APPROVE", "groupName": "按审核通过时间", "params": [{"displayOrder": 1, "groupCode": "TIME_APPROVE", "paramCode": "approveStartTime"}, {"displayOrder": 1, "groupCode": "TIME_APPROVE", "paramCode": "approveEndTime"}]}, {"displayOrder": 2, "enumValues": [{"code": "1", "name": "是"}, {"code": "0", "name": "否"}], "groupCode": "STATUS_ACTIVE", "groupName": "当前在投", "params": [{"displayOrder": 2, "groupCode": "STATUS_ACTIVE", "paramCode": "status"}]}, {"displayOrder": 3, "enumValues": [{"code": "1", "name": "是"}, {"code": "0", "name": "否"}], "groupCode": "STATUS_INACTIVE", "groupName": "当前未使用", "params": [{"displayOrder": 3, "groupCode": "STATUS_INACTIVE", "paramCode": "isUsed"}]}], "sourceCode": "assetPlacement", "sourceName": "资产投放", "status": "1"}], "dimensionCode": "ASSET", "dimensionName": "资产", "interpretationPromptFragments": [{"fragmentKey": "ROLE", "fragmentName": "角色定义", "id": 40, "mainPromptId": 6, "sortOrder": 1}, {"fragmentKey": "DATA", "fragmentName": "数据输入", "id": 41, "mainPromptId": 6, "sortOrder": 2}, {"fragmentKey": "ANALYSIS_REQUIREMENTS", "fragmentName": "分析与输出要求", "id": 42, "mainPromptId": 6, "sortOrder": 3}, {"fragmentKey": "SCORING_STANDARD", "fragmentName": "模型维度打分标准", "id": 43, "mainPromptId": 6, "sortOrder": 4}, {"fragmentKey": "EXAMPLE", "fragmentName": "示例", "id": 44, "mainPromptId": 6, "sortOrder": 5}]}, {"dataSources": [{"apiUrl": "", "belongDimensionCode": "PAID_DISPLAY", "belongDimensionName": "付费陈列", "dataItemsByType": [{"dataItems": [{"dataTypeCode": "METRIC", "description": "BY客户资效比数据，按投放批次、当前客户画像维度统计", "id": 9, "itemCode": "paidColumnEffectivenessRatio", "itemName": "费效比", "level": 0, "parent": true, "placeholderName": "${paidColumnEffectivenessRatio}", "queryBusinessCode": "paidColumn", "selected": false, "sortOrder": 0, "status": "1"}], "dataTypeCode": "METRIC", "dataTypeName": "指标"}], "description": "付费列相关数据源", "httpMethod": "", "id": 5, "paramGroups": [], "sourceCode": "paidColumn", "sourceName": "付费列", "status": "1"}], "dimensionCode": "PAID_DISPLAY", "dimensionName": "付费陈列", "interpretationPromptFragments": [{"fragmentKey": "ROLE", "fragmentName": "角色定义", "id": 30, "mainPromptId": 4, "sortOrder": 1}, {"fragmentKey": "DATA", "fragmentName": "数据输入", "id": 31, "mainPromptId": 4, "sortOrder": 2}, {"fragmentKey": "ANALYSIS_REQUIREMENTS", "fragmentName": "分析与输出要求", "id": 32, "mainPromptId": 4, "sortOrder": 3}, {"fragmentKey": "SCORING_STANDARD", "fragmentName": "模型维度打分标准", "id": 33, "mainPromptId": 4, "sortOrder": 4}, {"fragmentKey": "EXAMPLE", "fragmentName": "示例", "id": 34, "mainPromptId": 4, "sortOrder": 5}]}], "summaryTemplate": {"comprehensivePromptFragments": [{"fragmentKey": "HEALTH_SCORE", "fragmentName": "健康度评分", "id": 6, "mainPromptId": 11, "sortOrder": 1}, {"fragmentKey": "HEALTH_SCORE_WEIGHT_RULE", "fragmentName": "健康度评分权重规则", "id": 7, "mainPromptId": 11, "sortOrder": 2}], "summaryAdvicePromptFragments": [{"fragmentKey": "ROLE", "fragmentName": "角色定义", "id": 1, "mainPromptId": 12, "sortOrder": 1}, {"fragmentKey": "DATA", "fragmentName": "数据输入", "id": 2, "mainPromptId": 12, "sortOrder": 2}, {"fragmentKey": "ANALYSIS_REQUIREMENTS", "fragmentName": "分析与输出要求", "id": 3, "mainPromptId": 12, "sortOrder": 3}, {"fragmentKey": "SCORING_STANDARD", "fragmentName": "模型维度打分标准", "id": 4, "mainPromptId": 12, "sortOrder": 4}, {"fragmentKey": "EXAMPLE", "fragmentName": "示例", "id": 5, "mainPromptId": 12, "sortOrder": 5}]}}, "error": false, "message": "", "ok": true}