CREATE TABLE qc_knowledge_file (
    id int8 NOT NULL,
    folder_id int8,
    file_name varchar(4000) COLLATE pg_catalog.default,
    file_comments text COLLATE pg_catalog.default,
    file_logo varchar(4000) COLLATE pg_catalog.default,
    file_url varchar(4000) COLLATE pg_catalog.default,
    file_size numeric(20,0),
    file_status char(1) COLLATE pg_catalog.default,
    file_type varchar(32) COLLATE pg_catalog.default,
    create_user_id int8,
    create_user_name varchar(255) COLLATE pg_catalog.default,
    modify_user_id int8,
    modify_user_name varchar(255) COLLATE pg_catalog.default,
    view_count numeric(12,0),
    down_count numeric(12,0),
    database_id int8,
    collection_id int8,
    create_time timestamp(6),
    modify_time timestamp(6),
    category_id bigint,
    origin_name varchar(4000),
    data_imp_type varchar(32) COLLATE pg_catalog.default,
    CONSTRAINT qc_knowledge_file_pkey PRIMARY KEY (id)
);

CREATE TABLE qc_knowledge_folder (
    id int8 NOT NULL,
    folder_name varchar(4000) COLLATE pg_catalog.default,
    folder_comments text COLLATE pg_catalog.default,
    create_time timestamp(6),
    modify_time timestamp(6),
    folder_logo_url varchar(4000) COLLATE pg_catalog.default,
    create_user_id int8,
    create_user_name varchar(255) COLLATE pg_catalog.default,
    modify_user_id int8,
    modify_user_name varchar(255) COLLATE pg_catalog.default,
    parent_id int8,
    sequence numeric(12,2),
    CONSTRAINT qc_knowledge_folder_pkey PRIMARY KEY (id)
);

CREATE TABLE qc_knowledge_history_question (
    id int8 NOT NULL,
    question_id int8,
    history_question_id int8,
    question_title varchar(4000) COLLATE pg_catalog.default,
    question_content text COLLATE pg_catalog.default,
    create_time timestamp(6),
    create_user_id int8,
    question_status varchar(32) COLLATE pg_catalog.default,
    CONSTRAINT qc_knowledge_history_quesiton_pkey PRIMARY KEY (id)
);

CREATE TABLE qc_knowledge_info (
    id int8 NOT NULL,
    status char(1) COLLATE pg_catalog.default DEFAULT '1'::bpchar,
    creator_id int8,
    creator_name varchar(32),
    create_time timestamp(6) NOT NULL,
    modifyier_id int8,
    modifyier_name varchar(32),
    modify_time timestamp(6),
    index_model varchar(32) COLLATE pg_catalog.default,
    max_tokens numeric(12,0),
    knowledge_face varchar(4000) COLLATE pg_catalog.default,
    knowledge_name varchar(100) COLLATE pg_catalog.default,
    knowledge_des text COLLATE pg_catalog.default,
    enable varchar(32),
    publish_flag varchar(32),
    publish_time timestamp(6),
    publish_user_id int8,
    publish_user_name varchar(32),
    CONSTRAINT qc_knowledge_info_pkey PRIMARY KEY (id)
);

CREATE TABLE qc_knowledge_question (
    id int8 NOT NULL,
    folder_id int8,
    question_title varchar(4000) COLLATE pg_catalog.default,
    question_content text COLLATE pg_catalog.default,
    answer_content text COLLATE pg_catalog.default,
    create_time timestamp(6),
    modify_time timestamp(6),
    create_user_id int8,
    create_user_name varchar(255) COLLATE pg_catalog.default,
    modify_user_id int8,
    modify_user_name varchar(255) COLLATE pg_catalog.default,
    question_status varchar(32) COLLATE pg_catalog.default,
    answer_status varchar(32) COLLATE pg_catalog.default,
    view_count numeric(12,0),
    database_id int8,
    collection_id int8,
    session_id int8,
    reference_content text,
    feedback_type varchar(32),
    feedback_message text,
    msg_id varchar(200),
    CONSTRAINT qc_knowledge_question_pkey PRIMARY KEY (id)
);

CREATE TABLE qc_knowledge_setting (
    id int8 NOT NULL,
    question_threshold numeric(12,0),
    answer_count numeric(12,0),
    bank_answer text COLLATE pg_catalog.default,
    default_answer text COLLATE pg_catalog.default,
    function_logo VARCHAR(255) NULL,
    CONSTRAINT qc_knowledge_setting_pkey PRIMARY KEY (id)
);

CREATE TABLE qc_knowledge_answer_session (
    id int8 NOT NULL,
    status char(1) NULL,
    create_time timestamp NULL,
    modify_time timestamp NULL,
    create_user_id int8 NULL,
    create_user_name varchar(255) NULL,
    modify_user_id int8 NULL,
    modify_user_name varchar(255) NULL,
    session_name varchar(255) NULL,
    CONSTRAINT qc_knowledge_answer_session_pkey PRIMARY KEY (id)
);



CREATE TABLE qc_knowledge_question_answer_setting (
      id int8 NOT NULL,
      status char(1) NULL,
      create_time timestamp NULL,
      modify_time timestamp NULL,
      create_user_id int8 NULL,
      create_user_name varchar(255) NULL,
      modify_user_id int8 NULL,
      modify_user_name varchar(255) NULL,
      threshold numeric(12,2),
      each_search_count smallint default 1,
      engine_type smallint,
      prompt_word text COLLATE pg_catalog.default,
      default_answer text COLLATE pg_catalog.default,
      function_logo VARCHAR(255) NULL,
      answer_search_range VARCHAR(32) NULL,
      prompt TEXT NULL,
      error_answer TEXT NULL,
      show_reference VARCHAR(32) NULL DEFAULT 0,
      qa_threshold numeric(12,2) default 0.95,
      CONSTRAINT qc_knowledge_question_answer_setting_pkey PRIMARY KEY (id)
);

CREATE TABLE qc_storage_detail
(
    id                INT8  NOT NULL,
    url               varchar(512) NOT NULL,
    size              NUMERIC(20)   DEFAULT NULL,
    filename          varchar(256) DEFAULT NULL,
    original_filename varchar(256) DEFAULT NULL,
    base_path         varchar(256) DEFAULT NULL,
    path              varchar(256) DEFAULT NULL,
    ext               varchar(32)  DEFAULT NULL,
    content_type      varchar(128) DEFAULT NULL,
    platform          varchar(32)  DEFAULT NULL,
    th_url            varchar(512) DEFAULT NULL,
    th_filename       varchar(256) DEFAULT NULL,
    th_size           NUMERIC(20)   DEFAULT NULL,
    th_content_type   varchar(128) DEFAULT NULL,
    object_id         varchar(32)  DEFAULT NULL,
    object_type       varchar(32)  DEFAULT NULL,
    metadata          text,
    user_metadata     text,
    th_metadata       text,
    th_user_metadata  text,
    attr              text,
    file_acl          varchar(32)  DEFAULT NULL,
    th_file_acl       varchar(32)  DEFAULT NULL,
    hash_info         text,
    upload_id         varchar(128) DEFAULT NULL,
    upload_status     INT4      DEFAULT NULL,
    create_time       TIMESTAMP     DEFAULT NULL,
    CONSTRAINT qc_storage_detail_pkey PRIMARY KEY (id)
);
COMMENT ON COLUMN qc_storage_detail.url IS '文件访问地址';
COMMENT ON COLUMN qc_storage_detail.size IS '文件大小，单位字节';
COMMENT ON COLUMN qc_storage_detail.filename IS '文件名称';
COMMENT ON COLUMN qc_storage_detail.original_filename IS '原始文件名';
COMMENT ON COLUMN qc_storage_detail.base_path IS '基础存储路径';
COMMENT ON COLUMN qc_storage_detail.path IS '存储路径';
COMMENT ON COLUMN qc_storage_detail.ext IS '文件扩展名';
COMMENT ON COLUMN qc_storage_detail.content_type IS 'MIME类型';
COMMENT ON COLUMN qc_storage_detail.platform IS '存储平台';
COMMENT ON COLUMN qc_storage_detail.th_url IS '缩略图访问路径';
COMMENT ON COLUMN qc_storage_detail.th_filename IS '缩略图名称';
COMMENT ON COLUMN qc_storage_detail.th_size IS '缩略图大小，单位字节';
COMMENT ON COLUMN qc_storage_detail.th_content_type IS '缩略图MIME类型';
COMMENT ON COLUMN qc_storage_detail.object_id IS '文件所属对象id';
COMMENT ON COLUMN qc_storage_detail.object_type IS '文件所属对象类型，例如用户头像，评价图片';
COMMENT ON COLUMN qc_storage_detail.metadata IS '文件元数据';
COMMENT ON COLUMN qc_storage_detail.user_metadata IS '文件用户元数据';
COMMENT ON COLUMN qc_storage_detail.th_metadata IS '缩略图元数据';
COMMENT ON COLUMN qc_storage_detail.th_user_metadata IS '缩略图用户元数据';
COMMENT ON COLUMN qc_storage_detail.attr IS '附加属性';
COMMENT ON COLUMN qc_storage_detail.file_acl IS '文件ACL';
COMMENT ON COLUMN qc_storage_detail.th_file_acl IS '缩略图文件ACL';
COMMENT ON COLUMN qc_storage_detail.hash_info IS '哈希信息';
COMMENT ON COLUMN qc_storage_detail.upload_id IS '上传ID，仅在手动分片上传时使用';
COMMENT ON COLUMN qc_storage_detail.upload_status IS '上传状态，仅在手动分片上传时使用，1：初始化完成，2：上传完成';
COMMENT ON COLUMN qc_storage_detail.create_time IS '创建时间';


CREATE TABLE qc_storage_part_detail
(
    id          INT8 NOT NULL,
    platform    varchar(32)  DEFAULT NULL,
    upload_id   varchar(128) DEFAULT NULL,
    e_tag       varchar(255) DEFAULT NULL,
    part_number NUMERIC(11)      DEFAULT NULL,
    part_size   NUMERIC(20)   DEFAULT NULL,
    hash_info   text,
    create_time TIMESTAMP     DEFAULT NULL,
    CONSTRAINT qc_storage_part_detail_pkey PRIMARY KEY (id)
);

COMMENT ON COLUMN qc_storage_part_detail.platform IS '存储平台';
COMMENT ON COLUMN qc_storage_part_detail.upload_id IS '上传ID，仅在手动分片上传时使用';
COMMENT ON COLUMN qc_storage_part_detail.e_tag IS '分片 ETag';
COMMENT ON COLUMN qc_storage_part_detail.part_number IS '分片号。每一个上传的分片都有一个分片号，一般情况下取值范围是1~10000';
COMMENT ON COLUMN qc_storage_part_detail.part_size IS '文件大小，单位字节';
COMMENT ON COLUMN qc_storage_part_detail.hash_info IS '哈希信息';
COMMENT ON COLUMN qc_storage_part_detail.create_time IS '创建时间';

CREATE TABLE qc_knowledge_excel_data (
    id int8 NOT NULL,
    status bpchar(1) NULL,
    create_time timestamp NULL,
    modify_time timestamp NULL,
    create_user_id int8 NULL,
    create_user_name varchar(255) NULL,
    modify_user_id int8 NULL,
    modify_user_name varchar(255) NULL,
    sheet_id int8 NULL,
    question text NULL,
    answer text NULL,
    sequ numeric(15) NULL,
    file_id int8 NULL,
    CONSTRAINT qc_knowledge_excel_data_pkey PRIMARY KEY (id)
);


CREATE TABLE public.qc_ai_agent (
    id int8 NOT NULL,
    status bpchar(1) NULL DEFAULT '1'::bpchar,
    creator_id int8 NULL,
    creator_name varchar(32) NULL,
    create_time timestamp NULL,
    modifyier_id int8 NULL,
    modifyier_name varchar(32) NULL,
    modify_time timestamp NULL,
    model_id int8 null,
    sequ numeric(10, 2) NULL,
    name varchar(255) null,
    logo varchar(255) null,
    description varchar(500) null,
    prompt text null,
    leading_question text null,
    category_id int8 null,
    context_search_amount numeric(5) null,
    introduction varchar(500) null,
    internet_search varchar(32) null,
    null_result_answer varchar(3000) null,
    error_result_answer varchar(3000) null,
    data_fetch_url varchar(500) NULL,
    data_range text null,
    internal_flag varchar(32) null,
    publish_flag varchar(32) null,
    publish_user_id int8 null,
    publish_user_name varchar(32) null,
    publish_time timestamp null,
    model_temperature numeric(10,5) null,
    model_top_p numeric(10,5) null,
    model_max_tokens numeric(10) null,
    enable varchar(32) null,
    max_recall_count NUMERIC(8),
    min_match_threshold NUMERIC(3,2),
    qa_min_match_threshold NUMERIC(3,2),
    search_scope varchar(2),
    split_sql_prompt text null,
    biz_prompt text null,
    show_chat_log_content_type varchar(2) null,
    intent_is_enabled varchar(2) null,
    h5_url varchar(200) null,
    CONSTRAINT qc_ai_agent_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE "public"."qc_ai_agent" IS '勤策ai agent表';
COMMENT ON COLUMN "public"."qc_ai_agent"."name" IS '智能体名称';
COMMENT ON COLUMN "public"."qc_ai_agent"."logo" IS '智能体logo';
COMMENT ON COLUMN "public"."qc_ai_agent"."description" IS '描述';
COMMENT ON COLUMN "public"."qc_ai_agent"."model_id" IS '模型';
COMMENT ON COLUMN "public"."qc_ai_agent"."prompt" IS '提示';
COMMENT ON COLUMN "public"."qc_ai_agent"."leading_question" IS '引导问题';
COMMENT ON COLUMN "public"."qc_ai_agent"."category_id" IS '分类id';
COMMENT ON COLUMN "public"."qc_ai_agent"."context_search_amount" IS '上下文检索数量';
COMMENT ON COLUMN "public"."qc_ai_agent"."introduction" IS '开场白';
COMMENT ON COLUMN "public"."qc_ai_agent"."internet_search" IS '1：联网搜索，0：不联网';
COMMENT ON COLUMN "public"."qc_ai_agent"."null_result_answer" IS '空结果回复';
COMMENT ON COLUMN "public"."qc_ai_agent"."error_result_answer" IS '报错结果回复';
COMMENT ON COLUMN "public"."qc_ai_agent"."data_fetch_url" IS '数据获取url';
COMMENT ON COLUMN "public"."qc_ai_agent"."data_range" IS '数据范围';
COMMENT ON COLUMN "public"."qc_ai_agent"."internal_flag" IS '1：内置智能体，0：自定义智能体';
COMMENT ON COLUMN "public"."qc_ai_agent"."publish_flag" IS '1：已发布，0：未发布';
COMMENT ON COLUMN "public"."qc_ai_agent"."publish_time" IS '发布时间';
COMMENT ON COLUMN "public"."qc_ai_agent"."publish_user_id" IS '发布人id';
COMMENT ON COLUMN "public"."qc_ai_agent"."publish_user_name" IS '发布人名称';
COMMENT ON COLUMN "public"."qc_ai_agent"."model_temperature" IS '采样温度，控制模型生成文本的多样性。temperature越高，生成的文本更多样，反之，生成的文本更确定。';
COMMENT ON COLUMN "public"."qc_ai_agent"."model_top_p" IS '核采样的概率阈值，控制模型生成文本的多样性。top_p越高，生成的文本更多样。反之，生成的文本更确定';
COMMENT ON COLUMN "public"."qc_ai_agent"."model_max_tokens" IS '本次请求返回的最大 Token 数';
COMMENT ON COLUMN "public"."qc_ai_agent"."enable" IS '1：已启用，0：停用';
COMMENT ON COLUMN "public"."qc_ai_agent"."max_recall_count" IS '最大召回数量';
COMMENT ON COLUMN "public"."qc_ai_agent"."min_match_threshold" IS '最小匹配度';
COMMENT ON COLUMN "public"."qc_ai_agent"."qa_min_match_threshold" IS 'qa最小匹配度';
COMMENT ON COLUMN "public"."qc_ai_agent"."search_scope" IS '检索范围 1:仅引用知识库 2:引用知识库+模型通用知识库';
COMMENT ON COLUMN "public"."qc_ai_agent"."split_sql_prompt" IS '拆分为sql的提示词';
COMMENT ON COLUMN "public"."qc_ai_agent"."biz_prompt" IS '业务的提示词';
COMMENT ON COLUMN "public"."qc_ai_agent"."show_chat_log_content_type" IS 'agent日志中是否展示完整对话内容 1展示 0不展示';
COMMENT ON COLUMN "public"."qc_ai_agent"."intent_is_enabled" IS 'agent是否启用意图识别 1启用 0关闭';
COMMENT ON COLUMN "public"."qc_ai_agent"."h5_url" IS 'h5页面地址';


CREATE TABLE public.qc_ai_agent_knowledge (
    id int8 NOT NULL,
    status bpchar(1) NULL DEFAULT '1'::bpchar,
    creator_id int8 NULL,
    creator_name varchar(32) NULL,
    create_time timestamp NULL,
    agent_id int8 NULL,
    knowledge_id int8 null,
    CONSTRAINT qc_ai_agent_knowledge_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE "public"."qc_ai_agent_knowledge" IS '勤策ai agent知识库表';
COMMENT ON COLUMN "public"."qc_ai_agent_knowledge"."agent_id" IS '智能体id';
COMMENT ON COLUMN "public"."qc_ai_agent_knowledge"."knowledge_id" IS '知识库id';

CREATE TABLE public.qc_ai_agent_category (
    id int8 NOT NULL,
    status bpchar(1) NULL DEFAULT '1'::bpchar,
    creator_id int8 NULL,
    creator_name varchar(32) NULL,
    create_time timestamp NULL,
    modifyier_id int8 NULL,
    modifyier_name varchar(32) NULL,
    modify_time timestamp NULL,
    name varchar(255) null,
    CONSTRAINT qc_ai_agent_category_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE "public"."qc_ai_agent_category" IS '勤策ai agent分类表';
COMMENT ON COLUMN "public"."qc_ai_agent_category"."name" IS '分类名称';

CREATE TABLE public.qc_ai_agent_conversation (
    id int8 NOT NULL,
    status bpchar(1) NULL DEFAULT '1'::bpchar,
    creator_id int8 NULL,
    creator_name varchar(32) NULL,
    create_time timestamp NULL,
    answer_time timestamp NULL,
    session_id int8 null,
    model_id int8 null,
    user_id int8 null,
    agent_id int8 null,
    question text null,
    question_token NUMERIC(10) null,
    answer text null,
    answer_token NUMERIC(10) null,
    conversation_time NUMERIC(10,2) null,
    conversation_status varchar(32) null,
    source varchar(32) null,
    chat_session_id varchar(40) null,
    evaluation_type VARCHAR(1) default '2'::bpchar,
    feedback TEXT null,
    view_count NUMERIC(12, 0),
    reference_content TEXT,
    intent_id bigint,
    CONSTRAINT qc_ai_agent_conversation_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE "public"."qc_ai_agent_conversation" IS '勤策ai agent会话表';
COMMENT ON COLUMN "public"."qc_ai_agent_conversation"."answer_time" IS '回答时间';
COMMENT ON COLUMN "public"."qc_ai_agent_conversation"."session_id" IS '会话id';
COMMENT ON COLUMN "public"."qc_ai_agent_conversation"."model_id" IS '大模型id';
COMMENT ON COLUMN "public"."qc_ai_agent_conversation"."user_id" IS '用户id';
COMMENT ON COLUMN "public"."qc_ai_agent_conversation"."agent_id" IS '智能体id';
COMMENT ON COLUMN "public"."qc_ai_agent_conversation"."question" IS '问题';
COMMENT ON COLUMN "public"."qc_ai_agent_conversation"."question_token" IS '问题token数';
COMMENT ON COLUMN "public"."qc_ai_agent_conversation"."answer" IS '回答';
COMMENT ON COLUMN "public"."qc_ai_agent_conversation"."answer_token" IS '回答token数';
COMMENT ON COLUMN "public"."qc_ai_agent_conversation"."conversation_time" IS '会话时长，单位：秒';
COMMENT ON COLUMN "public"."qc_ai_agent_conversation"."conversation_status" IS '-1：对话失败，0：对话中，1：对话成功';
COMMENT ON COLUMN "public"."qc_ai_agent_conversation"."source" IS '1：web端，2：客户端';
COMMENT ON COLUMN "public"."qc_ai_agent_conversation"."chat_session_id" IS '前端生成，传入的记录id';
COMMENT ON COLUMN "public"."qc_ai_agent_conversation"."evaluation_type" IS '评价类型 0:不满意 1:满意 2:未评价';
COMMENT ON COLUMN "public"."qc_ai_agent_conversation"."feedback" IS '评价反馈内容';
COMMENT ON COLUMN "public"."qc_ai_agent_conversation"."view_count" IS '引用数量';
COMMENT ON COLUMN "public"."qc_ai_agent_conversation"."reference_content" IS '引用内容';
COMMENT ON COLUMN "public"."qc_ai_agent_conversation"."intent_id" IS '意图id';

CREATE TABLE public.qc_ai_agent_model (
    id int8 NOT NULL,
    status bpchar(1) NULL DEFAULT '1'::bpchar,
    creator_id int8 NULL,
    creator_name varchar(32) NULL,
    create_time timestamp NULL,
    modifyier_id int8 NULL,
    modifyier_name varchar(32) NULL,
    modify_time timestamp NULL,
    vendor varchar(255) null,
    vendor_name varchar(255) null,
    model varchar(255) null,
    model_name varchar(255) null,
    key varchar(255) null,
    icon varchar(255) null,
    secret varchar(255) null,
    enable bpchar(1) null default '0'::bpchar,
    api_key_config_url varchar(255) null,
    api_url varchar(255) null,
    api_port varchar(100) NULL,
    privacy_deployment bpchar(1) null default '0'::bpchar,
    mode_endpoint varchar(100) null,
    thinking varchar(30) null,
    aip_application_id varchar(100) null,
    aip_user_id varchar(100) null,
    CONSTRAINT qc_ai_agent_model_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE "public"."qc_ai_agent_model" IS '勤策ai agent大模型表';
COMMENT ON COLUMN "public"."qc_ai_agent_model"."vendor" IS '大模型厂商';
COMMENT ON COLUMN "public"."qc_ai_agent_model"."vendor_name" IS '大模型厂商名称';
COMMENT ON COLUMN "public"."qc_ai_agent_model"."model" IS '大模型--参数';
COMMENT ON COLUMN "public"."qc_ai_agent_model"."model_name" IS '大模型展示名称';
COMMENT ON COLUMN "public"."qc_ai_agent_model"."key" IS 'api调用key';
COMMENT ON COLUMN "public"."qc_ai_agent_model"."secret" IS 'api调用secret';
COMMENT ON COLUMN "public"."qc_ai_agent_model"."icon" IS '大模型图标url';
COMMENT ON COLUMN "public"."qc_ai_agent_model"."api_key_config_url" IS 'api调用配置url';
COMMENT ON COLUMN "public"."qc_ai_agent_model"."enable" IS '是否启用 0：未启用，1：启用';
COMMENT ON COLUMN "public"."qc_ai_agent_model"."api_url" IS '接口请求地址';
COMMENT ON COLUMN "public"."qc_ai_agent_model"."api_port" IS '接口请求端口';
comment on COLUMN "public"."qc_ai_agent_model"."privacy_deployment" IS '是否是私有化部署 0：否 1：是';
comment on COLUMN "public"."qc_ai_agent_model"."mode_endpoint" IS 'endpoint';
COMMENT ON COLUMN "public"."qc_ai_agent_model"."thinking" IS '是否开始思考 enable: 开启，disabled: 关闭';
COMMENT ON COLUMN "public"."qc_ai_agent_model"."aip_application_id" IS 'aip应用id';
COMMENT ON COLUMN "public"."qc_ai_agent_model"."aip_user_id" IS 'aip用户id';


CREATE TABLE public.qc_ai_agent_prompt (
           id int8 NOT NULL,
           status bpchar(1) NULL DEFAULT '1'::bpchar,
           creator_id int8 NULL,
           creator_name varchar(32) NULL,
           create_time timestamp NULL,
           modifyier_id int8 NULL,
           modifyier_name varchar(32) NULL,
           modify_time timestamp NULL,
           name varchar(255) null,
           describe varchar(255) null,
           content text null,
           recommend_flag varchar(32),
           logo varchar(255) null,
           CONSTRAINT qc_ai_agent_prompt_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE "public"."qc_ai_agent_prompt" IS '勤策ai agent提示词表';
COMMENT ON COLUMN "public"."qc_ai_agent_prompt"."name" IS '名称';
COMMENT ON COLUMN "public"."qc_ai_agent_prompt"."describe" IS '描述';
COMMENT ON COLUMN "public"."qc_ai_agent_prompt"."content" IS '提示词';
COMMENT ON COLUMN "public"."qc_ai_agent_prompt"."recommend_flag" IS '1：推荐';
COMMENT ON COLUMN "public"."qc_ai_agent_prompt"."logo" IS '提示词logo';


CREATE TABLE qc_ai_agent_authority_distribute_detail (
          id int8 NOT NULL,
          status char(1) COLLATE "pg_catalog"."default" DEFAULT '1'::bpchar,
          creator_id int8,
          create_time timestamp(6),
          modifier_id int8,
          modify_time timestamp(6),
          agent_id int8,
          dept_id int8,
          dept_name varchar(32) null,
          user_id int8,
          user_name varchar(32) null,
          CONSTRAINT "pk_qc_ai_agent_authority_distribute_detail" PRIMARY KEY (id),
          CONSTRAINT "agent_id_dept_id_user_id_unique" UNIQUE (agent_id,user_id, dept_id)
);
COMMENT ON TABLE "public"."qc_ai_agent_authority_distribute_detail" IS '勤策ai agent权限分配表';
COMMENT ON COLUMN "public"."qc_ai_agent_authority_distribute_detail"."agent_id" IS 'qc_ai_agent表id';
COMMENT ON COLUMN "public"."qc_ai_agent_authority_distribute_detail"."dept_id" IS '部门id';
COMMENT ON COLUMN "public"."qc_ai_agent_authority_distribute_detail"."user_id" IS '用户id';
COMMENT ON COLUMN "public"."qc_ai_agent_authority_distribute_detail"."dept_name" IS '部门名称';
COMMENT ON COLUMN "public"."qc_ai_agent_authority_distribute_detail"."user_name" IS '用户名称';

CREATE TABLE public.qc_knowledge_info_range (
            id int8 NOT NULL,
            status bpchar(1) NULL DEFAULT '1'::bpchar,
            creator_id int8 NULL,
            creator_name varchar(32) NULL,
            create_time timestamp NULL,
            modifyier_id int8 NULL,
            modifyier_name varchar(32) NULL,
            modify_time timestamp NULL,
            range_id int8 null,
            range_name varchar(255) null,
            range_type varchar(32) null,
            knowledge_id int8 null,
            CONSTRAINT qc_knowledge_info_range_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE "public"."qc_knowledge_info_range" IS '勤策ai agent知识库范围表';
COMMENT ON COLUMN "public"."qc_knowledge_info_range"."range_id" IS '部门或员工id';
COMMENT ON COLUMN "public"."qc_knowledge_info_range"."range_name" IS '部门或员工名称';
COMMENT ON COLUMN "public"."qc_knowledge_info_range"."range_type" IS '1：部门，2：员工';
COMMENT ON COLUMN "public"."qc_knowledge_info_range"."knowledge_id" IS '知识库id';

CREATE TABLE qc_ai_conversation_common_feedback (
                                                    id BIGINT PRIMARY KEY,
                                                    feedback_type VARCHAR(255) NULL,
                                                    feedback_content TEXT NOT NULL
);
Comment On Table qc_ai_conversation_common_feedback Is '通用反馈表';
Comment On Column qc_ai_conversation_common_feedback.id Is '主键';
Comment On Column qc_ai_conversation_common_feedback.feedback_type Is '反馈类型 0:不满意, 1:满意';
Comment On Column qc_ai_conversation_common_feedback.feedback_content Is '反馈内容';

CREATE TABLE "public"."qc_ai_agent_conversation_quote" (
   "id" int8 NOT NULL,
   "status" char(1) COLLATE "pg_catalog"."default" DEFAULT '1'::bpchar,
   "creator_id" int8,
   "create_time" timestamp(6),
   "modifier_id" int8,
   "modify_time" timestamp(6),
   "conversation_id" int8,
   "doc_name" varchar(100) COLLATE "pg_catalog"."default",
   "knowledge_name" varchar(32) COLLATE "pg_catalog"."default",
   "score" numeric(6,4),
   "content" text COLLATE "pg_catalog"."default",
   CONSTRAINT "pk_qc_ai_agent_conversation_quote" PRIMARY KEY ("id")
)
;
COMMENT ON COLUMN "public"."qc_ai_agent_conversation_quote"."conversation_id" IS '部门名称';
COMMENT ON COLUMN "public"."qc_ai_agent_conversation_quote"."doc_name" IS '对话id qc_ai_agent_conversation表的id';
COMMENT ON COLUMN "public"."qc_ai_agent_conversation_quote"."knowledge_name" IS '知识库名称';
COMMENT ON COLUMN "public"."qc_ai_agent_conversation_quote"."score" IS '分数';
COMMENT ON COLUMN "public"."qc_ai_agent_conversation_quote"."content" IS '引用内容';

CREATE TABLE "public"."qc_ai_agent_visit_detail" (
    "id" int8 NOT NULL,
    "status" char(1) COLLATE "pg_catalog"."default" DEFAULT '1'::bpchar,
    "creator_id" int8,
    "creator_name" varchar(32) COLLATE "pg_catalog"."default",
    "create_time" timestamp(6),
    "modifyier_id" int8,
    "modifyier_name" varchar(32) COLLATE "pg_catalog"."default",
    "modify_time" timestamp(6),
    "agent_id" int8 NOT NULL,
    "user_id" int8 NOT NULL,
    "retrieve_radius" numeric(20,2) ,
    "search_scope_days" numeric(8),
    CONSTRAINT "qc_ai_agent_visit_detail_pkey" PRIMARY KEY ("id")
)
;
COMMENT ON COLUMN "public"."qc_ai_agent_visit_detail"."agent_id" IS 'agent_id';
COMMENT ON COLUMN "public"."qc_ai_agent_visit_detail"."agent_id" IS '用户id';
COMMENT ON COLUMN "public"."qc_ai_agent_visit_detail"."retrieve_radius" IS '检索半径';
COMMENT ON COLUMN "public"."qc_ai_agent_visit_detail"."search_scope_days" IS '检索范围-天数';

CREATE TABLE "public"."qc_ai_agent_suit" (
                                             "id" int8 NOT NULL,
                                             "status" char(1) COLLATE "pg_catalog"."default" DEFAULT '1'::bpchar,
                                             "creator_id" int8,
                                             "creator_name" varchar(32) COLLATE "pg_catalog"."default",
                                             "create_time" timestamp(6),
                                             "modifyier_id" int8,
                                             "modifyier_name" varchar(32) COLLATE "pg_catalog"."default",
                                             "modify_time" timestamp(6),
                                             "name" varchar(32),
                                             "describe" varchar(255),
                                             "sequ" numeric(20,2) ,
                                             CONSTRAINT "qc_ai_agent_suit_pkey" PRIMARY KEY ("id")
)
;
COMMENT ON TABLE "public"."qc_ai_agent_suit" IS '勤策ai agent api套件';
COMMENT ON COLUMN "public"."qc_ai_agent_suit"."name" IS '名称';
COMMENT ON COLUMN "public"."qc_ai_agent_suit"."describe" IS '描述';
COMMENT ON COLUMN "public"."qc_ai_agent_suit"."sequ" IS '排序';


CREATE TABLE "public"."qc_ai_agent_suit_module" (
                                                    "id" int8 NOT NULL,
                                                    "status" char(1) COLLATE "pg_catalog"."default" DEFAULT '1'::bpchar,
                                                    "creator_id" int8,
                                                    "creator_name" varchar(32) COLLATE "pg_catalog"."default",
                                                    "create_time" timestamp(6),
                                                    "modifyier_id" int8,
                                                    "modifyier_name" varchar(32) COLLATE "pg_catalog"."default",
                                                    "modify_time" timestamp(6),
                                                    "suit_id" int8,
                                                    "name" varchar(32),
                                                    "describe" varchar(255),
                                                    "sequ" numeric(20,2) ,
                                                    CONSTRAINT "qc_ai_agent_suit_module_pkey" PRIMARY KEY ("id")
)
;
COMMENT ON TABLE "public"."qc_ai_agent_suit_module" IS '勤策ai agent api套件模块';
COMMENT ON COLUMN "public"."qc_ai_agent_suit_module"."name" IS '名称';
COMMENT ON COLUMN "public"."qc_ai_agent_suit_module"."describe" IS '描述';
COMMENT ON COLUMN "public"."qc_ai_agent_suit_module"."sequ" IS '排序';


CREATE TABLE "public"."qc_ai_agent_module_api" (
                                                   "id" int8 NOT NULL,
                                                   "status" char(1) COLLATE "pg_catalog"."default" DEFAULT '1'::bpchar,
                                                   "creator_id" int8,
                                                   "creator_name" varchar(32) COLLATE "pg_catalog"."default",
                                                   "create_time" timestamp(6),
                                                   "modifyier_id" int8,
                                                   "modifyier_name" varchar(32) COLLATE "pg_catalog"."default",
                                                   "modify_time" timestamp(6),
                                                   "module_id" int8,
                                                   "name" varchar(32),
                                                   "describe" varchar(255),
                                                   "sequ" numeric(20,2) ,
                                                   "url" varchar(255),
                                                   "document_url" varchar(255),
                                                   "request_method" varchar(32),
                                                   "headers" text,
                                                   "body" text,
                                                   "query_param" text,
                                                   "response" text,
                                                   CONSTRAINT "qc_ai_agent_module_api_pkey" PRIMARY KEY ("id")
)
;
COMMENT ON TABLE "public"."qc_ai_agent_module_api" IS '勤策ai agent api套件模块';
COMMENT ON COLUMN "public"."qc_ai_agent_module_api"."module_id" IS '名称';
COMMENT ON COLUMN "public"."qc_ai_agent_module_api"."name" IS '名称';
COMMENT ON COLUMN "public"."qc_ai_agent_module_api"."describe" IS '描述';
COMMENT ON COLUMN "public"."qc_ai_agent_module_api"."sequ" IS '排序';
COMMENT ON COLUMN "public"."qc_ai_agent_module_api"."url" IS '接口地址';
COMMENT ON COLUMN "public"."qc_ai_agent_module_api"."document_url" IS '接口文档地址';
COMMENT ON COLUMN "public"."qc_ai_agent_module_api"."request_method" IS '请求方法';
COMMENT ON COLUMN "public"."qc_ai_agent_module_api"."headers" IS '请求头';
COMMENT ON COLUMN "public"."qc_ai_agent_module_api"."body" IS '请求体';
COMMENT ON COLUMN "public"."qc_ai_agent_module_api"."query_param" IS 'get请求参数';
COMMENT ON COLUMN "public"."qc_ai_agent_module_api"."response" IS '返回值';


create table "public"."qc_knowledge_file_category"
(
    id            bigint       not null
        primary key,
    category_name varchar(255) not null,
    description   text,
    create_time   timestamp(6) default now(),
    modify_time   timestamp(6) default now(),
    status        varchar(1),
    collection_id bigint
);
comment on table "public"."qc_knowledge_file_category" is '知识库文件分类表';
comment on column "public"."qc_knowledge_file_category".id is '分类ID';
comment on column "public"."qc_knowledge_file_category".category_name is '分类名称';
comment on column "public"."qc_knowledge_file_category".description is '分类描述';
comment on column "public"."qc_knowledge_file_category".create_time is '创建时间';
comment on column "public"."qc_knowledge_file_category".modify_time is '修改时间';
comment on column "public"."qc_knowledge_file_category".status is '状态 0:正常 1:删除';
comment on column "public"."qc_knowledge_file_category".collection_id is '知识库ID';


CREATE TABLE qc_ai_agent_intent_recognition (
  "id" int8 NOT NULL,
  "status" char(1) COLLATE "pg_catalog"."default" DEFAULT '1'::bpchar,
  "creator_id" int8,
  "creator_name" varchar(32) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "modifyier_id" int8,
  "modifyier_name" varchar(32) COLLATE "pg_catalog"."default",
  "modify_time" timestamp(6),
  "type" varchar(2) COLLATE "pg_catalog"."default",
  "agent_id" int8,
  "intent_name" text,
  "intent_code" varchar(32),
  "max_recall_count" numeric(8,0),
  "min_match_threshold" numeric(3,2),
  "qa_min_match_threshold" numeric(3,2),
  "search_scope" varchar(2) COLLATE "pg_catalog"."default"
);

COMMENT ON COLUMN qc_ai_agent_intent_recognition."type" IS '类型 1：知识库 2：文件';
COMMENT ON COLUMN qc_ai_agent_intent_recognition."agent_id" IS 'qc_ai_agent 表的id';
COMMENT ON COLUMN qc_ai_agent_intent_recognition."intent_name" IS '意图名称';
COMMENT ON COLUMN qc_ai_agent_intent_recognition."intent_code" IS '意图编码';
COMMENT ON COLUMN qc_ai_agent_intent_recognition."max_recall_count" IS '最大召回数量';
COMMENT ON COLUMN qc_ai_agent_intent_recognition."min_match_threshold" IS '最小匹配度';
COMMENT ON COLUMN qc_ai_agent_intent_recognition."search_scope" IS '检索范围 1:仅引用知识库 2:引用知识库+模型通用知识库';
COMMENT ON COLUMN qc_ai_agent_intent_recognition."qa_min_match_threshold" IS 'qa最小匹配度';
COMMENT ON TABLE qc_ai_agent_intent_recognition IS '勤策ai agent意图识别表';


CREATE TABLE qc_ai_agent_intent_knowledge_detail (
   "id" int8 NOT NULL,
   "status" char(1) COLLATE "pg_catalog"."default" DEFAULT '1'::bpchar,
   "creator_id" int8,
   "creator_name" varchar(32) COLLATE "pg_catalog"."default",
   "create_time" timestamp(6),
   "modifyier_id" int8,
   "modifyier_name" varchar(32) COLLATE "pg_catalog"."default",
   "modify_time" timestamp(6),
   "agent_id" int8,
   "intent_id" int8,
   "collection_id" int8
);

COMMENT ON COLUMN qc_ai_agent_intent_knowledge_detail."agent_id" IS 'agent_id - qc_ai_agent的id';
COMMENT ON COLUMN qc_ai_agent_intent_knowledge_detail."intent_id" IS '意图id  qc_ai_agent_Intent_recognition的id';
COMMENT ON COLUMN qc_ai_agent_intent_knowledge_detail."collection_id" IS '知识库id  qc_knowledge_info的id';
COMMENT ON TABLE qc_ai_agent_intent_knowledge_detail IS '勤策ai agent意图识别关联的知识库表';


CREATE TABLE qc_ai_agent_intent_knowledge_category_mapping (
  id BIGSERIAL PRIMARY KEY,
  "status" char(1) COLLATE "pg_catalog"."default" DEFAULT '1'::bpchar,
  agent_id BIGINT NOT NULL,
  relation_id BIGINT NOT NULL,
  collection_id BIGINT NOT NULL,
  category_id BIGINT NOT NULL
);

COMMENT ON TABLE qc_ai_agent_intent_knowledge_category_mapping IS '知识库与类型映射表';
COMMENT ON COLUMN qc_ai_agent_intent_knowledge_category_mapping.id IS 'id - 主键';
COMMENT ON COLUMN qc_ai_agent_intent_knowledge_detail."agent_id" IS 'agent_id - qc_ai_agent的id';
COMMENT ON  COLUMN qc_ai_agent_intent_knowledge_category_mapping.relation_id IS 'relation_id - qc_ai_agent_intent_knowledge_detail的id';
COMMENT ON  COLUMN qc_ai_agent_intent_knowledge_category_mapping.collection_id IS 'collection_id - qc_knowledge_info的id';
COMMENT ON  COLUMN qc_ai_agent_intent_knowledge_category_mapping.category_id IS 'category_id - qc_knowledge_file_category的id';

CREATE TABLE qc_ai_agent_intent_file_detail (
  "id" int8 NOT NULL,
  "status" char(1) COLLATE "pg_catalog"."default" DEFAULT '1'::bpchar,
  "creator_id" int8,
  "creator_name" varchar(32) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "modifyier_id" int8,
  "modifyier_name" varchar(32) COLLATE "pg_catalog"."default",
  "modify_time" timestamp(6),
  "agent_id" int8,
  "intent_id" int8,
  "file_id" int8
);

COMMENT ON COLUMN qc_ai_agent_intent_file_detail."agent_id" IS 'agent_id - qc_ai_agent的id';
COMMENT ON COLUMN qc_ai_agent_intent_file_detail."intent_id" IS '意图id  qc_ai_agent_Intent_recognition的id';
COMMENT ON COLUMN qc_ai_agent_intent_file_detail."file_id" IS '文件id  qc_upload_file的id';

CREATE TABLE qc_upload_file (
    "id" INT8 NOT NULL,
    "status" VARCHAR ( 1 ) COLLATE "pg_catalog"."default",
    "create_user_id" INT8,
    "create_user_name" VARCHAR ( 255 ) COLLATE "pg_catalog"."default",
    "create_time" TIMESTAMP ( 6 ),
    "modify_time" TIMESTAMP ( 6 ),
    "modify_user_id" INT8,
    "modify_user_name" VARCHAR ( 255 ) COLLATE "pg_catalog"."default",
    "name" VARCHAR ( 4000 ) COLLATE "pg_catalog"."default",
    "size" NUMERIC ( 20, 0 ),
    "platform" VARCHAR ( 32 ) COLLATE "pg_catalog"."default",
    "url" VARCHAR ( 4000 ) COLLATE "pg_catalog"."default",
    "base_path" VARCHAR ( 4000 ) COLLATE "pg_catalog"."default",
    "path" VARCHAR ( 4000 ) COLLATE "pg_catalog"."default",
    "ext" VARCHAR ( 4000 ) COLLATE "pg_catalog"."default",
    CONSTRAINT "qc_intent_relation_file_pkey" PRIMARY KEY ( "id" )
);
COMMENT ON COLUMN qc_upload_file.NAME IS '文件名称';
COMMENT ON COLUMN qc_upload_file.SIZE IS '文件大小';
COMMENT ON COLUMN qc_upload_file.platform IS '平台';
COMMENT ON COLUMN qc_upload_file.url IS '文件路径-绝对路径';
COMMENT ON COLUMN qc_upload_file.base_path IS '基础路径';
COMMENT ON COLUMN qc_upload_file.PATH IS '相对路径';
COMMENT ON COLUMN qc_upload_file.ext IS '文件后缀';


CREATE TABLE public.qc_ai_agent_ext_config
(
    id           int8 PRIMARY KEY,
    agent_id     INT8                  NOT NULL,
    config_key   VARCHAR(64)           NOT NULL,
    description  VARCHAR(255)          ,
    config_value VARCHAR(255)          ,
    status       bpchar(1) DEFAULT '1' NOT NULL,
    create_time  TIMESTAMP DEFAULT now(),
    modify_time  TIMESTAMP DEFAULT now()
);

COMMENT ON TABLE public.qc_ai_agent_ext_config IS 'AI 智能体扩展参数配置表';
COMMENT ON COLUMN public.qc_ai_agent_ext_config.agent_id IS '关联的智能体ID';
COMMENT ON COLUMN public.qc_ai_agent_ext_config.config_key IS '配置项Key';
COMMENT ON COLUMN public.qc_ai_agent_ext_config.config_value IS '配置项Value';
COMMENT ON COLUMN public.qc_ai_agent_ext_config.description IS '配置项描述';
COMMENT ON COLUMN public.qc_ai_agent_ext_config.status IS '状态：1-启用，0-禁用';

CREATE TABLE public.qc_ai_agent_recommend_questions (
    "id" int8 NOT NULL,
    "status" char(1) COLLATE "pg_catalog"."default" DEFAULT '1'::bpchar,
    "creator_id" int8,
    "creator_name" varchar(32) COLLATE "pg_catalog"."default",
    "create_time" timestamp(6),
    "conversation_id" int8,
    "content" varchar(200) ,
    "seq" numeric(10,2) ,
    CONSTRAINT "qc_ai_agent_recommend_questions_pkey" PRIMARY KEY ("id")
);

COMMENT ON COLUMN public.qc_ai_agent_recommend_questions."conversation_id" IS '对话id';
COMMENT ON COLUMN public.qc_ai_agent_recommend_questions."content" IS '推荐问题';
COMMENT ON COLUMN public.qc_ai_agent_recommend_questions."seq" IS '排序号';
COMMENT ON TABLE public.qc_ai_agent_recommend_questions IS '勤策ai agent推荐问题表';

CREATE TABLE qc_ai_agent_goods_recommend (
                                          id BIGINT PRIMARY KEY,
                                          store_id BIGINT NOT NULL,
                                          source VARCHAR(50),
                                          timestamp VARCHAR(50),
                                          unsold_goods TEXT,
                                          recommend_goods TEXT,
                                          recommend_questions TEXT,
                                          create_date DATE DEFAULT CURRENT_DATE
);

COMMENT ON TABLE qc_ai_agent_goods_recommend IS 'AI商品推荐表';
COMMENT ON COLUMN qc_ai_agent_goods_recommend.id IS '主键ID';
COMMENT ON COLUMN qc_ai_agent_goods_recommend.store_id IS '门店ID';
COMMENT ON COLUMN qc_ai_agent_goods_recommend.source IS '数据来源（order:订单,delivery:铺货,sales:销量,stock:库存）';
COMMENT ON COLUMN qc_ai_agent_goods_recommend.timestamp IS '时间戳标识，用于检测数据变化';
COMMENT ON COLUMN qc_ai_agent_goods_recommend.unsold_goods IS '可卖商品列表';
COMMENT ON COLUMN qc_ai_agent_goods_recommend.recommend_goods IS '推荐商品列表';
COMMENT ON COLUMN qc_ai_agent_goods_recommend.recommend_questions IS '推荐问题列表';
COMMENT ON COLUMN qc_ai_agent_goods_recommend.create_date IS '创建日期';
ALTER TABLE qc_ai_agent_goods_recommend
    ADD CONSTRAINT qc_ai_agent_goods_recommend_uniq_store_id UNIQUE (store_id);


CREATE TABLE qc_ai_agent_goods_unsold (
                                       id VARCHAR(50) NOT NULL,
                                       name VARCHAR(255) NOT NULL,
                                       brand VARCHAR(255),
                                       tag_values TEXT,
                                       short_name VARCHAR(255),
                                       create_time TIMESTAMP,
                                       modify_time TIMESTAMP,
                                       short_name VARCHAR(255),
                                       PRIMARY KEY (id)
);

COMMENT ON TABLE qc_ai_agent_goods_unsold IS '未卖进商品表';
COMMENT ON COLUMN qc_ai_agent_goods_unsold.id IS '商品ID';
COMMENT ON COLUMN qc_ai_agent_goods_unsold.name IS '商品名称';
COMMENT ON COLUMN qc_ai_agent_goods_unsold.short_name IS '商品简称';
COMMENT ON COLUMN qc_ai_agent_goods_unsold.brand IS '商品品牌';
COMMENT ON COLUMN qc_ai_agent_goods_unsold.tag_values IS '商品标签值';

CREATE TABLE qc_ai_store_goods_unsold (
                                          store_id bigint NOT NULL,
                                          goods_id VARCHAR(32) NOT NULL,
                                          create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                          PRIMARY KEY (store_id, goods_id)
) PARTITION BY HASH (store_id);
comment on table qc_ai_store_goods_unsold is '门店商品关系表';
comment on column qc_ai_store_goods_unsold."store_id" is '店铺id';
comment on column qc_ai_store_goods_unsold."goods_id" is '商品id';
comment on column qc_ai_store_goods_unsold."create_time" is '创建时间';

DO $$
    DECLARE
        i INT;
        partition_sql TEXT;
    BEGIN
        FOR i IN 0..99 LOOP
                partition_sql := format(
                        'CREATE TABLE qc_ai_store_goods_unsold_p%s PARTITION OF qc_ai_store_goods_unsold FOR VALUES WITH (MODULUS 100, REMAINDER %s);',
                        i, i
                                 );
                EXECUTE partition_sql;
            END LOOP;
    END $$;

