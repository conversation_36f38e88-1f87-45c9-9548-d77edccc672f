# 🧠 客户洞察助手功能设计文档

---

## 一、背景说明

客户洞察助手是基于大模型的业务 Agent，结合内部系统接口数据，智能分析客户或经销商的业务表现，生成可视化洞察报告。在前端通过编排页面配置定义不同的洞察逻辑，后端负责调度接口和生成分析结果。
客户洞察助手智能体在 qc_ai_agent 中维护一条数据，qc_ai_agent 为智能体表。

---

## 二、核心业务流程

### 1. 客户意图识别

- 用户通过对话发起洞察请求；
- 使用大模型识别用户的意图；
- 能够匹配客户 OR 门店/经销商的名称，那么直接进行按编排配置后续逻辑，如匹配不到，则需要用户选择/补充
- 若识别失败，则由页面提示用户选择客户对象。

### 2. 洞察数据生成流程

- 若客户 **从未生成过洞察数据**：

  - 根据客户 ID 拉取各业务维度的数据；
  - 发起大模型推理生成完整洞察结果；
  - 返回并展示；

- 若客户 **已生成过洞察数据**：

  - 查询已有结果；
  - 直接展示。

---

## 三、洞察配置结构

洞察助手的配置根据企业配置（查询接口可得）分为两种模式，每种模式下涵盖多个配置项，洞察维度配置项包含多个维度，总结及建议配置项涵盖多个衡量标准。

### 1. 洞察模式类型

| 模式           | 编排配置项                         |
| -------------- | ---------------------------------- |
| 客户模式       | 客户维度 + 总结及建议              |
| 终端经销商模式 | 终端维度 + 经销商维度 + 总结及建议 |

### 2. 页面展示控制

| 参数                    | 说明                 |
| ----------------------- | -------------------- |
| show_on_visit_page      | 是否在拜访页面展示   |
| show_on_customer_detail | 是否在客户详情页展示 |

以上两参数可在表 qc_ai_agent_ext_config 中维护，关联在 qc_ai_agent 中新增的客户洞察助手的 agent_id

## 四、维度配置详情

每种模式配置项中均支持配置多个维度，每个维度支持多个数据源、指标项和提示词。如客户模式的客户维度配置项内可配置多个具体维度，具体维度结构如下：

### 1. 模式维度配置项具体维度字段

| 字段名       | 说明                                                                                                                                                                                                                                                                 |
| ------------ | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 维度标识     | 维度标识类型包括{“订单”、“铺货”、“生动化”、“付费陈列”、“兑换券”、“资产”、“真实性”}，类型后续可能会新增                                                                                                                                                               |
| 展示名称     | 配置的具体维度展示的名称                                                                                                                                                                                                                                             |
| 数据源       | 一个具体维度可以配置多个数据源，每个数据源都有自己的查询业务，数据类型，数据源名称，查询范围（其实就是配置该维度可以调用哪些接口，返回哪些数据来组合），该数据源应该是要单独设置一个表的，并且数据源是一些接口，在配置的时候是要选则的，所以这里估计要搞个配置表应该 |
| 解读提示词   | 大模型提示词                                                                                                                                                                                                                                                         |
| 衡量标准定义 | 每个具体维度支持多个衡量标准（名称 + 定义）这里要和总结及建议的标准设置为 1 个表么，反正都是名称+定义                                                                                                                                                                |

---

### 2. 完整维度示例列表

| 维度标识 | 展示名称 | 查询业务                                         | 数据类型    | 数据源示例                                                                                           |
| -------- | -------- | ------------------------------------------------ | ----------- | ---------------------------------------------------------------------------------------------------- |
| 订单     | 订单     | 分销订单                                         | 明细 / 指标 | - 订单满足率（明细）<br>- 订单配送率（明细）<br>- 分销订单明细（明细）<br>- 分销订单随单促销（明细） |
| 铺货     | 铺货     | 铺货上报 / 铺货上报（old）                       | 明细 / 指标 | - 铺货标准合格次数（指标）<br>- 铺货标准不合格次数（指标）<br>- 铺货率（定制）（指标）               |
| 生动化   | 生动化   | 拜访                                             | 明细 / 指标 | - 拜访子任务中的字段值（明细）                                                                       |
| 付费陈列 | 付费陈列 | 付费陈列                                         | 明细 / 指标 | - 费效比（指标）                                                                                     |
| 兑换券   | 兑换券   | 兑换券                                           | 明细 / 指标 | - 兑换券明细（指标）                                                                                 |
| 资产     | 资产     | 资产投放                                         | 明细 / 指标 | - 资产投放明细（指标）                                                                               |
| 真实性   | 真实性   | 翻拍 / 窜拍 / 客户主数据 / 客户联系人 / 相似照片 | 明细 / 指标 | - 翻拍明细（明细）<br>- 窜拍明细（明细）<br>- 字段明细（明细）<br>- 相似照片（明细）                 |

---

## 五、总结及建议配置项

每种模式支持独立的总结及建议配置：

| 字段名称           | 说明                                                                                                                         |
| ------------------ | ---------------------------------------------------------------------------------------------------------------------------- |
| 衡量标准           | 包含名称和定义，比如：健康：总分 ≥6 分判定为 “健康”。但是这里不能用 json，是不是和内个具体维度配置中的标准设置同一个表比较好 |
| 综合衡量标准提示词 | 引导模型分析维度表现的提示词                                                                                                 |
| 总结及建议的提示词 | 引导模型生成最终建议总结的提示词                                                                                             |

---

## 六、日志记录结构

洞察助手每一次分析都记录完整日志，已有日志表 qc_ai_agent_conversation，但是有些字段似乎是缺乏的，能不能建立在原来的基础上进行扩展，既不要直接修改 qc_ai_agent_conversation 表，也不要完全直接新建一个表代替 qc_ai_agent_conversation。便于后续查询、评估与优化：

### 1. 日志字段说明

| 字段名             | 类型 / 描述                           |
| ------------------ | ------------------------------------- |
| 发起时间           | timestamp                             |
| 用户               | 用户 ID / 姓名                        |
| 洞察对象           | 客户或经销商 ID                       |
| 洞察配置           | 使用的配置 ID                         |
| 状态               | 处理中 / 成功 / 失败                  |
| 生成时间           | 洞察完成时间                          |
| 消耗 TOKENS        | 本次分析总共消耗的 token 数           |
| 耗时（秒）         | 总耗时                                |
| 诊断结论           | 模型生成的总体结论                    |
| 订单洞察评价与结果 | 如“订单满足率达标”，数据为结构化 JSON |
| 铺货洞察评价与结果 | 同上                                  |
| 总结及建议         | 最终的建议内容                        |
| 用户反馈状态       | 已反馈 / 未反馈                       |
| 用户反馈内容       | 自定义文本                            |
| tokens 分类明细    | 每个维度消耗的 token 数量（结构化）   |

---

## 七、建模目标说明（交给大模型或 Cursor）

```text
请根据本功能文档，完成以下工作：

1. 概念建模：抽象出本系统中涉及的实体及关系；
2. 逻辑建模：为每个实体设计字段及字段含义；
3. 物理建模：为 PostgreSQL 生成建表 SQL；
4. 要求：
   - 使用语义明确的表名和字段名；
   - 明细/结构化字段建议使用 JSONB；
   - 所有主键为 BIGINT，必要字段加索引；
   - 外键字段命名为 xxx_id；
   - 时间字段统一为 `timestamp`；
```

---
