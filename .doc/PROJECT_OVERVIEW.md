# **企业级应用开发规范文档 (qc-ai-agent 项目)**

## **1. 项目概况分析**

### **1.1 代码结构与技术栈**

- **项目架构**: 本项目是一个基于 **Maven** 构建的 **多模块 (Multi-module)** 项目。根 `pom.xml` 以 `packaging: pom` 的形式聚合了所有子模块，体现了良好的模块化设计思想。
- **核心框架**:
  - **后端**: **Spring Boot 2.7.14**
  - **Java 版本**: **Java 17**
- **技术栈**:
  - **数据库**: **PostgreSQL**
  - **持久层框架**: **MyBatis**
  - **缓存**: **Redis**
  - **分布式任务**: **Elastic-Job**
  - **日志**: **SLF4J** + **Log4j2**
  - **AI/LLM**: 集成**腾讯云混元大模型**，并通过自定义的 `ai-agent-lla` 模块进行适配。
  - **向量数据库**: 通过 `ai-agent-vectordb` 模块进行管理。
  - **文件处理**: **Apache POI** 和 **EasyExcel** 用于处理 Office 文档。
  - **测试**: **JUnit 5** 和 **Mockito**。

### **1.2 主要模块与职责划分**

项目遵循“高内聚、低耦合”的原则，将不同功能拆分到独立的模块中，职责清晰：

- `admin`: **核心业务与启动模块**。作为项目的可执行入口，负责编排其他模块的服务，实现具体的业务逻辑。
- `ai-agent-common`: **通用基础模块**。封装了项目范围内的通用工具类、统一异常处理、拦截器、统一响应体等基础组件。
- `ai-agent-jdbc`: **数据库访问模块**。定义数据源配置等。
- `ai-agent-lla`: **大语言模型适配层**。隔离了对具体 LLM SDK 的直接依赖，便于未来切换或扩展模型。
- `ai-agent-redis`: **Redis 客户端模块**。提供 Redis 相关操作的封装。
- `ai-agent-storage`: **存储服务模块**。用于处理文件上传、下载等。
- `ai-agent-vectordb`: **向量数据库交互模块**。
- `ai-agent-elastic-job`: **分布式任务模块**。

## **2. 命名规范**

### **2.1 Java 代码命名**

- **类名**: **大驼峰命名法 (UpperCamelCase)**。
  - **Service/ServiceImpl**: 必须以 `Service` / `ServiceImpl` 结尾。例：`QcAiAgentConversationService` / `QcAiAgentConversationServiceImpl`。
  - **Controller**: 必须以 `Controller` 结尾。例：`QcAiAgentConversationController`。
  - **POJO**:
    - 数据传输对象 (DTO): 以 `DTO` 结尾。例：`QcAiAgentChatDTO`。
    - 视图对象 (VO): 以 `VO` 结尾。例：`ConversationVO`。
    - 数据库持久化对象 (PO/DO): 直接使用名词。例：`QcAiAgentConversation`。
    - 查询参数对象: 以 `Query` 结尾。例：`ExtractConversationQuery`。
- **方法名**: **小驼峰命名法 (lowerCamelCase)**。动词或动宾短语。
  - `select` / `query` / `get`: 查询操作。
  - `insert` / `add` / `save`: 新增操作。
  - `update`: 修改操作。
  - `delete` / `remove`: 删除操作。
- **变量名**: **小驼峰命名法 (lowerCamelCase)**。
- **常量名**: **全大写，下划线分隔 (UPPER_SNAKE_CASE)**。例：`CONVERSATION_SUCCESS`。

### **2.2 数据库命名**

- **表名**: **小写，下划线分隔 (snake_case)**。采用 `业务模块_名词` 的格式。例：`qc_ai_dimension_config`。
- **字段名**: **小写，下划线分隔 (snake_case)**。例：`creator_id`, `modify_time`。
- **索引**: `idx_字段名` 或 `uk_字段名`（唯一索引）。

## **3. 目录结构规范与模块职责划分**

- **遵循标准 Maven 目录**: `src/main/java`, `src/main/resources`, `src/test/java`。
- **包结构**: `com.qc.agent.app.{模块名}.{分层}`。
  - **分层**: `controller`, `service`, `mapper`, `model` (或 `pojo`, `dto`, `vo`), `util`, `config`, `enums`, `constants`。
- **模块职责**:
  - **`common` 模块**: 只存放项目通用的、无业务耦合的代码。
  - **业务模块 (`admin` 内)**: 各业务逻辑应在 `app` 包下按业务域划分包，如 `agent`, `knowledge`。领域内的工具类应放在该业务包的 `util` 下，而非 `common` 模块。

## **4. 通用写法建议**

### **4.1 工具类 (Util)**

- **风格**: 工具类应设计为**无状态**的。
- **规范**:

  1.  类名以 `Utils` 或 `Util` 结尾。
  2.  方法应为 `public static`。
  3.  **必须提供一个私有构造函数**，防止被实例化。

  ```java
  public final class MyCustomUtils {
      private MyCustomUtils() {
          // 防止实例化
      }

      public static void doSomething() {
          // ...
      }
  }
  ```

  4.  **异常处理**: 工具类中的方法不应直接 `throws Exception`。应捕获具体异常，并包装为自定义的运行时异常（如 `SystemException` 或 `BizException`）。

### **4.2 常量类 (Constants)**

- **风格**: 使用 `public final class` + `public static final` 字段来定义。
- **规范**:
  1.  不应在一个 `CommonConstants` 文件中堆积所有常量。应按业务域或层级进行拆分。例如 `MessageConstants`, `RedisKeyConstants`。
  2.  禁止使用接口定义常量。

### **4.3 枚举类 (Enum)**

- **强烈推荐**使用枚举类代替常量类来表示有固定范围的业务状态、类型等。
- **规范**:

  1.  所有表示状态、类型等的 `public static final String` 都应重构为枚举。
  2.  枚举应包含 `code` 和 `description` 两个字段，并提供 `getter` 方法。

  ```java
  // 重构前 (在 POJO 中)
  // public static final String CONVERSATION_SUCCESS = "1";

  // 重构后
  @Getter
  @AllArgsConstructor
  public enum ConversationStatusEnum {
      FAIL("-1", "回答失败"),
      IN_PROGRESS("0", "回答中"),
      SUCCESS("1", "回答成功");

      private final String code;
      private final String description;

      // 可选：提供一个静态方法用于从 code 转换
      public static ConversationStatusEnum fromCode(String code) {
          return Arrays.stream(values())
                       .filter(e -> e.code.equals(code))
                       .findFirst()
                       .orElse(null);
      }
  }
  ```

## **5. 接口设计与响应规范**

### **5.1 统一响应体**

- **规范**: 所有 Controller 返回给前端的 **JSON 数据**，都必须封装在 `com.qc.agent.common.core.Message` 对象中。
- **结构**:
  ```json
  {
    "code": "1", // 状态码: "1" 为成功, "0" 为失败
    "message": "操作成功", // 提示信息
    "data": { ... } // 业务数据
  }
  ```
- **使用**: 采用链式调用生成响应。
  ```java
  // 成功返回数据
  return Message.of().data(userInfo).ok();
  // 成功但无数据
  return Message.of().ok("删除成功");
  // 失败
  return Message.of().error("用户名不存在");
  ```

### **5.2 统一分页封装**

- **现状**: 暂未发现统一的分页响应对象。
- **建议**: 参照 `MyBatis` 的 `PageInfo` 或自定义一个分页 VO 类，封装分页数据。
  ```java
  @Data
  public class PageResult<T> {
      private int pageNum;    // 当前页码
      private int pageSize;   // 每页数量
      private long totalSize;  // 总记录数
      private int totalPages; // 总页数
      private List<T> content;  // 数据列表
  }
  ```
  Controller 返回时，将 `PageResult` 对象放入 `Message` 的 `data` 字段。

### **5.3 统一异常处理**

- **规范**: 利用 `GlobalExceptionHandler` 进行全局异常处理。
- **自定义异常**:
  - `BizException`: **业务异常**。用于可预见的业务逻辑错误，如“用户名已存在”、“余额不足”。这种异常**不需要记录详细的 error 日志**，因为它是正常业务流的一部分。
  - `SystemException`: **系统异常**。用于非预期的、由代码缺陷或外部资源问题（如数据库连接失败）导致的错误。**必须记录 error 级别的日志**，并包含异常堆栈。
- **Controller 规范**: Controller 方法中**原则上不应出现 `try-catch` 块**。直接将业务异常 `throw` 出去，交由 `GlobalExceptionHandler` 统一处理。

## **6. ID 生成策略与数据库规范**

### **6.1 ID 生成策略**

- **数据库主键**: 所有数据表的主键 (`id`) 统一使用 **PostgreSQL 的 `BIGSERIAL` 类型**，由数据库负责自增。
- **分布式唯一 ID**: 在需要程序生成唯一 ID 的场景（如 `chatSessionId`），调用 `UUIDUtils.getUUID2Long()` 方法。该方法生成的负数长整型可以有效避免与数据库自增主键冲突。

### **6.2 数据库规范**

- **通用字段**: 所有业务表都应包含以下审计字段，由持久层框架自动填充：
  - `creator_id`, `creator_name`, `create_time`
  - `modifyier_id`, `modifyier_name`, `modify_time`
- **注释**: **必须**为每个表和每个字段添加 `COMMENT` 注释，说明其业务含义。
- **逻辑删除**: 使用 `status` 字段 (`CHAR(1)`) 进行逻辑删除，`'1'` 表示有效，`'0'` 表示无效。

## **7. 日志、注释、代码格式、单元测试**

### **7.1 日志规范**

- **使用 SLF4J**: 统一使用 `org.slf4j.Logger` 和 `org.slf4j.LoggerFactory`。
- **日志级别**:
  - `error`: 系统级错误、严重异常。**必须包含异常堆栈信息**。
  - `warn`: 业务逻辑中出现的不期望但可恢复的情况。
  - `info`: 关键业务流程的开始/结束、重要状态变更。
  - `debug`: 开发和测试阶段用于调试的详细信息。
- **规范**:
  1.  使用**占位符 `{}`** 而非字符串拼接。
      ```java
      // 好
      log.info("User {} logged in successfully.", userId);
      // 差
      log.info("User " + userId + " logged in successfully.");
      ```
  2.  在 `catch` 块中记录 `error` 日志时，**必须将异常对象作为最后一个参数传入**。
      ```java
      catch (Exception e) {
          log.error("Failed to process order {}", orderId, e);
      }
      ```

### **7.2 注释规范**

- **类注释**: 所有类都必须有 Javadoc 注释，说明类的用途、作者、创建日期。
- **方法注释**: 所有 `public` 方法都必须有 Javadoc 注释，说明方法的功能、参数 (`@param`)、返回值 (`@return`)。
- **代码注释**: 对于复杂或非直观的业务逻辑，应在代码上方添加必要的块注释。

### **7.3 代码格式**

- **遵循标准**: 团队应统一代码格式化风格。建议使用 IDE 内置的格式化功能，并共享配置文件。
- **Lombok**: 推荐广泛使用 Lombok (`@Data`, `@Getter`, `@Setter`, `@Builder`, `@Slf4j`等) 来简化代码。

### **7.4 单元测试**

- **强制要求**: 核心业务逻辑、复杂算法、通用工具类**必须编写单元测试**。
- **框架**: 使用 **JUnit 5** + **Mockito**。
- **目标**: 提高代码质量和可维护性，确保重构的安全性。

## **8. 总结与后续建议**

### **8.1 总结**

当前项目具有非常良好的基础：

- **架构清晰**: 多模块划分合理，职责明确。
- **技术选型现代**: 采用 Spring Boot、Java 17 等主流技术。
- **已有良好实践**: 实现了事实上的统一响应、全局异常处理，数据库设计规范。

主要问题在于部分规范未能完全统一和贯彻，存在一些“重复造轮子”和“魔术字符串”的问题。

### **8.2 重构与优化建议**

1.  **枚举类重构 (高优先级)**:
    - **任务**: 将 `QcAiAgentConversation.java` 等 POJO 中定义的 `public static final String` 状态常量，全部重构为枚举类 (`Enum`)。
    - **收益**: 提升类型安全，增强代码可读性和可维护性。
2.  **统一分页封装**:
    - **任务**: 设计一个统一的分页 VO（如 `PageResult`），并应用于所有分页查询接口。
    - **收益**: 统一前端分页数据处理逻辑。
3.  **工具类规范化**:
    - **任务**: 检查所有 `*Utils` 类，确保它们都有私有构造函数，并对异常进行了合理封装。
    - **收益**: 提升代码健壮性。
4.  **`GlobalExceptionHandler` 优化**:
    - **任务**: 审视 `handleGlobalBizException` 方法，其逻辑似乎与 `handleGlobalException` 中对 `BizException` 的处理重复且不一致。应统一处理逻辑，并考虑对 `BizException` 只记录 `warn` 或 `info` 级别的日志。
    - **收益**: 简化异常处理逻辑，减少不必要的 `error` 日志。
5.  **提取通用组件**:
    - **任务**: `customer_insight_schema.sql` 中的表结构非常规范，其 `creator_id` 等审计字段是通用的。可以考虑创建一个 `BaseEntity` 类，包含这些通用字段，并让所有 POJO 类继承它，减少模板代码。
    - **收益**: 提高代码复用性。

---
