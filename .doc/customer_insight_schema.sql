-- =====================================================
-- 客户洞察助手系统数据库 Schema（正式版）
-- PostgreSQL
-- 说明：结构优化，约束合理化，注释精简，增加常用视图
-- =====================================================

-- 1. 维度配置表
CREATE TABLE qc_ai_dimension_config (
    id BIGSERIAL PRIMARY KEY,
    status CHAR(1) DEFAULT '1',
    creator_id BIGINT,
    creator_name VARCHAR(100),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modifyier_id BIGINT,
    modifyier_name VARCHAR(100),
    modify_time TIMESTAMP,
    agent_id BIGINT NOT NULL,
    dimension_code VARCHAR(20) NOT NULL,
    dimension_name VARCHAR(100),
    insight_mode VARCHAR(1),
    insight_mode_type VARCHAR(1),
    sort_order INTEGER DEFAULT 0,
    logo VARCHAR(255)
);
-- 表注释
COMMENT ON TABLE qc_ai_dimension_config IS '维度配置表，定义智能体的各种分析维度';
COMMENT ON COLUMN qc_ai_dimension_config.id IS '维度配置ID，主键';
COMMENT ON COLUMN qc_ai_dimension_config.status IS '逻辑删除状态：1-有效，0-无效';
COMMENT ON COLUMN qc_ai_dimension_config.creator_id IS '创建人ID';
COMMENT ON COLUMN qc_ai_dimension_config.creator_name IS '创建人姓名';
COMMENT ON COLUMN qc_ai_dimension_config.create_time IS '创建时间';
COMMENT ON COLUMN qc_ai_dimension_config.modifyier_id IS '修改人ID';
COMMENT ON COLUMN qc_ai_dimension_config.modifyier_name IS '修改人姓名';
COMMENT ON COLUMN qc_ai_dimension_config.modify_time IS '修改时间';
COMMENT ON COLUMN qc_ai_dimension_config.agent_id IS '关联的智能体ID';
COMMENT ON COLUMN qc_ai_dimension_config.dimension_code IS '维度编码：ORDER-订单, DISPLAY-铺货, VIVID-生动化, PAID_DISPLAY-付费陈列, COUPON-兑换券, ASSET-资产, AUTHENTICITY-真实性';
COMMENT ON COLUMN qc_ai_dimension_config.dimension_name IS '维度展示名称';
COMMENT ON COLUMN qc_ai_dimension_config.insight_mode IS '洞察模式：0-客户模式，1-终端经销商模式';
COMMENT ON COLUMN qc_ai_dimension_config.insight_mode_type IS '洞察模式类型：0-客户模式，1-终端模式，2-经销商模式';
COMMENT ON COLUMN qc_ai_dimension_config.sort_order IS '排序顺序，控制前端展示顺序';
COMMENT ON COLUMN qc_ai_dimension_config.logo IS '维度logo';


-- 2. 数据源表
CREATE TABLE IF NOT EXISTS qc_ai_data_source (
    id BIGSERIAL PRIMARY KEY,
    status CHAR(1),
    creator_id BIGINT,
    creator_name VARCHAR(100),
    create_time TIMESTAMP,
    modifyier_id BIGINT,
    modifyier_name VARCHAR(100),
    modify_time TIMESTAMP,
    source_name VARCHAR(100),
    source_code VARCHAR(50),
    api_url VARCHAR(255),
    http_method VARCHAR(10),
    description TEXT,
    belong_dimension_code VARCHAR(50),
    belong_dimension_name VARCHAR(100)
);

COMMENT ON TABLE qc_ai_data_source IS '数据源表，定义可用的接口和数据来源';
COMMENT ON COLUMN qc_ai_data_source.id IS '数据源ID，主键';
COMMENT ON COLUMN qc_ai_data_source.status IS '逻辑删除状态：1-有效，0-无效';
COMMENT ON COLUMN qc_ai_data_source.creator_id IS '创建人ID';
COMMENT ON COLUMN qc_ai_data_source.creator_name IS '创建人姓名';
COMMENT ON COLUMN qc_ai_data_source.create_time IS '创建时间';
COMMENT ON COLUMN qc_ai_data_source.modifyier_id IS '修改人ID';
COMMENT ON COLUMN qc_ai_data_source.modifyier_name IS '修改人姓名';
COMMENT ON COLUMN qc_ai_data_source.modify_time IS '修改时间';
COMMENT ON COLUMN qc_ai_data_source.source_name IS '数据源名称';
COMMENT ON COLUMN qc_ai_data_source.source_code IS '数据源编码，全局唯一标识';
COMMENT ON COLUMN qc_ai_data_source.api_url IS '接口url';
COMMENT ON COLUMN qc_ai_data_source.http_method IS '请求方式：POST, GET';
COMMENT ON COLUMN qc_ai_data_source.description IS '数据源描述说明';
COMMENT ON COLUMN qc_ai_data_source.belong_dimension_code IS '数据源从属维度编码，关联qc_ai_dimension_config.dimension_code';
COMMENT ON COLUMN qc_ai_data_source.belong_dimension_name IS '数据源从属维度名称';

CREATE TABLE IF NOT EXISTS qc_ai_source_param_config (
     id BIGSERIAL PRIMARY KEY,
     source_id BIGINT NOT NULL,
     param_code VARCHAR(50),
    group_code VARCHAR(50),
    group_name VARCHAR(100),
    group_enum_values jsonb,
    display_order INTEGER DEFAULT 0
    );
COMMENT ON TABLE qc_ai_source_param_config IS '数据源参数配置表，通过分组概念管理相关参数';
COMMENT ON COLUMN qc_ai_source_param_config.param_code IS '参数编码，对应接口的具体参数名，如：startTime, endTime';
COMMENT ON COLUMN qc_ai_source_param_config.group_code IS '分组编码，相关参数的分组标识';
COMMENT ON COLUMN qc_ai_source_param_config.group_name IS '分组名称，前端展示的选项名称';
COMMENT ON COLUMN qc_ai_source_param_config.group_enum_values IS '分组枚举值，JSON格式，如：[{"code": "1", "name": "是"}, {"code": "0", "name": "否"}]';
COMMENT ON COLUMN qc_ai_source_param_config.display_order IS '排序顺序，控制前端展示顺序';

-- 3. 数据源引用数据项定义表
CREATE TABLE IF NOT EXISTS qc_ai_data_source_ref_data_item (
    id BIGSERIAL PRIMARY KEY,
    status CHAR(1) DEFAULT '1',
    item_code VARCHAR(50) NOT NULL UNIQUE,
    item_name VARCHAR(100) NOT NULL,
    query_business_code VARCHAR(50),
    data_type_code VARCHAR(10),
    description TEXT,
    sort_order INTEGER
);
COMMENT ON TABLE qc_ai_data_source_ref_data_item IS '数据源引用数据项定义表，定义数据源引用的数据项';
COMMENT ON COLUMN qc_ai_data_source_ref_data_item.id IS '数据项ID，主键';
COMMENT ON COLUMN qc_ai_data_source_ref_data_item.status IS '逻辑删除状态：1-有效，0-无效';
COMMENT ON COLUMN qc_ai_data_source_ref_data_item.item_code IS '数据项编码，全局唯一标识';
COMMENT ON COLUMN qc_ai_data_source_ref_data_item.item_name IS '数据项显示名称';
COMMENT ON COLUMN qc_ai_data_source_ref_data_item.query_business_code IS '关联的查询业务--对应qc_ai_data_source.source_code';
COMMENT ON COLUMN qc_ai_data_source_ref_data_item.data_type_code IS '数据类型编码：DETAIL-明细, METRIC-指标';
COMMENT ON COLUMN qc_ai_data_source_ref_data_item.description IS '数据项描述说明';
COMMENT ON COLUMN qc_ai_data_source_ref_data_item.sort_order IS '排序顺序，控制前端展示顺序';

-- 5. 维度数据项关系表
CREATE TABLE IF NOT EXISTS qc_ai_dimension_ref_data_item_rel (
    id BIGSERIAL PRIMARY KEY,
    status CHAR(1),
    creator_id BIGINT,
    creator_name VARCHAR(100),
    create_time TIMESTAMP,
    modifyier_id BIGINT,
    modifyier_name VARCHAR(100),
    modify_time TIMESTAMP,
    dimension_config_id BIGINT,
    ref_data_item_id BIGINT,
    sort_order INTEGER,
    query_value VARCHAR(255),
    group_code varchar(50)
);
COMMENT ON TABLE qc_ai_dimension_ref_data_item_rel IS '维度与数据项关系';

-- 表注释
COMMENT ON TABLE qc_ai_dimension_ref_data_item_rel IS '维度数据项关系表，定义维度使用哪些引用数据源及查询参数';
COMMENT ON COLUMN qc_ai_dimension_ref_data_item_rel.id IS '关系ID，主键';
COMMENT ON COLUMN qc_ai_dimension_ref_data_item_rel.status IS '逻辑删除状态：1-有效，0-无效';
COMMENT ON COLUMN qc_ai_dimension_ref_data_item_rel.creator_id IS '创建人ID';
COMMENT ON COLUMN qc_ai_dimension_ref_data_item_rel.creator_name IS '创建人姓名';
COMMENT ON COLUMN qc_ai_dimension_ref_data_item_rel.create_time IS '创建时间';
COMMENT ON COLUMN qc_ai_dimension_ref_data_item_rel.modifyier_id IS '修改人ID';
COMMENT ON COLUMN qc_ai_dimension_ref_data_item_rel.modifyier_name IS '修改人姓名';
COMMENT ON COLUMN qc_ai_dimension_ref_data_item_rel.modify_time IS '修改时间';
COMMENT ON COLUMN qc_ai_dimension_ref_data_item_rel.dimension_config_id IS '维度配置ID，关联qc_ai_dimension_config';
COMMENT ON COLUMN qc_ai_dimension_ref_data_item_rel.ref_data_item_id IS '引用数据项ID，关联qc_ai_data_source_ref_data_item';
COMMENT ON COLUMN qc_ai_dimension_ref_data_item_rel.sort_order IS '数据源在维度中的排序';
COMMENT ON COLUMN qc_ai_dimension_ref_data_item_rel.query_value IS '查询参数值 1-最近1个月 2-最近2个月 3-最近3个月';
COMMENT ON COLUMN qc_ai_dimension_ref_data_item_rel.group_code IS '分组编码，qc_ai_source_param_config.group_code';


-- 提示词变量记录表
CREATE TABLE qc_ai_prompt_variable_record (
                                              id BIGSERIAL PRIMARY KEY,
                                              status CHAR(1) DEFAULT '1',
                                              create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                              modify_time TIMESTAMP,
                                              config_id BIGINT,                    -- 配置ID，关联qc_ai_dimension_config.id
                                              dimension_code VARCHAR(20),          -- 维度代码
                                              prompt_type VARCHAR(100) ,            -- 提示词类型
                                              prompt_fragment_id BIGINT ,           -- 提示词片段ID，关联qc_ai_prompt_fragment.id
                                              variable_key VARCHAR(100) ,           -- 变量的key（如OrderSatisfactionRate）
                                              variable_order INTEGER              -- 变量在该片段中的出现顺序
);
COMMENT ON TABLE qc_ai_prompt_variable_record IS '提示词变量记录表';
COMMENT ON COLUMN qc_ai_prompt_variable_record.id IS '主键ID';
COMMENT ON COLUMN qc_ai_prompt_variable_record.status IS '状态：1-有效，0-无效';
COMMENT ON COLUMN qc_ai_prompt_variable_record.config_id IS '配置ID';
COMMENT ON COLUMN qc_ai_prompt_variable_record.dimension_code IS '维度代码';
COMMENT ON COLUMN qc_ai_prompt_variable_record.prompt_type IS '提示词类型';
COMMENT ON COLUMN qc_ai_prompt_variable_record.prompt_fragment_id IS '提示词片段ID';
COMMENT ON COLUMN qc_ai_prompt_variable_record.variable_key IS '变量的key';
COMMENT ON COLUMN qc_ai_prompt_variable_record.variable_order IS '变量在片段中的出现顺序';

-- =====================================================
-- 6. 衡量标准表
-- =====================================================
CREATE TABLE qc_ai_measurement_standard (
    id BIGSERIAL PRIMARY KEY,
    status CHAR(1) DEFAULT '1',
    creator_id BIGINT,
    creator_name VARCHAR(100),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modifyier_id BIGINT,
    modifyier_name VARCHAR(100),
    modify_time TIMESTAMP,
    standard_name VARCHAR(100) NOT NULL,
    standard_definition TEXT,
    standard_type_code VARCHAR(10),
    standard_type VARCHAR(20),
    sort_order INTEGER DEFAULT 0
);
COMMENT ON TABLE qc_ai_measurement_standard IS '衡量标准表';

-- 表注释
COMMENT ON TABLE qc_ai_measurement_standard IS '衡量标准表，定义评价标准';
COMMENT ON COLUMN qc_ai_measurement_standard.id IS '标准ID，主键';
COMMENT ON COLUMN qc_ai_measurement_standard.status IS '逻辑删除状态：1-有效，0-无效';
COMMENT ON COLUMN qc_ai_measurement_standard.creator_id IS '创建人ID';
COMMENT ON COLUMN qc_ai_measurement_standard.creator_name IS '创建人姓名';
COMMENT ON COLUMN qc_ai_measurement_standard.create_time IS '创建时间';
COMMENT ON COLUMN qc_ai_measurement_standard.modifyier_id IS '修改人ID';
COMMENT ON COLUMN qc_ai_measurement_standard.modifyier_name IS '修改人姓名';
COMMENT ON COLUMN qc_ai_measurement_standard.modify_time IS '修改时间';
COMMENT ON COLUMN qc_ai_measurement_standard.standard_name IS '标准名称，如"健康"、"良好"、"达标"等';
COMMENT ON COLUMN qc_ai_measurement_standard.standard_definition IS '标准定义，如"总分≥6分判定为健康"';
COMMENT ON COLUMN qc_ai_measurement_standard.standard_type_code IS '标准类型编码：DIMENSION-维度标准，SUMMARY-总结标准';
COMMENT ON COLUMN qc_ai_measurement_standard.standard_type IS '标准类型中文名称：维度标准、总结标准';
COMMENT ON COLUMN qc_ai_measurement_standard.sort_order IS '排序顺序，控制前端展示顺序';

-- =====================================================
-- 7. 总结配置表
-- =====================================================
CREATE TABLE qc_ai_summary_config (
    id BIGSERIAL PRIMARY KEY,
    status CHAR(1) DEFAULT '1',
    creator_id BIGINT,
    creator_name VARCHAR(100),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modifyier_id BIGINT,
    modifyier_name VARCHAR(100),
    modify_time TIMESTAMP,
    agent_id BIGINT NOT NULL,
    comprehensive_prompt TEXT,
    summary_advice_prompt TEXT,
    insight_mode CHAR(1)
);
COMMENT ON TABLE qc_ai_summary_config IS '总结配置表';

-- 表注释
COMMENT ON TABLE qc_ai_summary_config IS '总结配置表，定义综合分析和建议生成规则';
COMMENT ON COLUMN qc_ai_summary_config.id IS '总结配置ID，主键';
COMMENT ON COLUMN qc_ai_summary_config.status IS '逻辑删除状态：1-有效，0-无效';
COMMENT ON COLUMN qc_ai_summary_config.creator_id IS '创建人ID';
COMMENT ON COLUMN qc_ai_summary_config.creator_name IS '创建人姓名';
COMMENT ON COLUMN qc_ai_summary_config.create_time IS '创建时间';
COMMENT ON COLUMN qc_ai_summary_config.modifyier_id IS '修改人ID';
COMMENT ON COLUMN qc_ai_summary_config.modifyier_name IS '修改人姓名';
COMMENT ON COLUMN qc_ai_summary_config.modify_time IS '修改时间';
COMMENT ON COLUMN qc_ai_summary_config.agent_id IS '关联的智能体ID';
COMMENT ON COLUMN qc_ai_summary_config.comprehensive_prompt IS '综合衡量标准提示词，引导模型分析维度表现';
COMMENT ON COLUMN qc_ai_summary_config.summary_advice_prompt IS '总结及建议提示词，引导模型生成最终建议';
COMMENT ON COLUMN qc_ai_summary_config.insight_mode IS '洞察模式：0-客户模式，1-终端经销商模式';

-- =====================================================
-- 8. 配置标准关联表
-- =====================================================
CREATE TABLE qc_ai_config_standard_rel (
    id BIGSERIAL PRIMARY KEY,
    status CHAR(1) DEFAULT '1',
    creator_id BIGINT,
    creator_name VARCHAR(100),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modifyier_id BIGINT,
    modifyier_name VARCHAR(100),
    modify_time TIMESTAMP,
    config_id BIGINT NOT NULL,
    config_type VARCHAR(10),
    standard_id BIGINT NOT NULL
);
COMMENT ON TABLE qc_ai_config_standard_rel IS '配置与标准关联';

-- 表注释
COMMENT ON TABLE qc_ai_config_standard_rel IS '配置与衡量标准关联表，维度/总结多对多统一';
COMMENT ON COLUMN qc_ai_config_standard_rel.id IS '关系ID，主键';
COMMENT ON COLUMN qc_ai_config_standard_rel.status IS '逻辑删除状态：1-有效，0-无效';
COMMENT ON COLUMN qc_ai_config_standard_rel.creator_id IS '创建人ID';
COMMENT ON COLUMN qc_ai_config_standard_rel.creator_name IS '创建人姓名';
COMMENT ON COLUMN qc_ai_config_standard_rel.create_time IS '创建时间';
COMMENT ON COLUMN qc_ai_config_standard_rel.modifyier_id IS '修改人ID';
COMMENT ON COLUMN qc_ai_config_standard_rel.modifyier_name IS '修改人姓名';
COMMENT ON COLUMN qc_ai_config_standard_rel.modify_time IS '修改时间';
COMMENT ON COLUMN qc_ai_config_standard_rel.config_id IS 'qc_ai_summary_config或者dimension_config_id';
COMMENT ON COLUMN qc_ai_config_standard_rel.config_type IS '标准所属分配编码：DIMENSION-维度标准，SUMMARY-总结标准';
COMMENT ON COLUMN qc_ai_config_standard_rel.standard_id IS '标准ID，关联qc_ai_measurement_standard';

-- =====================================================
-- 9. 对话记录扩展表
-- =====================================================
CREATE TABLE qc_ai_conversation_ext (
    id BIGSERIAL PRIMARY KEY,
    status CHAR(1) DEFAULT '1',
    conversation_id BIGINT NOT NULL,
    insight_object_id BIGINT,
    insight_object_name VARCHAR(200),
    diagnosis_conclusion TEXT,
    order_insight_evaluation TEXT,
    order_insight_result TEXT,
    display_evaluation TEXT,
    display_insight_result TEXT,
    summary_advice TEXT
);
COMMENT ON TABLE qc_ai_conversation_ext IS '对话扩展表';

-- 表注释
COMMENT ON TABLE qc_ai_conversation_ext IS '对话记录扩展表，存储洞察分析的详细结果和日志';
COMMENT ON COLUMN qc_ai_conversation_ext.id IS '扩展记录ID，主键';
COMMENT ON COLUMN qc_ai_conversation_ext.status IS '逻辑删除状态：1-有效，0-无效';
COMMENT ON COLUMN qc_ai_conversation_ext.conversation_id IS '对话ID，关联qc_ai_agent_conversation表的id';
COMMENT ON COLUMN qc_ai_conversation_ext.insight_object_id IS '洞察对象ID，客户或经销商的ID';
COMMENT ON COLUMN qc_ai_conversation_ext.insight_object_name IS '洞察对象名称，客户或经销商的名字';
COMMENT ON COLUMN qc_ai_conversation_ext.diagnosis_conclusion IS '诊断结论，模型生成的总体结论';
COMMENT ON COLUMN qc_ai_conversation_ext.order_insight_evaluation IS '订单洞察评价，模型生成的订单洞察评价';
COMMENT ON COLUMN qc_ai_conversation_ext.order_insight_result IS '订单洞察结果，模型生成的订单洞察结果';
COMMENT ON COLUMN qc_ai_conversation_ext.display_evaluation IS '铺货评价，模型生成的铺货评价';
COMMENT ON COLUMN qc_ai_conversation_ext.display_insight_result IS '铺货洞察结果，模型生成的铺货洞察结果';
COMMENT ON COLUMN qc_ai_conversation_ext.summary_advice IS '总结及建议，模型生成的总结及建议';



CREATE TABLE qc_ai_main_prompt (
    id BIGSERIAL PRIMARY KEY,
    status CHAR(1) DEFAULT '1',
    -- 提示词基本信息
    prompt_name VARCHAR(100) NOT NULL,           -- 提示词名称
    prompt_type VARCHAR(100) NOT NULL,           -- 提示词类型：DIMENSION/SUMMARY_COMPREHENSIVE/SUMMARY_ADVICE
    
    -- 关联信息
    dimension_code VARCHAR(100) -- 关联qc_ai_data_source的belong_dimension_code
);
COMMENT ON TABLE qc_ai_main_prompt IS '主提示词表';
COMMENT ON COLUMN qc_ai_main_prompt.id IS '主提示词ID，主键';
COMMENT ON COLUMN qc_ai_main_prompt.status IS '逻辑删除状态：1-有效，0-无效';
COMMENT ON COLUMN qc_ai_main_prompt.prompt_name IS '提示词名称';
COMMENT ON COLUMN qc_ai_main_prompt.prompt_type IS '提示词类型：DIMENSION/SUMMARY_COMPREHENSIVE/SUMMARY_ADVICE';
COMMENT ON COLUMN qc_ai_main_prompt.dimension_code IS '关联qc_ai_data_source的belong_dimension_code';

CREATE TABLE qc_ai_prompt_fragment (
    id BIGSERIAL PRIMARY KEY,
    status CHAR(1) DEFAULT '1',
    -- 关联信息
    main_prompt_id BIGINT NOT NULL,             -- 关联主提示词ID
    
    -- 片段信息
    fragment_key VARCHAR(100) NOT NULL,         -- 片段键名
    fragment_name VARCHAR(100) NOT NULL,        -- 片段名称
    sort_order INTEGER DEFAULT 0               -- 排序号
);
COMMENT ON TABLE qc_ai_prompt_fragment IS '提示词片段表';
COMMENT ON COLUMN qc_ai_prompt_fragment.id IS '提示词片段ID，主键';
COMMENT ON COLUMN qc_ai_prompt_fragment.status IS '逻辑删除状态：1-有效，0-无效';
COMMENT ON COLUMN qc_ai_prompt_fragment.main_prompt_id IS '关联主提示词ID';
COMMENT ON COLUMN qc_ai_prompt_fragment.fragment_key IS '片段键名';
COMMENT ON COLUMN qc_ai_prompt_fragment.fragment_name IS '片段名称';
COMMENT ON COLUMN qc_ai_prompt_fragment.sort_order IS '排序号';

create table qc_ai_dimension_prompt_value
(
    id                  bigserial
        primary key,
    status              char default '1'::bpchar,
    config_id bigint not null,
    prompt_fragment_id  bigint not null,
    fragment_value      text   not null
);

comment on table qc_ai_dimension_prompt_value is '维度提示词片段值表';
comment on column qc_ai_dimension_prompt_value.id is '维度提示词片段值ID，主键';
comment on column qc_ai_dimension_prompt_value.status is '逻辑删除状态：1-有效，0-无效';
comment on column qc_ai_dimension_prompt_value.config_id is '维度配置ID，关联qc_ai_dimension_config表，qc_ai_summary_config表';
comment on column qc_ai_dimension_prompt_value.prompt_fragment_id is '提示词片段ID，关联qc_ai_prompt_fragment表';
comment on column qc_ai_dimension_prompt_value.fragment_value is '片段值';

create table qc_ai_customer_variable_data (
    id bigserial primary key,
    status char default '1',
    create_time timestamp default current_timestamp,
    modify_time timestamp,
    customer_id bigint not null,
    prompt_variable_record_id bigint not null,
    conversation_id bigint not null,
    variable_value text not null
);

comment on table qc_ai_customer_variable_data is '客户洞察变量数据表';
comment on column qc_ai_customer_variable_data.id is '客户洞察变量数据ID，主键';
comment on column qc_ai_customer_variable_data.status is '逻辑删除状态：1-有效，0-无效';
comment on column qc_ai_customer_variable_data.create_time is '创建时间';
comment on column qc_ai_customer_variable_data.modify_time is '修改时间';
comment on column qc_ai_customer_variable_data.customer_id is '客户ID';
comment on column qc_ai_customer_variable_data.prompt_variable_record_id is '提示词变量记录ID，关联qc_ai_prompt_variable_record表';
comment on column qc_ai_customer_variable_data.conversation_id is '对话ID，关联qc_ai_agent_conversation表';
comment on column qc_ai_customer_variable_data.variable_value is '变量值';







