package com.qc.agent.vectordb;



import com.qc.agent.vectordb.pojo.ChatInput;
import com.qc.agent.vectordb.pojo.SearchContent;
import com.qc.agent.vectordb.llm.tencentHunyuan;
import com.tencentcloudapi.common.SSEResponseModel;
import com.tencentcloudapi.hunyuan.v20230901.models.ChatStdResponse;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;

/**
 * @Author: xuyunjie
 * @Date: 2024/2/1 8:47
 */
@SpringBootTest
public class testHunyuan {

    @Autowired
    tencentHunyuan hunyuanClient;

    @Test
    public void test() {
        hunyuanClient.initClient();
        ChatInput input = new ChatInput();

//        // 添加历史问答对, 如果没有则跳过，注意历史问答对需要按时间升序排列
//        ArrayList<ChatQA> historyQAs = new ArrayList<>();
//        ChatQA historyQA = new ChatQA();
//        // 历史问题1
//        historyQA.setQuestion("你的角色是一个客服工程师，接下来会给你一份从知识库中提取的文档内容（标记为DOCUMENT）和一个问题（标记为QUESTION），你只能严格根据文档中信息回答问题，不能回答文档中不存在的内容");
//        // 历史问题1的检索语料, 若果没有则跳过
//        historyQA.setSearchContents(new ArrayList<>());
//        // 历史问题1的回答
//        historyQA.setAnswer("好的，我会尽力根据提供的文档内容和问题为您提供准确的答案。请提供文档内容和问题。");
//        // 历史问题2，不存在则跳过
//
//        historyQAs.add(historyQA);
//        input.setHistoryChatQA(historyQAs);
        // 提问问题
        input.setQuestion("错误码405是什么意思");
//      提问问题在向量库中检索的语料构造prompt, 如果没有则跳过
        SearchContent content = new SearchContent();
        content.setScore(0.85);
        content.setContent("1.调用API接口时返回错误码“101”\n" +
                " ```\n" +
                "企业唯一授权标识校验失败，首先排查openID和Appkey录入是否有误，如果录入无误，再向客户经理确认openApi接口访问能力是否已经开通\n" +
                " ```\n" +
                "2.调用API接口时返回错误码“102”\n" +
                " ```\n" +
                " 错误码102是签名错误，请参考“业务接口格式规范”按照说明生成签名信息。注意：生成签名时的数据要和请求体中数据保持一致，包括空格回车等。\n" +
                "```\n" +
                "3.调用API是返回错误码“104”\n" +
                "\n" +
                "```\n" +
                "错误码104表示实际请求接口的IP地址不在企业授权访问接口的IP地址列表中\n" +
                "```\n" +
                "4.调用API时返回HTTP状态码“405”\n" +
                "```\n" +
                "API接口仅支持POST请求，如果请求方式为GET，服务器会返回405状态码\n" +
                "```\n" +
                "5.调用API时请求数据体中非必填参数如何传值\n" +
                "```\n" +
                "API接口如果参数非必填且业务中不需要对该数据信息做操作，请求数据时就不要带该参数信息。系统不允许传递值为NULL的参数。\n" +
                "```\n" +
                "6.调用API时报域名解析失败错误(java.net.UnKnownHostException)\n" +
                "```\n" +
                "如果请求时出现域名解析失败时可以在本地请求服务器hosts文件中增加本地域名解析，如下：\n" +
                "************* openapi.waiqin365.com\n" +
                "```");
        ArrayList<SearchContent> contents = new ArrayList<>();
        contents.add(content);
        input.setSearchContents(contents);

        long time1 = System.currentTimeMillis();
        ChatStdResponse resp = hunyuanClient.chat(input);
        System.out.println("chatstd cost " + (System.currentTimeMillis() - time1));

        if (resp != null) {
            for (SSEResponseModel.SSE e : resp) {
                System.out.println(e.Data);
            }
        }
    }
}
