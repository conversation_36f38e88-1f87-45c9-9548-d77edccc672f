package com.qc.agent.vectordb;

import com.qc.agent.vectordb.tencentdb.tencentDbClient;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * @Author: xuyunjie
 * @Date: 2024/1/30 10:11
 */
@SpringBootTest
public class tencentDb {

    @Autowired
    tencentDbClient dbClient;

    private String defaultDb = "qince-aidatabase";
    private String defaultCView = "qince-view";
    private String defaultQACView = "qince-qaview";

    @Test
    public void test() throws Exception {
        String filePath = "E:\\SFA\\answer-AI-parent\\answer-AI-vectordb\\rag_doc\\help.md";
        String databaseName = "qincd";
        String collectionName = "hql-test-collectionVie";
        dbClient.initClient();
//        dbClient.queryByFileName(databaseName, collectionName, "asdaaaa.md");
        dbClient.createDatabase(defaultDb);
        dbClient.createCollection(defaultDb, defaultCView);
        dbClient.createCollection(defaultDb, defaultQACView);
//        dbClient.loadAndSplitFile(databaseName, collectionName, filePath);
//        VectorDbResponse fileInfo = dbClient.getFileInfo(databaseName, collectionName, "asda.md", 2);
//        List<String> chunks = (List<String>) fileInfo.get("data");
//        VectorDbResponse content = dbClient.searchFileContent(databaseName, collectionName, "asda.md", "识别解析过程中区分sku未结合单位id导致解析数据异常", 3, 0.4);
//        ArrayList<SearchContent> data = (ArrayList<SearchContent>) content.get("data");
//        System.out.println(data.get(0).getContent());



//        for (String info: fileInfo) {
//            System.out.println(fileInfo.indexOf(info) + "+++++");
//            System.out.println(info);
//        }
//        ArrayList<SearchContent> searchContents = dbClient.searchFile(databaseName, collectionName, new File(filePath).getName(), "错误码", 3);
//        for(SearchContent content: searchContents) {
//            System.out.println(content);
//        }
    }
    @Test
    public void test1() {
        String a = "";
        System.out.println(a.isEmpty());
    }

}
