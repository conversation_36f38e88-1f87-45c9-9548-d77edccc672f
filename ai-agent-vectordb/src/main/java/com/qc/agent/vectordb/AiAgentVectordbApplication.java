package com.qc.agent.vectordb;

import com.qc.agent.vectordb.pojo.domain.TencentDbProperties;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;

@EnableConfigurationProperties(TencentDbProperties.class)
@SpringBootApplication
public class AiAgentVectordbApplication
{

	public static void main(String[] args) {
		SpringApplication.run(AiAgentVectordbApplication.class, args);
	}

}
