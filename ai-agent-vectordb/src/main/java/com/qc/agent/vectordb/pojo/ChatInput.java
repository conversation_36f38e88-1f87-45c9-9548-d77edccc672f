package com.qc.agent.vectordb.pojo;

import com.tencentcloudapi.hunyuan.v20230901.models.Message;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: xuyunjie
 * @Date: 2024/2/1 16:36
 */
public class ChatInput{
    // 历史消息的长度限制，腾讯混元模型限制40条Message 20个问答对
    private int messageLimit = 30;
    // 提问的问题
    private String question = "问题：";
    // 问题在向量数据库中检索的语料
    private List<SearchContent> searchContents = new ArrayList<>();
    private String prompt = "你是一个客服，下面会给你一段背景知识，背景知识中包含多段正文和多个链接，其中每个链接属于它前一个正文。" +
            "只能根据背景知识中的内容回答，你要按照下面五个步骤一步一步思考回答：\n" +
            "1、严格按照背景知识中的内容回答，禁止回答背景知识不包含的内容。\n" +
            "2、如果背景知识中不存在问题的答案，你要回答不知道。\n" +
            "3、如果答案中包含正文和链接，把链接放在正文后面，。\n" +
            "4、禁止访问链接。\n" +
            "5、答案要精炼简短，不要超过300字。\n" +
            "背景知识：";
    // 历史问答对
    private List<ChatQA> historyChatQA = new ArrayList<>();

    public List<ChatQA> getHistoryChatQA() {
        return historyChatQA;
    }

    public void setHistoryChatQA(List<ChatQA> historyCharQA) {
        this.historyChatQA = historyCharQA;
    }

    public List<Message> messageConstruct() {
        List<Message> messages = new ArrayList<>();

//        for (ChatQA qa: historyChatQA) {
//            Message Q = new Message();
//            Q.setRole("user");
//            Q.setContent(qa.getPrompt() + qa.getQuestion());
//            messages.add(Q);
//            Message A = new Message();
//            A.setRole("assistant");
//            A.setContent(qa.getAnswer());
//            messages.add(A);
//        }
        Message msg = new Message();
        msg.setRole("user");
        msg.setContent(this.getPrompt() + question);
        messages.add(msg);
        if (messages.size() > messageLimit) {
            messages = messages.subList(messages.size() - messageLimit, messages.size());
        }
        return messages;
    }

    public String getQuestion() {
        return question;
    }

    public void setQuestion(String question) {
        this.question = this.question + question;
    }

    public List<SearchContent> getSearchContents() {
        return searchContents;
    }

    public void setSearchContents(List<SearchContent> searchContents) {
        this.searchContents = searchContents;
    }

    public String getPrompt() {
        for(SearchContent content: searchContents) {
            String knowledge = content.getContent() + content.getAppendix();
            prompt += knowledge + "\n";
        }
        return prompt;
    }

    public void setPrompt(String prompt) {
        if (!prompt.isEmpty()) {
            this.prompt = prompt;
        }
    }
}
