package com.qc.agent.vectordb.tencentdb;

import com.tencent.tcvectordb.client.VectorDBClient;
import com.tencent.tcvectordb.model.Collection;
import com.tencent.tcvectordb.model.Database;
import com.tencent.tcvectordb.model.Document;
import com.tencent.tcvectordb.model.param.collection.CreateCollectionParam;
import com.tencent.tcvectordb.model.param.dml.*;
import com.tencent.tcvectordb.model.param.entity.AffectRes;
import com.tencent.tcvectordb.model.param.entity.SearchRes;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Objects;

/**
 * A generic, reusable service for interacting with Tencent Cloud VectorDB.
 * This service is application-agnostic and supports a log prefix for contextual tracing.
 *
 * <AUTHOR>
 * @date 2025-07-12
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Slf4j
@RequiredArgsConstructor
public class VectorDBService {

    private final VectorDBClient client;

    /**
     * Ensures a database and collection exist, creating them if necessary.
     *
     * @param dbName          The name of the database.
     * @param collectionParam The parameters defining the collection.
     * @param logPrefix       A prefix for log messages to provide context (e.g., a trace ID).
     */
    public void createDatabaseAndCollectionIfNotExists(String dbName, CreateCollectionParam collectionParam, String logPrefix) {
        Assert.hasText(dbName, "Database name must not be empty.");
        Objects.requireNonNull(collectionParam, "CreateCollectionParam must not be null.");

        String p = formatPrefix(logPrefix);
        try {
            if (!client.listDatabase().contains(dbName)) {
                log.info("{}Database [{}] not found. Proceeding to create.", p, dbName);
                client.createDatabase(dbName);
                log.info("{}Successfully created database [{}].", p, dbName);
            }

            Database database = client.database(dbName);
            String collectionName = collectionParam.getCollection();
            List<String> existingCollections = database.listCollections().stream()
                    .map(Collection::getCollection)
                    .toList();

            if (!existingCollections.contains(collectionName)) {
                log.info("{}Collection [{}] in database [{}] not found. Proceeding to create.", p, collectionName, dbName);
                database.createCollection(collectionParam);
                log.info("{}Successfully created collection [{}] in database [{}].", p, collectionName, dbName);
            } else {
                log.debug("{}Database [{}] and collection [{}] already exist.", p, dbName, collectionName);
            }
        } catch (Exception e) {
            log.error("{}Failed to create database [{}] or collection [{}]. Error: {}",
                    p, dbName, collectionParam.getCollection(), e.getMessage(), e);
            throw new IllegalStateException("Failed during database/collection setup.", e);
        }
    }

    /**
     * Upserts a list of documents into a specified collection.
     */
    public AffectRes upsertDocuments(String dbName, String collectionName, List<Document> documents, String logPrefix) {
        Assert.hasText(dbName, "Database name must not be empty.");
        Assert.hasText(collectionName, "Collection name must not be empty.");

        String p = formatPrefix(logPrefix);
        if (documents == null || documents.isEmpty()) {
            log.warn("{}Upsert documents for collection [{}/{}] was called with an empty list. No action taken.", p, dbName, collectionName);
            return new AffectRes();
        }

        log.info("{}Upserting {} document(s) into collection [{}/{}].", p, documents.size(), dbName, collectionName);
        var insertParam = InsertParam.newBuilder()
                .addAllDocument(documents)
                .withBuildIndex(true)
                .build();
        return client.database(dbName).collection(collectionName).upsert(insertParam);
    }

    /**
     * Searches a collection using text queries that will be converted to embeddings.
     */
    public SearchRes searchByEmbeddingItems(String dbName, String collectionName, SearchByEmbeddingItemsParam searchParam, String logPrefix) {
        log.debug("{}Performing search by embedding items in collection [{}/{}].", formatPrefix(logPrefix), dbName, collectionName);
        return client.database(dbName).collection(collectionName).searchByEmbeddingItems(searchParam);
    }

    /**
     * Performs a vector search directly with provided vector data.
     */
    public List<List<Document>> searchByVectors(String dbName, String collectionName, SearchByVectorParam searchParam, String logPrefix) {
        log.debug("{}Performing search by vectors in collection [{}/{}].", formatPrefix(logPrefix), dbName, collectionName);
        return client.database(dbName).collection(collectionName).search(searchParam);
    }

    /**
     * Queries a collection based on a filter, limit, and offset.
     */
    public List<Document> query(String dbName, String collectionName, QueryParam queryParam, String logPrefix) {
        log.debug("{}Querying collection [{}/{}] with filter [{}] and limit [{}].",
                formatPrefix(logPrefix), dbName, collectionName, queryParam.getFilter(), queryParam.getLimit());
        return client.database(dbName).collection(collectionName).query(queryParam);
    }

    /**
     * Deletes documents from a collection.
     */
    public AffectRes delete(String dbName, String collectionName, DeleteParam deleteParam, String logPrefix) {
        String p = formatPrefix(logPrefix);
        log.warn("{}Executing delete operation on collection [{}/{}] with filter [{}].",
                p, dbName, collectionName, deleteParam.getFilter());
        return client.database(dbName).collection(collectionName).delete(deleteParam);
    }

    /**
     * Deletes all data from a specified collection.
     */
    public AffectRes truncateCollection(String dbName, String collectionName, String logPrefix) {
        log.warn("{}Truncating all data from collection [{}/{}]. This action is irreversible.", formatPrefix(logPrefix), dbName, collectionName);
        return client.database(dbName).truncateCollections(collectionName);
    }

    /**
     * Drops an entire database.
     */
    public void dropDatabase(String dbName, String logPrefix) {
        log.warn("{}Dropping entire database [{}]. This action is irreversible.", formatPrefix(logPrefix), dbName);
        client.dropDatabase(dbName);
    }

    /**
     * Helper method to format the log prefix consistently, handling null or blank inputs.
     *
     * @param prefix The raw prefix string.
     * @return A formatted prefix string ending with a space, or an empty string if the input is null/blank.
     */
    private String formatPrefix(String prefix) {
        return (prefix != null && !prefix.isBlank()) ? prefix + " " : "";
    }
}