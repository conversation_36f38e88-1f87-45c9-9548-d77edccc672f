package com.qc.agent.vectordb.pojo;

import java.util.HashMap;
import java.util.List;

/**
 * @Author: xuyunjie
 * @Date: 2024/1/29 10:34
 */
public class VectorDbResponse extends HashMap<String, Object> {
    // 向量库响应code
    // 0 失败
    // 1 成功
    // 102
    // 103
    // 104
    public static int CODE101 = 101;  // 101 文件未上传知识库或者未上传完成
    public VectorDbResponse code(int code) {
        this.put("code", code);
        return this;
    }

    public VectorDbResponse message(String msg) {
        this.put("message", msg);
        return this;
    }

    public VectorDbResponse data(Object data) {
        this.put("data", data);
        return this;
    }

    public VectorDbResponse fail() {
        this.put("code", 0);
        return this;
    }

    public VectorDbResponse success() {
        this.put("code", 1);
        return this;
    }

    @Override
    public VectorDbResponse put(String key, Object value) {
        super.put(key, value);
        return this;
    }

    public List<SearchContent> getData(){
        return (List<SearchContent>)get("data");
    }

}
