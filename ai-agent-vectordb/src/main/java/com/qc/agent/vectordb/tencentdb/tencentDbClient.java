package com.qc.agent.vectordb.tencentdb;

import com.qc.agent.vectordb.pojo.SearchContent;
import com.qc.agent.vectordb.pojo.VectorDbResponse;
import com.tencent.tcvectordb.client.VectorDBClient;
import com.tencent.tcvectordb.model.Collection;
import com.tencent.tcvectordb.model.*;
import com.tencent.tcvectordb.model.param.collection.*;
import com.tencent.tcvectordb.model.param.collectionView.*;
import com.tencent.tcvectordb.model.param.dml.*;
import com.tencent.tcvectordb.model.param.entity.*;
import com.tencent.tcvectordb.model.param.enums.ParsingTypeEnum;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.tencent.tcvectordb.model.param.collectionView.LanguageType.ZH;
import static com.tencent.tcvectordb.model.param.enums.EmbeddingModelEnum.BGE_BASE_ZH;
import static java.util.stream.Collectors.toList;

/**
 * @Author: xuyunjie
 * @Date: 2024/1/29 10:43
 */
@Slf4j
@Data
@Component
@RequiredArgsConstructor
public class tencentDbClient {

    private String url;
    private String username;
    private String key;
    private final @Lazy VectorDBClient vDbClient;

    private String defaultDb = "qince-aidatabase";
    private String defaultCView = "qince-view";
    private String defaultQACView = "qince-qaview";

    private static final String FIELD_ID = "id";
    private static final String FIELD_QUESTION = "question";
    private static final String FIELD_ANSWER = "answer";
    private static final String FIELD_DOC_NAME = "docName";
    private static final String FIELD_FILE_STATUS = "fileStatus";
    private static final String FIELD_CATEGORY_ID = "categoryId";
    private static final String FILE_STATUS_FILTER_VALUE = "2"; // As

    public void initClient() {
    }

    public synchronized VectorDbResponse createDatabase(String databaseName) {
        List<String> createdDatabase = vDbClient.listDatabase();
        if (createdDatabase.contains(databaseName)) {
            log.info(databaseName + ": already exist!");
            return new VectorDbResponse().success();
        } else {
            try {
                AIDatabase db = vDbClient.createAIDatabase(databaseName);
                return new VectorDbResponse().success();
            } catch (Exception e) {
                log.info("create database " + databaseName + " exception: " + e);
                return new VectorDbResponse().fail();
            }

        }
    }

    public synchronized VectorDbResponse createCollection(String databaseName, String collectionName) {
        AIDatabase db = vDbClient.aiDatabase(databaseName);
        List<CollectionView> collectionViews = db.listCollectionView();
        List<String> collectionViewNames = collectionViews.stream().map(CollectionView::getCollectionView).collect(toList());
        if (!collectionViewNames.contains(collectionName)) {
            CreateCollectionViewParam collectionParam = CreateCollectionViewParam.newBuilder()
                    .withName(collectionName)
                    .withDescription("create ai collection")
                    .withEmbedding(EmbeddingParams.newBuilder().withEnableWordEmbedding(true).withLanguage(ZH).Build())
                    .withSplitterPreprocess(SplitterPreprocessParams.newBuilder().
                            withAppendKeywordsToChunkEnum(false).
                            withAppendTitleToChunkEnum(false).Build())
                    .build();
            try {
                CollectionView cView = db.createCollectionView(collectionParam);
                return new VectorDbResponse().success();
            } catch (Exception e) {
                log.info("create collection " + collectionName + " exception: " + e);
                return new VectorDbResponse().fail();
            }
        } else {
            log.info(collectionName + ": already exist!");
            return new VectorDbResponse().success();
        }
    }

    public VectorDbResponse loadAndSplitQAFile(String databaseName, String collectionName, String filePath) throws Exception {
        AIDatabase db = vDbClient.aiDatabase(databaseName);
        CollectionView collection = db.describeCollectionView(collectionName);
        LoadAndSplitTextParam param = null;
        if (filePath.endsWith(".docx")) {
            param = LoadAndSplitTextParam.newBuilder()
                    .withLocalFilePath(filePath)
                    .withDocumentSetName(new File(filePath).getName())
                    .withSplitterProcess(SplitterPreprocessParams.newBuilder()
                            .withChunkSplitter("\n{2,}")
                            .Build())
                    .Build();
        } else {
            param = LoadAndSplitTextParam.newBuilder()
                    .withLocalFilePath(filePath)
                    .withDocumentSetName(new File(filePath).getName())
                    .withSplitterProcess(SplitterPreprocessParams.newBuilder()
                            .withAppendKeywordsToChunkEnum(true)
                            .withAppendTitleToChunkEnum(false)
                            .withChunkSplitter("\r\n[\r\n]+")
                            .Build())
                    .Build();
        }

        collection.loadAndSplitText(param);
        if (isFileLoaded(collection, new File(filePath).getName())) {
            log.info(filePath + " load succeed.");
            return new VectorDbResponse().success();
        } else {
            log.info(filePath + " load fail.");
            return new VectorDbResponse().fail();
        }
    }

    public VectorDbResponse loadAndSplitFile(String databaseName, String collectionName, String filePath, String chunkSplitType) throws Exception {
        AIDatabase db = vDbClient.aiDatabase(databaseName);
        CollectionView collection = db.describeCollectionView(collectionName);

        SplitterPreprocessParams.Builder splitterBuilder = SplitterPreprocessParams.newBuilder()
                .withAppendKeywordsToChunkEnum(true)
                .withAppendTitleToChunkEnum(false);

        if ("1".equals(chunkSplitType)) {
            splitterBuilder.withChunkSplitter("\n{2,}");
        }


        LoadAndSplitTextParam param = LoadAndSplitTextParam.newBuilder()
                .withLocalFilePath(filePath)
                .withParsingProcess(ParsingProcessParam.newBuilder()
                        .withParsingType(ParsingTypeEnum.VisionModel)
                        .build())
                .withDocumentSetName(new File(filePath).getName())
                .withSplitterProcess(splitterBuilder.Build())
                .Build();
        log.info("loading " + filePath + " ...");

        collection.loadAndSplitText(param);
        if (isFileLoaded(collection, new File(filePath).getName())) {
            log.info(filePath + " load succeed.");
            return new VectorDbResponse().success();
        } else {
            log.info(filePath + " load fail.");
            return new VectorDbResponse().fail();
        }
    }

    public VectorDbResponse getFileInfo(String databaseName, String collectionName, String fileName, int limit) {
        AIDatabase db = vDbClient.aiDatabase(databaseName);
        CollectionView collection = db.describeCollectionView(collectionName);
        if (isFileLoaded(collection, fileName)) {
            List<ChunkInfo> fileChunks = collection.getChunks(fileName, limit, 0).getChunks();
            List<String> chunks = fileChunks.stream().map(ChunkInfo::getText).collect(toList());
            return new VectorDbResponse().success().data(chunks);
        } else {
            return new VectorDbResponse().code(VectorDbResponse.CODE101).message("file not parse");
        }
    }

    public VectorDbResponse getFileInfo(String databaseName, String collectionName, String fileName, int limit, int offset) {
        AIDatabase db = vDbClient.aiDatabase(databaseName);
        CollectionView collection = db.describeCollectionView(collectionName);
        if (isFileLoaded(collection, fileName)) {
            List<ChunkInfo> fileChunks = collection.getChunks(fileName, limit, offset).getChunks();
            List<String> chunks = fileChunks.stream().map(ChunkInfo::getText).collect(toList());
            return new VectorDbResponse().success().data(chunks);
        } else {
            return new VectorDbResponse().code(VectorDbResponse.CODE101).message("file not parse");
        }
    }

    private boolean isFileLoaded(CollectionView cView, String fileName) {
        String fileId = "";
        int cnt = 0;

        /**
         * 文件预处理、Embedding 向量化的状态。
         * New：等待解析。
         * Loading：文件解析中。
         * Failure：文件解析、写入出错。
         * Ready：文件解析、写入完成。
         */
        while (true) {
            try {
                DocumentFileContent file = cView.getFile(fileName, fileId);
                String indexedStatus = file.getDocumentSetInfo().getIndexedStatus();
                int indexedProgress = file.getDocumentSetInfo().getIndexedProgress();
                String indexedErrorMsg = file.getDocumentSetInfo().getIndexedErrorMsg();
                log.info(fileName + " indexed Progress: " + indexedProgress);
                log.info(fileName + " indexed message: " + indexedErrorMsg);
                if (indexedStatus.equalsIgnoreCase("Ready")) {
                    return true;
                } else if (indexedStatus.equalsIgnoreCase("Failure")) {
                    return false;
                } else {
                    continue;
                }
            } catch (Exception e) {
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException ex) {
                    ex.printStackTrace();
                }
            }
            cnt += 1;
            if (cnt >= 10) {
                log.info(fileName + " parse timeout.");
                return false;
            }

        }

    }

    // 指定文件检索语料
    public VectorDbResponse searchFileContent(String databaseName, String collectionName, String fileName, String content, int limit, double scoreThresh) {
        List<SearchContent> searchContents = new ArrayList<>();
        AIDatabase db = vDbClient.aiDatabase(databaseName);
        CollectionView collection = db.describeCollectionView(collectionName);
        SearchByContentsParam searchByContentsParam = SearchByContentsParam.newBuilder()
                .withContent(content)
                .withSearchContentOption(SearchOption.newBuilder().withChunkExpand(Arrays.asList(0, 0)).build())
                .withDocumentSetName(fileName)
                .withLimit(limit)
                .build();
        List<SearchContentInfo> searchResults = collection.search(searchByContentsParam);
        for (SearchContentInfo res : searchResults) {
            searchContents.add(new SearchContent(res.getScore(), res.getData().getText(), fileName));
        }
        searchContents = searchContents.stream().filter(sc -> sc.getScore() > scoreThresh).collect(toList());
        return new VectorDbResponse().success().data(searchContents);
    }


    // 全知识库检索语料(无结构文档知识库和QA问答对知识库)
    public VectorDbResponse searchFileContentV2(String databaseName, String QADatabaseName, String content, int limit, double scoreThresh, double QAscoreThresh) {
        List<SearchContent> searchContents = searchQAContent(QADatabaseName, content, limit, QAscoreThresh);
        AIDatabase db = vDbClient.aiDatabase(databaseName);
        List<CollectionView> collectionViews = db.listCollectionView();

        for (CollectionView cView : collectionViews) {
            SearchByContentsParam searchByContentsParam = SearchByContentsParam.newBuilder()
                    .withContent(content)
                    .withSearchContentOption(SearchOption.newBuilder().withChunkExpand(Arrays.asList(1, 1)).build())
                    .withLimit(limit)
                    .build();
            List<SearchContentInfo> searchResults = cView.search(searchByContentsParam);
            for (SearchContentInfo res : searchResults) {
                String preText = "";
                String nextText = "";
                String documentName = res.getDocumentSet().getDocumentSetName().replace("\"", "");
                if (res.getData().getPre().size() > 0) {
                    preText = res.getData().getPre().get(0);
                }
                if (res.getData().getNext().size() > 0) {
                    nextText = res.getData().getNext().get(0);
                }
                searchContents.add(new SearchContent(res.getScore(), preText + "\n" + res.getData().getText() + "\n" + nextText, documentName));
            }
        }
        Collections.sort(searchContents);
        searchContents = searchContents.stream().filter(sc -> sc.getScore() > scoreThresh).collect(toList());
        int subLimit = Math.min(searchContents.size(), limit);
        return new VectorDbResponse().success().data(new ArrayList<>(searchContents.subList(0, subLimit)));
    }

    // QA问答对知识库检索
    public VectorDbResponse searchFileContentV2(String qaCollectionName, String QADatabaseName, String content, int limit, double scoreThresh) {
        List<SearchContent> searchContents = searchQAContent(qaCollectionName, QADatabaseName, content, limit, scoreThresh);
        int subLimit = Math.min(searchContents.size(), limit);
        return new VectorDbResponse().success().data(new ArrayList<>(searchContents.subList(0, subLimit)));
    }

    // 全知识库检索语料(无结构文档知识库和QA问答对知识库) + 公共知识库
    public VectorDbResponse searchFileContentV2(String databaseName, String QADatabaseName, String content, int limit, double scoreThresh, double QAscoreThresh, String publicDatabaseName) {
        List<SearchContent> searchContents = searchQAContent(QADatabaseName, content, limit, QAscoreThresh);
        AIDatabase db = vDbClient.aiDatabase(databaseName);
        List<CollectionView> collectionViews = db.listCollectionView();

        AIDatabase publicDb = vDbClient.aiDatabase(publicDatabaseName);
        List<CollectionView> publicCollectionViews = publicDb.listCollectionView();

        collectionViews.addAll(publicCollectionViews);
        for (CollectionView cView : collectionViews) {
            SearchByContentsParam searchByContentsParam = SearchByContentsParam.newBuilder()
                    .withContent(content)
                    .withSearchContentOption(SearchOption.newBuilder().withChunkExpand(Arrays.asList(1, 1)).build())
                    .withLimit(limit)
                    .build();
            List<SearchContentInfo> searchResults = cView.search(searchByContentsParam);
            for (SearchContentInfo res : searchResults) {
                String preText = "";
                String nextText = "";
                String documentName = res.getDocumentSet().getDocumentSetName().replace("\"", "");
                if (res.getData().getPre().size() > 0) {
                    preText = res.getData().getPre().get(0);
                }
                if (res.getData().getNext().size() > 0) {
                    nextText = res.getData().getNext().get(0);
                }
                searchContents.add(new SearchContent(res.getScore(), preText + "\n" + res.getData().getText() + "\n" + nextText, documentName));
            }
        }
        Collections.sort(searchContents);
        searchContents = searchContents.stream().filter(sc -> sc.getScore() > scoreThresh).collect(toList());
        int subLimit = Math.min(searchContents.size(), limit);
        return new VectorDbResponse().success().data(new ArrayList<>(searchContents.subList(0, subLimit)));
    }


    public VectorDbResponse deleteKnowledgeFile(String databaseName, String collectionName, String fileName) {
        AIDatabase db = vDbClient.aiDatabase(databaseName);
        CollectionView collection = db.describeCollectionView(collectionName);
        CollectionViewConditionParam build = CollectionViewConditionParam
                .newBuilder()
                .withDocumentSetNames(Arrays.asList(fileName))
                .build();
        AffectRes affectRes = collection.deleteDocumentSets(build);
        if (affectRes.getCode() == 0) {
            return new VectorDbResponse().success();
        }
        return new VectorDbResponse().fail().message(affectRes.getMsg());
    }

    public VectorDbResponse updateQAField(
            Long tenantId,
            Long collectionId,
            String docName,
            Map<String, Object> fieldsToUpdate
    ) {
        try {
            String dataBaseName = "QA-database-" + tenantId;
            String collectionName = "QA-collection-" + collectionId;
            // 结构化文档更新（Collection）
            Database db = vDbClient.database(dataBaseName);
            Collection collection = db.collection(collectionName);
            checkQAFilterField("fileStatus", collection, dataBaseName);
            checkQAFilterField("categoryId", collection, dataBaseName);

            Document.Builder docBuilder = Document.newBuilder();
            fieldsToUpdate.forEach((k, v) -> docBuilder.addDocField(new DocField(k, v)));

            UpdateParam updateParam = UpdateParam.newBuilder()
                    .withFilter(new Filter("docName = \"" + docName + "\""))
                    .build();

            AffectRes affectRes = collection.update(updateParam, docBuilder.build());
            if (affectRes.getCode() == 0) {
                return new VectorDbResponse().success();
            }
            return new VectorDbResponse().fail().message(affectRes.getMsg());
        } catch (Exception e) {
            log.error("向量文档字段更新失败", e);
            return new VectorDbResponse().fail().message("更新异常：" + e.getMessage());
        }
    }

    private void checkQAFilterField(String field, Collection collection, String dataBaseName) {
        boolean hasIndex = collection.getIndexes().stream()
                .anyMatch(index -> field.equals(index.getFieldName()));

        if (!hasIndex) {
            vDbClient.addIndex(
                    dataBaseName,
                    collection.getCollection(),
                    AddIndexParam.newBuilder()
                            .withBuildExistedData(true)
                            .withIndexes(List.of(
                                    new FilterIndex(field, FieldType.String, IndexType.FILTER)
                            ))
                            .build()
            );
        }
    }


    public synchronized VectorDbResponse createQuestionDatabase(String databaseName) {
        List<String> createdDatabase = vDbClient.listDatabase();
        if (createdDatabase.contains(databaseName)) {
            log.info(databaseName + ": already exist!");
            return new VectorDbResponse().success();
        } else {
            try {
                Database db = vDbClient.createDatabase(databaseName);
                return new VectorDbResponse().success();
            } catch (Exception e) {
                log.info("create database " + databaseName + " exception: " + e);
                return new VectorDbResponse().fail();
            }
        }
    }

    public synchronized VectorDbResponse createQuestionCollection(String databaseName, String collectionName) {
        Database db = vDbClient.database(databaseName);
        List<Collection> collections = db.listCollections();
        List<String> collectionNames = collections.stream().map(Collection::getCollection).collect(toList());
        if (!collectionNames.contains(collectionName)) {
            CreateCollectionParam collectionParam = CreateCollectionParam.newBuilder()
                    .withName(collectionName)
                    .withShardNum(1)
                    .withReplicaNum(0)
                    .withDescription("QA embedding collection")
                    .addField(new FilterIndex("id", FieldType.String, IndexType.PRIMARY_KEY))
                    .addField(new VectorIndex("vector", BGE_BASE_ZH.getDimension(), IndexType.HNSW,
                            MetricType.COSINE, new HNSWParams(16, 200)))
                    .addField(new FilterIndex("timeStamp", FieldType.Uint64, IndexType.FILTER))
                    .withEmbedding(
                            Embedding
                                    .newBuilder()
                                    .withModel(BGE_BASE_ZH)
                                    .withField("question")
                                    .withVectorField("vector")
                                    .build())
                    .build();
            try {
                Collection collection = db.createCollection(collectionParam);
                return new VectorDbResponse().success();
            } catch (Exception e) {
                log.info("create collection " + collectionName + " exception: " + e);
                return new VectorDbResponse().fail();
            }
        } else {
            log.info(collectionName + ": already exist!");
            return new VectorDbResponse().success();
        }
    }

    public VectorDbResponse insertQuestion(String databaseName, String collectionName, String question, String answer, String id, long timeStamp) {
        Database db = vDbClient.database(databaseName);
        Collection collection = db.collection(collectionName);
        Document document = Document.newBuilder()
                .withId(id)
                .addDocField(new DocField("question", question))
                .addDocField(new DocField("answer", answer))
                .addDocField(new DocField("timeStamp", timeStamp))
                .build();
        InsertParam insertParam = InsertParam.newBuilder()
                .addDocument(document)
                .withBuildIndex(true)
                .build();
        AffectRes affectRes = collection.upsert(insertParam);
        if (affectRes.getCode() == 0) {
            return new VectorDbResponse().success();
        }
        return new VectorDbResponse().fail().message(affectRes.getMsg());
    }

    public VectorDbResponse deleteQuestion(String databaseName, String collectionName, String id) {
        Database db = vDbClient.database(databaseName);
        Collection collection = db.collection(collectionName);
        DeleteParam deleteParam = DeleteParam.newBuilder()
                .withDocumentIds(Arrays.asList(id))
                .build();
        AffectRes affectRes = collection.delete(deleteParam);
        if (affectRes.getCode() == 0) {
            return new VectorDbResponse().success();
        }
        return new VectorDbResponse().fail().message(affectRes.getMsg());
    }

    public VectorDbResponse searchQuestion(String databaseName, String collectionName, String question, int keepDay) {
        long startTime = System.currentTimeMillis() - keepDay * 24 * 60 * 60 * 1000;
        SearchContent similarQuestion = new SearchContent();
        Database db = vDbClient.database(databaseName);
        Collection collection = db.collection(collectionName);
        SearchByEmbeddingItemsParam searchByEmbeddingItemsParam = SearchByEmbeddingItemsParam.newBuilder()
                .withEmbeddingItems(Arrays.asList(question))
                .withParams(new HNSWSearchParams(200))
                .withRetrieveVector(false)
                .withLimit(1)
                .withFilter(new Filter("timeStamp > " + startTime))
                .withOutputFields(Arrays.asList("id", "question", "answer"))
                .build();
        List<List<Document>> documents = collection.searchByEmbeddingItems(searchByEmbeddingItemsParam).getDocuments();
        for (List<Document> docs : documents) {
            Map<String, String> QA = new HashMap();
            for (Document doc : docs) {
                List<DocField> docFields = doc.getDocFields();
                for (DocField df : docFields) {
                    QA.put(df.getName(), df.getStringValue());
                }
                similarQuestion.setScore(doc.getScore());
                similarQuestion.setContent(QA.get("question"));
                similarQuestion.setAppendix(QA.get("answer"));
                similarQuestion.setContentId(doc.getId());
                similarQuestion.setDocumentName("QA");
            }
        }
        return new VectorDbResponse().success().data(similarQuestion);
    }

    /**
     * 删除企业库, 删除前先删除企业库下的所有知识库
     *
     * @param databaseName
     * @return
     */
    public synchronized VectorDbResponse dropDatabase(String databaseName) {
        List<String> createdDatabase = vDbClient.listDatabase();
        if (!createdDatabase.contains(databaseName)) {
            return new VectorDbResponse().fail().message("database " + databaseName + " not exist");
        }
        AIDatabase db = vDbClient.aiDatabase(databaseName);
        List<CollectionView> collectionViews = db.listCollectionView();
        if (collectionViews.size() > 0) {
            return new VectorDbResponse().fail().message("database " + databaseName + " is not empty, drop collectionView first");
        }
        AffectRes affectRes = vDbClient.dropAIDatabase(databaseName);
        if (affectRes.getCode() == 0) {
            return new VectorDbResponse().success().message(affectRes.getMsg());
        } else {
            return new VectorDbResponse().fail().message(affectRes.getMsg());
        }
    }

    /**
     * 删除知识库
     *
     * @param databaseName
     * @param collectionName
     * @return
     */
    public synchronized VectorDbResponse dropCollection(String databaseName, String collectionName) {
        List<String> createdDatabase = vDbClient.listDatabase();
        if (!createdDatabase.contains(databaseName)) {
            return new VectorDbResponse().fail().message("database " + databaseName + " not exist");
        }
        AIDatabase db = vDbClient.aiDatabase(databaseName);
        List<CollectionView> collectionViews = db.listCollectionView();
        List<String> collectionViewNames = collectionViews.stream().map(CollectionView::getCollectionView).collect(toList());
        if (!collectionViewNames.contains(collectionName)) {
            return new VectorDbResponse().fail().message("collectionView " + collectionName + " not exist");
        }
        AffectRes affectRes = db.dropCollectionView(collectionName);
        if (affectRes.getCode() == 0) {
            return new VectorDbResponse().success().message(affectRes.getMsg());
        } else {
            return new VectorDbResponse().fail().message(affectRes.getMsg());
        }
    }

    /**
     * 拆分无结构文档
     *
     * @param filePath       文档路径
     * @param limit          文档拆分后返回文档块的数量
     * @param chunkSplitType
     * @return
     * @throws Exception
     */
    public VectorDbResponse parseUnstructuredFile(String filePath, int limit, String chunkSplitType) throws Exception {
        AIDatabase db = vDbClient.aiDatabase(defaultDb);
        CollectionView collection = db.describeCollectionView(defaultCView);
        String fileName = new File(filePath).getName();
        SplitterPreprocessParams.Builder splitterBuilder = SplitterPreprocessParams.newBuilder()
                .withAppendKeywordsToChunkEnum(true)
                .withAppendTitleToChunkEnum(false);

        if ("1".equals(chunkSplitType)) {
            splitterBuilder.withChunkSplitter("\n{2,}");
        }

        LoadAndSplitTextParam param = LoadAndSplitTextParam.newBuilder()
                .withLocalFilePath(filePath)
                .withParsingProcess(ParsingProcessParam.newBuilder()
                        .withParsingType(ParsingTypeEnum.VisionModel)
                        .build())
                .withDocumentSetName(fileName)
                .withSplitterProcess(splitterBuilder.Build())
                .Build();
        log.info("parsing  " + filePath + " ...");
        collection.loadAndSplitText(param);
        if (isFileLoaded(collection, fileName)) {
            log.info(filePath + " parse succeed.");
            VectorDbResponse fileInfo = getFileInfo(defaultDb, defaultCView, fileName, limit);
            return fileInfo;
        } else {
            log.info(filePath + " parse fail.");
            return new VectorDbResponse().fail();
        }
    }

    /**
     * 拆分qa问答对文档
     *
     * @param filePath 文档路径
     * @param limit    文档拆分后返回文档块的数量
     * @return
     * @throws Exception
     */
    public VectorDbResponse parseQAFile(String filePath, int limit) throws Exception {
        AIDatabase db = vDbClient.aiDatabase(defaultDb);
        CollectionView collection = db.describeCollectionView(defaultQACView);
        String fileName = new File(filePath).getName();
        LoadAndSplitTextParam param = null;
        if (fileName.endsWith(".docx")) {
            param = LoadAndSplitTextParam.newBuilder()
                    .withLocalFilePath(filePath)
                    .withDocumentSetName(fileName)
                    .withSplitterProcess(SplitterPreprocessParams.newBuilder()
                            .withChunkSplitter("\n{2,}")
                            .Build())
                    .Build();
        } else {
            param = LoadAndSplitTextParam.newBuilder()
                    .withLocalFilePath(filePath)
                    .withDocumentSetName(fileName)
                    .withSplitterProcess(SplitterPreprocessParams.newBuilder()
                            .withAppendKeywordsToChunkEnum(true)
                            .withAppendTitleToChunkEnum(false)
                            .withChunkSplitter("\r\n[\r\n]+")
                            .Build())
                    .Build();
        }
        log.info("parsing  " + filePath + " ...");
        collection.loadAndSplitText(param);
        if (isFileLoaded(collection, fileName)) {
            log.info(filePath + " parse succeed.");
            VectorDbResponse fileInfo = getFileInfo(defaultDb, defaultQACView, fileName, limit);
            return fileInfo;
        } else {
            log.info(filePath + " parse fail.");
            return new VectorDbResponse().fail();
        }
    }

    public List<SearchContent> searchQAContent(String collectionName, String QADatabaseName, String content, int limit, double QAscoreThresh) {
        List<SearchContent> searchContents = new ArrayList<>();
        Database QAdb = vDbClient.database(QADatabaseName);
        List<Collection> collections = QAdb.listCollections();
        List<Collection> collectionsToSearch = collections.stream()
                .filter(cView -> Objects.equals(collectionName, cView.getCollection()))
                .toList();
        for (Collection col : collectionsToSearch) {
            checkQAFilterField("fileStatus", col, QADatabaseName);
            SearchByEmbeddingItemsParam searchByEmbeddingItemsParam = SearchByEmbeddingItemsParam.newBuilder()
                    .withEmbeddingItems(Arrays.asList(content))
                    .withParams(new HNSWSearchParams(200))
                    .withRetrieveVector(false)
                    .withFilter(new Filter("fileStatus != \"2\""))
                    .withLimit(limit)
                    .withOutputFields(Arrays.asList("id", "question", "answer", "docName"))
                    .build();
            List<List<Document>> documents = col.searchByEmbeddingItems(searchByEmbeddingItemsParam).getDocuments();
            for (List<Document> docs : documents) {
                Map<String, String> QA = new HashMap();
                for (Document doc : docs) {
                    List<DocField> docFields = doc.getDocFields();
                    for (DocField df : docFields) {
                        QA.put(df.getName(), df.getStringValue());
                    }
                    SearchContent sc = new SearchContent();
                    sc.setScore(doc.getScore());
                    sc.setContent(QA.get("question"));
                    sc.setAppendix(QA.get("answer"));
                    sc.setContentId(doc.getId());
                    sc.setDocumentName(QA.get("docName"));
                    searchContents.add(sc);
                }
            }
        }
        Collections.sort(searchContents);
        searchContents = searchContents.stream().filter(sc -> sc.getScore() > QAscoreThresh).collect(toList());
        return searchContents;
    }

    public List<SearchContent> searchQAContent(String QADatabaseName, String content, int limit, double QAscoreThresh) {
        List<SearchContent> searchContents = new ArrayList<>();
        Database QAdb = vDbClient.database(QADatabaseName);
        List<Collection> collections = QAdb.listCollections();
        for (Collection col : collections) {
            checkQAFilterField("fileStatus", col, QADatabaseName);
            SearchByEmbeddingItemsParam searchByEmbeddingItemsParam = SearchByEmbeddingItemsParam.newBuilder()
                    .withEmbeddingItems(Arrays.asList(content))
                    .withParams(new HNSWSearchParams(200))
                    .withRetrieveVector(false)
//                    .withFilter(new Filter("fileStatus != \"2\""))
                    .withLimit(limit)
                    .withOutputFields(Arrays.asList("id", "question", "answer", "docName"))
                    .build();
            List<List<Document>> documents = col.searchByEmbeddingItems(searchByEmbeddingItemsParam).getDocuments();
            for (List<Document> docs : documents) {
                Map<String, String> QA = new HashMap();
                for (Document doc : docs) {
                    List<DocField> docFields = doc.getDocFields();
                    for (DocField df : docFields) {
                        QA.put(df.getName(), df.getStringValue());
                    }
                    SearchContent sc = new SearchContent();
                    sc.setScore(doc.getScore());
                    sc.setContent(QA.get("question"));
                    sc.setAppendix(QA.get("answer"));
                    sc.setContentId(doc.getId());
                    sc.setDocumentName(QA.get("docName"));
                    searchContents.add(sc);
                }
            }
        }
        Collections.sort(searchContents);
        searchContents = searchContents.stream().filter(sc -> sc.getScore() > QAscoreThresh).collect(toList());
        return searchContents;
    }

    public synchronized VectorDbResponse createQADatabase(String databaseName) {
        List<String> createdDatabase = vDbClient.listDatabase();
        if (createdDatabase.contains(databaseName)) {
            log.info(databaseName + ": already exist!");
            return new VectorDbResponse().success();
        } else {
            try {
                Database db = vDbClient.createDatabase(databaseName);
                return new VectorDbResponse().success();
            } catch (Exception e) {
                log.info("create database " + databaseName + " exception: " + e);
                return new VectorDbResponse().fail();
            }
        }
    }

    public synchronized VectorDbResponse createQACollection(String databaseName, String collectionName) {
        Database db = vDbClient.database(databaseName);
        List<Collection> collections = db.listCollections();
        List<String> collectionNames = collections.stream().map(Collection::getCollection).collect(toList());
        if (!collectionNames.contains(collectionName)) {
            CreateCollectionParam collectionParam = CreateCollectionParam.newBuilder()
                    .withName(collectionName)
                    .withShardNum(1)
                    .withReplicaNum(0)
                    .withDescription("QA embedding collection")
                    .addField(new FilterIndex("id", FieldType.String, IndexType.PRIMARY_KEY))
                    .addField(new VectorIndex("vector", BGE_BASE_ZH.getDimension(), IndexType.HNSW,
                            MetricType.COSINE, new HNSWParams(16, 200)))
                    .addField(new FilterIndex("docName", FieldType.String, IndexType.FILTER))
                    .addField(new FilterIndex("categoryId", FieldType.String, IndexType.FILTER))
                    .addField(new FilterIndex("timeStamp", FieldType.Uint64, IndexType.FILTER))
                    .addField(new FilterIndex("fileStatus", FieldType.String, IndexType.FILTER))
                    .withEmbedding(
                            Embedding
                                    .newBuilder()
                                    .withModel(BGE_BASE_ZH)
                                    .withField("question")
                                    .withVectorField("vector")
                                    .build())
                    .build();
            try {
                db.createCollection(collectionParam);
                return new VectorDbResponse().success();
            } catch (Exception e) {
                log.info("create collection " + collectionName + " exception: " + e);
                return new VectorDbResponse().fail();
            }
        } else {
            log.info(collectionName + ": already exist!");
            return new VectorDbResponse().success();
        }
    }

    public VectorDbResponse insertQA(String databaseName, String collectionName, String docName, String question, String answer, String id, String categoryId) {
        Database db = vDbClient.database(databaseName);
        Collection collection = db.collection(collectionName);
        Document document = Document.newBuilder()
                .withId(id)
                .addDocField(new DocField("question", question))
                .addDocField(new DocField("answer", answer))
                .addDocField(new DocField("docName", docName))
                .addDocField(new DocField("timeStamp", System.currentTimeMillis()))
                .addDocField(new DocField("fileStatus", "1"))
                .addDocField((new DocField("categoryId", StringUtils.isEmpty(categoryId) ? "-1" : categoryId)))
                .build();

        InsertParam insertParam = InsertParam.newBuilder()
                .addDocument(document)
                .withBuildIndex(true)
                .build();
        AffectRes affectRes = collection.upsert(insertParam);
        if (affectRes.getCode() == 0) {
            return new VectorDbResponse().success();
        }
        return new VectorDbResponse().fail().message(affectRes.getMsg());
    }

    public VectorDbResponse deleteQA(String databaseName, String collectionName, String docName, String id) {
        Database db = vDbClient.database(databaseName);
        Collection collection = db.collection(collectionName);
        DeleteParam deleteParam = DeleteParam.newBuilder()
                .withDocumentIds(Arrays.asList(id))
                .withFilter(new Filter("docName=" + "\"" + docName + "\""))
                .build();
        AffectRes affectRes = collection.delete(deleteParam);
        if (affectRes.getCode() == 0) {
            return new VectorDbResponse().success();
        }
        return new VectorDbResponse().fail().message(affectRes.getMsg());
    }

    public VectorDbResponse updateQ(String databaseName, String collectionName, String docName, String id, String newQuestion) {
        Database db = vDbClient.database(databaseName);
        Collection collection = db.collection(collectionName);
        Document updateDoc = Document.newBuilder()
                .addDocField(new DocField("question", newQuestion))
                .build();
        UpdateParam updateParam = UpdateParam
                .newBuilder()
                .addAllDocumentId(Arrays.asList(id))
                .withFilter(new Filter("docName=" + "\"" + docName + "\""))
                .build();
        AffectRes affectRes = collection.update(updateParam, updateDoc);
        if (affectRes.getCode() == 0) {
            return new VectorDbResponse().success();
        }
        return new VectorDbResponse().fail().message(affectRes.getMsg());
    }

    public VectorDbResponse updateA(String databaseName, String collectionName, String docName, String id, String newAnswer) {
        Database db = vDbClient.database(databaseName);
        Collection collection = db.collection(collectionName);
        Document updateDoc = Document.newBuilder()
                .addDocField(new DocField("answer", newAnswer))
                .build();
        UpdateParam updateParam = UpdateParam
                .newBuilder()
                .addAllDocumentId(Arrays.asList(id))
                .withFilter(new Filter("docName=" + "\"" + docName + "\""))
                .build();
        AffectRes affectRes = collection.update(updateParam, updateDoc);
        if (affectRes.getCode() == 0) {
            return new VectorDbResponse().success();
        }
        return new VectorDbResponse().fail().message(affectRes.getMsg());
    }

    public VectorDbResponse updateDocName(String databaseName, String collectionName, String oldDocName, String newDocName, String categoryId) {
        Database db = vDbClient.database(databaseName);
        Collection collection = db.collection(collectionName);

        // 构造更新内容：新的 docName
        Document updateDoc = Document.newBuilder()
                .addDocField(new DocField("docName", newDocName))
                .addDocField(new DocField("categoryId", StringUtils.isEmpty(categoryId) ? "-1" : categoryId))
                .build();

        // 构造过滤条件：原来的 docName + id
        UpdateParam updateParam = UpdateParam
                .newBuilder()
                .withFilter(new Filter("docName=" + "\"" + oldDocName + "\""))
                .build();

        AffectRes affectRes = collection.update(updateParam, updateDoc);
        if (affectRes.getCode() == 0) {
            return new VectorDbResponse().success();
        }
        return new VectorDbResponse().fail().message(affectRes.getMsg());
    }


    public VectorDbResponse deleteQAFile(String databaseName, String collectionName, String docName) {
        Database db = vDbClient.database(databaseName);
        Collection collection = db.collection(collectionName);
        DeleteParam deleteParam = DeleteParam.newBuilder()
                .withFilter(new Filter("docName=" + "\"" + docName + "\""))
                .build();
        AffectRes affectRes = collection.delete(deleteParam);
        if (affectRes.getCode() == 0) {
            return new VectorDbResponse().success();
        }
        return new VectorDbResponse().fail().message(affectRes.getMsg());
    }


    // 全知识库检索语料(无结构文档知识库)
    public List<SearchContent> searchNoStructureFileContent(String databaseName, List<String> collectionNames,
                                                            String content, Double minMatchThreshold,
                                                            Integer maxRecallCount,
                                                            Map<Long, String> knowledgeId2NameMap,
                                                            Map<String, List<String>> collectionFileNames,
                                                            String logPrefix) {
        log.info("{} Entering searchNoStructureFileContent. DB: '{}', Collections: {}, Query: '{}...', Threshold: {}, MaxRecall: {}",
                logPrefix, databaseName, collectionNames, content.substring(0, Math.min(content.length(), 20)), minMatchThreshold, maxRecallCount);

        if (collectionNames == null || collectionNames.isEmpty()) {
            log.warn("{} Collection names list is null or empty. Returning empty list.", logPrefix);
            return List.of();
        }
        if (knowledgeId2NameMap == null) {
            log.warn("{} knowledgeId2NameMap is null. Knowledge names may not be resolved.", logPrefix);
        }

        var db = vDbClient.aiDatabase(databaseName);
        Set<String> targetCollectionNames = new HashSet<>(collectionNames);

        List<CollectionView> relevantCollectionViews = db.listCollectionView().stream()
                .filter(cView -> targetCollectionNames.contains(cView.getCollectionView()))
                .toList();

        if (relevantCollectionViews.isEmpty()) {
            log.warn("{} No relevant collection views found in database '{}' for names: {}. Returning empty list.",
                    logPrefix, databaseName, targetCollectionNames);
            return List.of();
        }
        log.debug("{} Found {} relevant collection views: {}", logPrefix, relevantCollectionViews.size(),
                relevantCollectionViews.stream().map(CollectionView::getCollectionView).toList());

        final List<Integer> chunkExpandOption = List.of(1, 1);

        // 1. Collect all results from all relevant views
        List<SearchContent> allCollectedContents = relevantCollectionViews.stream()
                .flatMap(cView -> processCollectionView(cView, content, chunkExpandOption,
                        collectionFileNames, knowledgeId2NameMap, logPrefix, maxRecallCount))
                .collect(Collectors.toCollection(ArrayList::new)); // Collect into a mutable list for subsequent operations

        log.info("{} Collected {} total potential search results from all {} relevant views.",
                logPrefix, allCollectedContents.size(), relevantCollectionViews.size());

        if (allCollectedContents.isEmpty()) {
            log.info("{} No search results found across all collections. Returning empty list.", logPrefix);
            return List.of();
        }

        // 2. Filter by minMatchThreshold
        List<SearchContent> filteredContents = allCollectedContents.stream()
                .filter(sc -> sc.getScore() >= minMatchThreshold)
                .toList(); // Can be immutable here

        int discardedCount = allCollectedContents.size() - filteredContents.size();
        if (discardedCount > 0) {
            log.info("{} Filtered out {} results with score <= {}. {} results remaining.",
                    logPrefix, discardedCount, minMatchThreshold, filteredContents.size());
        } else {
            log.debug("{} No results filtered out by threshold {}. {} results remaining.",
                    logPrefix, minMatchThreshold, filteredContents.size());
        }


        if (filteredContents.isEmpty()) {
            log.info("{} All results were filtered out by the threshold. Returning empty list.", logPrefix);
            return List.of();
        }

        // 3. Sort the filtered results (make a mutable copy if `filteredContents` is immutable and sort is in-place)
        List<SearchContent> sortedContents = new ArrayList<>(filteredContents); // Ensure mutable for sort
        sortedContents.sort(Comparator.comparingDouble(SearchContent::getScore).reversed());
        log.debug("{} Sorted {} results by score descending.", logPrefix, sortedContents.size());

        // 4. Limit to maxRecallCount
        List<SearchContent> finalResults;
        if (sortedContents.size() > maxRecallCount) {
            finalResults = sortedContents.subList(0, maxRecallCount);
            log.info("{} Limited results from {} to {} (maxRecallCount).", logPrefix, sortedContents.size(), finalResults.size());
        } else {
            finalResults = sortedContents; // Already within limit
            log.debug("{} No limiting needed, {} results is not more than maxRecallCount {}.", logPrefix, finalResults.size(), maxRecallCount);
        }

        log.info("{} Exiting searchNoStructureFileContent. Returning {} search results.", logPrefix, finalResults.size());
        return finalResults;
    }

    private Stream<SearchContent> processCollectionView(CollectionView cView, String content, List<Integer> chunkExpand,
                                                        Map<String, List<String>> collectionFileNames,
                                                        Map<Long, String> knowledgeId2NameMap, String logPrefix,
                                                        int limit) {
        String collectionViewName = cView.getCollectionView();
        log.debug("{} Processing collection view: {}", logPrefix, collectionViewName);

        var searchBuilder = SearchByContentsParam.newBuilder()
                .withLimit(limit)
                .withContent(content)
                .withSearchContentOption(SearchOption.newBuilder().withChunkExpand(chunkExpand).build());

        List<String> specificFileNames = collectionFileNames.get(collectionViewName);
        if (specificFileNames != null && !specificFileNames.isEmpty()) {
            log.debug("{} Applying document set filter for collection '{}': {}", logPrefix, collectionViewName, specificFileNames);
            searchBuilder.withDocumentSetName(specificFileNames);
        } else {
            log.warn("{} No specific document set filter for collection '{}'. Searching all documents in collection.", logPrefix, collectionViewName);
        }
        var searchParam = searchBuilder.build();

        log.debug("{} Calling search API on collection view '{}' with param: {}", logPrefix, collectionViewName, searchParam);
        List<SearchContentInfo> searchResults;
        try {
            searchResults = cView.search(searchParam);
        } catch (Exception e) {
            log.error("{} Error during search for collection '{}'", logPrefix, collectionViewName, e);
            return Stream.empty(); // Return empty stream on error to not break the chain
        }

        log.info("{} Collection view '{}' returned {} search results from API.", logPrefix, collectionViewName, searchResults.size());

        if (searchResults.isEmpty()) {
            return Stream.empty();
        }

        return searchResults.stream()
                .map(res -> mapToSearchContent(res, cView, knowledgeId2NameMap, logPrefix))
                .filter(Objects::nonNull);
    }

    private SearchContent mapToSearchContent(SearchContentInfo res, CollectionView cView,
                                             Map<Long, String> knowledgeId2NameMap, String logPrefix) {
        try {
            var data = res.getData();
            if (data == null) {
                log.warn("{} SearchContentInfo.getData() is null for a result in collection '{}'. Skipping this result.", logPrefix, cView.getCollectionView());
                return null;
            }

            var preText = data.getPre() != null && !data.getPre().isEmpty() ? data.getPre().get(0) : "";
            var nextText = data.getNext() != null && !data.getNext().isEmpty() ? data.getNext().get(0) : "";
            String mainText = data.getText();
            if (mainText == null) {
                log.warn("{} SearchContentInfo.getData().getText() is null for a result in collection '{}'. Skipping this result.", logPrefix, cView.getCollectionView());
                return null;
            }

            List<String> contentParts = new ArrayList<>();
            if (!preText.isEmpty()) contentParts.add(preText);
            contentParts.add(mainText);
            if (!nextText.isEmpty()) contentParts.add(nextText);
            String combinedContent = String.join("\n", contentParts);

            String documentSetNameStr = (res.getDocumentSet() != null && res.getDocumentSet().getDocumentSetName() != null)
                    ? res.getDocumentSet().getDocumentSetName() : "";
            var documentName = documentSetNameStr.replace("\"", "");

            var searchContent = new SearchContent();
            searchContent.setScore(res.getScore());
            searchContent.setContent(combinedContent);
            searchContent.setDocumentName(documentName);

            String[] collectionViewParts = cView.getCollectionView().split("-", 2);
            if (collectionViewParts.length > 1 && knowledgeId2NameMap != null) {
                String idPart = collectionViewParts[1];
                String knowledgeIdStr = extractNumbers(idPart);
                if (!knowledgeIdStr.isEmpty()) {
                    try {
                        long knowledgeId = Long.parseLong(knowledgeIdStr);
                        String knowledgeName = knowledgeId2NameMap.get(knowledgeId);
                        searchContent.setKnowledgeName(knowledgeName);
                        if (knowledgeName == null) {
                            log.trace("{} Knowledge ID {} (from collection part '{}') not found in knowledgeId2NameMap for collection '{}'.",
                                    logPrefix, knowledgeId, idPart, cView.getCollectionView());
                        }
                    } catch (NumberFormatException e) {
                        log.warn("{} Could not parse knowledge ID from '{}' (extracted: '{}') in collection '{}'. Error: {}",
                                logPrefix, idPart, knowledgeIdStr, cView.getCollectionView(), e.getMessage());
                    }
                } else {
                    log.trace("{} No numeric knowledge ID found in part '{}' of collection name '{}'.", logPrefix, idPart, cView.getCollectionView());
                }
            } else if (knowledgeId2NameMap == null) {
                log.trace("{} knowledgeId2NameMap is null, cannot set knowledge name for collection '{}'.", logPrefix, cView.getCollectionView());
            }
            return searchContent;
        } catch (Exception e) {
            log.error(logPrefix + " Unexpected error mapping SearchContentInfo to SearchContent for result in collection '" + cView.getCollectionView() + "'. Score: " + res.getScore(), e);
            return null; // Prevent one bad result from failing the whole process
        }
    }

    /**
     * 提取字符串中的数字部分
     *
     * @param input 输入字符串
     * @return 提取的数字部分（如果没有数字，则返回空字符串）
     */
    public static String extractNumbers(String input) {
        if (input == null || input.isEmpty()) {
            return "";
        }
        StringBuilder numberBuilder = new StringBuilder();
        for (char c : input.toCharArray()) {
            if (Character.isDigit(c)) {
                numberBuilder.append(c);
            }
        }
        return numberBuilder.toString();
    }

    public List<SearchContent> searchQAContent(String qaDatabaseName, List<String> targetCollectionNames, String content,
                                               Map<Long, String> knowledgeId2NameMap,
                                               Map<String, Set<String>> collection2categoryIdListMap,
                                               int limit,
                                               String logPrefix) {
        log.info("{} Initiating QA content search in database '{}' for collections: {}.",
                logPrefix, qaDatabaseName, targetCollectionNames);

        if (targetCollectionNames == null || targetCollectionNames.isEmpty()) {
            log.warn("{} No target collection names provided. Returning empty list.", logPrefix);
            return List.of();
        }

        Database qaDb = vDbClient.database(qaDatabaseName);
        if (qaDb == null) {
            log.error("{} Database '{}' not found. Returning empty list.", logPrefix, qaDatabaseName);
            return List.of();
        }

        List<Collection> collectionsToSearch = qaDb.listCollections().stream()
                .filter(cView -> targetCollectionNames.contains(cView.getCollection()))
                .toList();

        if (collectionsToSearch.isEmpty()) {
            log.warn("{} No matching collections found in database '{}' for names: {}. Returning empty list.",
                    logPrefix, qaDatabaseName, targetCollectionNames);
            return List.of();
        }

        log.debug("{} Found {} collections to search: {}", logPrefix, collectionsToSearch.size(),
                collectionsToSearch.stream().map(Collection::getCollection).toList());

        List<SearchContent> allSearchResults = new ArrayList<>();

        for (var col : collectionsToSearch) {
            String currentCollectionName = col.getCollection();
            log.debug("{} Processing collection: {}", logPrefix, currentCollectionName);

            checkQAFilterField(FIELD_FILE_STATUS, col, qaDatabaseName);
            checkQAFilterField(FIELD_CATEGORY_ID, col, qaDatabaseName);

            var filter = new Filter(FIELD_FILE_STATUS + " != \"" + FILE_STATUS_FILTER_VALUE + "\"");
            Set<String> categoryIds = collection2categoryIdListMap.getOrDefault(currentCollectionName, Collections.emptySet());

            if (!categoryIds.isEmpty()) {
                filter.and(Filter.in(FIELD_CATEGORY_ID, new ArrayList<>(categoryIds)));
                log.info("{} Added category filter for collection {}: {}", logPrefix, currentCollectionName, categoryIds);
            }

            var searchParams = SearchByEmbeddingItemsParam.newBuilder()
                    .withEmbeddingItems(Collections.singletonList(content))
                    .withParams(new HNSWSearchParams(200)) // Consider making HNSW 'ef' configurable
                    .withFilter(filter)
                    .withRetrieveVector(false)
                    .withOutputFields(Arrays.asList(FIELD_ID, FIELD_QUESTION, FIELD_ANSWER, FIELD_DOC_NAME))
                    .withLimit(limit)
                    .build();

            try {
                List<List<Document>> searchResultDocuments = col.searchByEmbeddingItems(searchParams).getDocuments();

                int docCount = searchResultDocuments.stream().mapToInt(List::size).sum();
                log.info("{} {} documents retrieved from collection '{}'", logPrefix, docCount, currentCollectionName);

                searchResultDocuments.stream()
                        .flatMap(List::stream)
                        .map(doc -> convertToSearchContent(doc, currentCollectionName, knowledgeId2NameMap, logPrefix))
                        .flatMap(Optional::stream)
                        .forEach(allSearchResults::add);
            } catch (Exception e) {
                log.error("{} Error searching collection '{}': {}", logPrefix, currentCollectionName, e.getMessage(), e);
            }
        }

        if (allSearchResults.isEmpty()) {
            log.info("{} No search results found across all processed collections.", logPrefix);
            return List.of();
        }
        return allSearchResults;

    }

    private Optional<SearchContent> convertToSearchContent(Document doc, String collectionName,
                                                           Map<Long, String> knowledgeId2NameMap,
                                                           String logPrefix) {
        if (doc == null || doc.getDocFields() == null) {
            log.warn("{} Null or empty doc in collection '{}'. Skipping.", logPrefix, collectionName);
            return Optional.empty();
        }

        Map<String, String> fields = doc.getDocFields().stream()
                .collect(Collectors.toMap(DocField::getName, DocField::getStringValue, (a, b) -> b));

        SearchContent sc = new SearchContent();
        sc.setScore(doc.getScore());
        sc.setContentId(doc.getId());
        sc.setContent(fields.get(FIELD_QUESTION));
        sc.setAppendix(fields.get(FIELD_ANSWER));
        sc.setDocumentName(fields.get(FIELD_DOC_NAME));

        if (sc.getContent() == null) {
            log.warn("{} Document ID '{}' in collection '{}' missing '{}' field.", logPrefix, doc.getId(), collectionName, FIELD_QUESTION);
        }

        try {
            String knowledgeIdStr = extractNumbers(collectionName);
            if (!knowledgeIdStr.isEmpty()) {
                long knowledgeId = Long.parseLong(knowledgeIdStr);
                String knowledgeName = knowledgeId2NameMap.get(knowledgeId);
                if (knowledgeName != null) {
                    sc.setKnowledgeName(knowledgeName);
                } else {
                    log.warn("{} No knowledge name for ID {} from collection '{}'", logPrefix, knowledgeId, collectionName);
                }
            }
        } catch (Exception e) {
            log.warn("{} Failed to extract knowledge ID from '{}': {}", logPrefix, collectionName, e.getMessage(), e);
        }

        return Optional.of(sc);
    }


    public int getChunkCount(String databaseName, String collectionName, String documentSetName) {
        AIDatabase db = vDbClient.aiDatabase(databaseName);
        CollectionView collection = db.describeCollectionView(collectionName);

        int limit = 1000;
        int offset = 0;
        int totalCount = 0;

        while (true) {
            GetChunksRes res = collection.getChunks(documentSetName, limit, offset);
            List<ChunkInfo> chunkList = res.getChunks();
            int batchSize = chunkList.size();
            totalCount += batchSize;

            if (batchSize < limit) {
                break;
            }
            offset += limit;
        }

        return totalCount;
    }


}
