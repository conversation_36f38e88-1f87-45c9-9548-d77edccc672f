package com.qc.agent.vectordb.pojo;


/**
 * @Author: xuyunjie
 * @Date: 2024/1/30 16:44
 */
public class SearchContent implements Comparable<SearchContent> {
    public double score;
    public String content;
    public String documentName;
    public String knowledgeName;
    public String appendix = "";
    public String contentId = "";

    public String getKnowledgeName() {
        return knowledgeName;
    }

    public void setKnowledgeName(String knowledgeName) {
        this.knowledgeName = knowledgeName;
    }

    public String getContentId() {
        return contentId;
    }

    public void setContentId(String contentId) {
        this.contentId = contentId;
    }

    public SearchContent() {

    }


    public String getDocumentName() {
        return documentName;
    }

    public void setDocumentName(String documentName) {
        this.documentName = documentName;
    }

    public SearchContent(double score, String content, String documentName) {
        this.score = score;
        this.content = content;
        this.documentName = documentName;
    }

    public String getAppendix() {
        return appendix;
    }

    public void setAppendix(String appendix) {
        this.appendix = appendix;
    }

    public SearchContent(double score, String content, String documentName, String appendix) {
        this.score = score;
        this.content = content;
        this.documentName = documentName;
        this.appendix = appendix;
    }

    public double getScore() {
        return score;
    }

    public void setScore(double score) {
        this.score = score;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    @Override
    public int compareTo(SearchContent content) {
        if (this.score < content.score) {
            return 1;
        } else if (this.score == content.score){
            return 0;
        } else {
            return -1;
        }
    }
}
