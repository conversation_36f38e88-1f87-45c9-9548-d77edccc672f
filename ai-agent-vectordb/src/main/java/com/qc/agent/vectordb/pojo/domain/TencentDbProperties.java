package com.qc.agent.vectordb.pojo.domain;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.ConstructorBinding;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotBlank;


/**
 * <AUTHOR>
 * @date 2025-07-12
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Validated
@ConstructorBinding
@ConfigurationProperties(prefix = "tencentdb")
public record TencentDbProperties(
        @NotBlank String url,
        @NotBlank  String username,
        @NotBlank  String key
) {}
