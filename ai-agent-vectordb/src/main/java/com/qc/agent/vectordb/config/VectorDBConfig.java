package com.qc.agent.vectordb.config;

import com.qc.agent.vectordb.pojo.domain.TencentDbProperties;
import com.qc.agent.vectordb.tencentdb.VectorDBService;
import com.tencent.tcvectordb.client.VectorDBClient;
import com.tencent.tcvectordb.model.param.database.ConnectParam;
import com.tencent.tcvectordb.model.param.enums.ReadConsistencyEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

/**
 * <AUTHOR>
 * @date 2025-07-12
 * @Copyright：Copyright ® 掌控网络. All rights reserved.
 */
@Slf4j
@Configuration
public class VectorDBConfig {

    /**
     * Creates and configures the VectorDBClient bean.
     * This client is the core component for all communication with the VectorDB.
     * The bean is configured to be properly shut down when the Spring context is closed.
     *
     * @param properties The TencentDB properties injected by Spring.
     * @return A configured instance of VectorDBClient.
     */
    @Lazy
    @Bean(destroyMethod = "close") // IMPORTANT: Ensures the client connection pool is closed on shutdown
    public VectorDBClient vectorDBClient(TencentDbProperties properties) {
        log.info("Configuring VectorDBClient for URL: {}", properties.url());

        // Using Java 11+ 'var' for local variable type inference to keep it clean
        var connectParam = ConnectParam.newBuilder()
                .withUrl(properties.url())
                .withUsername(properties.username())
                .withKey(properties.key()) // The key is used here but NEVER logged
                .withTimeout(10)
                .build();

        var client = new VectorDBClient(connectParam, ReadConsistencyEnum.EVENTUAL_CONSISTENCY);

        log.info("VectorDBClient bean created successfully. Read consistency is set to [{}].",
                ReadConsistencyEnum.EVENTUAL_CONSISTENCY);

        return client;
    }

    /**
     * Creates the generic VectorDBService bean.
     * This service encapsulates the logic for interacting with VectorDB collections
     * and is injected into other parts of the application.
     *
     * @param vectorDBClient The automatically injected VectorDBClient bean.
     * @return An instance of VectorDBService.
     */
    @Lazy
    @Bean
    public VectorDBService vectorDBService(VectorDBClient vectorDBClient) {
        // Renamed method from 'bookVectorService' to 'vectorDBService' for clarity.
        // The bean name will be 'vectorDBService' in the Spring context.
        log.info("Creating VectorDBService bean.");
        return new VectorDBService(vectorDBClient);
    }

}
