package com.qc.agent.vectordb.pojo;

import java.util.ArrayList;

/**
 * @Author: x<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/2/4 8:46
 */
public class ChatQA {

    // 问题
    private String question = "你是一个客服，你必须根据提供的背景知识回答问题，\n" +
            "要求：1、严格根据背景知识中的内容回答；\n" +
            "2、禁止回答背景知识中没有的内容；\n" +
            "3、如果背景知识中不存在问题的答案，你要回答不知道。\n" +
            "问题：";
    // 语料
    private ArrayList<SearchContent> searchContents = new ArrayList<>();
    private String prompt = "背景知识：\n";
    // 模型回答
    private String answer  = "";

    public String getQuestion() {
        return question;
    }

    public void setQuestion(String question) {
        this.question = this.question + question;
    }

    public String getAnswer() {
        return answer;
    }

    public void setAnswer(String answer) {
        this.answer = answer;
    }

    public ArrayList<SearchContent> getSearchContents() {
        return searchContents;
    }

    public void setSearchContents(ArrayList<SearchContent> searchContents) {
        this.searchContents = searchContents;
    }

    public String getPrompt() {
        for(SearchContent content: searchContents) {
            prompt += content.getContent() + "\n";
        }
        return prompt;
    }
}
