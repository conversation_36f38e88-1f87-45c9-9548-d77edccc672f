package com.qc.agent.vectordb.llm;

import com.qc.agent.vectordb.pojo.ChatInput;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.hunyuan.v20230901.HunyuanClient;
import com.tencentcloudapi.hunyuan.v20230901.models.ChatStdRequest;
import com.tencentcloudapi.hunyuan.v20230901.models.ChatStdResponse;
import com.tencentcloudapi.hunyuan.v20230901.models.Message;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * @Author: xuyunjie
 * @Date: 2024/1/31 15:28
 */
@Data
@Component
@ConfigurationProperties(prefix = "hunyuan")
@Slf4j
public class tencentHunyuan {
    private String secretId;
    private String secretKey;
    private HunyuanClient client;

    public void initClient() {
        Credential cred = new Credential(secretId, secretKey);
        ClientProfile clientProfile = new ClientProfile();
        client = new HunyuanClient(cred, "ap-beijing", clientProfile);
    }


    // 标准版
    public ChatStdResponse chat(ChatInput chatInput) {
        ChatStdResponse resp = null;
        try {
            ChatStdRequest req = new ChatStdRequest();
            Message[] msgs = chatInput.messageConstruct().stream().toArray(Message[]::new);
            req.setMessages(msgs);
            resp = client.ChatStd(req);

        } catch (TencentCloudSDKException e) {
            log.error("TencentCloudSDKException", e);
        }
        return resp;
    }

}
