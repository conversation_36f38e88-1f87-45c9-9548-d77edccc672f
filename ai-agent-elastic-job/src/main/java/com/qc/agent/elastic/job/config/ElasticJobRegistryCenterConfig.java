package com.qc.agent.elastic.job.config;

import com.dangdang.ddframe.job.reg.base.CoordinatorRegistryCenter;
import com.dangdang.ddframe.job.reg.zookeeper.ZookeeperConfiguration;
import com.dangdang.ddframe.job.reg.zookeeper.ZookeeperRegistryCenter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 *
 *
 * <AUTHOR>
 * @date 2025/4/1 16:24:42
 *
 */
@Configuration
public class ElasticJobRegistryCenterConfig
{
    // zk address
    @Value("${elasticjob.reg-center.server-lists}")
    private String zookeeperAddressList;

    // 命名空间
    @Value("${elasticjob.reg-center.namespace}")
    private String namespace;

    /****
     * 注册中心
     * @return 返回协调者注册中心对象
     */
    @Bean(initMethod = "init")
    public CoordinatorRegistryCenter zkCenter() {
        // 1 zk的配置
        // 参数一：zk的地址，如果是集群，每个地址用逗号隔开
        ZookeeperConfiguration zookeeperConfiguration = new ZookeeperConfiguration(zookeeperAddressList, namespace);
        // 创建协调注册中心
        // 2 CoordinatorRegistryCenter接口，elastic-job提供了一个实现ZookeeperRegistryCenter
        CoordinatorRegistryCenter zookeeperRegistryCenter = new ZookeeperRegistryCenter(zookeeperConfiguration);
        // 3 注册中心初始化
        zookeeperRegistryCenter.init();
        return zookeeperRegistryCenter;
    }
}
