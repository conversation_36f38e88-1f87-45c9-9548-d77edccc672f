package com.qc.agent.elastic.job.config;

import com.dangdang.ddframe.job.api.ElasticJob;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.dangdang.ddframe.job.config.JobCoreConfiguration;
import com.dangdang.ddframe.job.config.JobTypeConfiguration;
import com.dangdang.ddframe.job.config.simple.SimpleJobConfiguration;
import com.dangdang.ddframe.job.lite.config.LiteJobConfiguration;
import com.dangdang.ddframe.job.lite.spring.api.SpringJobScheduler;
import com.dangdang.ddframe.job.reg.base.CoordinatorRegistryCenter;
import com.qc.agent.common.util.ContextUtil;
import com.qc.agent.elastic.job.model.JobScheduler;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 *
 * <AUTHOR>
 * @date 2025/4/1 16:27:20
 *
 */
@Component
@EnableConfigurationProperties
@ConfigurationProperties(prefix = "elasticjob.jobs")
@Slf4j
public class ElasticJobConfig  implements InitializingBean
{
    /**
     * 任务列表job
     */
    @Setter
    private List<JobScheduler> list;

    @Resource
    private CoordinatorRegistryCenter zkCenter;


    @Override
    public void afterPropertiesSet() throws Exception {
        //这里可以替换为从数据库查询，将JobScheduler的信息配置到表里
        Map<String, JobScheduler> jobSchedulerMap = new HashMap<>(list.size());
        for (JobScheduler scheduler : list) {
            jobSchedulerMap.put(scheduler.getClazz(), scheduler);
        }
        //收集所有实现SimpleJob接口的job实例
        Map<String, SimpleJob> simpleJobMap = ContextUtil.getContext().getBeansOfType(SimpleJob.class);
        Collection<SimpleJob> simpleJobs = simpleJobMap.values();
        for (SimpleJob simpleJob : simpleJobs) {
            //根据job实例的全类名获取JobScheduler信息
            JobScheduler jobScheduler = jobSchedulerMap.get(simpleJob.getClass().getName());
            if (jobScheduler == null) {
                throw new RuntimeException("请配置[" + simpleJob.getClass().getName() + "]的JobScheduler");
            }
            log.info("job scheduler init :{}", jobScheduler.getClazz());
            SpringJobScheduler springJobScheduler = new SpringJobScheduler(simpleJob, zkCenter,
                    liteJobConfiguration(simpleJob.getClass(), jobScheduler.getCron(), jobScheduler.getShardingTotalCount(),
                            jobScheduler.getShardingItemParam(), jobScheduler.getJobParam(), jobScheduler.getJobName()));
            //启动任务
            springJobScheduler.init();
        }
    }

    private LiteJobConfiguration liteJobConfiguration(final Class<? extends ElasticJob> jobClass,
            final String cron,
            final int shardingTotalCount,
            final String shardingItemParam,
            final String jobParam,
            final String jobName
    ) {
        // 1 job核心配置
        JobCoreConfiguration.Builder builder = JobCoreConfiguration.newBuilder(
                // job的名字
                jobName,
                // cron表达式
                cron,
                // 分片总数
                shardingTotalCount
        );
        // 分片参数不是空，设置分片参数
        if (StringUtils.isNotBlank(shardingItemParam)) {
            builder.shardingItemParameters(shardingItemParam);
        }
        // 任务参数不是空，设置任务参数
        if(StringUtils.isNotBlank(jobParam)){
            builder.jobParameter(jobParam);
        }
        JobCoreConfiguration jobCoreConfiguration = builder.build();
        // 2 job类型配置
        // 参数1： job核心配置JobCoreConfiguration
        // 参数2 job的全类名
        JobTypeConfiguration jobTypeConfiguration = new SimpleJobConfiguration(jobCoreConfiguration, jobClass.getCanonicalName());
        // 3 job根配置（LiteJobConfiguration）
        return LiteJobConfiguration.newBuilder(jobTypeConfiguration)
                .overwrite(true)
                .build();
    }
}
