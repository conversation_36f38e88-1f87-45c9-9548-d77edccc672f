package com.qc.agent.elastic.job.model;

import lombok.Data;

/**
 *
 *
 * <AUTHOR>
 * @date 2025/4/1 16:25:54
 *
 */
@Data
public class JobScheduler
{
    /**
     * 继承ElasticJob的类的全路径名称
     */
    private String clazz;
    /**
     * cron表达式
     */
    private String cron;
    /**
     * 分片数
     */
    private int shardingTotalCount;
    /**
     * 分片参数
     */
    private String shardingItemParam;

    /**
     * 任务参数
     */
    private String jobParam;

    /**
     * job名称
     */
    private String jobName;

}
