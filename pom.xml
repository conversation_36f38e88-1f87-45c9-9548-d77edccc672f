<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.qc.agent</groupId>
	<artifactId>qc-ai-agent</artifactId>
	<version>1.0.0</version>
	<name>qc-ai-agent</name>
	<description>qc-ai-agent</description>
	<packaging>pom</packaging>

	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.7.14</version>
	</parent>

	<modules>
		<module>ai-agent-common</module>
		<module>admin</module>
		<module>ai-agent-jdbc</module>
		<module>ai-agent-vectordb</module>
		<module>ai-agent-storage</module>
		<module>ai-agent-redis</module>
		<module>ai-agent-lla</module>
		<module>ai-agent-elastic-job</module>
	</modules>

	<properties>
		<java.version>17</java.version>
		<application.version>1.0.0</application.version>
		<waiqin365-core-common.version>2.0.6</waiqin365-core-common.version>
	</properties>

	<!-- 仓库配置放在这里 ↓↓↓ -->
	<repositories>
		<repository>
			<id>spring-snapshots</id>
			<url>https://repo.spring.io/snapshot</url>
			<snapshots>
				<enabled>true</enabled>
			</snapshots>
		</repository>
	</repositories>
	<dependencyManagement>
		<dependencies>
<!--			<dependency>-->
<!--				<groupId>org.springframework.data</groupId>-->
<!--				<artifactId>spring-data-redis</artifactId>-->
<!--				<version>1.8.17.RELEASE</version>-->
<!--			</dependency>-->
<!--			<dependency>-->
<!--				<groupId>org.springframework.data</groupId>-->
<!--				<artifactId>spring-data-commons</artifactId>-->
<!--				<version>1.13.1.RELEASE</version>-->
<!--			</dependency>-->
<!--			<dependency>-->
<!--				<groupId>redis.clients</groupId>-->
<!--				<artifactId>jedis</artifactId>-->
<!--				&lt;!&ndash; 1.8.17.RELEASE 里的JedisConnectionFactory,用的redis.clients.util.Pool，而3.0的是redis.clients.jedis.util.pool &ndash;&gt;-->
<!--				&lt;!&ndash;  <version>3.0.0</version> &ndash;&gt;-->
<!--				<version>2.9.0</version>-->
<!--			</dependency>-->

		</dependencies>
	</dependencyManagement>
	<build>
		<finalName>ai-agent-${application.version}</finalName>
	</build>

</project>
